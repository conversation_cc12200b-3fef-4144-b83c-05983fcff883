using Microsoft.AspNetCore.Mvc;
using CrmApi.Models.State;
using CrmApi.Services.State;

namespace CrmApi.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class StatesController : ControllerBase
    {
        private readonly IStateService _stateService;

        public StatesController(IStateService stateService)
        {
            _stateService = stateService;
        }

        [HttpGet]
        public async Task<ActionResult<IEnumerable<StateResponse>>> GetStates()
        {
            var states = await _stateService.GetAllStatesAsync();
            return Ok(states);
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<StateResponse>> GetState(int id)
        {
            var state = await _stateService.GetStateByIdAsync(id);

            if (state == null)
            {
                return NotFound();
            }

            return Ok(state);
        }

        [HttpPost]
        public async Task<ActionResult<StateResponse>> PostState([FromBody] CreateStateRequest request)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var state = await _stateService.CreateStateAsync(request);
            return CreatedAtAction("GetState", new { id = state.Id }, state);
        }

        [HttpPut("{id}")]
        public async Task<ActionResult<StateResponse>> PutState(int id, [FromBody] UpdateStateRequest request)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var state = await _stateService.UpdateStateAsync(id, request);
            return Ok(state);
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteState(int id)
        {
            var result = await _stateService.DeleteStateAsync(id);
            if (!result)
            {
                return NotFound();
            }

            return NoContent();
        }
    }
}