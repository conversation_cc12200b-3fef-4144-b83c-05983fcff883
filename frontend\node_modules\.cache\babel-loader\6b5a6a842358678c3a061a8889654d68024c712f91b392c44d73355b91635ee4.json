{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Advanced\\\\Advanced CRM filed all use\\\\frontend\\\\src\\\\App.js\";\nimport React from 'react';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport { ToastContainer } from 'react-toastify';\nimport 'react-toastify/dist/ReactToastify.css';\nimport { AuthProvider } from './context/AuthContext';\nimport ProtectedRoute from './components/ProtectedRoute';\nimport Navbar from './components/Navbar';\nimport Dashboard from './components/Dashboard';\nimport DivisionSetup from './components/DivisionSetup';\nimport CategoryManagement from './components/CategoryManagement';\nimport PersonManagement from './components/PersonManagement';\nimport PersonsView from './components/PersonsView';\nimport ImportPersons from './components/import/ImportPersons';\nimport FormBuilder from './components/forms/FormBuilder';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(AuthProvider, {\n    children: /*#__PURE__*/_jsxDEV(Router, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"App\",\n        children: [/*#__PURE__*/_jsxDEV(ProtectedRoute, {\n          children: [/*#__PURE__*/_jsxDEV(Navbar, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 23,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"main-content\",\n            children: /*#__PURE__*/_jsxDEV(Routes, {\n              children: [/*#__PURE__*/_jsxDEV(Route, {\n                path: \"/\",\n                element: /*#__PURE__*/_jsxDEV(Dashboard, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 26,\n                  columnNumber: 42\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 26,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/dashboard\",\n                element: /*#__PURE__*/_jsxDEV(Dashboard, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 27,\n                  columnNumber: 51\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 27,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/divisions\",\n                element: /*#__PURE__*/_jsxDEV(DivisionSetup, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 28,\n                  columnNumber: 51\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 28,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/categories\",\n                element: /*#__PURE__*/_jsxDEV(CategoryManagement, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 29,\n                  columnNumber: 52\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 29,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/persons-view\",\n                element: /*#__PURE__*/_jsxDEV(PersonsView, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 30,\n                  columnNumber: 54\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 30,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/persons\",\n                element: /*#__PURE__*/_jsxDEV(PersonManagement, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 31,\n                  columnNumber: 49\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 31,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/import\",\n                element: /*#__PURE__*/_jsxDEV(ImportPersons, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 32,\n                  columnNumber: 48\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 32,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/form-builder\",\n                element: /*#__PURE__*/_jsxDEV(FormBuilder, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 33,\n                  columnNumber: 54\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 33,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 25,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 24,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 22,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ToastContainer, {\n          position: \"top-right\",\n          autoClose: 3000,\n          hideProgressBar: false,\n          newestOnTop: false,\n          closeOnClick: true,\n          rtl: false,\n          pauseOnFocusLoss: true,\n          draggable: true,\n          pauseOnHover: true,\n          theme: \"light\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 37,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 21,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 20,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 19,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "ToastContainer", "<PERSON>th<PERSON><PERSON><PERSON>", "ProtectedRoute", "<PERSON><PERSON><PERSON>", "Dashboard", "DivisionSetup", "CategoryManagement", "PersonManagement", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>rt<PERSON><PERSON><PERSON>", "FormBuilder", "jsxDEV", "_jsxDEV", "App", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "path", "element", "position", "autoClose", "hideProgressBar", "newestOnTop", "closeOnClick", "rtl", "pauseOnFocusLoss", "draggable", "pauseOnHover", "theme", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/Advanced/Advanced CRM filed all use/frontend/src/App.js"], "sourcesContent": ["import React from 'react';\r\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\r\nimport { ToastContainer } from 'react-toastify';\r\nimport 'react-toastify/dist/ReactToastify.css';\r\nimport { AuthProvider } from './context/AuthContext';\r\nimport ProtectedRoute from './components/ProtectedRoute';\r\nimport Navbar from './components/Navbar';\r\nimport Dashboard from './components/Dashboard';\r\nimport DivisionSetup from './components/DivisionSetup';\r\nimport CategoryManagement from './components/CategoryManagement';\r\nimport PersonManagement from './components/PersonManagement';\r\nimport PersonsView from './components/PersonsView';\r\nimport ImportPersons from './components/import/ImportPersons';\r\nimport FormBuilder from './components/forms/FormBuilder';\r\n\r\n\r\nfunction App() {\r\n  return (\r\n    <AuthProvider>\r\n      <Router>\r\n        <div className=\"App\">\r\n          <ProtectedRoute>\r\n            <Navbar />\r\n            <div className=\"main-content\">\r\n              <Routes>\r\n                <Route path=\"/\" element={<Dashboard />} />\r\n                <Route path=\"/dashboard\" element={<Dashboard />} />\r\n                <Route path=\"/divisions\" element={<DivisionSetup />} />\r\n                <Route path=\"/categories\" element={<CategoryManagement />} />\r\n                <Route path=\"/persons-view\" element={<PersonsView />} />\r\n                <Route path=\"/persons\" element={<PersonManagement />} />\r\n                <Route path=\"/import\" element={<ImportPersons />} />\r\n                <Route path=\"/form-builder\" element={<FormBuilder />} />\r\n              </Routes>\r\n            </div>\r\n          </ProtectedRoute>\r\n          <ToastContainer\r\n            position=\"top-right\"\r\n            autoClose={3000}\r\n            hideProgressBar={false}\r\n            newestOnTop={false}\r\n            closeOnClick\r\n            rtl={false}\r\n            pauseOnFocusLoss\r\n            draggable\r\n            pauseOnHover\r\n            theme=\"light\"\r\n          />\r\n        </div>\r\n      </Router>\r\n    </AuthProvider>\r\n  );\r\n}\r\n\r\nexport default App;"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,QAAQ,kBAAkB;AACzE,SAASC,cAAc,QAAQ,gBAAgB;AAC/C,OAAO,uCAAuC;AAC9C,SAASC,YAAY,QAAQ,uBAAuB;AACpD,OAAOC,cAAc,MAAM,6BAA6B;AACxD,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,OAAOC,aAAa,MAAM,4BAA4B;AACtD,OAAOC,kBAAkB,MAAM,iCAAiC;AAChE,OAAOC,gBAAgB,MAAM,+BAA+B;AAC5D,OAAOC,WAAW,MAAM,0BAA0B;AAClD,OAAOC,aAAa,MAAM,mCAAmC;AAC7D,OAAOC,WAAW,MAAM,gCAAgC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAGzD,SAASC,GAAGA,CAAA,EAAG;EACb,oBACED,OAAA,CAACX,YAAY;IAAAa,QAAA,eACXF,OAAA,CAACf,MAAM;MAAAiB,QAAA,eACLF,OAAA;QAAKG,SAAS,EAAC,KAAK;QAAAD,QAAA,gBAClBF,OAAA,CAACV,cAAc;UAAAY,QAAA,gBACbF,OAAA,CAACT,MAAM;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACVP,OAAA;YAAKG,SAAS,EAAC,cAAc;YAAAD,QAAA,eAC3BF,OAAA,CAACd,MAAM;cAAAgB,QAAA,gBACLF,OAAA,CAACb,KAAK;gBAACqB,IAAI,EAAC,GAAG;gBAACC,OAAO,eAAET,OAAA,CAACR,SAAS;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC1CP,OAAA,CAACb,KAAK;gBAACqB,IAAI,EAAC,YAAY;gBAACC,OAAO,eAAET,OAAA,CAACR,SAAS;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACnDP,OAAA,CAACb,KAAK;gBAACqB,IAAI,EAAC,YAAY;gBAACC,OAAO,eAAET,OAAA,CAACP,aAAa;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACvDP,OAAA,CAACb,KAAK;gBAACqB,IAAI,EAAC,aAAa;gBAACC,OAAO,eAAET,OAAA,CAACN,kBAAkB;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC7DP,OAAA,CAACb,KAAK;gBAACqB,IAAI,EAAC,eAAe;gBAACC,OAAO,eAAET,OAAA,CAACJ,WAAW;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACxDP,OAAA,CAACb,KAAK;gBAACqB,IAAI,EAAC,UAAU;gBAACC,OAAO,eAAET,OAAA,CAACL,gBAAgB;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACxDP,OAAA,CAACb,KAAK;gBAACqB,IAAI,EAAC,SAAS;gBAACC,OAAO,eAAET,OAAA,CAACH,aAAa;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACpDP,OAAA,CAACb,KAAK;gBAACqB,IAAI,EAAC,eAAe;gBAACC,OAAO,eAAET,OAAA,CAACF,WAAW;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACQ,CAAC,eACjBP,OAAA,CAACZ,cAAc;UACbsB,QAAQ,EAAC,WAAW;UACpBC,SAAS,EAAE,IAAK;UAChBC,eAAe,EAAE,KAAM;UACvBC,WAAW,EAAE,KAAM;UACnBC,YAAY;UACZC,GAAG,EAAE,KAAM;UACXC,gBAAgB;UAChBC,SAAS;UACTC,YAAY;UACZC,KAAK,EAAC;QAAO;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEnB;AAACa,EAAA,GApCQnB,GAAG;AAsCZ,eAAeA,GAAG;AAAC,IAAAmB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}