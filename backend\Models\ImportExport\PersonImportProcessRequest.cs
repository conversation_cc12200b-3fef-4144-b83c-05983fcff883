using System.ComponentModel.DataAnnotations;

namespace CrmApi.Models.ImportExport
{
    /// <summary>
    /// Internal request model for processing import in background tasks.
    /// Contains copied file content instead of IFormFile to avoid stream disposal issues.
    /// </summary>
    public class PersonImportProcessRequest
    {
        [Required(ErrorMessage = "File content is required")]
        public MemoryStream FileContent { get; set; } = null!;

        [Required(ErrorMessage = "File name is required")]
        public string FileName { get; set; } = string.Empty;

        [Required(ErrorMessage = "Import mode is required")]
        public ImportMode ImportMode { get; set; } = ImportMode.SkipDuplicates;

        public bool ValidateOnly { get; set; } = false;

        public int? BatchSize { get; set; } = 100;

        // Mandatory division and category for all imported records
        [Required(ErrorMessage = "Division ID is required for import")]
        [Range(1, int.MaxValue, ErrorMessage = "Division ID must be a positive number")]
        public int DefaultDivisionId { get; set; }

        [Required(ErrorMessage = "Category ID is required for import")]
        [Range(1, int.MaxValue, ErrorMessage = "Category ID must be a positive number")]
        public int DefaultCategoryId { get; set; }

        // Optional subcategory for all imported records
        [Range(1, int.MaxValue, ErrorMessage = "SubCategory ID must be a positive number")]
        public int? DefaultSubCategoryId { get; set; }

        // Default values for required fields that are missing from Excel file
        public string? DefaultValues { get; set; }

        // Field mapping from Excel columns to person fields (JSON)
        public string? FieldMapping { get; set; }
    }
}
