/// <reference types="react" />
declare const Bounce: ({ children, position, preventExitTransition, done, nodeRef, isIn }: import("..").ToastTransitionProps) => import("react").JSX.Element;
declare const Slide: ({ children, position, preventExitTransition, done, nodeRef, isIn }: import("..").ToastTransitionProps) => import("react").JSX.Element;
declare const Zoom: ({ children, position, preventExitTransition, done, nodeRef, isIn }: import("..").ToastTransitionProps) => import("react").JSX.Element;
declare const Flip: ({ children, position, preventExitTransition, done, nodeRef, isIn }: import("..").ToastTransitionProps) => import("react").JSX.Element;
export { Bounce, Slide, Zoom, Flip };
