{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Advanced\\\\Advanced CRM filed all use\\\\frontend\\\\src\\\\components\\\\forms\\\\FieldConfigModal.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport './FieldConfigModal.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst FieldConfigModal = ({\n  field,\n  onSave,\n  onCancel\n}) => {\n  _s();\n  var _config$conditional, _config$conditional2;\n  const [config, setConfig] = useState({\n    key: '',\n    label: '',\n    type: '',\n    required: false,\n    placeholder: '',\n    helpText: '',\n    validation: {},\n    conditional: null,\n    options: []\n  });\n  useEffect(() => {\n    if (field) {\n      setConfig({\n        key: field.key,\n        label: field.label,\n        type: field.type,\n        required: field.required || false,\n        placeholder: field.placeholder || '',\n        helpText: field.helpText || '',\n        validation: field.validation || {},\n        conditional: field.conditional || null,\n        options: field.options || []\n      });\n    }\n  }, [field]);\n  const handleInputChange = (key, value) => {\n    setConfig(prev => ({\n      ...prev,\n      [key]: value\n    }));\n  };\n  const handleValidationChange = (key, value) => {\n    setConfig(prev => ({\n      ...prev,\n      validation: {\n        ...prev.validation,\n        [key]: value\n      }\n    }));\n  };\n  const handleConditionalChange = (key, value) => {\n    setConfig(prev => ({\n      ...prev,\n      conditional: prev.conditional ? {\n        ...prev.conditional,\n        [key]: value\n      } : {\n        [key]: value\n      }\n    }));\n  };\n  const handleSave = () => {\n    const updatedField = {\n      ...field,\n      ...config\n    };\n    onSave(updatedField);\n  };\n  const renderValidationOptions = () => {\n    switch (config.type) {\n      case 'text':\n      case 'textarea':\n        return /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"Minimum Length\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 75,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"number\",\n              value: config.validation.minLength || '',\n              onChange: e => handleValidationChange('minLength', parseInt(e.target.value) || undefined),\n              placeholder: \"Minimum character length\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 76,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 74,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"Maximum Length\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 84,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"number\",\n              value: config.validation.maxLength || '',\n              onChange: e => handleValidationChange('maxLength', parseInt(e.target.value) || undefined),\n              placeholder: \"Maximum character length\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 85,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 83,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"Pattern (Regex)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 93,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: config.validation.pattern || '',\n              onChange: e => handleValidationChange('pattern', e.target.value),\n              placeholder: \"Regular expression pattern\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 94,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 92,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true);\n      case 'number':\n        return /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"Minimum Value\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 107,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"number\",\n              value: config.validation.min || '',\n              onChange: e => handleValidationChange('min', parseInt(e.target.value) || undefined),\n              placeholder: \"Minimum value\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 108,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 106,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"Maximum Value\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 116,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"number\",\n              value: config.validation.max || '',\n              onChange: e => handleValidationChange('max', parseInt(e.target.value) || undefined),\n              placeholder: \"Maximum value\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 117,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 115,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true);\n      case 'email':\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: /*#__PURE__*/_jsxDEV(\"label\", {\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"checkbox\",\n              checked: config.validation.allowMultiple || false,\n              onChange: e => handleValidationChange('allowMultiple', e.target.checked)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 130,\n              columnNumber: 15\n            }, this), \"Allow multiple emails (comma-separated)\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 128,\n          columnNumber: 11\n        }, this);\n      default:\n        return null;\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"modal-overlay\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: [\"Configure Field: \", field === null || field === void 0 ? void 0 : field.label]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          onClick: onCancel,\n          className: \"close-button\",\n          children: \"\\xD7\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 149,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 147,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-body\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            children: \"Field Label *\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 154,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            value: config.label,\n            onChange: e => handleInputChange('label', e.target.value),\n            placeholder: \"Enter field label\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 153,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            children: \"Field Type\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 164,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: config.type,\n            onChange: e => handleInputChange('type', e.target.value),\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"text\",\n              children: \"Text\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 169,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"textarea\",\n              children: \"Textarea\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"number\",\n              children: \"Number\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"email\",\n              children: \"Email\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"tel\",\n              children: \"Phone\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"url\",\n              children: \"URL\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 174,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"date\",\n              children: \"Date\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"checkbox\",\n              children: \"Checkbox\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"select\",\n              children: \"Select\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 177,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"multiselect\",\n              children: \"Multi-Select\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 178,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"array\",\n              children: \"Array\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 163,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: /*#__PURE__*/_jsxDEV(\"label\", {\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"checkbox\",\n              checked: config.required,\n              onChange: e => handleInputChange('required', e.target.checked)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 185,\n              columnNumber: 15\n            }, this), \"Required Field\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 184,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            children: \"Placeholder Text\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            value: config.placeholder,\n            onChange: e => handleInputChange('placeholder', e.target.value),\n            placeholder: \"Enter placeholder text\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 196,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            children: \"Help Text\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n            value: config.helpText,\n            onChange: e => handleInputChange('helpText', e.target.value),\n            placeholder: \"Enter help text for users\",\n            rows: \"2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"config-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            children: \"Validation Rules\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 216,\n            columnNumber: 13\n          }, this), renderValidationOptions()]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 215,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"config-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            children: \"Conditional Display\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 222,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"Show when field:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 224,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: ((_config$conditional = config.conditional) === null || _config$conditional === void 0 ? void 0 : _config$conditional.field) || '',\n              onChange: e => handleConditionalChange('field', e.target.value),\n              placeholder: \"Field name (e.g., hasAssociate)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 225,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"Has value:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 233,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: ((_config$conditional2 = config.conditional) === null || _config$conditional2 === void 0 ? void 0 : _config$conditional2.value) || '',\n              onChange: e => handleConditionalChange('value', e.target.value),\n              placeholder: \"Value (e.g., true, false, or specific value)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 234,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 232,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 221,\n          columnNumber: 11\n        }, this), (config.type === 'select' || config.type === 'multiselect') && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"config-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            children: \"Options\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"options-list\",\n            children: [config.options.map((option, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"option-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                value: option.label,\n                onChange: e => {\n                  const newOptions = [...config.options];\n                  newOptions[index] = {\n                    ...option,\n                    label: e.target.value\n                  };\n                  handleInputChange('options', newOptions);\n                },\n                placeholder: \"Option label\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 250,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                value: option.value,\n                onChange: e => {\n                  const newOptions = [...config.options];\n                  newOptions[index] = {\n                    ...option,\n                    value: e.target.value\n                  };\n                  handleInputChange('options', newOptions);\n                },\n                placeholder: \"Option value\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 260,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                onClick: () => {\n                  const newOptions = config.options.filter((_, i) => i !== index);\n                  handleInputChange('options', newOptions);\n                },\n                className: \"btn-remove\",\n                children: \"Remove\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 270,\n                columnNumber: 21\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 249,\n              columnNumber: 19\n            }, this)), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              onClick: () => {\n                const newOptions = [...config.options, {\n                  label: '',\n                  value: ''\n                }];\n                handleInputChange('options', newOptions);\n              },\n              className: \"btn-add\",\n              children: \"Add Option\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 282,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 247,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 245,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 152,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-footer\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          onClick: onCancel,\n          className: \"btn btn-outline\",\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 298,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          onClick: handleSave,\n          className: \"btn btn-primary\",\n          children: \"Save Configuration\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 301,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 297,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 146,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 145,\n    columnNumber: 5\n  }, this);\n};\n_s(FieldConfigModal, \"UfIQVHtBLEh4Hn76EzVJz4mYU1A=\");\n_c = FieldConfigModal;\nexport default FieldConfigModal;\nvar _c;\n$RefreshReg$(_c, \"FieldConfigModal\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "FieldConfigModal", "field", "onSave", "onCancel", "_s", "_config$conditional", "_config$conditional2", "config", "setConfig", "key", "label", "type", "required", "placeholder", "helpText", "validation", "conditional", "options", "handleInputChange", "value", "prev", "handleValidationChange", "handleConditionalChange", "handleSave", "updatedField", "renderValidationOptions", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "<PERSON><PERSON><PERSON><PERSON>", "onChange", "e", "parseInt", "target", "undefined", "max<PERSON><PERSON><PERSON>", "pattern", "min", "max", "checked", "allowMultiple", "onClick", "rows", "map", "option", "index", "newOptions", "filter", "_", "i", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/Advanced/Advanced CRM filed all use/frontend/src/components/forms/FieldConfigModal.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport './FieldConfigModal.css';\n\nconst FieldConfigModal = ({ field, onSave, onCancel }) => {\n  const [config, setConfig] = useState({\n    key: '',\n    label: '',\n    type: '',\n    required: false,\n    placeholder: '',\n    helpText: '',\n    validation: {},\n    conditional: null,\n    options: []\n  });\n\n  useEffect(() => {\n    if (field) {\n      setConfig({\n        key: field.key,\n        label: field.label,\n        type: field.type,\n        required: field.required || false,\n        placeholder: field.placeholder || '',\n        helpText: field.helpText || '',\n        validation: field.validation || {},\n        conditional: field.conditional || null,\n        options: field.options || []\n      });\n    }\n  }, [field]);\n\n  const handleInputChange = (key, value) => {\n    setConfig(prev => ({\n      ...prev,\n      [key]: value\n    }));\n  };\n\n  const handleValidationChange = (key, value) => {\n    setConfig(prev => ({\n      ...prev,\n      validation: {\n        ...prev.validation,\n        [key]: value\n      }\n    }));\n  };\n\n  const handleConditionalChange = (key, value) => {\n    setConfig(prev => ({\n      ...prev,\n      conditional: prev.conditional ? {\n        ...prev.conditional,\n        [key]: value\n      } : { [key]: value }\n    }));\n  };\n\n  const handleSave = () => {\n    const updatedField = {\n      ...field,\n      ...config\n    };\n    onSave(updatedField);\n  };\n\n  const renderValidationOptions = () => {\n    switch (config.type) {\n      case 'text':\n      case 'textarea':\n        return (\n          <>\n            <div className=\"form-group\">\n              <label>Minimum Length</label>\n              <input\n                type=\"number\"\n                value={config.validation.minLength || ''}\n                onChange={(e) => handleValidationChange('minLength', parseInt(e.target.value) || undefined)}\n                placeholder=\"Minimum character length\"\n              />\n            </div>\n            <div className=\"form-group\">\n              <label>Maximum Length</label>\n              <input\n                type=\"number\"\n                value={config.validation.maxLength || ''}\n                onChange={(e) => handleValidationChange('maxLength', parseInt(e.target.value) || undefined)}\n                placeholder=\"Maximum character length\"\n              />\n            </div>\n            <div className=\"form-group\">\n              <label>Pattern (Regex)</label>\n              <input\n                type=\"text\"\n                value={config.validation.pattern || ''}\n                onChange={(e) => handleValidationChange('pattern', e.target.value)}\n                placeholder=\"Regular expression pattern\"\n              />\n            </div>\n          </>\n        );\n      case 'number':\n        return (\n          <>\n            <div className=\"form-group\">\n              <label>Minimum Value</label>\n              <input\n                type=\"number\"\n                value={config.validation.min || ''}\n                onChange={(e) => handleValidationChange('min', parseInt(e.target.value) || undefined)}\n                placeholder=\"Minimum value\"\n              />\n            </div>\n            <div className=\"form-group\">\n              <label>Maximum Value</label>\n              <input\n                type=\"number\"\n                value={config.validation.max || ''}\n                onChange={(e) => handleValidationChange('max', parseInt(e.target.value) || undefined)}\n                placeholder=\"Maximum value\"\n              />\n            </div>\n          </>\n        );\n      case 'email':\n        return (\n          <div className=\"form-group\">\n            <label>\n              <input\n                type=\"checkbox\"\n                checked={config.validation.allowMultiple || false}\n                onChange={(e) => handleValidationChange('allowMultiple', e.target.checked)}\n              />\n              Allow multiple emails (comma-separated)\n            </label>\n          </div>\n        );\n      default:\n        return null;\n    }\n  };\n\n  return (\n    <div className=\"modal-overlay\">\n      <div className=\"modal-content\">\n        <div className=\"modal-header\">\n          <h3>Configure Field: {field?.label}</h3>\n          <button type=\"button\" onClick={onCancel} className=\"close-button\">×</button>\n        </div>\n\n        <div className=\"modal-body\">\n          <div className=\"form-group\">\n            <label>Field Label *</label>\n            <input\n              type=\"text\"\n              value={config.label}\n              onChange={(e) => handleInputChange('label', e.target.value)}\n              placeholder=\"Enter field label\"\n            />\n          </div>\n\n          <div className=\"form-group\">\n            <label>Field Type</label>\n            <select\n              value={config.type}\n              onChange={(e) => handleInputChange('type', e.target.value)}\n            >\n              <option value=\"text\">Text</option>\n              <option value=\"textarea\">Textarea</option>\n              <option value=\"number\">Number</option>\n              <option value=\"email\">Email</option>\n              <option value=\"tel\">Phone</option>\n              <option value=\"url\">URL</option>\n              <option value=\"date\">Date</option>\n              <option value=\"checkbox\">Checkbox</option>\n              <option value=\"select\">Select</option>\n              <option value=\"multiselect\">Multi-Select</option>\n              <option value=\"array\">Array</option>\n            </select>\n          </div>\n\n          <div className=\"form-group\">\n            <label>\n              <input\n                type=\"checkbox\"\n                checked={config.required}\n                onChange={(e) => handleInputChange('required', e.target.checked)}\n              />\n              Required Field\n            </label>\n          </div>\n\n          <div className=\"form-group\">\n            <label>Placeholder Text</label>\n            <input\n              type=\"text\"\n              value={config.placeholder}\n              onChange={(e) => handleInputChange('placeholder', e.target.value)}\n              placeholder=\"Enter placeholder text\"\n            />\n          </div>\n\n          <div className=\"form-group\">\n            <label>Help Text</label>\n            <textarea\n              value={config.helpText}\n              onChange={(e) => handleInputChange('helpText', e.target.value)}\n              placeholder=\"Enter help text for users\"\n              rows=\"2\"\n            />\n          </div>\n\n          {/* Validation Options */}\n          <div className=\"config-section\">\n            <h4>Validation Rules</h4>\n            {renderValidationOptions()}\n          </div>\n\n          {/* Conditional Display */}\n          <div className=\"config-section\">\n            <h4>Conditional Display</h4>\n            <div className=\"form-group\">\n              <label>Show when field:</label>\n              <input\n                type=\"text\"\n                value={config.conditional?.field || ''}\n                onChange={(e) => handleConditionalChange('field', e.target.value)}\n                placeholder=\"Field name (e.g., hasAssociate)\"\n              />\n            </div>\n            <div className=\"form-group\">\n              <label>Has value:</label>\n              <input\n                type=\"text\"\n                value={config.conditional?.value || ''}\n                onChange={(e) => handleConditionalChange('value', e.target.value)}\n                placeholder=\"Value (e.g., true, false, or specific value)\"\n              />\n            </div>\n          </div>\n\n          {/* Options for select/multiselect */}\n          {(config.type === 'select' || config.type === 'multiselect') && (\n            <div className=\"config-section\">\n              <h4>Options</h4>\n              <div className=\"options-list\">\n                {config.options.map((option, index) => (\n                  <div key={index} className=\"option-item\">\n                    <input\n                      type=\"text\"\n                      value={option.label}\n                      onChange={(e) => {\n                        const newOptions = [...config.options];\n                        newOptions[index] = { ...option, label: e.target.value };\n                        handleInputChange('options', newOptions);\n                      }}\n                      placeholder=\"Option label\"\n                    />\n                    <input\n                      type=\"text\"\n                      value={option.value}\n                      onChange={(e) => {\n                        const newOptions = [...config.options];\n                        newOptions[index] = { ...option, value: e.target.value };\n                        handleInputChange('options', newOptions);\n                      }}\n                      placeholder=\"Option value\"\n                    />\n                    <button\n                      type=\"button\"\n                      onClick={() => {\n                        const newOptions = config.options.filter((_, i) => i !== index);\n                        handleInputChange('options', newOptions);\n                      }}\n                      className=\"btn-remove\"\n                    >\n                      Remove\n                    </button>\n                  </div>\n                ))}\n                <button\n                  type=\"button\"\n                  onClick={() => {\n                    const newOptions = [...config.options, { label: '', value: '' }];\n                    handleInputChange('options', newOptions);\n                  }}\n                  className=\"btn-add\"\n                >\n                  Add Option\n                </button>\n              </div>\n            </div>\n          )}\n        </div>\n\n        <div className=\"modal-footer\">\n          <button type=\"button\" onClick={onCancel} className=\"btn btn-outline\">\n            Cancel\n          </button>\n          <button type=\"button\" onClick={handleSave} className=\"btn btn-primary\">\n            Save Configuration\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default FieldConfigModal;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAO,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEhC,MAAMC,gBAAgB,GAAGA,CAAC;EAAEC,KAAK;EAAEC,MAAM;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,mBAAA,EAAAC,oBAAA;EACxD,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGd,QAAQ,CAAC;IACnCe,GAAG,EAAE,EAAE;IACPC,KAAK,EAAE,EAAE;IACTC,IAAI,EAAE,EAAE;IACRC,QAAQ,EAAE,KAAK;IACfC,WAAW,EAAE,EAAE;IACfC,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,CAAC,CAAC;IACdC,WAAW,EAAE,IAAI;IACjBC,OAAO,EAAE;EACX,CAAC,CAAC;EAEFtB,SAAS,CAAC,MAAM;IACd,IAAIM,KAAK,EAAE;MACTO,SAAS,CAAC;QACRC,GAAG,EAAER,KAAK,CAACQ,GAAG;QACdC,KAAK,EAAET,KAAK,CAACS,KAAK;QAClBC,IAAI,EAAEV,KAAK,CAACU,IAAI;QAChBC,QAAQ,EAAEX,KAAK,CAACW,QAAQ,IAAI,KAAK;QACjCC,WAAW,EAAEZ,KAAK,CAACY,WAAW,IAAI,EAAE;QACpCC,QAAQ,EAAEb,KAAK,CAACa,QAAQ,IAAI,EAAE;QAC9BC,UAAU,EAAEd,KAAK,CAACc,UAAU,IAAI,CAAC,CAAC;QAClCC,WAAW,EAAEf,KAAK,CAACe,WAAW,IAAI,IAAI;QACtCC,OAAO,EAAEhB,KAAK,CAACgB,OAAO,IAAI;MAC5B,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAAChB,KAAK,CAAC,CAAC;EAEX,MAAMiB,iBAAiB,GAAGA,CAACT,GAAG,EAAEU,KAAK,KAAK;IACxCX,SAAS,CAACY,IAAI,KAAK;MACjB,GAAGA,IAAI;MACP,CAACX,GAAG,GAAGU;IACT,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAME,sBAAsB,GAAGA,CAACZ,GAAG,EAAEU,KAAK,KAAK;IAC7CX,SAAS,CAACY,IAAI,KAAK;MACjB,GAAGA,IAAI;MACPL,UAAU,EAAE;QACV,GAAGK,IAAI,CAACL,UAAU;QAClB,CAACN,GAAG,GAAGU;MACT;IACF,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMG,uBAAuB,GAAGA,CAACb,GAAG,EAAEU,KAAK,KAAK;IAC9CX,SAAS,CAACY,IAAI,KAAK;MACjB,GAAGA,IAAI;MACPJ,WAAW,EAAEI,IAAI,CAACJ,WAAW,GAAG;QAC9B,GAAGI,IAAI,CAACJ,WAAW;QACnB,CAACP,GAAG,GAAGU;MACT,CAAC,GAAG;QAAE,CAACV,GAAG,GAAGU;MAAM;IACrB,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMI,UAAU,GAAGA,CAAA,KAAM;IACvB,MAAMC,YAAY,GAAG;MACnB,GAAGvB,KAAK;MACR,GAAGM;IACL,CAAC;IACDL,MAAM,CAACsB,YAAY,CAAC;EACtB,CAAC;EAED,MAAMC,uBAAuB,GAAGA,CAAA,KAAM;IACpC,QAAQlB,MAAM,CAACI,IAAI;MACjB,KAAK,MAAM;MACX,KAAK,UAAU;QACb,oBACEd,OAAA,CAAAE,SAAA;UAAA2B,QAAA,gBACE7B,OAAA;YAAK8B,SAAS,EAAC,YAAY;YAAAD,QAAA,gBACzB7B,OAAA;cAAA6B,QAAA,EAAO;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC7BlC,OAAA;cACEc,IAAI,EAAC,QAAQ;cACbQ,KAAK,EAAEZ,MAAM,CAACQ,UAAU,CAACiB,SAAS,IAAI,EAAG;cACzCC,QAAQ,EAAGC,CAAC,IAAKb,sBAAsB,CAAC,WAAW,EAAEc,QAAQ,CAACD,CAAC,CAACE,MAAM,CAACjB,KAAK,CAAC,IAAIkB,SAAS,CAAE;cAC5FxB,WAAW,EAAC;YAA0B;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNlC,OAAA;YAAK8B,SAAS,EAAC,YAAY;YAAAD,QAAA,gBACzB7B,OAAA;cAAA6B,QAAA,EAAO;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC7BlC,OAAA;cACEc,IAAI,EAAC,QAAQ;cACbQ,KAAK,EAAEZ,MAAM,CAACQ,UAAU,CAACuB,SAAS,IAAI,EAAG;cACzCL,QAAQ,EAAGC,CAAC,IAAKb,sBAAsB,CAAC,WAAW,EAAEc,QAAQ,CAACD,CAAC,CAACE,MAAM,CAACjB,KAAK,CAAC,IAAIkB,SAAS,CAAE;cAC5FxB,WAAW,EAAC;YAA0B;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNlC,OAAA;YAAK8B,SAAS,EAAC,YAAY;YAAAD,QAAA,gBACzB7B,OAAA;cAAA6B,QAAA,EAAO;YAAe;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC9BlC,OAAA;cACEc,IAAI,EAAC,MAAM;cACXQ,KAAK,EAAEZ,MAAM,CAACQ,UAAU,CAACwB,OAAO,IAAI,EAAG;cACvCN,QAAQ,EAAGC,CAAC,IAAKb,sBAAsB,CAAC,SAAS,EAAEa,CAAC,CAACE,MAAM,CAACjB,KAAK,CAAE;cACnEN,WAAW,EAAC;YAA4B;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA,eACN,CAAC;MAEP,KAAK,QAAQ;QACX,oBACElC,OAAA,CAAAE,SAAA;UAAA2B,QAAA,gBACE7B,OAAA;YAAK8B,SAAS,EAAC,YAAY;YAAAD,QAAA,gBACzB7B,OAAA;cAAA6B,QAAA,EAAO;YAAa;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC5BlC,OAAA;cACEc,IAAI,EAAC,QAAQ;cACbQ,KAAK,EAAEZ,MAAM,CAACQ,UAAU,CAACyB,GAAG,IAAI,EAAG;cACnCP,QAAQ,EAAGC,CAAC,IAAKb,sBAAsB,CAAC,KAAK,EAAEc,QAAQ,CAACD,CAAC,CAACE,MAAM,CAACjB,KAAK,CAAC,IAAIkB,SAAS,CAAE;cACtFxB,WAAW,EAAC;YAAe;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNlC,OAAA;YAAK8B,SAAS,EAAC,YAAY;YAAAD,QAAA,gBACzB7B,OAAA;cAAA6B,QAAA,EAAO;YAAa;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC5BlC,OAAA;cACEc,IAAI,EAAC,QAAQ;cACbQ,KAAK,EAAEZ,MAAM,CAACQ,UAAU,CAAC0B,GAAG,IAAI,EAAG;cACnCR,QAAQ,EAAGC,CAAC,IAAKb,sBAAsB,CAAC,KAAK,EAAEc,QAAQ,CAACD,CAAC,CAACE,MAAM,CAACjB,KAAK,CAAC,IAAIkB,SAAS,CAAE;cACtFxB,WAAW,EAAC;YAAe;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA,eACN,CAAC;MAEP,KAAK,OAAO;QACV,oBACElC,OAAA;UAAK8B,SAAS,EAAC,YAAY;UAAAD,QAAA,eACzB7B,OAAA;YAAA6B,QAAA,gBACE7B,OAAA;cACEc,IAAI,EAAC,UAAU;cACf+B,OAAO,EAAEnC,MAAM,CAACQ,UAAU,CAAC4B,aAAa,IAAI,KAAM;cAClDV,QAAQ,EAAGC,CAAC,IAAKb,sBAAsB,CAAC,eAAe,EAAEa,CAAC,CAACE,MAAM,CAACM,OAAO;YAAE;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5E,CAAC,2CAEJ;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAEV;QACE,OAAO,IAAI;IACf;EACF,CAAC;EAED,oBACElC,OAAA;IAAK8B,SAAS,EAAC,eAAe;IAAAD,QAAA,eAC5B7B,OAAA;MAAK8B,SAAS,EAAC,eAAe;MAAAD,QAAA,gBAC5B7B,OAAA;QAAK8B,SAAS,EAAC,cAAc;QAAAD,QAAA,gBAC3B7B,OAAA;UAAA6B,QAAA,GAAI,mBAAiB,EAACzB,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAES,KAAK;QAAA;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACxClC,OAAA;UAAQc,IAAI,EAAC,QAAQ;UAACiC,OAAO,EAAEzC,QAAS;UAACwB,SAAS,EAAC,cAAc;UAAAD,QAAA,EAAC;QAAC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzE,CAAC,eAENlC,OAAA;QAAK8B,SAAS,EAAC,YAAY;QAAAD,QAAA,gBACzB7B,OAAA;UAAK8B,SAAS,EAAC,YAAY;UAAAD,QAAA,gBACzB7B,OAAA;YAAA6B,QAAA,EAAO;UAAa;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC5BlC,OAAA;YACEc,IAAI,EAAC,MAAM;YACXQ,KAAK,EAAEZ,MAAM,CAACG,KAAM;YACpBuB,QAAQ,EAAGC,CAAC,IAAKhB,iBAAiB,CAAC,OAAO,EAAEgB,CAAC,CAACE,MAAM,CAACjB,KAAK,CAAE;YAC5DN,WAAW,EAAC;UAAmB;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENlC,OAAA;UAAK8B,SAAS,EAAC,YAAY;UAAAD,QAAA,gBACzB7B,OAAA;YAAA6B,QAAA,EAAO;UAAU;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACzBlC,OAAA;YACEsB,KAAK,EAAEZ,MAAM,CAACI,IAAK;YACnBsB,QAAQ,EAAGC,CAAC,IAAKhB,iBAAiB,CAAC,MAAM,EAAEgB,CAAC,CAACE,MAAM,CAACjB,KAAK,CAAE;YAAAO,QAAA,gBAE3D7B,OAAA;cAAQsB,KAAK,EAAC,MAAM;cAAAO,QAAA,EAAC;YAAI;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAClClC,OAAA;cAAQsB,KAAK,EAAC,UAAU;cAAAO,QAAA,EAAC;YAAQ;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC1ClC,OAAA;cAAQsB,KAAK,EAAC,QAAQ;cAAAO,QAAA,EAAC;YAAM;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACtClC,OAAA;cAAQsB,KAAK,EAAC,OAAO;cAAAO,QAAA,EAAC;YAAK;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACpClC,OAAA;cAAQsB,KAAK,EAAC,KAAK;cAAAO,QAAA,EAAC;YAAK;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAClClC,OAAA;cAAQsB,KAAK,EAAC,KAAK;cAAAO,QAAA,EAAC;YAAG;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAChClC,OAAA;cAAQsB,KAAK,EAAC,MAAM;cAAAO,QAAA,EAAC;YAAI;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAClClC,OAAA;cAAQsB,KAAK,EAAC,UAAU;cAAAO,QAAA,EAAC;YAAQ;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC1ClC,OAAA;cAAQsB,KAAK,EAAC,QAAQ;cAAAO,QAAA,EAAC;YAAM;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACtClC,OAAA;cAAQsB,KAAK,EAAC,aAAa;cAAAO,QAAA,EAAC;YAAY;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACjDlC,OAAA;cAAQsB,KAAK,EAAC,OAAO;cAAAO,QAAA,EAAC;YAAK;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAENlC,OAAA;UAAK8B,SAAS,EAAC,YAAY;UAAAD,QAAA,eACzB7B,OAAA;YAAA6B,QAAA,gBACE7B,OAAA;cACEc,IAAI,EAAC,UAAU;cACf+B,OAAO,EAAEnC,MAAM,CAACK,QAAS;cACzBqB,QAAQ,EAAGC,CAAC,IAAKhB,iBAAiB,CAAC,UAAU,EAAEgB,CAAC,CAACE,MAAM,CAACM,OAAO;YAAE;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClE,CAAC,kBAEJ;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAENlC,OAAA;UAAK8B,SAAS,EAAC,YAAY;UAAAD,QAAA,gBACzB7B,OAAA;YAAA6B,QAAA,EAAO;UAAgB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC/BlC,OAAA;YACEc,IAAI,EAAC,MAAM;YACXQ,KAAK,EAAEZ,MAAM,CAACM,WAAY;YAC1BoB,QAAQ,EAAGC,CAAC,IAAKhB,iBAAiB,CAAC,aAAa,EAAEgB,CAAC,CAACE,MAAM,CAACjB,KAAK,CAAE;YAClEN,WAAW,EAAC;UAAwB;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENlC,OAAA;UAAK8B,SAAS,EAAC,YAAY;UAAAD,QAAA,gBACzB7B,OAAA;YAAA6B,QAAA,EAAO;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACxBlC,OAAA;YACEsB,KAAK,EAAEZ,MAAM,CAACO,QAAS;YACvBmB,QAAQ,EAAGC,CAAC,IAAKhB,iBAAiB,CAAC,UAAU,EAAEgB,CAAC,CAACE,MAAM,CAACjB,KAAK,CAAE;YAC/DN,WAAW,EAAC,2BAA2B;YACvCgC,IAAI,EAAC;UAAG;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGNlC,OAAA;UAAK8B,SAAS,EAAC,gBAAgB;UAAAD,QAAA,gBAC7B7B,OAAA;YAAA6B,QAAA,EAAI;UAAgB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EACxBN,uBAAuB,CAAC,CAAC;QAAA;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB,CAAC,eAGNlC,OAAA;UAAK8B,SAAS,EAAC,gBAAgB;UAAAD,QAAA,gBAC7B7B,OAAA;YAAA6B,QAAA,EAAI;UAAmB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5BlC,OAAA;YAAK8B,SAAS,EAAC,YAAY;YAAAD,QAAA,gBACzB7B,OAAA;cAAA6B,QAAA,EAAO;YAAgB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC/BlC,OAAA;cACEc,IAAI,EAAC,MAAM;cACXQ,KAAK,EAAE,EAAAd,mBAAA,GAAAE,MAAM,CAACS,WAAW,cAAAX,mBAAA,uBAAlBA,mBAAA,CAAoBJ,KAAK,KAAI,EAAG;cACvCgC,QAAQ,EAAGC,CAAC,IAAKZ,uBAAuB,CAAC,OAAO,EAAEY,CAAC,CAACE,MAAM,CAACjB,KAAK,CAAE;cAClEN,WAAW,EAAC;YAAiC;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNlC,OAAA;YAAK8B,SAAS,EAAC,YAAY;YAAAD,QAAA,gBACzB7B,OAAA;cAAA6B,QAAA,EAAO;YAAU;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACzBlC,OAAA;cACEc,IAAI,EAAC,MAAM;cACXQ,KAAK,EAAE,EAAAb,oBAAA,GAAAC,MAAM,CAACS,WAAW,cAAAV,oBAAA,uBAAlBA,oBAAA,CAAoBa,KAAK,KAAI,EAAG;cACvCc,QAAQ,EAAGC,CAAC,IAAKZ,uBAAuB,CAAC,OAAO,EAAEY,CAAC,CAACE,MAAM,CAACjB,KAAK,CAAE;cAClEN,WAAW,EAAC;YAA8C;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAGL,CAACxB,MAAM,CAACI,IAAI,KAAK,QAAQ,IAAIJ,MAAM,CAACI,IAAI,KAAK,aAAa,kBACzDd,OAAA;UAAK8B,SAAS,EAAC,gBAAgB;UAAAD,QAAA,gBAC7B7B,OAAA;YAAA6B,QAAA,EAAI;UAAO;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChBlC,OAAA;YAAK8B,SAAS,EAAC,cAAc;YAAAD,QAAA,GAC1BnB,MAAM,CAACU,OAAO,CAAC6B,GAAG,CAAC,CAACC,MAAM,EAAEC,KAAK,kBAChCnD,OAAA;cAAiB8B,SAAS,EAAC,aAAa;cAAAD,QAAA,gBACtC7B,OAAA;gBACEc,IAAI,EAAC,MAAM;gBACXQ,KAAK,EAAE4B,MAAM,CAACrC,KAAM;gBACpBuB,QAAQ,EAAGC,CAAC,IAAK;kBACf,MAAMe,UAAU,GAAG,CAAC,GAAG1C,MAAM,CAACU,OAAO,CAAC;kBACtCgC,UAAU,CAACD,KAAK,CAAC,GAAG;oBAAE,GAAGD,MAAM;oBAAErC,KAAK,EAAEwB,CAAC,CAACE,MAAM,CAACjB;kBAAM,CAAC;kBACxDD,iBAAiB,CAAC,SAAS,EAAE+B,UAAU,CAAC;gBAC1C,CAAE;gBACFpC,WAAW,EAAC;cAAc;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B,CAAC,eACFlC,OAAA;gBACEc,IAAI,EAAC,MAAM;gBACXQ,KAAK,EAAE4B,MAAM,CAAC5B,KAAM;gBACpBc,QAAQ,EAAGC,CAAC,IAAK;kBACf,MAAMe,UAAU,GAAG,CAAC,GAAG1C,MAAM,CAACU,OAAO,CAAC;kBACtCgC,UAAU,CAACD,KAAK,CAAC,GAAG;oBAAE,GAAGD,MAAM;oBAAE5B,KAAK,EAAEe,CAAC,CAACE,MAAM,CAACjB;kBAAM,CAAC;kBACxDD,iBAAiB,CAAC,SAAS,EAAE+B,UAAU,CAAC;gBAC1C,CAAE;gBACFpC,WAAW,EAAC;cAAc;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B,CAAC,eACFlC,OAAA;gBACEc,IAAI,EAAC,QAAQ;gBACbiC,OAAO,EAAEA,CAAA,KAAM;kBACb,MAAMK,UAAU,GAAG1C,MAAM,CAACU,OAAO,CAACiC,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,KAAKJ,KAAK,CAAC;kBAC/D9B,iBAAiB,CAAC,SAAS,EAAE+B,UAAU,CAAC;gBAC1C,CAAE;gBACFtB,SAAS,EAAC,YAAY;gBAAAD,QAAA,EACvB;cAED;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA,GA9BDiB,KAAK;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA+BV,CACN,CAAC,eACFlC,OAAA;cACEc,IAAI,EAAC,QAAQ;cACbiC,OAAO,EAAEA,CAAA,KAAM;gBACb,MAAMK,UAAU,GAAG,CAAC,GAAG1C,MAAM,CAACU,OAAO,EAAE;kBAAEP,KAAK,EAAE,EAAE;kBAAES,KAAK,EAAE;gBAAG,CAAC,CAAC;gBAChED,iBAAiB,CAAC,SAAS,EAAE+B,UAAU,CAAC;cAC1C,CAAE;cACFtB,SAAS,EAAC,SAAS;cAAAD,QAAA,EACpB;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAENlC,OAAA;QAAK8B,SAAS,EAAC,cAAc;QAAAD,QAAA,gBAC3B7B,OAAA;UAAQc,IAAI,EAAC,QAAQ;UAACiC,OAAO,EAAEzC,QAAS;UAACwB,SAAS,EAAC,iBAAiB;UAAAD,QAAA,EAAC;QAErE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTlC,OAAA;UAAQc,IAAI,EAAC,QAAQ;UAACiC,OAAO,EAAErB,UAAW;UAACI,SAAS,EAAC,iBAAiB;UAAAD,QAAA,EAAC;QAEvE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC3B,EAAA,CAhTIJ,gBAAgB;AAAAqD,EAAA,GAAhBrD,gBAAgB;AAkTtB,eAAeA,gBAAgB;AAAC,IAAAqD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}