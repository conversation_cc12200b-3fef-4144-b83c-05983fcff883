using System.ComponentModel.DataAnnotations;

namespace CrmApi.Models.SubCategory
{
    public class CreateSubCategoryRequest
    {
        [Required(ErrorMessage = "Sub-category name is required")]
        [StringLength(255, ErrorMessage = "Sub-category name cannot exceed 255 characters")]
        public string Name { get; set; } = string.Empty;

        [Required(ErrorMessage = "Category ID is required")]
        [Range(1, int.MaxValue, ErrorMessage = "Category ID must be a positive number")]
        public int CategoryId { get; set; }
    }
}
