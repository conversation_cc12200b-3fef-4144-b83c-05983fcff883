{"ast": null, "code": "import { PersonFieldDefinitions, getAllPersonFields } from '../constants/personConstants';\nclass FormConfigService {\n  constructor() {\n    this.storagePrefix = 'crm_form_config_';\n    this.defaultFormKey = 'default_person_form';\n  }\n\n  // Generate storage key for form configuration\n  getStorageKey(type, id) {\n    return `${this.storagePrefix}${type}_${id}`;\n  }\n\n  // Save form configuration\n  saveFormConfig(type, id, config) {\n    try {\n      const key = this.getStorageKey(type, id);\n\n      // Deduplicate fields before saving\n      const deduplicatedFields = this.deduplicateFields(config.fields || []);\n      const formConfig = {\n        id: `${type}_${id}`,\n        type,\n        // 'division', 'category', or 'subcategory'\n        associatedId: id,\n        name: config.name || `${type} ${id} Form`,\n        description: config.description || '',\n        fields: deduplicatedFields,\n        // Use deduplicated fields\n        sections: config.sections || [],\n        settings: config.settings || {},\n        hierarchy: config.hierarchy || {},\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString(),\n        version: 1,\n        ...config,\n        fields: deduplicatedFields // Ensure deduplicated fields override any from ...config\n      };\n      localStorage.setItem(key, JSON.stringify(formConfig));\n      return formConfig;\n    } catch (error) {\n      console.error('Error saving form configuration:', error);\n      throw new Error('Failed to save form configuration');\n    }\n  }\n\n  // Load form configuration\n  loadFormConfig(type, id) {\n    try {\n      const key = this.getStorageKey(type, id);\n      const stored = localStorage.getItem(key);\n      if (stored) {\n        return JSON.parse(stored);\n      }\n      return null;\n    } catch (error) {\n      console.error('Error loading form configuration:', error);\n      return null;\n    }\n  }\n\n  // Delete form configuration\n  deleteFormConfig(type, id) {\n    try {\n      const key = this.getStorageKey(type, id);\n      localStorage.removeItem(key);\n      return true;\n    } catch (error) {\n      console.error('Error deleting form configuration:', error);\n      return false;\n    }\n  }\n\n  // Clear all form configurations (for testing/debugging)\n  clearAllFormConfigs() {\n    try {\n      const keys = Object.keys(localStorage);\n      const formKeys = keys.filter(key => key.startsWith('form_config_'));\n      formKeys.forEach(key => localStorage.removeItem(key));\n      return true;\n    } catch (error) {\n      console.error('Error clearing form configurations:', error);\n      return false;\n    }\n  }\n\n  // Get all form configurations\n  getAllFormConfigs() {\n    try {\n      const configs = [];\n      for (let i = 0; i < localStorage.length; i++) {\n        const key = localStorage.key(i);\n        if (key && key.startsWith(this.storagePrefix)) {\n          const config = JSON.parse(localStorage.getItem(key));\n          configs.push(config);\n        }\n      }\n      return configs;\n    } catch (error) {\n      console.error('Error loading all form configurations:', error);\n      return [];\n    }\n  }\n\n  // Check if a form exists for a specific category\n  hasFormForCategory(categoryId) {\n    try {\n      const key = this.getStorageKey('category', categoryId);\n      return localStorage.getItem(key) !== null;\n    } catch (error) {\n      console.error('Error checking category form:', error);\n      return false;\n    }\n  }\n\n  // Check if a form exists for a specific subcategory\n  hasFormForSubCategory(subCategoryId) {\n    try {\n      const key = this.getStorageKey('subcategory', subCategoryId);\n      return localStorage.getItem(key) !== null;\n    } catch (error) {\n      console.error('Error checking subcategory form:', error);\n      return false;\n    }\n  }\n\n  // Get all subcategories for a category that have forms\n  getSubCategoriesWithForms(categoryId) {\n    try {\n      const allConfigs = this.getAllFormConfigs();\n      return allConfigs.filter(config => {\n        var _config$hierarchy;\n        return config.type === 'subcategory' && ((_config$hierarchy = config.hierarchy) === null || _config$hierarchy === void 0 ? void 0 : _config$hierarchy.categoryId) === categoryId;\n      }).map(config => {\n        var _config$hierarchy2;\n        return (_config$hierarchy2 = config.hierarchy) === null || _config$hierarchy2 === void 0 ? void 0 : _config$hierarchy2.subCategoryId;\n      }).filter(id => id !== null && id !== undefined);\n    } catch (error) {\n      console.error('Error getting subcategories with forms:', error);\n      return [];\n    }\n  }\n\n  // Get existing form information for a category/subcategory\n  getExistingFormInfo(categoryId, subCategoryId = null) {\n    try {\n      const info = {\n        categoryHasForm: false,\n        subCategoryHasForm: false,\n        subCategoriesWithForms: [],\n        existingForms: []\n      };\n      if (categoryId) {\n        info.categoryHasForm = this.hasFormForCategory(categoryId);\n        info.subCategoriesWithForms = this.getSubCategoriesWithForms(categoryId);\n        if (info.categoryHasForm) {\n          const categoryForm = this.loadFormConfig('category', categoryId);\n          if (categoryForm) {\n            info.existingForms.push({\n              type: 'category',\n              name: categoryForm.name,\n              description: categoryForm.description\n            });\n          }\n        }\n      }\n      if (subCategoryId) {\n        info.subCategoryHasForm = this.hasFormForSubCategory(subCategoryId);\n        if (info.subCategoryHasForm) {\n          const subCategoryForm = this.loadFormConfig('subcategory', subCategoryId);\n          if (subCategoryForm) {\n            info.existingForms.push({\n              type: 'subcategory',\n              name: subCategoryForm.name,\n              description: subCategoryForm.description\n            });\n          }\n        }\n      }\n      return info;\n    } catch (error) {\n      console.error('Error getting existing form info:', error);\n      return {\n        categoryHasForm: false,\n        subCategoryHasForm: false,\n        subCategoriesWithForms: [],\n        existingForms: []\n      };\n    }\n  }\n\n  // Validate form creation rules\n  validateFormCreation(categoryId, subCategoryId = null) {\n    const errors = [];\n    if (subCategoryId) {\n      // Rule 1: Check if category already has a form\n      if (this.hasFormForCategory(categoryId)) {\n        errors.push('Cannot create form for subcategory because a form already exists for the parent category');\n      }\n\n      // Rule 2: Check if subcategory already has a form\n      if (this.hasFormForSubCategory(subCategoryId)) {\n        errors.push('A form already exists for this subcategory. Only one form per subcategory is allowed');\n      }\n    } else if (categoryId) {\n      // Rule 3: Check if any subcategories have forms\n      const subCategoriesWithForms = this.getSubCategoriesWithForms(categoryId);\n      if (subCategoriesWithForms.length > 0) {\n        errors.push('Cannot create form for category because forms already exist for its subcategories');\n      }\n\n      // Rule 4: Check if category already has a form\n      if (this.hasFormForCategory(categoryId)) {\n        errors.push('A form already exists for this category');\n      }\n    }\n    return {\n      isValid: errors.length === 0,\n      errors\n    };\n  }\n\n  // Get form configuration for category or subcategory\n  getFormConfigForSelection(divisionId, categoryId, subCategoryId = null) {\n    // divisionId is kept for future use and API consistency\n    // First try to get subcategory-specific form if subcategory is selected\n    if (subCategoryId) {\n      const subCategoryForm = this.loadFormConfig('subcategory', subCategoryId);\n      if (subCategoryForm) {\n        return subCategoryForm;\n      }\n    }\n\n    // Then try category-specific form\n    const categoryForm = this.loadFormConfig('category', categoryId);\n    if (categoryForm) {\n      return categoryForm;\n    }\n\n    // Return default form if no custom form found\n    return this.getDefaultFormConfig();\n  }\n\n  // Get default form configuration with all fields\n  getDefaultFormConfig() {\n    const allFields = getAllPersonFields();\n\n    // Deduplicate fields to ensure no duplicates\n    const deduplicatedFields = this.deduplicateFields(allFields);\n    return {\n      id: 'default',\n      type: 'default',\n      name: 'Default Person Form',\n      description: 'Default form with all person fields',\n      fields: deduplicatedFields,\n      sections: Object.keys(PersonFieldDefinitions),\n      settings: {\n        showSections: true,\n        allowConditionalFields: true,\n        validateOnChange: true\n      },\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString(),\n      version: 1\n    };\n  }\n\n  // Create form configuration from field selection\n  createFormConfigFromFields(selectedFields, settings = {}) {\n    const config = {\n      fields: selectedFields,\n      sections: this.groupFieldsBySections(selectedFields),\n      settings: {\n        showSections: true,\n        allowConditionalFields: true,\n        validateOnChange: true,\n        ...settings\n      }\n    };\n    return config;\n  }\n\n  // Deduplicate fields based on field key\n  deduplicateFields(fields) {\n    const seen = new Set();\n    const deduplicated = [];\n    fields.forEach(field => {\n      if (!seen.has(field.key)) {\n        seen.add(field.key);\n        deduplicated.push(field);\n      } else {\n        console.warn(`FormConfigService: Removing duplicate field: ${field.key}`);\n      }\n    });\n    if (fields.length !== deduplicated.length) {\n      console.log(`FormConfigService: Deduplicated ${fields.length} fields to ${deduplicated.length} unique fields`);\n    }\n    return deduplicated;\n  }\n\n  // Group fields by their sections\n  groupFieldsBySections(fields) {\n    const sections = {};\n    fields.forEach(field => {\n      const sectionKey = field.section;\n      if (!sections[sectionKey]) {\n        var _PersonFieldDefinitio;\n        sections[sectionKey] = {\n          key: sectionKey,\n          title: ((_PersonFieldDefinitio = PersonFieldDefinitions[sectionKey]) === null || _PersonFieldDefinitio === void 0 ? void 0 : _PersonFieldDefinitio.title) || sectionKey,\n          fields: []\n        };\n      }\n      sections[sectionKey].fields.push(field);\n    });\n    return Object.values(sections);\n  }\n\n  // Validate form configuration\n  validateFormConfig(config) {\n    const errors = [];\n    if (!config.name || config.name.trim() === '') {\n      errors.push('Form name is required');\n    }\n    if (!config.fields || config.fields.length === 0) {\n      errors.push('At least one field must be selected');\n    }\n\n    // Check if required fields are included\n    const requiredFields = ['name', 'mobileNumber', 'nature'];\n    const selectedFieldKeys = config.fields.map(f => f.key);\n    requiredFields.forEach(requiredField => {\n      if (!selectedFieldKeys.includes(requiredField)) {\n        errors.push(`Required field '${requiredField}' must be included`);\n      }\n    });\n    return {\n      isValid: errors.length === 0,\n      errors\n    };\n  }\n\n  // Export form configuration\n  exportFormConfig(type, id) {\n    const config = this.loadFormConfig(type, id);\n    if (!config) {\n      throw new Error('Form configuration not found');\n    }\n    const exportData = {\n      ...config,\n      exportedAt: new Date().toISOString(),\n      exportVersion: '1.0'\n    };\n    return JSON.stringify(exportData, null, 2);\n  }\n\n  // Import form configuration\n  importFormConfig(configJson, type, id) {\n    try {\n      const config = JSON.parse(configJson);\n\n      // Validate imported configuration\n      const validation = this.validateFormConfig(config);\n      if (!validation.isValid) {\n        throw new Error(`Invalid configuration: ${validation.errors.join(', ')}`);\n      }\n\n      // Update metadata for import\n      config.type = type;\n      config.associatedId = id;\n      config.importedAt = new Date().toISOString();\n      config.updatedAt = new Date().toISOString();\n      return this.saveFormConfig(type, id, config);\n    } catch (error) {\n      console.error('Error importing form configuration:', error);\n      throw new Error('Failed to import form configuration');\n    }\n  }\n\n  // Clone form configuration\n  cloneFormConfig(sourceType, sourceId, targetType, targetId, newName) {\n    const sourceConfig = this.loadFormConfig(sourceType, sourceId);\n    if (!sourceConfig) {\n      throw new Error('Source form configuration not found');\n    }\n    const clonedConfig = {\n      ...sourceConfig,\n      name: newName || `${sourceConfig.name} (Copy)`,\n      type: targetType,\n      associatedId: targetId,\n      clonedFrom: `${sourceType}_${sourceId}`,\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString()\n    };\n    return this.saveFormConfig(targetType, targetId, clonedConfig);\n  }\n\n  // Get form statistics\n  getFormStatistics() {\n    const allConfigs = this.getAllFormConfigs();\n    return {\n      totalForms: allConfigs.length,\n      categoryForms: allConfigs.filter(c => c.type === 'category').length,\n      subCategoryForms: allConfigs.filter(c => c.type === 'subcategory').length,\n      averageFieldCount: allConfigs.length > 0 ? Math.round(allConfigs.reduce((sum, c) => {\n        var _c$fields;\n        return sum + (((_c$fields = c.fields) === null || _c$fields === void 0 ? void 0 : _c$fields.length) || 0);\n      }, 0) / allConfigs.length) : 0,\n      mostUsedFields: this.getMostUsedFields(allConfigs),\n      recentlyModified: allConfigs.sort((a, b) => new Date(b.updatedAt) - new Date(a.updatedAt)).slice(0, 5)\n    };\n  }\n\n  // Get most used fields across all forms\n  getMostUsedFields(configs) {\n    const fieldUsage = {};\n    configs.forEach(config => {\n      if (config.fields) {\n        config.fields.forEach(field => {\n          fieldUsage[field.key] = (fieldUsage[field.key] || 0) + 1;\n        });\n      }\n    });\n    return Object.entries(fieldUsage).sort(([, a], [, b]) => b - a).slice(0, 10).map(([key, count]) => ({\n      field: key,\n      usage: count\n    }));\n  }\n\n  // Get all saved form configurations\n  getAllFormConfigs() {\n    try {\n      const forms = [];\n      for (let i = 0; i < localStorage.length; i++) {\n        const key = localStorage.key(i);\n        if (key && key.startsWith(this.storagePrefix)) {\n          const stored = localStorage.getItem(key);\n          if (stored) {\n            var _config$fields;\n            const config = JSON.parse(stored);\n            forms.push({\n              key,\n              ...config,\n              summary: {\n                fieldCount: ((_config$fields = config.fields) === null || _config$fields === void 0 ? void 0 : _config$fields.length) || 0,\n                type: config.type,\n                associatedId: config.associatedId,\n                createdAt: config.createdAt,\n                updatedAt: config.updatedAt\n              }\n            });\n          }\n        }\n      }\n\n      // Sort by updated date (most recent first)\n      return forms.sort((a, b) => new Date(b.updatedAt) - new Date(a.updatedAt));\n    } catch (error) {\n      console.error('Error getting form configurations:', error);\n      return [];\n    }\n  }\n\n  // Delete a specific form configuration\n  deleteFormConfig(type, id) {\n    try {\n      const key = this.getStorageKey(type, id);\n      localStorage.removeItem(key);\n      return true;\n    } catch (error) {\n      console.error('Error deleting form configuration:', error);\n      return false;\n    }\n  }\n\n  // Clear all form configurations (for development/testing)\n  clearAllFormConfigs() {\n    try {\n      const keysToRemove = [];\n      for (let i = 0; i < localStorage.length; i++) {\n        const key = localStorage.key(i);\n        if (key && key.startsWith(this.storagePrefix)) {\n          keysToRemove.push(key);\n        }\n      }\n      keysToRemove.forEach(key => localStorage.removeItem(key));\n      return true;\n    } catch (error) {\n      console.error('Error clearing form configurations:', error);\n      return false;\n    }\n  }\n}\nexport default new FormConfigService();", "map": {"version": 3, "names": ["PersonFieldDefinitions", "<PERSON><PERSON><PERSON><PERSON><PERSON>F<PERSON>s", "FormConfigService", "constructor", "storagePrefix", "defaultFormKey", "getStorageKey", "type", "id", "saveFormConfig", "config", "key", "deduplicatedFields", "deduplicateFields", "fields", "formConfig", "associatedId", "name", "description", "sections", "settings", "hierarchy", "createdAt", "Date", "toISOString", "updatedAt", "version", "localStorage", "setItem", "JSON", "stringify", "error", "console", "Error", "loadFormConfig", "stored", "getItem", "parse", "deleteFormConfig", "removeItem", "clearAllFormConfigs", "keys", "Object", "formKeys", "filter", "startsWith", "for<PERSON>ach", "getAllFormConfigs", "configs", "i", "length", "push", "hasFormForCategory", "categoryId", "hasFormForSubCategory", "subCategoryId", "getSubCategoriesWithForms", "allConfigs", "_config$hierarchy", "map", "_config$hierarchy2", "undefined", "getExistingFormInfo", "info", "categoryHasForm", "subCategoryHasForm", "subCategoriesWithForms", "existingForms", "categoryForm", "subCategoryForm", "validateFormCreation", "errors", "<PERSON><PERSON><PERSON><PERSON>", "getFormConfigForSelection", "divisionId", "getDefaultFormConfig", "allFields", "showSections", "allowConditionalFields", "validateOnChange", "createFormConfigFromFields", "<PERSON><PERSON><PERSON>s", "groupFieldsBySections", "seen", "Set", "deduplicated", "field", "has", "add", "warn", "log", "sectionKey", "section", "_PersonFieldDefinitio", "title", "values", "validateFormConfig", "trim", "requiredFields", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "f", "requiredField", "includes", "exportFormConfig", "exportData", "exportedAt", "exportVersion", "importFormConfig", "config<PERSON><PERSON>", "validation", "join", "importedAt", "cloneFormConfig", "sourceType", "sourceId", "targetType", "targetId", "newName", "sourceConfig", "clonedConfig", "clonedFrom", "getFormStatistics", "totalForms", "categoryForms", "c", "subCategoryForms", "averageFieldCount", "Math", "round", "reduce", "sum", "_c$fields", "mostUsedFields", "getMostUsedFields", "recentlyModified", "sort", "a", "b", "slice", "fieldUsage", "entries", "count", "usage", "forms", "_config$fields", "summary", "fieldCount", "keysToRemove"], "sources": ["C:/Users/<USER>/Downloads/Advanced/Advanced CRM filed all use/frontend/src/services/formConfigService.js"], "sourcesContent": ["import { PersonFieldDefinitions, getAllPersonFields } from '../constants/personConstants';\n\nclass FormConfigService {\n  constructor() {\n    this.storagePrefix = 'crm_form_config_';\n    this.defaultFormKey = 'default_person_form';\n  }\n\n  // Generate storage key for form configuration\n  getStorageKey(type, id) {\n    return `${this.storagePrefix}${type}_${id}`;\n  }\n\n  // Save form configuration\n  saveFormConfig(type, id, config) {\n    try {\n      const key = this.getStorageKey(type, id);\n\n      // Deduplicate fields before saving\n      const deduplicatedFields = this.deduplicateFields(config.fields || []);\n\n      const formConfig = {\n        id: `${type}_${id}`,\n        type, // 'division', 'category', or 'subcategory'\n        associatedId: id,\n        name: config.name || `${type} ${id} Form`,\n        description: config.description || '',\n        fields: deduplicatedFields, // Use deduplicated fields\n        sections: config.sections || [],\n        settings: config.settings || {},\n        hierarchy: config.hierarchy || {},\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString(),\n        version: 1,\n        ...config,\n        fields: deduplicatedFields // Ensure deduplicated fields override any from ...config\n      };\n\n      localStorage.setItem(key, JSON.stringify(formConfig));\n      return formConfig;\n    } catch (error) {\n      console.error('Error saving form configuration:', error);\n      throw new Error('Failed to save form configuration');\n    }\n  }\n\n  // Load form configuration\n  loadFormConfig(type, id) {\n    try {\n      const key = this.getStorageKey(type, id);\n      const stored = localStorage.getItem(key);\n      \n      if (stored) {\n        return JSON.parse(stored);\n      }\n      \n      return null;\n    } catch (error) {\n      console.error('Error loading form configuration:', error);\n      return null;\n    }\n  }\n\n  // Delete form configuration\n  deleteFormConfig(type, id) {\n    try {\n      const key = this.getStorageKey(type, id);\n      localStorage.removeItem(key);\n      return true;\n    } catch (error) {\n      console.error('Error deleting form configuration:', error);\n      return false;\n    }\n  }\n\n  // Clear all form configurations (for testing/debugging)\n  clearAllFormConfigs() {\n    try {\n      const keys = Object.keys(localStorage);\n      const formKeys = keys.filter(key => key.startsWith('form_config_'));\n      formKeys.forEach(key => localStorage.removeItem(key));\n      return true;\n    } catch (error) {\n      console.error('Error clearing form configurations:', error);\n      return false;\n    }\n  }\n\n  // Get all form configurations\n  getAllFormConfigs() {\n    try {\n      const configs = [];\n      for (let i = 0; i < localStorage.length; i++) {\n        const key = localStorage.key(i);\n        if (key && key.startsWith(this.storagePrefix)) {\n          const config = JSON.parse(localStorage.getItem(key));\n          configs.push(config);\n        }\n      }\n      return configs;\n    } catch (error) {\n      console.error('Error loading all form configurations:', error);\n      return [];\n    }\n  }\n\n  // Check if a form exists for a specific category\n  hasFormForCategory(categoryId) {\n    try {\n      const key = this.getStorageKey('category', categoryId);\n      return localStorage.getItem(key) !== null;\n    } catch (error) {\n      console.error('Error checking category form:', error);\n      return false;\n    }\n  }\n\n  // Check if a form exists for a specific subcategory\n  hasFormForSubCategory(subCategoryId) {\n    try {\n      const key = this.getStorageKey('subcategory', subCategoryId);\n      return localStorage.getItem(key) !== null;\n    } catch (error) {\n      console.error('Error checking subcategory form:', error);\n      return false;\n    }\n  }\n\n  // Get all subcategories for a category that have forms\n  getSubCategoriesWithForms(categoryId) {\n    try {\n      const allConfigs = this.getAllFormConfigs();\n      return allConfigs\n        .filter(config => config.type === 'subcategory' && config.hierarchy?.categoryId === categoryId)\n        .map(config => config.hierarchy?.subCategoryId)\n        .filter(id => id !== null && id !== undefined);\n    } catch (error) {\n      console.error('Error getting subcategories with forms:', error);\n      return [];\n    }\n  }\n\n  // Get existing form information for a category/subcategory\n  getExistingFormInfo(categoryId, subCategoryId = null) {\n    try {\n      const info = {\n        categoryHasForm: false,\n        subCategoryHasForm: false,\n        subCategoriesWithForms: [],\n        existingForms: []\n      };\n\n      if (categoryId) {\n        info.categoryHasForm = this.hasFormForCategory(categoryId);\n        info.subCategoriesWithForms = this.getSubCategoriesWithForms(categoryId);\n\n        if (info.categoryHasForm) {\n          const categoryForm = this.loadFormConfig('category', categoryId);\n          if (categoryForm) {\n            info.existingForms.push({\n              type: 'category',\n              name: categoryForm.name,\n              description: categoryForm.description\n            });\n          }\n        }\n      }\n\n      if (subCategoryId) {\n        info.subCategoryHasForm = this.hasFormForSubCategory(subCategoryId);\n\n        if (info.subCategoryHasForm) {\n          const subCategoryForm = this.loadFormConfig('subcategory', subCategoryId);\n          if (subCategoryForm) {\n            info.existingForms.push({\n              type: 'subcategory',\n              name: subCategoryForm.name,\n              description: subCategoryForm.description\n            });\n          }\n        }\n      }\n\n      return info;\n    } catch (error) {\n      console.error('Error getting existing form info:', error);\n      return {\n        categoryHasForm: false,\n        subCategoryHasForm: false,\n        subCategoriesWithForms: [],\n        existingForms: []\n      };\n    }\n  }\n\n  // Validate form creation rules\n  validateFormCreation(categoryId, subCategoryId = null) {\n    const errors = [];\n\n    if (subCategoryId) {\n      // Rule 1: Check if category already has a form\n      if (this.hasFormForCategory(categoryId)) {\n        errors.push('Cannot create form for subcategory because a form already exists for the parent category');\n      }\n\n      // Rule 2: Check if subcategory already has a form\n      if (this.hasFormForSubCategory(subCategoryId)) {\n        errors.push('A form already exists for this subcategory. Only one form per subcategory is allowed');\n      }\n    } else if (categoryId) {\n      // Rule 3: Check if any subcategories have forms\n      const subCategoriesWithForms = this.getSubCategoriesWithForms(categoryId);\n      if (subCategoriesWithForms.length > 0) {\n        errors.push('Cannot create form for category because forms already exist for its subcategories');\n      }\n\n      // Rule 4: Check if category already has a form\n      if (this.hasFormForCategory(categoryId)) {\n        errors.push('A form already exists for this category');\n      }\n    }\n\n    return {\n      isValid: errors.length === 0,\n      errors\n    };\n  }\n\n  // Get form configuration for category or subcategory\n  getFormConfigForSelection(divisionId, categoryId, subCategoryId = null) {\n    // divisionId is kept for future use and API consistency\n    // First try to get subcategory-specific form if subcategory is selected\n    if (subCategoryId) {\n      const subCategoryForm = this.loadFormConfig('subcategory', subCategoryId);\n      if (subCategoryForm) {\n        return subCategoryForm;\n      }\n    }\n\n    // Then try category-specific form\n    const categoryForm = this.loadFormConfig('category', categoryId);\n    if (categoryForm) {\n      return categoryForm;\n    }\n\n    // Return default form if no custom form found\n    return this.getDefaultFormConfig();\n  }\n\n  // Get default form configuration with all fields\n  getDefaultFormConfig() {\n    const allFields = getAllPersonFields();\n\n    // Deduplicate fields to ensure no duplicates\n    const deduplicatedFields = this.deduplicateFields(allFields);\n\n    return {\n      id: 'default',\n      type: 'default',\n      name: 'Default Person Form',\n      description: 'Default form with all person fields',\n      fields: deduplicatedFields,\n      sections: Object.keys(PersonFieldDefinitions),\n      settings: {\n        showSections: true,\n        allowConditionalFields: true,\n        validateOnChange: true\n      },\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString(),\n      version: 1\n    };\n  }\n\n  // Create form configuration from field selection\n  createFormConfigFromFields(selectedFields, settings = {}) {\n    const config = {\n      fields: selectedFields,\n      sections: this.groupFieldsBySections(selectedFields),\n      settings: {\n        showSections: true,\n        allowConditionalFields: true,\n        validateOnChange: true,\n        ...settings\n      }\n    };\n\n    return config;\n  }\n\n  // Deduplicate fields based on field key\n  deduplicateFields(fields) {\n    const seen = new Set();\n    const deduplicated = [];\n\n    fields.forEach(field => {\n      if (!seen.has(field.key)) {\n        seen.add(field.key);\n        deduplicated.push(field);\n      } else {\n        console.warn(`FormConfigService: Removing duplicate field: ${field.key}`);\n      }\n    });\n\n    if (fields.length !== deduplicated.length) {\n      console.log(`FormConfigService: Deduplicated ${fields.length} fields to ${deduplicated.length} unique fields`);\n    }\n\n    return deduplicated;\n  }\n\n  // Group fields by their sections\n  groupFieldsBySections(fields) {\n    const sections = {};\n\n    fields.forEach(field => {\n      const sectionKey = field.section;\n      if (!sections[sectionKey]) {\n        sections[sectionKey] = {\n          key: sectionKey,\n          title: PersonFieldDefinitions[sectionKey]?.title || sectionKey,\n          fields: []\n        };\n      }\n      sections[sectionKey].fields.push(field);\n    });\n\n    return Object.values(sections);\n  }\n\n  // Validate form configuration\n  validateFormConfig(config) {\n    const errors = [];\n\n    if (!config.name || config.name.trim() === '') {\n      errors.push('Form name is required');\n    }\n\n    if (!config.fields || config.fields.length === 0) {\n      errors.push('At least one field must be selected');\n    }\n\n    // Check if required fields are included\n    const requiredFields = ['name', 'mobileNumber', 'nature'];\n    const selectedFieldKeys = config.fields.map(f => f.key);\n    \n    requiredFields.forEach(requiredField => {\n      if (!selectedFieldKeys.includes(requiredField)) {\n        errors.push(`Required field '${requiredField}' must be included`);\n      }\n    });\n\n    return {\n      isValid: errors.length === 0,\n      errors\n    };\n  }\n\n  // Export form configuration\n  exportFormConfig(type, id) {\n    const config = this.loadFormConfig(type, id);\n    if (!config) {\n      throw new Error('Form configuration not found');\n    }\n\n    const exportData = {\n      ...config,\n      exportedAt: new Date().toISOString(),\n      exportVersion: '1.0'\n    };\n\n    return JSON.stringify(exportData, null, 2);\n  }\n\n  // Import form configuration\n  importFormConfig(configJson, type, id) {\n    try {\n      const config = JSON.parse(configJson);\n      \n      // Validate imported configuration\n      const validation = this.validateFormConfig(config);\n      if (!validation.isValid) {\n        throw new Error(`Invalid configuration: ${validation.errors.join(', ')}`);\n      }\n\n      // Update metadata for import\n      config.type = type;\n      config.associatedId = id;\n      config.importedAt = new Date().toISOString();\n      config.updatedAt = new Date().toISOString();\n\n      return this.saveFormConfig(type, id, config);\n    } catch (error) {\n      console.error('Error importing form configuration:', error);\n      throw new Error('Failed to import form configuration');\n    }\n  }\n\n  // Clone form configuration\n  cloneFormConfig(sourceType, sourceId, targetType, targetId, newName) {\n    const sourceConfig = this.loadFormConfig(sourceType, sourceId);\n    if (!sourceConfig) {\n      throw new Error('Source form configuration not found');\n    }\n\n    const clonedConfig = {\n      ...sourceConfig,\n      name: newName || `${sourceConfig.name} (Copy)`,\n      type: targetType,\n      associatedId: targetId,\n      clonedFrom: `${sourceType}_${sourceId}`,\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString()\n    };\n\n    return this.saveFormConfig(targetType, targetId, clonedConfig);\n  }\n\n  // Get form statistics\n  getFormStatistics() {\n    const allConfigs = this.getAllFormConfigs();\n    \n    return {\n      totalForms: allConfigs.length,\n      categoryForms: allConfigs.filter(c => c.type === 'category').length,\n      subCategoryForms: allConfigs.filter(c => c.type === 'subcategory').length,\n      averageFieldCount: allConfigs.length > 0 \n        ? Math.round(allConfigs.reduce((sum, c) => sum + (c.fields?.length || 0), 0) / allConfigs.length)\n        : 0,\n      mostUsedFields: this.getMostUsedFields(allConfigs),\n      recentlyModified: allConfigs\n        .sort((a, b) => new Date(b.updatedAt) - new Date(a.updatedAt))\n        .slice(0, 5)\n    };\n  }\n\n  // Get most used fields across all forms\n  getMostUsedFields(configs) {\n    const fieldUsage = {};\n    \n    configs.forEach(config => {\n      if (config.fields) {\n        config.fields.forEach(field => {\n          fieldUsage[field.key] = (fieldUsage[field.key] || 0) + 1;\n        });\n      }\n    });\n\n    return Object.entries(fieldUsage)\n      .sort(([,a], [,b]) => b - a)\n      .slice(0, 10)\n      .map(([key, count]) => ({ field: key, usage: count }));\n  }\n\n  // Get all saved form configurations\n  getAllFormConfigs() {\n    try {\n      const forms = [];\n      for (let i = 0; i < localStorage.length; i++) {\n        const key = localStorage.key(i);\n        if (key && key.startsWith(this.storagePrefix)) {\n          const stored = localStorage.getItem(key);\n          if (stored) {\n            const config = JSON.parse(stored);\n            forms.push({\n              key,\n              ...config,\n              summary: {\n                fieldCount: config.fields?.length || 0,\n                type: config.type,\n                associatedId: config.associatedId,\n                createdAt: config.createdAt,\n                updatedAt: config.updatedAt\n              }\n            });\n          }\n        }\n      }\n\n      // Sort by updated date (most recent first)\n      return forms.sort((a, b) => new Date(b.updatedAt) - new Date(a.updatedAt));\n    } catch (error) {\n      console.error('Error getting form configurations:', error);\n      return [];\n    }\n  }\n\n  // Delete a specific form configuration\n  deleteFormConfig(type, id) {\n    try {\n      const key = this.getStorageKey(type, id);\n      localStorage.removeItem(key);\n      return true;\n    } catch (error) {\n      console.error('Error deleting form configuration:', error);\n      return false;\n    }\n  }\n\n  // Clear all form configurations (for development/testing)\n  clearAllFormConfigs() {\n    try {\n      const keysToRemove = [];\n      for (let i = 0; i < localStorage.length; i++) {\n        const key = localStorage.key(i);\n        if (key && key.startsWith(this.storagePrefix)) {\n          keysToRemove.push(key);\n        }\n      }\n\n      keysToRemove.forEach(key => localStorage.removeItem(key));\n      return true;\n    } catch (error) {\n      console.error('Error clearing form configurations:', error);\n      return false;\n    }\n  }\n}\n\nexport default new FormConfigService();\n"], "mappings": "AAAA,SAASA,sBAAsB,EAAEC,kBAAkB,QAAQ,8BAA8B;AAEzF,MAAMC,iBAAiB,CAAC;EACtBC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,aAAa,GAAG,kBAAkB;IACvC,IAAI,CAACC,cAAc,GAAG,qBAAqB;EAC7C;;EAEA;EACAC,aAAaA,CAACC,IAAI,EAAEC,EAAE,EAAE;IACtB,OAAO,GAAG,IAAI,CAACJ,aAAa,GAAGG,IAAI,IAAIC,EAAE,EAAE;EAC7C;;EAEA;EACAC,cAAcA,CAACF,IAAI,EAAEC,EAAE,EAAEE,MAAM,EAAE;IAC/B,IAAI;MACF,MAAMC,GAAG,GAAG,IAAI,CAACL,aAAa,CAACC,IAAI,EAAEC,EAAE,CAAC;;MAExC;MACA,MAAMI,kBAAkB,GAAG,IAAI,CAACC,iBAAiB,CAACH,MAAM,CAACI,MAAM,IAAI,EAAE,CAAC;MAEtE,MAAMC,UAAU,GAAG;QACjBP,EAAE,EAAE,GAAGD,IAAI,IAAIC,EAAE,EAAE;QACnBD,IAAI;QAAE;QACNS,YAAY,EAAER,EAAE;QAChBS,IAAI,EAAEP,MAAM,CAACO,IAAI,IAAI,GAAGV,IAAI,IAAIC,EAAE,OAAO;QACzCU,WAAW,EAAER,MAAM,CAACQ,WAAW,IAAI,EAAE;QACrCJ,MAAM,EAAEF,kBAAkB;QAAE;QAC5BO,QAAQ,EAAET,MAAM,CAACS,QAAQ,IAAI,EAAE;QAC/BC,QAAQ,EAAEV,MAAM,CAACU,QAAQ,IAAI,CAAC,CAAC;QAC/BC,SAAS,EAAEX,MAAM,CAACW,SAAS,IAAI,CAAC,CAAC;QACjCC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;QACnCC,SAAS,EAAE,IAAIF,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;QACnCE,OAAO,EAAE,CAAC;QACV,GAAGhB,MAAM;QACTI,MAAM,EAAEF,kBAAkB,CAAC;MAC7B,CAAC;MAEDe,YAAY,CAACC,OAAO,CAACjB,GAAG,EAAEkB,IAAI,CAACC,SAAS,CAACf,UAAU,CAAC,CAAC;MACrD,OAAOA,UAAU;IACnB,CAAC,CAAC,OAAOgB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxD,MAAM,IAAIE,KAAK,CAAC,mCAAmC,CAAC;IACtD;EACF;;EAEA;EACAC,cAAcA,CAAC3B,IAAI,EAAEC,EAAE,EAAE;IACvB,IAAI;MACF,MAAMG,GAAG,GAAG,IAAI,CAACL,aAAa,CAACC,IAAI,EAAEC,EAAE,CAAC;MACxC,MAAM2B,MAAM,GAAGR,YAAY,CAACS,OAAO,CAACzB,GAAG,CAAC;MAExC,IAAIwB,MAAM,EAAE;QACV,OAAON,IAAI,CAACQ,KAAK,CAACF,MAAM,CAAC;MAC3B;MAEA,OAAO,IAAI;IACb,CAAC,CAAC,OAAOJ,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MACzD,OAAO,IAAI;IACb;EACF;;EAEA;EACAO,gBAAgBA,CAAC/B,IAAI,EAAEC,EAAE,EAAE;IACzB,IAAI;MACF,MAAMG,GAAG,GAAG,IAAI,CAACL,aAAa,CAACC,IAAI,EAAEC,EAAE,CAAC;MACxCmB,YAAY,CAACY,UAAU,CAAC5B,GAAG,CAAC;MAC5B,OAAO,IAAI;IACb,CAAC,CAAC,OAAOoB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;MAC1D,OAAO,KAAK;IACd;EACF;;EAEA;EACAS,mBAAmBA,CAAA,EAAG;IACpB,IAAI;MACF,MAAMC,IAAI,GAAGC,MAAM,CAACD,IAAI,CAACd,YAAY,CAAC;MACtC,MAAMgB,QAAQ,GAAGF,IAAI,CAACG,MAAM,CAACjC,GAAG,IAAIA,GAAG,CAACkC,UAAU,CAAC,cAAc,CAAC,CAAC;MACnEF,QAAQ,CAACG,OAAO,CAACnC,GAAG,IAAIgB,YAAY,CAACY,UAAU,CAAC5B,GAAG,CAAC,CAAC;MACrD,OAAO,IAAI;IACb,CAAC,CAAC,OAAOoB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC3D,OAAO,KAAK;IACd;EACF;;EAEA;EACAgB,iBAAiBA,CAAA,EAAG;IAClB,IAAI;MACF,MAAMC,OAAO,GAAG,EAAE;MAClB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGtB,YAAY,CAACuB,MAAM,EAAED,CAAC,EAAE,EAAE;QAC5C,MAAMtC,GAAG,GAAGgB,YAAY,CAAChB,GAAG,CAACsC,CAAC,CAAC;QAC/B,IAAItC,GAAG,IAAIA,GAAG,CAACkC,UAAU,CAAC,IAAI,CAACzC,aAAa,CAAC,EAAE;UAC7C,MAAMM,MAAM,GAAGmB,IAAI,CAACQ,KAAK,CAACV,YAAY,CAACS,OAAO,CAACzB,GAAG,CAAC,CAAC;UACpDqC,OAAO,CAACG,IAAI,CAACzC,MAAM,CAAC;QACtB;MACF;MACA,OAAOsC,OAAO;IAChB,CAAC,CAAC,OAAOjB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;MAC9D,OAAO,EAAE;IACX;EACF;;EAEA;EACAqB,kBAAkBA,CAACC,UAAU,EAAE;IAC7B,IAAI;MACF,MAAM1C,GAAG,GAAG,IAAI,CAACL,aAAa,CAAC,UAAU,EAAE+C,UAAU,CAAC;MACtD,OAAO1B,YAAY,CAACS,OAAO,CAACzB,GAAG,CAAC,KAAK,IAAI;IAC3C,CAAC,CAAC,OAAOoB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrD,OAAO,KAAK;IACd;EACF;;EAEA;EACAuB,qBAAqBA,CAACC,aAAa,EAAE;IACnC,IAAI;MACF,MAAM5C,GAAG,GAAG,IAAI,CAACL,aAAa,CAAC,aAAa,EAAEiD,aAAa,CAAC;MAC5D,OAAO5B,YAAY,CAACS,OAAO,CAACzB,GAAG,CAAC,KAAK,IAAI;IAC3C,CAAC,CAAC,OAAOoB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxD,OAAO,KAAK;IACd;EACF;;EAEA;EACAyB,yBAAyBA,CAACH,UAAU,EAAE;IACpC,IAAI;MACF,MAAMI,UAAU,GAAG,IAAI,CAACV,iBAAiB,CAAC,CAAC;MAC3C,OAAOU,UAAU,CACdb,MAAM,CAAClC,MAAM;QAAA,IAAAgD,iBAAA;QAAA,OAAIhD,MAAM,CAACH,IAAI,KAAK,aAAa,IAAI,EAAAmD,iBAAA,GAAAhD,MAAM,CAACW,SAAS,cAAAqC,iBAAA,uBAAhBA,iBAAA,CAAkBL,UAAU,MAAKA,UAAU;MAAA,EAAC,CAC9FM,GAAG,CAACjD,MAAM;QAAA,IAAAkD,kBAAA;QAAA,QAAAA,kBAAA,GAAIlD,MAAM,CAACW,SAAS,cAAAuC,kBAAA,uBAAhBA,kBAAA,CAAkBL,aAAa;MAAA,EAAC,CAC9CX,MAAM,CAACpC,EAAE,IAAIA,EAAE,KAAK,IAAI,IAAIA,EAAE,KAAKqD,SAAS,CAAC;IAClD,CAAC,CAAC,OAAO9B,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;MAC/D,OAAO,EAAE;IACX;EACF;;EAEA;EACA+B,mBAAmBA,CAACT,UAAU,EAAEE,aAAa,GAAG,IAAI,EAAE;IACpD,IAAI;MACF,MAAMQ,IAAI,GAAG;QACXC,eAAe,EAAE,KAAK;QACtBC,kBAAkB,EAAE,KAAK;QACzBC,sBAAsB,EAAE,EAAE;QAC1BC,aAAa,EAAE;MACjB,CAAC;MAED,IAAId,UAAU,EAAE;QACdU,IAAI,CAACC,eAAe,GAAG,IAAI,CAACZ,kBAAkB,CAACC,UAAU,CAAC;QAC1DU,IAAI,CAACG,sBAAsB,GAAG,IAAI,CAACV,yBAAyB,CAACH,UAAU,CAAC;QAExE,IAAIU,IAAI,CAACC,eAAe,EAAE;UACxB,MAAMI,YAAY,GAAG,IAAI,CAAClC,cAAc,CAAC,UAAU,EAAEmB,UAAU,CAAC;UAChE,IAAIe,YAAY,EAAE;YAChBL,IAAI,CAACI,aAAa,CAAChB,IAAI,CAAC;cACtB5C,IAAI,EAAE,UAAU;cAChBU,IAAI,EAAEmD,YAAY,CAACnD,IAAI;cACvBC,WAAW,EAAEkD,YAAY,CAAClD;YAC5B,CAAC,CAAC;UACJ;QACF;MACF;MAEA,IAAIqC,aAAa,EAAE;QACjBQ,IAAI,CAACE,kBAAkB,GAAG,IAAI,CAACX,qBAAqB,CAACC,aAAa,CAAC;QAEnE,IAAIQ,IAAI,CAACE,kBAAkB,EAAE;UAC3B,MAAMI,eAAe,GAAG,IAAI,CAACnC,cAAc,CAAC,aAAa,EAAEqB,aAAa,CAAC;UACzE,IAAIc,eAAe,EAAE;YACnBN,IAAI,CAACI,aAAa,CAAChB,IAAI,CAAC;cACtB5C,IAAI,EAAE,aAAa;cACnBU,IAAI,EAAEoD,eAAe,CAACpD,IAAI;cAC1BC,WAAW,EAAEmD,eAAe,CAACnD;YAC/B,CAAC,CAAC;UACJ;QACF;MACF;MAEA,OAAO6C,IAAI;IACb,CAAC,CAAC,OAAOhC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MACzD,OAAO;QACLiC,eAAe,EAAE,KAAK;QACtBC,kBAAkB,EAAE,KAAK;QACzBC,sBAAsB,EAAE,EAAE;QAC1BC,aAAa,EAAE;MACjB,CAAC;IACH;EACF;;EAEA;EACAG,oBAAoBA,CAACjB,UAAU,EAAEE,aAAa,GAAG,IAAI,EAAE;IACrD,MAAMgB,MAAM,GAAG,EAAE;IAEjB,IAAIhB,aAAa,EAAE;MACjB;MACA,IAAI,IAAI,CAACH,kBAAkB,CAACC,UAAU,CAAC,EAAE;QACvCkB,MAAM,CAACpB,IAAI,CAAC,0FAA0F,CAAC;MACzG;;MAEA;MACA,IAAI,IAAI,CAACG,qBAAqB,CAACC,aAAa,CAAC,EAAE;QAC7CgB,MAAM,CAACpB,IAAI,CAAC,sFAAsF,CAAC;MACrG;IACF,CAAC,MAAM,IAAIE,UAAU,EAAE;MACrB;MACA,MAAMa,sBAAsB,GAAG,IAAI,CAACV,yBAAyB,CAACH,UAAU,CAAC;MACzE,IAAIa,sBAAsB,CAAChB,MAAM,GAAG,CAAC,EAAE;QACrCqB,MAAM,CAACpB,IAAI,CAAC,mFAAmF,CAAC;MAClG;;MAEA;MACA,IAAI,IAAI,CAACC,kBAAkB,CAACC,UAAU,CAAC,EAAE;QACvCkB,MAAM,CAACpB,IAAI,CAAC,yCAAyC,CAAC;MACxD;IACF;IAEA,OAAO;MACLqB,OAAO,EAAED,MAAM,CAACrB,MAAM,KAAK,CAAC;MAC5BqB;IACF,CAAC;EACH;;EAEA;EACAE,yBAAyBA,CAACC,UAAU,EAAErB,UAAU,EAAEE,aAAa,GAAG,IAAI,EAAE;IACtE;IACA;IACA,IAAIA,aAAa,EAAE;MACjB,MAAMc,eAAe,GAAG,IAAI,CAACnC,cAAc,CAAC,aAAa,EAAEqB,aAAa,CAAC;MACzE,IAAIc,eAAe,EAAE;QACnB,OAAOA,eAAe;MACxB;IACF;;IAEA;IACA,MAAMD,YAAY,GAAG,IAAI,CAAClC,cAAc,CAAC,UAAU,EAAEmB,UAAU,CAAC;IAChE,IAAIe,YAAY,EAAE;MAChB,OAAOA,YAAY;IACrB;;IAEA;IACA,OAAO,IAAI,CAACO,oBAAoB,CAAC,CAAC;EACpC;;EAEA;EACAA,oBAAoBA,CAAA,EAAG;IACrB,MAAMC,SAAS,GAAG3E,kBAAkB,CAAC,CAAC;;IAEtC;IACA,MAAMW,kBAAkB,GAAG,IAAI,CAACC,iBAAiB,CAAC+D,SAAS,CAAC;IAE5D,OAAO;MACLpE,EAAE,EAAE,SAAS;MACbD,IAAI,EAAE,SAAS;MACfU,IAAI,EAAE,qBAAqB;MAC3BC,WAAW,EAAE,qCAAqC;MAClDJ,MAAM,EAAEF,kBAAkB;MAC1BO,QAAQ,EAAEuB,MAAM,CAACD,IAAI,CAACzC,sBAAsB,CAAC;MAC7CoB,QAAQ,EAAE;QACRyD,YAAY,EAAE,IAAI;QAClBC,sBAAsB,EAAE,IAAI;QAC5BC,gBAAgB,EAAE;MACpB,CAAC;MACDzD,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MACnCC,SAAS,EAAE,IAAIF,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MACnCE,OAAO,EAAE;IACX,CAAC;EACH;;EAEA;EACAsD,0BAA0BA,CAACC,cAAc,EAAE7D,QAAQ,GAAG,CAAC,CAAC,EAAE;IACxD,MAAMV,MAAM,GAAG;MACbI,MAAM,EAAEmE,cAAc;MACtB9D,QAAQ,EAAE,IAAI,CAAC+D,qBAAqB,CAACD,cAAc,CAAC;MACpD7D,QAAQ,EAAE;QACRyD,YAAY,EAAE,IAAI;QAClBC,sBAAsB,EAAE,IAAI;QAC5BC,gBAAgB,EAAE,IAAI;QACtB,GAAG3D;MACL;IACF,CAAC;IAED,OAAOV,MAAM;EACf;;EAEA;EACAG,iBAAiBA,CAACC,MAAM,EAAE;IACxB,MAAMqE,IAAI,GAAG,IAAIC,GAAG,CAAC,CAAC;IACtB,MAAMC,YAAY,GAAG,EAAE;IAEvBvE,MAAM,CAACgC,OAAO,CAACwC,KAAK,IAAI;MACtB,IAAI,CAACH,IAAI,CAACI,GAAG,CAACD,KAAK,CAAC3E,GAAG,CAAC,EAAE;QACxBwE,IAAI,CAACK,GAAG,CAACF,KAAK,CAAC3E,GAAG,CAAC;QACnB0E,YAAY,CAAClC,IAAI,CAACmC,KAAK,CAAC;MAC1B,CAAC,MAAM;QACLtD,OAAO,CAACyD,IAAI,CAAC,gDAAgDH,KAAK,CAAC3E,GAAG,EAAE,CAAC;MAC3E;IACF,CAAC,CAAC;IAEF,IAAIG,MAAM,CAACoC,MAAM,KAAKmC,YAAY,CAACnC,MAAM,EAAE;MACzClB,OAAO,CAAC0D,GAAG,CAAC,mCAAmC5E,MAAM,CAACoC,MAAM,cAAcmC,YAAY,CAACnC,MAAM,gBAAgB,CAAC;IAChH;IAEA,OAAOmC,YAAY;EACrB;;EAEA;EACAH,qBAAqBA,CAACpE,MAAM,EAAE;IAC5B,MAAMK,QAAQ,GAAG,CAAC,CAAC;IAEnBL,MAAM,CAACgC,OAAO,CAACwC,KAAK,IAAI;MACtB,MAAMK,UAAU,GAAGL,KAAK,CAACM,OAAO;MAChC,IAAI,CAACzE,QAAQ,CAACwE,UAAU,CAAC,EAAE;QAAA,IAAAE,qBAAA;QACzB1E,QAAQ,CAACwE,UAAU,CAAC,GAAG;UACrBhF,GAAG,EAAEgF,UAAU;UACfG,KAAK,EAAE,EAAAD,qBAAA,GAAA7F,sBAAsB,CAAC2F,UAAU,CAAC,cAAAE,qBAAA,uBAAlCA,qBAAA,CAAoCC,KAAK,KAAIH,UAAU;UAC9D7E,MAAM,EAAE;QACV,CAAC;MACH;MACAK,QAAQ,CAACwE,UAAU,CAAC,CAAC7E,MAAM,CAACqC,IAAI,CAACmC,KAAK,CAAC;IACzC,CAAC,CAAC;IAEF,OAAO5C,MAAM,CAACqD,MAAM,CAAC5E,QAAQ,CAAC;EAChC;;EAEA;EACA6E,kBAAkBA,CAACtF,MAAM,EAAE;IACzB,MAAM6D,MAAM,GAAG,EAAE;IAEjB,IAAI,CAAC7D,MAAM,CAACO,IAAI,IAAIP,MAAM,CAACO,IAAI,CAACgF,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MAC7C1B,MAAM,CAACpB,IAAI,CAAC,uBAAuB,CAAC;IACtC;IAEA,IAAI,CAACzC,MAAM,CAACI,MAAM,IAAIJ,MAAM,CAACI,MAAM,CAACoC,MAAM,KAAK,CAAC,EAAE;MAChDqB,MAAM,CAACpB,IAAI,CAAC,qCAAqC,CAAC;IACpD;;IAEA;IACA,MAAM+C,cAAc,GAAG,CAAC,MAAM,EAAE,cAAc,EAAE,QAAQ,CAAC;IACzD,MAAMC,iBAAiB,GAAGzF,MAAM,CAACI,MAAM,CAAC6C,GAAG,CAACyC,CAAC,IAAIA,CAAC,CAACzF,GAAG,CAAC;IAEvDuF,cAAc,CAACpD,OAAO,CAACuD,aAAa,IAAI;MACtC,IAAI,CAACF,iBAAiB,CAACG,QAAQ,CAACD,aAAa,CAAC,EAAE;QAC9C9B,MAAM,CAACpB,IAAI,CAAC,mBAAmBkD,aAAa,oBAAoB,CAAC;MACnE;IACF,CAAC,CAAC;IAEF,OAAO;MACL7B,OAAO,EAAED,MAAM,CAACrB,MAAM,KAAK,CAAC;MAC5BqB;IACF,CAAC;EACH;;EAEA;EACAgC,gBAAgBA,CAAChG,IAAI,EAAEC,EAAE,EAAE;IACzB,MAAME,MAAM,GAAG,IAAI,CAACwB,cAAc,CAAC3B,IAAI,EAAEC,EAAE,CAAC;IAC5C,IAAI,CAACE,MAAM,EAAE;MACX,MAAM,IAAIuB,KAAK,CAAC,8BAA8B,CAAC;IACjD;IAEA,MAAMuE,UAAU,GAAG;MACjB,GAAG9F,MAAM;MACT+F,UAAU,EAAE,IAAIlF,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MACpCkF,aAAa,EAAE;IACjB,CAAC;IAED,OAAO7E,IAAI,CAACC,SAAS,CAAC0E,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC;EAC5C;;EAEA;EACAG,gBAAgBA,CAACC,UAAU,EAAErG,IAAI,EAAEC,EAAE,EAAE;IACrC,IAAI;MACF,MAAME,MAAM,GAAGmB,IAAI,CAACQ,KAAK,CAACuE,UAAU,CAAC;;MAErC;MACA,MAAMC,UAAU,GAAG,IAAI,CAACb,kBAAkB,CAACtF,MAAM,CAAC;MAClD,IAAI,CAACmG,UAAU,CAACrC,OAAO,EAAE;QACvB,MAAM,IAAIvC,KAAK,CAAC,0BAA0B4E,UAAU,CAACtC,MAAM,CAACuC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;MAC3E;;MAEA;MACApG,MAAM,CAACH,IAAI,GAAGA,IAAI;MAClBG,MAAM,CAACM,YAAY,GAAGR,EAAE;MACxBE,MAAM,CAACqG,UAAU,GAAG,IAAIxF,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MAC5Cd,MAAM,CAACe,SAAS,GAAG,IAAIF,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MAE3C,OAAO,IAAI,CAACf,cAAc,CAACF,IAAI,EAAEC,EAAE,EAAEE,MAAM,CAAC;IAC9C,CAAC,CAAC,OAAOqB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC3D,MAAM,IAAIE,KAAK,CAAC,qCAAqC,CAAC;IACxD;EACF;;EAEA;EACA+E,eAAeA,CAACC,UAAU,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,OAAO,EAAE;IACnE,MAAMC,YAAY,GAAG,IAAI,CAACpF,cAAc,CAAC+E,UAAU,EAAEC,QAAQ,CAAC;IAC9D,IAAI,CAACI,YAAY,EAAE;MACjB,MAAM,IAAIrF,KAAK,CAAC,qCAAqC,CAAC;IACxD;IAEA,MAAMsF,YAAY,GAAG;MACnB,GAAGD,YAAY;MACfrG,IAAI,EAAEoG,OAAO,IAAI,GAAGC,YAAY,CAACrG,IAAI,SAAS;MAC9CV,IAAI,EAAE4G,UAAU;MAChBnG,YAAY,EAAEoG,QAAQ;MACtBI,UAAU,EAAE,GAAGP,UAAU,IAAIC,QAAQ,EAAE;MACvC5F,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MACnCC,SAAS,EAAE,IAAIF,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;IACpC,CAAC;IAED,OAAO,IAAI,CAACf,cAAc,CAAC0G,UAAU,EAAEC,QAAQ,EAAEG,YAAY,CAAC;EAChE;;EAEA;EACAE,iBAAiBA,CAAA,EAAG;IAClB,MAAMhE,UAAU,GAAG,IAAI,CAACV,iBAAiB,CAAC,CAAC;IAE3C,OAAO;MACL2E,UAAU,EAAEjE,UAAU,CAACP,MAAM;MAC7ByE,aAAa,EAAElE,UAAU,CAACb,MAAM,CAACgF,CAAC,IAAIA,CAAC,CAACrH,IAAI,KAAK,UAAU,CAAC,CAAC2C,MAAM;MACnE2E,gBAAgB,EAAEpE,UAAU,CAACb,MAAM,CAACgF,CAAC,IAAIA,CAAC,CAACrH,IAAI,KAAK,aAAa,CAAC,CAAC2C,MAAM;MACzE4E,iBAAiB,EAAErE,UAAU,CAACP,MAAM,GAAG,CAAC,GACpC6E,IAAI,CAACC,KAAK,CAACvE,UAAU,CAACwE,MAAM,CAAC,CAACC,GAAG,EAAEN,CAAC;QAAA,IAAAO,SAAA;QAAA,OAAKD,GAAG,IAAI,EAAAC,SAAA,GAAAP,CAAC,CAAC9G,MAAM,cAAAqH,SAAA,uBAARA,SAAA,CAAUjF,MAAM,KAAI,CAAC,CAAC;MAAA,GAAE,CAAC,CAAC,GAAGO,UAAU,CAACP,MAAM,CAAC,GAC/F,CAAC;MACLkF,cAAc,EAAE,IAAI,CAACC,iBAAiB,CAAC5E,UAAU,CAAC;MAClD6E,gBAAgB,EAAE7E,UAAU,CACzB8E,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAIlH,IAAI,CAACkH,CAAC,CAAChH,SAAS,CAAC,GAAG,IAAIF,IAAI,CAACiH,CAAC,CAAC/G,SAAS,CAAC,CAAC,CAC7DiH,KAAK,CAAC,CAAC,EAAE,CAAC;IACf,CAAC;EACH;;EAEA;EACAL,iBAAiBA,CAACrF,OAAO,EAAE;IACzB,MAAM2F,UAAU,GAAG,CAAC,CAAC;IAErB3F,OAAO,CAACF,OAAO,CAACpC,MAAM,IAAI;MACxB,IAAIA,MAAM,CAACI,MAAM,EAAE;QACjBJ,MAAM,CAACI,MAAM,CAACgC,OAAO,CAACwC,KAAK,IAAI;UAC7BqD,UAAU,CAACrD,KAAK,CAAC3E,GAAG,CAAC,GAAG,CAACgI,UAAU,CAACrD,KAAK,CAAC3E,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC;QAC1D,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;IAEF,OAAO+B,MAAM,CAACkG,OAAO,CAACD,UAAU,CAAC,CAC9BJ,IAAI,CAAC,CAAC,GAAEC,CAAC,CAAC,EAAE,GAAEC,CAAC,CAAC,KAAKA,CAAC,GAAGD,CAAC,CAAC,CAC3BE,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CACZ/E,GAAG,CAAC,CAAC,CAAChD,GAAG,EAAEkI,KAAK,CAAC,MAAM;MAAEvD,KAAK,EAAE3E,GAAG;MAAEmI,KAAK,EAAED;IAAM,CAAC,CAAC,CAAC;EAC1D;;EAEA;EACA9F,iBAAiBA,CAAA,EAAG;IAClB,IAAI;MACF,MAAMgG,KAAK,GAAG,EAAE;MAChB,KAAK,IAAI9F,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGtB,YAAY,CAACuB,MAAM,EAAED,CAAC,EAAE,EAAE;QAC5C,MAAMtC,GAAG,GAAGgB,YAAY,CAAChB,GAAG,CAACsC,CAAC,CAAC;QAC/B,IAAItC,GAAG,IAAIA,GAAG,CAACkC,UAAU,CAAC,IAAI,CAACzC,aAAa,CAAC,EAAE;UAC7C,MAAM+B,MAAM,GAAGR,YAAY,CAACS,OAAO,CAACzB,GAAG,CAAC;UACxC,IAAIwB,MAAM,EAAE;YAAA,IAAA6G,cAAA;YACV,MAAMtI,MAAM,GAAGmB,IAAI,CAACQ,KAAK,CAACF,MAAM,CAAC;YACjC4G,KAAK,CAAC5F,IAAI,CAAC;cACTxC,GAAG;cACH,GAAGD,MAAM;cACTuI,OAAO,EAAE;gBACPC,UAAU,EAAE,EAAAF,cAAA,GAAAtI,MAAM,CAACI,MAAM,cAAAkI,cAAA,uBAAbA,cAAA,CAAe9F,MAAM,KAAI,CAAC;gBACtC3C,IAAI,EAAEG,MAAM,CAACH,IAAI;gBACjBS,YAAY,EAAEN,MAAM,CAACM,YAAY;gBACjCM,SAAS,EAAEZ,MAAM,CAACY,SAAS;gBAC3BG,SAAS,EAAEf,MAAM,CAACe;cACpB;YACF,CAAC,CAAC;UACJ;QACF;MACF;;MAEA;MACA,OAAOsH,KAAK,CAACR,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAIlH,IAAI,CAACkH,CAAC,CAAChH,SAAS,CAAC,GAAG,IAAIF,IAAI,CAACiH,CAAC,CAAC/G,SAAS,CAAC,CAAC;IAC5E,CAAC,CAAC,OAAOM,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;MAC1D,OAAO,EAAE;IACX;EACF;;EAEA;EACAO,gBAAgBA,CAAC/B,IAAI,EAAEC,EAAE,EAAE;IACzB,IAAI;MACF,MAAMG,GAAG,GAAG,IAAI,CAACL,aAAa,CAACC,IAAI,EAAEC,EAAE,CAAC;MACxCmB,YAAY,CAACY,UAAU,CAAC5B,GAAG,CAAC;MAC5B,OAAO,IAAI;IACb,CAAC,CAAC,OAAOoB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;MAC1D,OAAO,KAAK;IACd;EACF;;EAEA;EACAS,mBAAmBA,CAAA,EAAG;IACpB,IAAI;MACF,MAAM2G,YAAY,GAAG,EAAE;MACvB,KAAK,IAAIlG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGtB,YAAY,CAACuB,MAAM,EAAED,CAAC,EAAE,EAAE;QAC5C,MAAMtC,GAAG,GAAGgB,YAAY,CAAChB,GAAG,CAACsC,CAAC,CAAC;QAC/B,IAAItC,GAAG,IAAIA,GAAG,CAACkC,UAAU,CAAC,IAAI,CAACzC,aAAa,CAAC,EAAE;UAC7C+I,YAAY,CAAChG,IAAI,CAACxC,GAAG,CAAC;QACxB;MACF;MAEAwI,YAAY,CAACrG,OAAO,CAACnC,GAAG,IAAIgB,YAAY,CAACY,UAAU,CAAC5B,GAAG,CAAC,CAAC;MACzD,OAAO,IAAI;IACb,CAAC,CAAC,OAAOoB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC3D,OAAO,KAAK;IACd;EACF;AACF;AAEA,eAAe,IAAI7B,iBAAiB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}