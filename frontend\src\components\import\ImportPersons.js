import React, { useState } from 'react';
import DivisionCategorySelection from './DivisionCategorySelection';
import FileUpload from './FileUpload';
import FieldMapping from './FieldMapping';
import ImportProgress from './ImportProgress';
import ImportResults from './ImportResults';
import apiService from '../../services/apiService';
import './ImportPersons.css';

const ImportPersons = ({ onClose, onSuccess }) => {
  const [currentStep, setCurrentStep] = useState(1); // 1: Division/Category, 2: Upload, 3: Mapping, 4: Progress, 5: Results
  const [divisionCategorySelection, setDivisionCategorySelection] = useState(null);
  const [uploadedFile, setUploadedFile] = useState(null);
  const [fileHeaders, setFileHeaders] = useState([]);
  const [fieldMapping, setFieldMapping] = useState({});
  const [importSettings, setImportSettings] = useState({
    importMode: 'SkipDuplicates',
    validateOnly: false,
    batchSize: 100
  });
  const [importJob, setImportJob] = useState(null);
  const [importResults, setImportResults] = useState(null);
  const [error, setError] = useState(null);
  const [loading, setLoading] = useState(false);

  // Convert status to string (handles both enum numbers and strings)
  const getStatusString = (status) => {
    if (typeof status === 'string') {
      return status;
    }

    // Handle enum values
    const statusMap = {
      1: 'Pending',
      2: 'Processing',
      3: 'Completed',
      4: 'Failed',
      5: 'Cancelled'
    };

    return statusMap[status] || 'Unknown';
  };

  const handleDivisionCategorySelection = (selection) => {
    setDivisionCategorySelection(selection);
    setError(null);
    setCurrentStep(2);
  };

  const handleFileUpload = async (file, headers) => {
    setUploadedFile(file);
    setFileHeaders(headers);
    setError(null);
    setCurrentStep(3);
  };

  const handleMappingComplete = (mapping) => {
    setFieldMapping(mapping);
    setCurrentStep(4);
    startImport(mapping);
  };

  const startImport = async (mapping) => {
    setLoading(true);
    setError(null);

    try {
      const formData = new FormData();
      formData.append('file', uploadedFile);
      formData.append('importMode', importSettings.importMode);
      formData.append('validateOnly', importSettings.validateOnly);
      formData.append('batchSize', importSettings.batchSize);

      // Add mandatory division and category selection
      formData.append('defaultDivisionId', divisionCategorySelection.divisionId);
      formData.append('defaultCategoryId', divisionCategorySelection.categoryId);
      if (divisionCategorySelection.subCategoryId) {
        formData.append('defaultSubCategoryId', divisionCategorySelection.subCategoryId);
      }

      // Add field mapping and default values as JSON
      const fieldMapping = mapping.fieldMapping || mapping; // Handle both old and new format
      const defaultValues = mapping.defaultValues || {};

      formData.append('fieldMapping', JSON.stringify(fieldMapping));
      formData.append('defaultValues', JSON.stringify(defaultValues));

      const response = await fetch(`${apiService.baseURL}/import-export/persons/import`, {
        method: 'POST',
        body: formData
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Import failed');
      }

      const result = await response.json();
      setImportJob(result);

      // Start polling for progress
      pollImportProgress(result.jobId);
    } catch (err) {
      console.error('Import error:', err);
      setError(err.message);
      setLoading(false);
    }
  };

  const pollImportProgress = async (jobId) => {
    try {
      const response = await fetch(`${apiService.baseURL}/import-export/persons/import-status/${jobId}`);
      
      if (!response.ok) {
        throw new Error('Failed to get import status');
      }

      const status = await response.json();
      setImportJob(status);

      const statusString = getStatusString(status.status);
      if (statusString === 'Completed' || statusString === 'Failed' || statusString === 'Cancelled') {
        setImportResults(status);
        setCurrentStep(5);
        setLoading(false);
        
        if (status.status === 'Completed' && onSuccess) {
          onSuccess(status);
        }
      } else {
        // Continue polling
        setTimeout(() => pollImportProgress(jobId), 2000);
      }
    } catch (err) {
      console.error('Progress polling error:', err);
      setError(err.message);
      setLoading(false);
    }
  };

  const handleRetry = () => {
    setCurrentStep(1);
    setUploadedFile(null);
    setFileHeaders([]);
    setFieldMapping({});
    setImportJob(null);
    setImportResults(null);
    setError(null);
    setLoading(false);
  };

  const handleSettingsChange = (key, value) => {
    setImportSettings(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const renderStepIndicator = () => {
    const steps = [
      { number: 1, title: 'Select Division & Category', icon: '🏢' },
      { number: 2, title: 'Upload File', icon: '📁' },
      { number: 3, title: 'Map Fields', icon: '🔗' },
      { number: 4, title: 'Import Progress', icon: '⏳' },
      { number: 5, title: 'Results', icon: '📊' }
    ];

    return (
      <div className="step-indicator">
        {steps.map(step => (
          <div 
            key={step.number}
            className={`step ${currentStep >= step.number ? 'active' : ''} ${currentStep === step.number ? 'current' : ''}`}
          >
            <div className="step-icon">{step.icon}</div>
            <div className="step-title">{step.title}</div>
          </div>
        ))}
      </div>
    );
  };

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <DivisionCategorySelection
            onSelectionComplete={handleDivisionCategorySelection}
            onBack={onClose}
            error={error}
          />
        );

      case 2:
        return (
          <FileUpload
            onFileUpload={handleFileUpload}
            importSettings={importSettings}
            onSettingsChange={handleSettingsChange}
            onBack={() => setCurrentStep(1)}
            error={error}
          />
        );

      case 3:
        return (
          <FieldMapping
            fileHeaders={fileHeaders}
            onMappingComplete={handleMappingComplete}
            onBack={() => setCurrentStep(2)}
            error={error}
          />
        );
      
      case 4:
        return (
          <ImportProgress
            importJob={importJob}
            onCancel={() => {
              // Cancel import job
              if (importJob?.jobId) {
                fetch(`${apiService.baseURL}/import-export/persons/import-cancel/${importJob.jobId}`, {
                  method: 'POST'
                });
              }
              setCurrentStep(1);
            }}
            error={error}
          />
        );

      case 5:
        return (
          <ImportResults
            results={importResults}
            onNewImport={handleRetry}
            onClose={onClose}
          />
        );
      
      default:
        return null;
    }
  };

  return (
    <div className="import-page">
      <div className="import-container">
        <div className="import-header">
          <h2>Import Persons</h2>
          <p className="import-subtitle">Upload and import person data from Excel/CSV files</p>
        </div>

        {renderStepIndicator()}

        <div className="import-body">
          {renderCurrentStep()}
        </div>

        {error && (
          <div className="import-error">
            <div className="error-content">
              <span className="error-icon">⚠️</span>
              <span className="error-message">{error}</span>
              <button onClick={handleRetry} className="retry-button">
                Try Again
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ImportPersons;
