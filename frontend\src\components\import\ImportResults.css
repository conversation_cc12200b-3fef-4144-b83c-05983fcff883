.import-results {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  padding: 1rem;
}

.results-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 4rem 2rem;
  text-align: center;
  color: #6c757d;
}

.results-header {
  display: flex;
  justify-content: center;
  margin-bottom: 1rem;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  border-radius: 8px;
  border: 2px solid;
}

.status-indicator.success {
  background-color: #d4edda;
  border-color: #28a745;
  color: #155724;
}

.status-indicator.error {
  background-color: #f8d7da;
  border-color: #dc3545;
  color: #721c24;
}

.status-icon {
  font-size: 3rem;
}

.status-content h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1.5rem;
}

.status-content p {
  margin: 0;
  font-size: 0.875rem;
  opacity: 0.8;
}

.results-summary {
  background-color: #f8f9fa;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  padding: 1.5rem;
}

.results-summary h4 {
  margin: 0 0 1rem 0;
  color: #495057;
}

.summary-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
}

.summary-card {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid;
}

.summary-card.total {
  background-color: #e7f3ff;
  border-color: #b3d7ff;
}

.summary-card.success {
  background-color: #d4edda;
  border-color: #c3e6cb;
}

.summary-card.error {
  background-color: #f8d7da;
  border-color: #f5c6cb;
}

.summary-card.warning {
  background-color: #fff3cd;
  border-color: #ffeaa7;
}

.summary-card.info {
  background-color: #d1ecf1;
  border-color: #bee5eb;
}

.card-icon {
  font-size: 1.5rem;
}

.card-content {
  display: flex;
  flex-direction: column;
}

.card-value {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 0.25rem;
}

.card-label {
  font-size: 0.875rem;
  color: #6c757d;
  font-weight: 500;
}

.processing-details {
  background-color: #f8f9fa;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  padding: 1.5rem;
}

.processing-details h4 {
  margin: 0 0 1rem 0;
  color: #495057;
}

.details-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background-color: white;
  border-radius: 4px;
  border: 1px solid #e1e5e9;
}

.detail-label {
  font-weight: 600;
  color: #495057;
}

.detail-value {
  font-weight: 700;
  color: #007bff;
}

.detailed-summary {
  background-color: #f8f9fa;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  padding: 1.5rem;
}

.detailed-summary h4 {
  margin: 0 0 1rem 0;
  color: #495057;
}

.summary-details {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.summary-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem;
  background-color: white;
  border-radius: 4px;
  border: 1px solid #e1e5e9;
}

.error-analysis {
  background-color: #f8f9fa;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  padding: 1.5rem;
}

.error-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.error-header h4 {
  margin: 0;
  color: #495057;
}

.error-actions {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.error-filter {
  padding: 0.5rem;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 0.875rem;
}

.error-groups {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.error-group {
  border: 1px solid #e1e5e9;
  border-radius: 4px;
  overflow: hidden;
}

.group-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background-color: #f8d7da;
  border-bottom: 1px solid #f5c6cb;
}

.group-type {
  font-weight: 600;
  color: #721c24;
}

.group-count {
  font-size: 0.875rem;
  color: #721c24;
  background-color: #dc3545;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 3px;
}

.group-examples {
  padding: 1rem;
  background-color: white;
}

.error-example {
  display: grid;
  grid-template-columns: auto auto 1fr auto;
  gap: 1rem;
  padding: 0.5rem;
  margin-bottom: 0.5rem;
  background-color: #f8f9fa;
  border-radius: 4px;
  font-size: 0.875rem;
}

.error-row {
  font-weight: 600;
  color: #dc3545;
}

.error-field {
  font-weight: 500;
  color: #495057;
}

.error-message {
  color: #721c24;
}

.error-value {
  font-family: monospace;
  color: #6c757d;
  font-style: italic;
}

.recommendations {
  background-color: #e7f3ff;
  border: 1px solid #b3d7ff;
  border-radius: 8px;
  padding: 1.5rem;
}

.recommendations h4 {
  margin: 0 0 1rem 0;
  color: #0056b3;
}

.recommendations ul {
  margin: 0;
  padding-left: 1.5rem;
  color: #0056b3;
}

.recommendations li {
  margin-bottom: 0.5rem;
}

.results-actions {
  display: flex;
  justify-content: center;
  gap: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #e1e5e9;
}

.btn {
  padding: 0.75rem 1.5rem;
  border: 1px solid transparent;
  border-radius: 4px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  text-decoration: none;
  display: inline-block;
  transition: all 0.2s ease;
}

.btn-primary {
  background-color: #007bff;
  border-color: #007bff;
  color: white;
}

.btn-primary:hover {
  background-color: #0056b3;
  border-color: #0056b3;
}

.btn-outline {
  background-color: transparent;
  border-color: #6c757d;
  color: #6c757d;
}

.btn-outline:hover {
  background-color: #6c757d;
  color: white;
}

/* Responsive design */
@media (max-width: 768px) {
  .import-results {
    padding: 0.5rem;
    gap: 1.5rem;
  }
  
  .status-indicator {
    flex-direction: column;
    text-align: center;
  }
  
  .summary-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .details-grid {
    grid-template-columns: 1fr;
  }
  
  .error-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  
  .error-actions {
    flex-direction: column;
    align-items: stretch;
  }
  
  .error-example {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }
  
  .results-actions {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .summary-grid {
    grid-template-columns: 1fr;
  }
  
  .status-indicator {
    padding: 1rem;
  }
  
  .status-icon {
    font-size: 2rem;
  }
}
