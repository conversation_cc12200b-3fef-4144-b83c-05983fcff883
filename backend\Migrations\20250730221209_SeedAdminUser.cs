﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace CrmApi.Migrations
{
    /// <inheritdoc />
    public partial class SeedAdminUser : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Create default admin user
            // Password: admin123 (hashed using BCrypt)
            var passwordHash = "$2a$11$8K1p/a0dL2LkqvQOuuHDMuEn/TxjYqGlGXbSHPa4plBdJXfXqSr4W"; // BCrypt hash for "admin123"

            migrationBuilder.Sql($@"
                INSERT INTO admins (
                    username,
                    email,
                    password_hash,
                    first_name,
                    last_name,
                    is_active,
                    created_at,
                    updated_at
                ) VALUES (
                    'admin',
                    '<EMAIL>',
                    '{passwordHash}',
                    'System',
                    'Administrator',
                    1,
                    NOW(),
                    NOW()
                )
                ON DUPLICATE KEY UPDATE
                    password_hash = VALUES(password_hash),
                    updated_at = NOW();
            ");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // Remove the default admin user
            migrationBuilder.Sql(@"
                DELETE FROM admins WHERE username = 'admin';
            ");
        }
    }
}
