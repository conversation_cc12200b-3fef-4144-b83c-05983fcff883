import React, { useState, useEffect } from 'react';
import apiService from '../../services/apiService';
import './HierarchicalSelector.css';

const HierarchicalSelector = ({ 
  onSelectionChange, 
  initialSelection = {}, 
  disabled = false,
  showLabels = true,
  required = false 
}) => {
  const [divisions, setDivisions] = useState([]);
  const [categories, setCategories] = useState([]);
  const [subCategories, setSubCategories] = useState([]);
  
  const [selectedDivision, setSelectedDivision] = useState(initialSelection.divisionId || '');
  const [selectedCategory, setSelectedCategory] = useState(initialSelection.categoryId || '');
  const [selectedSubCategory, setSelectedSubCategory] = useState(initialSelection.subCategoryId || '');
  
  const [loading, setLoading] = useState({
    divisions: false,
    categories: false,
    subCategories: false
  });
  
  const [errors, setErrors] = useState({});

  // Update state when initialSelection changes
  useEffect(() => {
    setSelectedDivision(initialSelection.divisionId || '');
    setSelectedCategory(initialSelection.categoryId || '');
    setSelectedSubCategory(initialSelection.subCategoryId || '');
  }, [initialSelection.divisionId, initialSelection.categoryId, initialSelection.subCategoryId]);

  // Load divisions on component mount
  useEffect(() => {
    loadDivisions();
  }, []);

  // Load categories when division changes
  useEffect(() => {
    if (selectedDivision) {
      loadCategories(selectedDivision);
    } else {
      setCategories([]);
      setSelectedCategory('');
      setSelectedSubCategory('');
      setSubCategories([]);
    }
  }, [selectedDivision]);

  // Load subcategories when category changes
  useEffect(() => {
    if (selectedCategory) {
      loadSubCategories(selectedCategory);
    } else {
      setSubCategories([]);
      setSelectedSubCategory('');
    }
  }, [selectedCategory]);

  // Notify parent of selection changes
  useEffect(() => {
    const selection = {
      divisionId: selectedDivision ? parseInt(selectedDivision) : null,
      categoryId: selectedCategory ? parseInt(selectedCategory) : null,
      subCategoryId: selectedSubCategory ? parseInt(selectedSubCategory) : null,
      division: divisions.find(d => d.id === parseInt(selectedDivision)) || null,
      category: categories.find(c => c.id === parseInt(selectedCategory)) || null,
      subCategory: subCategories.find(sc => sc.id === parseInt(selectedSubCategory)) || null
    };
    
    onSelectionChange(selection);
  }, [selectedDivision, selectedCategory, selectedSubCategory, divisions, categories, subCategories, onSelectionChange]);

  const loadDivisions = async () => {
    setLoading(prev => ({ ...prev, divisions: true }));
    try {
      const response = await apiService.getDivisions();
      setDivisions(response.data || []);
      setErrors(prev => ({ ...prev, divisions: null }));
    } catch (error) {
      console.error('Error loading divisions:', error);
      setErrors(prev => ({ ...prev, divisions: 'Failed to load divisions' }));
    } finally {
      setLoading(prev => ({ ...prev, divisions: false }));
    }
  };

  const loadCategories = async (divisionId) => {
    setLoading(prev => ({ ...prev, categories: true }));
    try {
      const response = await apiService.getCategoriesByDivision(divisionId);
      setCategories(response.data || []);
      setErrors(prev => ({ ...prev, categories: null }));
    } catch (error) {
      console.error('Error loading categories:', error);
      setErrors(prev => ({ ...prev, categories: 'Failed to load categories' }));
      setCategories([]);
    } finally {
      setLoading(prev => ({ ...prev, categories: false }));
    }
  };

  const loadSubCategories = async (categoryId) => {
    setLoading(prev => ({ ...prev, subCategories: true }));
    try {
      const response = await apiService.getSubCategoriesByCategory(categoryId);
      setSubCategories(response.data || []);
      setErrors(prev => ({ ...prev, subCategories: null }));
    } catch (error) {
      console.error('Error loading subcategories:', error);
      setErrors(prev => ({ ...prev, subCategories: 'Failed to load subcategories' }));
      setSubCategories([]);
    } finally {
      setLoading(prev => ({ ...prev, subCategories: false }));
    }
  };

  const handleDivisionChange = (e) => {
    const value = e.target.value;
    setSelectedDivision(value);
    setSelectedCategory('');
    setSelectedSubCategory('');
  };

  const handleCategoryChange = (e) => {
    const value = e.target.value;
    setSelectedCategory(value);
    setSelectedSubCategory('');
  };

  const handleSubCategoryChange = (e) => {
    const value = e.target.value;
    setSelectedSubCategory(value);
  };

  return (
    <div className="hierarchical-selector">


      {/* Division Selection */}
      <div className="selector-group">
        {showLabels && (
          <label className="selector-label">
            Division {required && <span className="required">*</span>}
          </label>
        )}
        <select
          value={selectedDivision}
          onChange={handleDivisionChange}
          disabled={disabled || loading.divisions}
          className={`selector-input ${errors.divisions ? 'error' : ''}`}
          required={required}
        >
          <option value="">
            {loading.divisions ? 'Loading divisions...' : 'Select Division'}
          </option>
          {divisions.map(division => (
            <option key={division.id} value={division.id}>
              {division.name}
            </option>
          ))}
        </select>
        {errors.divisions && (
          <div className="error-message">{errors.divisions}</div>
        )}
      </div>

      {/* Category Selection */}
      <div className="selector-group">
        {showLabels && (
          <label className="selector-label">
            Category {required && <span className="required">*</span>}
          </label>
        )}
        <select
          value={selectedCategory}
          onChange={handleCategoryChange}
          disabled={disabled || !selectedDivision || loading.categories}
          className={`selector-input ${errors.categories ? 'error' : ''}`}
          required={required}
        >
          <option value="">
            {!selectedDivision 
              ? 'Select Division first'
              : loading.categories 
                ? 'Loading categories...' 
                : 'Select Category'
            }
          </option>
          {categories.map(category => (
            <option key={category.id} value={category.id}>
              {category.name}
            </option>
          ))}
        </select>
        {errors.categories && (
          <div className="error-message">{errors.categories}</div>
        )}
      </div>

      {/* SubCategory Selection */}
      <div className="selector-group">
        {showLabels && (
          <label className="selector-label">SubCategory (Optional)</label>
        )}
        <select
          value={selectedSubCategory}
          onChange={handleSubCategoryChange}
          disabled={disabled || !selectedCategory || loading.subCategories}
          className={`selector-input ${errors.subCategories ? 'error' : ''}`}
        >
          <option value="">
            {!selectedCategory 
              ? 'Select Category first'
              : loading.subCategories 
                ? 'Loading subcategories...' 
                : subCategories.length === 0
                  ? 'No subcategories available'
                  : 'Select SubCategory (Optional)'
            }
          </option>
          {subCategories.map(subCategory => (
            <option key={subCategory.id} value={subCategory.id}>
              {subCategory.name}
            </option>
          ))}
        </select>
        {errors.subCategories && (
          <div className="error-message">{errors.subCategories}</div>
        )}
      </div>

      {/* Selection Summary */}
      {(selectedDivision || selectedCategory || selectedSubCategory) && (
        <div className="selection-summary">
          <h4>Current Selection:</h4>
          <div className="selection-path">
            {selectedDivision && (
              <span className="selection-item">
                {divisions.find(d => d.id === parseInt(selectedDivision))?.name}
              </span>
            )}
            {selectedCategory && (
              <>
                <span className="separator">→</span>
                <span className="selection-item">
                  {categories.find(c => c.id === parseInt(selectedCategory))?.name}
                </span>
              </>
            )}
            {selectedSubCategory && (
              <>
                <span className="separator">→</span>
                <span className="selection-item">
                  {subCategories.find(sc => sc.id === parseInt(selectedSubCategory))?.name}
                </span>
              </>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default HierarchicalSelector;
