{"ast": null, "code": "const warned = new Set();\nfunction warnOnce(condition, message, element) {\n  if (condition || warned.has(message)) return;\n  console.warn(message);\n  if (element) console.warn(element);\n  warned.add(message);\n}\nexport { warnOnce };", "map": {"version": 3, "names": ["warned", "Set", "warnOnce", "condition", "message", "element", "has", "console", "warn", "add"], "sources": ["C:/Users/<USER>/Downloads/Advanced/Advanced CRM filed all use/frontend/node_modules/framer-motion/dist/es/utils/warn-once.mjs"], "sourcesContent": ["const warned = new Set();\nfunction warnOnce(condition, message, element) {\n    if (condition || warned.has(message))\n        return;\n    console.warn(message);\n    if (element)\n        console.warn(element);\n    warned.add(message);\n}\n\nexport { warnOnce };\n"], "mappings": "AAAA,MAAMA,MAAM,GAAG,IAAIC,GAAG,CAAC,CAAC;AACxB,SAASC,QAAQA,CAACC,SAAS,EAAEC,OAAO,EAAEC,OAAO,EAAE;EAC3C,IAAIF,SAAS,IAAIH,MAAM,CAACM,GAAG,CAACF,OAAO,CAAC,EAChC;EACJG,OAAO,CAACC,IAAI,CAACJ,OAAO,CAAC;EACrB,IAAIC,OAAO,EACPE,OAAO,CAACC,IAAI,CAACH,OAAO,CAAC;EACzBL,MAAM,CAACS,GAAG,CAACL,OAAO,CAAC;AACvB;AAEA,SAASF,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}