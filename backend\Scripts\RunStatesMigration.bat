@echo off
echo Starting States Migration...

cd /d "%~dp0\.."
echo Current directory: %CD%

echo Building the project...
dotnet build

if %ERRORLEVEL% neq 0 (
    echo Build failed. Please fix compilation errors first.
    pause
    exit /b 1
)

echo Adding States migration...
dotnet ef migrations add CreateStatesTable --output-dir Migrations

echo Updating database with States migration...
dotnet ef database update

if %ERRORLEVEL% equ 0 (
    echo.
    echo States migration completed successfully!
    echo The following has been created:
    echo - States table with proper schema
    echo - All 28 Indian states inserted
    echo - All 8 Union Territories inserted
    echo - Proper indexes for performance
    echo - Regional categorization included
) else (
    echo Migration failed. Please check the error messages above.
    pause
    exit /b 1
)

echo Migration script completed.
pause
