{"ast": null, "code": "import { transformPropOrder } from './transform.mjs';\nconst translateAlias = {\n  x: \"translateX\",\n  y: \"translateY\",\n  z: \"translateZ\",\n  transformPerspective: \"perspective\"\n};\nconst numTransforms = transformPropOrder.length;\n/**\n * Build a CSS transform style from individual x/y/scale etc properties.\n *\n * This outputs with a default order of transforms/scales/rotations, this can be customised by\n * providing a transformTemplate function.\n */\nfunction buildTransform(transform, {\n  enableHardwareAcceleration = true,\n  allowTransformNone = true\n}, transformIsDefault, transformTemplate) {\n  // The transform string we're going to build into.\n  let transformString = \"\";\n  /**\n   * Loop over all possible transforms in order, adding the ones that\n   * are present to the transform string.\n   */\n  for (let i = 0; i < numTransforms; i++) {\n    const key = transformPropOrder[i];\n    if (transform[key] !== undefined) {\n      const transformName = translateAlias[key] || key;\n      transformString += `${transformName}(${transform[key]}) `;\n    }\n  }\n  if (enableHardwareAcceleration && !transform.z) {\n    transformString += \"translateZ(0)\";\n  }\n  transformString = transformString.trim();\n  // If we have a custom `transform` template, pass our transform values and\n  // generated transformString to that before returning\n  if (transformTemplate) {\n    transformString = transformTemplate(transform, transformIsDefault ? \"\" : transformString);\n  } else if (allowTransformNone && transformIsDefault) {\n    transformString = \"none\";\n  }\n  return transformString;\n}\nexport { buildTransform };", "map": {"version": 3, "names": ["transformPropOrder", "<PERSON><PERSON><PERSON><PERSON>", "x", "y", "z", "transformPerspective", "numTransforms", "length", "buildTransform", "transform", "enableHardwareAcceleration", "allowTransformNone", "transformIsDefault", "transformTemplate", "transformString", "i", "key", "undefined", "transformName", "trim"], "sources": ["C:/Users/<USER>/Downloads/Advanced/Advanced CRM filed all use/frontend/node_modules/framer-motion/dist/es/render/html/utils/build-transform.mjs"], "sourcesContent": ["import { transformPropOrder } from './transform.mjs';\n\nconst translateAlias = {\n    x: \"translateX\",\n    y: \"translateY\",\n    z: \"translateZ\",\n    transformPerspective: \"perspective\",\n};\nconst numTransforms = transformPropOrder.length;\n/**\n * Build a CSS transform style from individual x/y/scale etc properties.\n *\n * This outputs with a default order of transforms/scales/rotations, this can be customised by\n * providing a transformTemplate function.\n */\nfunction buildTransform(transform, { enableHardwareAcceleration = true, allowTransformNone = true, }, transformIsDefault, transformTemplate) {\n    // The transform string we're going to build into.\n    let transformString = \"\";\n    /**\n     * Loop over all possible transforms in order, adding the ones that\n     * are present to the transform string.\n     */\n    for (let i = 0; i < numTransforms; i++) {\n        const key = transformPropOrder[i];\n        if (transform[key] !== undefined) {\n            const transformName = translateAlias[key] || key;\n            transformString += `${transformName}(${transform[key]}) `;\n        }\n    }\n    if (enableHardwareAcceleration && !transform.z) {\n        transformString += \"translateZ(0)\";\n    }\n    transformString = transformString.trim();\n    // If we have a custom `transform` template, pass our transform values and\n    // generated transformString to that before returning\n    if (transformTemplate) {\n        transformString = transformTemplate(transform, transformIsDefault ? \"\" : transformString);\n    }\n    else if (allowTransformNone && transformIsDefault) {\n        transformString = \"none\";\n    }\n    return transformString;\n}\n\nexport { buildTransform };\n"], "mappings": "AAAA,SAASA,kBAAkB,QAAQ,iBAAiB;AAEpD,MAAMC,cAAc,GAAG;EACnBC,CAAC,EAAE,YAAY;EACfC,CAAC,EAAE,YAAY;EACfC,CAAC,EAAE,YAAY;EACfC,oBAAoB,EAAE;AAC1B,CAAC;AACD,MAAMC,aAAa,GAAGN,kBAAkB,CAACO,MAAM;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,cAAcA,CAACC,SAAS,EAAE;EAAEC,0BAA0B,GAAG,IAAI;EAAEC,kBAAkB,GAAG;AAAM,CAAC,EAAEC,kBAAkB,EAAEC,iBAAiB,EAAE;EACzI;EACA,IAAIC,eAAe,GAAG,EAAE;EACxB;AACJ;AACA;AACA;EACI,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGT,aAAa,EAAES,CAAC,EAAE,EAAE;IACpC,MAAMC,GAAG,GAAGhB,kBAAkB,CAACe,CAAC,CAAC;IACjC,IAAIN,SAAS,CAACO,GAAG,CAAC,KAAKC,SAAS,EAAE;MAC9B,MAAMC,aAAa,GAAGjB,cAAc,CAACe,GAAG,CAAC,IAAIA,GAAG;MAChDF,eAAe,IAAI,GAAGI,aAAa,IAAIT,SAAS,CAACO,GAAG,CAAC,IAAI;IAC7D;EACJ;EACA,IAAIN,0BAA0B,IAAI,CAACD,SAAS,CAACL,CAAC,EAAE;IAC5CU,eAAe,IAAI,eAAe;EACtC;EACAA,eAAe,GAAGA,eAAe,CAACK,IAAI,CAAC,CAAC;EACxC;EACA;EACA,IAAIN,iBAAiB,EAAE;IACnBC,eAAe,GAAGD,iBAAiB,CAACJ,SAAS,EAAEG,kBAAkB,GAAG,EAAE,GAAGE,eAAe,CAAC;EAC7F,CAAC,MACI,IAAIH,kBAAkB,IAAIC,kBAAkB,EAAE;IAC/CE,eAAe,GAAG,MAAM;EAC5B;EACA,OAAOA,eAAe;AAC1B;AAEA,SAASN,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}