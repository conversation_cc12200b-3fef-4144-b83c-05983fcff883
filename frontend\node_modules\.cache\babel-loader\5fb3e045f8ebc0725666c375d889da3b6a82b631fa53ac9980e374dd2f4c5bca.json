{"ast": null, "code": "import { PersonFieldDefinitions, getAllPersonFields } from '../constants/personConstants';\nclass FormConfigService {\n  constructor() {\n    this.storagePrefix = 'crm_form_config_';\n    this.defaultFormKey = 'default_person_form';\n  }\n\n  // Generate storage key for form configuration\n  getStorageKey(type, id) {\n    return `${this.storagePrefix}${type}_${id}`;\n  }\n\n  // Save form configuration\n  saveFormConfig(type, id, config) {\n    try {\n      const key = this.getStorageKey(type, id);\n      const formConfig = {\n        id: `${type}_${id}`,\n        type,\n        // 'division', 'category', or 'subcategory'\n        associatedId: id,\n        name: config.name || `${type} ${id} Form`,\n        description: config.description || '',\n        fields: config.fields || [],\n        sections: config.sections || [],\n        settings: config.settings || {},\n        hierarchy: config.hierarchy || {},\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString(),\n        version: 1,\n        ...config\n      };\n      localStorage.setItem(key, JSON.stringify(formConfig));\n      return formConfig;\n    } catch (error) {\n      console.error('Error saving form configuration:', error);\n      throw new Error('Failed to save form configuration');\n    }\n  }\n\n  // Load form configuration\n  loadFormConfig(type, id) {\n    try {\n      const key = this.getStorageKey(type, id);\n      const stored = localStorage.getItem(key);\n      if (stored) {\n        return JSON.parse(stored);\n      }\n      return null;\n    } catch (error) {\n      console.error('Error loading form configuration:', error);\n      return null;\n    }\n  }\n\n  // Delete form configuration\n  deleteFormConfig(type, id) {\n    try {\n      const key = this.getStorageKey(type, id);\n      localStorage.removeItem(key);\n      return true;\n    } catch (error) {\n      console.error('Error deleting form configuration:', error);\n      return false;\n    }\n  }\n\n  // Get all form configurations\n  getAllFormConfigs() {\n    try {\n      const configs = [];\n      for (let i = 0; i < localStorage.length; i++) {\n        const key = localStorage.key(i);\n        if (key && key.startsWith(this.storagePrefix)) {\n          const config = JSON.parse(localStorage.getItem(key));\n          configs.push(config);\n        }\n      }\n      return configs;\n    } catch (error) {\n      console.error('Error loading all form configurations:', error);\n      return [];\n    }\n  }\n\n  // Check if a form exists for a specific category\n  hasFormForCategory(categoryId) {\n    try {\n      const key = this.getStorageKey('category', categoryId);\n      return localStorage.getItem(key) !== null;\n    } catch (error) {\n      console.error('Error checking category form:', error);\n      return false;\n    }\n  }\n\n  // Check if a form exists for a specific subcategory\n  hasFormForSubCategory(subCategoryId) {\n    try {\n      const key = this.getStorageKey('subcategory', subCategoryId);\n      return localStorage.getItem(key) !== null;\n    } catch (error) {\n      console.error('Error checking subcategory form:', error);\n      return false;\n    }\n  }\n\n  // Get all subcategories for a category that have forms\n  getSubCategoriesWithForms(categoryId) {\n    try {\n      const allConfigs = this.getAllFormConfigs();\n      return allConfigs.filter(config => {\n        var _config$hierarchy;\n        return config.type === 'subcategory' && ((_config$hierarchy = config.hierarchy) === null || _config$hierarchy === void 0 ? void 0 : _config$hierarchy.categoryId) === categoryId;\n      }).map(config => {\n        var _config$hierarchy2;\n        return (_config$hierarchy2 = config.hierarchy) === null || _config$hierarchy2 === void 0 ? void 0 : _config$hierarchy2.subCategoryId;\n      }).filter(id => id !== null && id !== undefined);\n    } catch (error) {\n      console.error('Error getting subcategories with forms:', error);\n      return [];\n    }\n  }\n\n  // Validate form creation rules\n  validateFormCreation(categoryId, subCategoryId = null) {\n    const errors = [];\n    if (subCategoryId) {\n      // Rule 1: Check if category already has a form\n      if (this.hasFormForCategory(categoryId)) {\n        errors.push('Cannot create form for subcategory because a form already exists for the parent category');\n      }\n\n      // Rule 2: Check if subcategory already has a form\n      if (this.hasFormForSubCategory(subCategoryId)) {\n        errors.push('A form already exists for this subcategory. Only one form per subcategory is allowed');\n      }\n    } else if (categoryId) {\n      // Rule 3: Check if any subcategories have forms\n      const subCategoriesWithForms = this.getSubCategoriesWithForms(categoryId);\n      if (subCategoriesWithForms.length > 0) {\n        errors.push('Cannot create form for category because forms already exist for its subcategories');\n      }\n\n      // Rule 4: Check if category already has a form\n      if (this.hasFormForCategory(categoryId)) {\n        errors.push('A form already exists for this category');\n      }\n    }\n    return {\n      isValid: errors.length === 0,\n      errors\n    };\n  }\n\n  // Get form configuration for category or subcategory\n  getFormConfigForSelection(divisionId, categoryId, subCategoryId = null) {\n    // First try to get subcategory-specific form if subcategory is selected\n    if (subCategoryId) {\n      const subCategoryForm = this.loadFormConfig('subcategory', subCategoryId);\n      if (subCategoryForm) {\n        return subCategoryForm;\n      }\n    }\n\n    // Then try category-specific form\n    const categoryForm = this.loadFormConfig('category', categoryId);\n    if (categoryForm) {\n      return categoryForm;\n    }\n\n    // Return default form if no custom form found\n    return this.getDefaultFormConfig();\n  }\n\n  // Get default form configuration with all fields\n  getDefaultFormConfig() {\n    return {\n      id: 'default',\n      type: 'default',\n      name: 'Default Person Form',\n      description: 'Default form with all person fields',\n      fields: getAllPersonFields(),\n      sections: Object.keys(PersonFieldDefinitions),\n      settings: {\n        showSections: true,\n        allowConditionalFields: true,\n        validateOnChange: true\n      },\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString(),\n      version: 1\n    };\n  }\n\n  // Create form configuration from field selection\n  createFormConfigFromFields(selectedFields, settings = {}) {\n    const config = {\n      fields: selectedFields,\n      sections: this.groupFieldsBySections(selectedFields),\n      settings: {\n        showSections: true,\n        allowConditionalFields: true,\n        validateOnChange: true,\n        ...settings\n      }\n    };\n    return config;\n  }\n\n  // Group fields by their sections\n  groupFieldsBySections(fields) {\n    const sections = {};\n    fields.forEach(field => {\n      const sectionKey = field.section;\n      if (!sections[sectionKey]) {\n        var _PersonFieldDefinitio;\n        sections[sectionKey] = {\n          key: sectionKey,\n          title: ((_PersonFieldDefinitio = PersonFieldDefinitions[sectionKey]) === null || _PersonFieldDefinitio === void 0 ? void 0 : _PersonFieldDefinitio.title) || sectionKey,\n          fields: []\n        };\n      }\n      sections[sectionKey].fields.push(field);\n    });\n    return Object.values(sections);\n  }\n\n  // Validate form configuration\n  validateFormConfig(config) {\n    const errors = [];\n    if (!config.name || config.name.trim() === '') {\n      errors.push('Form name is required');\n    }\n    if (!config.fields || config.fields.length === 0) {\n      errors.push('At least one field must be selected');\n    }\n\n    // Check if required fields are included\n    const requiredFields = ['name', 'mobileNumber', 'nature'];\n    const selectedFieldKeys = config.fields.map(f => f.key);\n    requiredFields.forEach(requiredField => {\n      if (!selectedFieldKeys.includes(requiredField)) {\n        errors.push(`Required field '${requiredField}' must be included`);\n      }\n    });\n    return {\n      isValid: errors.length === 0,\n      errors\n    };\n  }\n\n  // Export form configuration\n  exportFormConfig(type, id) {\n    const config = this.loadFormConfig(type, id);\n    if (!config) {\n      throw new Error('Form configuration not found');\n    }\n    const exportData = {\n      ...config,\n      exportedAt: new Date().toISOString(),\n      exportVersion: '1.0'\n    };\n    return JSON.stringify(exportData, null, 2);\n  }\n\n  // Import form configuration\n  importFormConfig(configJson, type, id) {\n    try {\n      const config = JSON.parse(configJson);\n\n      // Validate imported configuration\n      const validation = this.validateFormConfig(config);\n      if (!validation.isValid) {\n        throw new Error(`Invalid configuration: ${validation.errors.join(', ')}`);\n      }\n\n      // Update metadata for import\n      config.type = type;\n      config.associatedId = id;\n      config.importedAt = new Date().toISOString();\n      config.updatedAt = new Date().toISOString();\n      return this.saveFormConfig(type, id, config);\n    } catch (error) {\n      console.error('Error importing form configuration:', error);\n      throw new Error('Failed to import form configuration');\n    }\n  }\n\n  // Clone form configuration\n  cloneFormConfig(sourceType, sourceId, targetType, targetId, newName) {\n    const sourceConfig = this.loadFormConfig(sourceType, sourceId);\n    if (!sourceConfig) {\n      throw new Error('Source form configuration not found');\n    }\n    const clonedConfig = {\n      ...sourceConfig,\n      name: newName || `${sourceConfig.name} (Copy)`,\n      type: targetType,\n      associatedId: targetId,\n      clonedFrom: `${sourceType}_${sourceId}`,\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString()\n    };\n    return this.saveFormConfig(targetType, targetId, clonedConfig);\n  }\n\n  // Get form statistics\n  getFormStatistics() {\n    const allConfigs = this.getAllFormConfigs();\n    return {\n      totalForms: allConfigs.length,\n      categoryForms: allConfigs.filter(c => c.type === 'category').length,\n      subCategoryForms: allConfigs.filter(c => c.type === 'subcategory').length,\n      averageFieldCount: allConfigs.length > 0 ? Math.round(allConfigs.reduce((sum, c) => {\n        var _c$fields;\n        return sum + (((_c$fields = c.fields) === null || _c$fields === void 0 ? void 0 : _c$fields.length) || 0);\n      }, 0) / allConfigs.length) : 0,\n      mostUsedFields: this.getMostUsedFields(allConfigs),\n      recentlyModified: allConfigs.sort((a, b) => new Date(b.updatedAt) - new Date(a.updatedAt)).slice(0, 5)\n    };\n  }\n\n  // Get most used fields across all forms\n  getMostUsedFields(configs) {\n    const fieldUsage = {};\n    configs.forEach(config => {\n      if (config.fields) {\n        config.fields.forEach(field => {\n          fieldUsage[field.key] = (fieldUsage[field.key] || 0) + 1;\n        });\n      }\n    });\n    return Object.entries(fieldUsage).sort(([, a], [, b]) => b - a).slice(0, 10).map(([key, count]) => ({\n      field: key,\n      usage: count\n    }));\n  }\n\n  // Get all saved form configurations\n  getAllFormConfigs() {\n    try {\n      const forms = [];\n      for (let i = 0; i < localStorage.length; i++) {\n        const key = localStorage.key(i);\n        if (key && key.startsWith(this.storagePrefix)) {\n          const stored = localStorage.getItem(key);\n          if (stored) {\n            var _config$fields;\n            const config = JSON.parse(stored);\n            forms.push({\n              key,\n              ...config,\n              summary: {\n                fieldCount: ((_config$fields = config.fields) === null || _config$fields === void 0 ? void 0 : _config$fields.length) || 0,\n                type: config.type,\n                associatedId: config.associatedId,\n                createdAt: config.createdAt,\n                updatedAt: config.updatedAt\n              }\n            });\n          }\n        }\n      }\n\n      // Sort by updated date (most recent first)\n      return forms.sort((a, b) => new Date(b.updatedAt) - new Date(a.updatedAt));\n    } catch (error) {\n      console.error('Error getting form configurations:', error);\n      return [];\n    }\n  }\n\n  // Delete a specific form configuration\n  deleteFormConfig(type, id) {\n    try {\n      const key = this.getStorageKey(type, id);\n      localStorage.removeItem(key);\n      return true;\n    } catch (error) {\n      console.error('Error deleting form configuration:', error);\n      return false;\n    }\n  }\n\n  // Clear all form configurations (for development/testing)\n  clearAllFormConfigs() {\n    try {\n      const keysToRemove = [];\n      for (let i = 0; i < localStorage.length; i++) {\n        const key = localStorage.key(i);\n        if (key && key.startsWith(this.storagePrefix)) {\n          keysToRemove.push(key);\n        }\n      }\n      keysToRemove.forEach(key => localStorage.removeItem(key));\n      return true;\n    } catch (error) {\n      console.error('Error clearing form configurations:', error);\n      return false;\n    }\n  }\n}\nexport default new FormConfigService();", "map": {"version": 3, "names": ["PersonFieldDefinitions", "<PERSON><PERSON><PERSON><PERSON><PERSON>F<PERSON>s", "FormConfigService", "constructor", "storagePrefix", "defaultFormKey", "getStorageKey", "type", "id", "saveFormConfig", "config", "key", "formConfig", "associatedId", "name", "description", "fields", "sections", "settings", "hierarchy", "createdAt", "Date", "toISOString", "updatedAt", "version", "localStorage", "setItem", "JSON", "stringify", "error", "console", "Error", "loadFormConfig", "stored", "getItem", "parse", "deleteFormConfig", "removeItem", "getAllFormConfigs", "configs", "i", "length", "startsWith", "push", "hasFormForCategory", "categoryId", "hasFormForSubCategory", "subCategoryId", "getSubCategoriesWithForms", "allConfigs", "filter", "_config$hierarchy", "map", "_config$hierarchy2", "undefined", "validateFormCreation", "errors", "subCategoriesWithForms", "<PERSON><PERSON><PERSON><PERSON>", "getFormConfigForSelection", "divisionId", "subCategoryForm", "categoryForm", "getDefaultFormConfig", "Object", "keys", "showSections", "allowConditionalFields", "validateOnChange", "createFormConfigFromFields", "<PERSON><PERSON><PERSON>s", "groupFieldsBySections", "for<PERSON>ach", "field", "sectionKey", "section", "_PersonFieldDefinitio", "title", "values", "validateFormConfig", "trim", "requiredFields", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "f", "requiredField", "includes", "exportFormConfig", "exportData", "exportedAt", "exportVersion", "importFormConfig", "config<PERSON><PERSON>", "validation", "join", "importedAt", "cloneFormConfig", "sourceType", "sourceId", "targetType", "targetId", "newName", "sourceConfig", "clonedConfig", "clonedFrom", "getFormStatistics", "totalForms", "categoryForms", "c", "subCategoryForms", "averageFieldCount", "Math", "round", "reduce", "sum", "_c$fields", "mostUsedFields", "getMostUsedFields", "recentlyModified", "sort", "a", "b", "slice", "fieldUsage", "entries", "count", "usage", "forms", "_config$fields", "summary", "fieldCount", "clearAllFormConfigs", "keysToRemove"], "sources": ["C:/Users/<USER>/Downloads/Advanced/Advanced CRM filed all use/frontend/src/services/formConfigService.js"], "sourcesContent": ["import { PersonFieldDefinitions, getAllPersonFields } from '../constants/personConstants';\n\nclass FormConfigService {\n  constructor() {\n    this.storagePrefix = 'crm_form_config_';\n    this.defaultFormKey = 'default_person_form';\n  }\n\n  // Generate storage key for form configuration\n  getStorageKey(type, id) {\n    return `${this.storagePrefix}${type}_${id}`;\n  }\n\n  // Save form configuration\n  saveFormConfig(type, id, config) {\n    try {\n      const key = this.getStorageKey(type, id);\n      const formConfig = {\n        id: `${type}_${id}`,\n        type, // 'division', 'category', or 'subcategory'\n        associatedId: id,\n        name: config.name || `${type} ${id} Form`,\n        description: config.description || '',\n        fields: config.fields || [],\n        sections: config.sections || [],\n        settings: config.settings || {},\n        hierarchy: config.hierarchy || {},\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString(),\n        version: 1,\n        ...config\n      };\n\n      localStorage.setItem(key, JSON.stringify(formConfig));\n      return formConfig;\n    } catch (error) {\n      console.error('Error saving form configuration:', error);\n      throw new Error('Failed to save form configuration');\n    }\n  }\n\n  // Load form configuration\n  loadFormConfig(type, id) {\n    try {\n      const key = this.getStorageKey(type, id);\n      const stored = localStorage.getItem(key);\n      \n      if (stored) {\n        return JSON.parse(stored);\n      }\n      \n      return null;\n    } catch (error) {\n      console.error('Error loading form configuration:', error);\n      return null;\n    }\n  }\n\n  // Delete form configuration\n  deleteFormConfig(type, id) {\n    try {\n      const key = this.getStorageKey(type, id);\n      localStorage.removeItem(key);\n      return true;\n    } catch (error) {\n      console.error('Error deleting form configuration:', error);\n      return false;\n    }\n  }\n\n  // Get all form configurations\n  getAllFormConfigs() {\n    try {\n      const configs = [];\n      for (let i = 0; i < localStorage.length; i++) {\n        const key = localStorage.key(i);\n        if (key && key.startsWith(this.storagePrefix)) {\n          const config = JSON.parse(localStorage.getItem(key));\n          configs.push(config);\n        }\n      }\n      return configs;\n    } catch (error) {\n      console.error('Error loading all form configurations:', error);\n      return [];\n    }\n  }\n\n  // Check if a form exists for a specific category\n  hasFormForCategory(categoryId) {\n    try {\n      const key = this.getStorageKey('category', categoryId);\n      return localStorage.getItem(key) !== null;\n    } catch (error) {\n      console.error('Error checking category form:', error);\n      return false;\n    }\n  }\n\n  // Check if a form exists for a specific subcategory\n  hasFormForSubCategory(subCategoryId) {\n    try {\n      const key = this.getStorageKey('subcategory', subCategoryId);\n      return localStorage.getItem(key) !== null;\n    } catch (error) {\n      console.error('Error checking subcategory form:', error);\n      return false;\n    }\n  }\n\n  // Get all subcategories for a category that have forms\n  getSubCategoriesWithForms(categoryId) {\n    try {\n      const allConfigs = this.getAllFormConfigs();\n      return allConfigs\n        .filter(config => config.type === 'subcategory' && config.hierarchy?.categoryId === categoryId)\n        .map(config => config.hierarchy?.subCategoryId)\n        .filter(id => id !== null && id !== undefined);\n    } catch (error) {\n      console.error('Error getting subcategories with forms:', error);\n      return [];\n    }\n  }\n\n  // Validate form creation rules\n  validateFormCreation(categoryId, subCategoryId = null) {\n    const errors = [];\n\n    if (subCategoryId) {\n      // Rule 1: Check if category already has a form\n      if (this.hasFormForCategory(categoryId)) {\n        errors.push('Cannot create form for subcategory because a form already exists for the parent category');\n      }\n\n      // Rule 2: Check if subcategory already has a form\n      if (this.hasFormForSubCategory(subCategoryId)) {\n        errors.push('A form already exists for this subcategory. Only one form per subcategory is allowed');\n      }\n    } else if (categoryId) {\n      // Rule 3: Check if any subcategories have forms\n      const subCategoriesWithForms = this.getSubCategoriesWithForms(categoryId);\n      if (subCategoriesWithForms.length > 0) {\n        errors.push('Cannot create form for category because forms already exist for its subcategories');\n      }\n\n      // Rule 4: Check if category already has a form\n      if (this.hasFormForCategory(categoryId)) {\n        errors.push('A form already exists for this category');\n      }\n    }\n\n    return {\n      isValid: errors.length === 0,\n      errors\n    };\n  }\n\n  // Get form configuration for category or subcategory\n  getFormConfigForSelection(divisionId, categoryId, subCategoryId = null) {\n    // First try to get subcategory-specific form if subcategory is selected\n    if (subCategoryId) {\n      const subCategoryForm = this.loadFormConfig('subcategory', subCategoryId);\n      if (subCategoryForm) {\n        return subCategoryForm;\n      }\n    }\n\n    // Then try category-specific form\n    const categoryForm = this.loadFormConfig('category', categoryId);\n    if (categoryForm) {\n      return categoryForm;\n    }\n\n    // Return default form if no custom form found\n    return this.getDefaultFormConfig();\n  }\n\n  // Get default form configuration with all fields\n  getDefaultFormConfig() {\n    return {\n      id: 'default',\n      type: 'default',\n      name: 'Default Person Form',\n      description: 'Default form with all person fields',\n      fields: getAllPersonFields(),\n      sections: Object.keys(PersonFieldDefinitions),\n      settings: {\n        showSections: true,\n        allowConditionalFields: true,\n        validateOnChange: true\n      },\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString(),\n      version: 1\n    };\n  }\n\n  // Create form configuration from field selection\n  createFormConfigFromFields(selectedFields, settings = {}) {\n    const config = {\n      fields: selectedFields,\n      sections: this.groupFieldsBySections(selectedFields),\n      settings: {\n        showSections: true,\n        allowConditionalFields: true,\n        validateOnChange: true,\n        ...settings\n      }\n    };\n\n    return config;\n  }\n\n  // Group fields by their sections\n  groupFieldsBySections(fields) {\n    const sections = {};\n    \n    fields.forEach(field => {\n      const sectionKey = field.section;\n      if (!sections[sectionKey]) {\n        sections[sectionKey] = {\n          key: sectionKey,\n          title: PersonFieldDefinitions[sectionKey]?.title || sectionKey,\n          fields: []\n        };\n      }\n      sections[sectionKey].fields.push(field);\n    });\n\n    return Object.values(sections);\n  }\n\n  // Validate form configuration\n  validateFormConfig(config) {\n    const errors = [];\n\n    if (!config.name || config.name.trim() === '') {\n      errors.push('Form name is required');\n    }\n\n    if (!config.fields || config.fields.length === 0) {\n      errors.push('At least one field must be selected');\n    }\n\n    // Check if required fields are included\n    const requiredFields = ['name', 'mobileNumber', 'nature'];\n    const selectedFieldKeys = config.fields.map(f => f.key);\n    \n    requiredFields.forEach(requiredField => {\n      if (!selectedFieldKeys.includes(requiredField)) {\n        errors.push(`Required field '${requiredField}' must be included`);\n      }\n    });\n\n    return {\n      isValid: errors.length === 0,\n      errors\n    };\n  }\n\n  // Export form configuration\n  exportFormConfig(type, id) {\n    const config = this.loadFormConfig(type, id);\n    if (!config) {\n      throw new Error('Form configuration not found');\n    }\n\n    const exportData = {\n      ...config,\n      exportedAt: new Date().toISOString(),\n      exportVersion: '1.0'\n    };\n\n    return JSON.stringify(exportData, null, 2);\n  }\n\n  // Import form configuration\n  importFormConfig(configJson, type, id) {\n    try {\n      const config = JSON.parse(configJson);\n      \n      // Validate imported configuration\n      const validation = this.validateFormConfig(config);\n      if (!validation.isValid) {\n        throw new Error(`Invalid configuration: ${validation.errors.join(', ')}`);\n      }\n\n      // Update metadata for import\n      config.type = type;\n      config.associatedId = id;\n      config.importedAt = new Date().toISOString();\n      config.updatedAt = new Date().toISOString();\n\n      return this.saveFormConfig(type, id, config);\n    } catch (error) {\n      console.error('Error importing form configuration:', error);\n      throw new Error('Failed to import form configuration');\n    }\n  }\n\n  // Clone form configuration\n  cloneFormConfig(sourceType, sourceId, targetType, targetId, newName) {\n    const sourceConfig = this.loadFormConfig(sourceType, sourceId);\n    if (!sourceConfig) {\n      throw new Error('Source form configuration not found');\n    }\n\n    const clonedConfig = {\n      ...sourceConfig,\n      name: newName || `${sourceConfig.name} (Copy)`,\n      type: targetType,\n      associatedId: targetId,\n      clonedFrom: `${sourceType}_${sourceId}`,\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString()\n    };\n\n    return this.saveFormConfig(targetType, targetId, clonedConfig);\n  }\n\n  // Get form statistics\n  getFormStatistics() {\n    const allConfigs = this.getAllFormConfigs();\n    \n    return {\n      totalForms: allConfigs.length,\n      categoryForms: allConfigs.filter(c => c.type === 'category').length,\n      subCategoryForms: allConfigs.filter(c => c.type === 'subcategory').length,\n      averageFieldCount: allConfigs.length > 0 \n        ? Math.round(allConfigs.reduce((sum, c) => sum + (c.fields?.length || 0), 0) / allConfigs.length)\n        : 0,\n      mostUsedFields: this.getMostUsedFields(allConfigs),\n      recentlyModified: allConfigs\n        .sort((a, b) => new Date(b.updatedAt) - new Date(a.updatedAt))\n        .slice(0, 5)\n    };\n  }\n\n  // Get most used fields across all forms\n  getMostUsedFields(configs) {\n    const fieldUsage = {};\n    \n    configs.forEach(config => {\n      if (config.fields) {\n        config.fields.forEach(field => {\n          fieldUsage[field.key] = (fieldUsage[field.key] || 0) + 1;\n        });\n      }\n    });\n\n    return Object.entries(fieldUsage)\n      .sort(([,a], [,b]) => b - a)\n      .slice(0, 10)\n      .map(([key, count]) => ({ field: key, usage: count }));\n  }\n\n  // Get all saved form configurations\n  getAllFormConfigs() {\n    try {\n      const forms = [];\n      for (let i = 0; i < localStorage.length; i++) {\n        const key = localStorage.key(i);\n        if (key && key.startsWith(this.storagePrefix)) {\n          const stored = localStorage.getItem(key);\n          if (stored) {\n            const config = JSON.parse(stored);\n            forms.push({\n              key,\n              ...config,\n              summary: {\n                fieldCount: config.fields?.length || 0,\n                type: config.type,\n                associatedId: config.associatedId,\n                createdAt: config.createdAt,\n                updatedAt: config.updatedAt\n              }\n            });\n          }\n        }\n      }\n\n      // Sort by updated date (most recent first)\n      return forms.sort((a, b) => new Date(b.updatedAt) - new Date(a.updatedAt));\n    } catch (error) {\n      console.error('Error getting form configurations:', error);\n      return [];\n    }\n  }\n\n  // Delete a specific form configuration\n  deleteFormConfig(type, id) {\n    try {\n      const key = this.getStorageKey(type, id);\n      localStorage.removeItem(key);\n      return true;\n    } catch (error) {\n      console.error('Error deleting form configuration:', error);\n      return false;\n    }\n  }\n\n  // Clear all form configurations (for development/testing)\n  clearAllFormConfigs() {\n    try {\n      const keysToRemove = [];\n      for (let i = 0; i < localStorage.length; i++) {\n        const key = localStorage.key(i);\n        if (key && key.startsWith(this.storagePrefix)) {\n          keysToRemove.push(key);\n        }\n      }\n\n      keysToRemove.forEach(key => localStorage.removeItem(key));\n      return true;\n    } catch (error) {\n      console.error('Error clearing form configurations:', error);\n      return false;\n    }\n  }\n}\n\nexport default new FormConfigService();\n"], "mappings": "AAAA,SAASA,sBAAsB,EAAEC,kBAAkB,QAAQ,8BAA8B;AAEzF,MAAMC,iBAAiB,CAAC;EACtBC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,aAAa,GAAG,kBAAkB;IACvC,IAAI,CAACC,cAAc,GAAG,qBAAqB;EAC7C;;EAEA;EACAC,aAAaA,CAACC,IAAI,EAAEC,EAAE,EAAE;IACtB,OAAO,GAAG,IAAI,CAACJ,aAAa,GAAGG,IAAI,IAAIC,EAAE,EAAE;EAC7C;;EAEA;EACAC,cAAcA,CAACF,IAAI,EAAEC,EAAE,EAAEE,MAAM,EAAE;IAC/B,IAAI;MACF,MAAMC,GAAG,GAAG,IAAI,CAACL,aAAa,CAACC,IAAI,EAAEC,EAAE,CAAC;MACxC,MAAMI,UAAU,GAAG;QACjBJ,EAAE,EAAE,GAAGD,IAAI,IAAIC,EAAE,EAAE;QACnBD,IAAI;QAAE;QACNM,YAAY,EAAEL,EAAE;QAChBM,IAAI,EAAEJ,MAAM,CAACI,IAAI,IAAI,GAAGP,IAAI,IAAIC,EAAE,OAAO;QACzCO,WAAW,EAAEL,MAAM,CAACK,WAAW,IAAI,EAAE;QACrCC,MAAM,EAAEN,MAAM,CAACM,MAAM,IAAI,EAAE;QAC3BC,QAAQ,EAAEP,MAAM,CAACO,QAAQ,IAAI,EAAE;QAC/BC,QAAQ,EAAER,MAAM,CAACQ,QAAQ,IAAI,CAAC,CAAC;QAC/BC,SAAS,EAAET,MAAM,CAACS,SAAS,IAAI,CAAC,CAAC;QACjCC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;QACnCC,SAAS,EAAE,IAAIF,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;QACnCE,OAAO,EAAE,CAAC;QACV,GAAGd;MACL,CAAC;MAEDe,YAAY,CAACC,OAAO,CAACf,GAAG,EAAEgB,IAAI,CAACC,SAAS,CAAChB,UAAU,CAAC,CAAC;MACrD,OAAOA,UAAU;IACnB,CAAC,CAAC,OAAOiB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxD,MAAM,IAAIE,KAAK,CAAC,mCAAmC,CAAC;IACtD;EACF;;EAEA;EACAC,cAAcA,CAACzB,IAAI,EAAEC,EAAE,EAAE;IACvB,IAAI;MACF,MAAMG,GAAG,GAAG,IAAI,CAACL,aAAa,CAACC,IAAI,EAAEC,EAAE,CAAC;MACxC,MAAMyB,MAAM,GAAGR,YAAY,CAACS,OAAO,CAACvB,GAAG,CAAC;MAExC,IAAIsB,MAAM,EAAE;QACV,OAAON,IAAI,CAACQ,KAAK,CAACF,MAAM,CAAC;MAC3B;MAEA,OAAO,IAAI;IACb,CAAC,CAAC,OAAOJ,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MACzD,OAAO,IAAI;IACb;EACF;;EAEA;EACAO,gBAAgBA,CAAC7B,IAAI,EAAEC,EAAE,EAAE;IACzB,IAAI;MACF,MAAMG,GAAG,GAAG,IAAI,CAACL,aAAa,CAACC,IAAI,EAAEC,EAAE,CAAC;MACxCiB,YAAY,CAACY,UAAU,CAAC1B,GAAG,CAAC;MAC5B,OAAO,IAAI;IACb,CAAC,CAAC,OAAOkB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;MAC1D,OAAO,KAAK;IACd;EACF;;EAEA;EACAS,iBAAiBA,CAAA,EAAG;IAClB,IAAI;MACF,MAAMC,OAAO,GAAG,EAAE;MAClB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGf,YAAY,CAACgB,MAAM,EAAED,CAAC,EAAE,EAAE;QAC5C,MAAM7B,GAAG,GAAGc,YAAY,CAACd,GAAG,CAAC6B,CAAC,CAAC;QAC/B,IAAI7B,GAAG,IAAIA,GAAG,CAAC+B,UAAU,CAAC,IAAI,CAACtC,aAAa,CAAC,EAAE;UAC7C,MAAMM,MAAM,GAAGiB,IAAI,CAACQ,KAAK,CAACV,YAAY,CAACS,OAAO,CAACvB,GAAG,CAAC,CAAC;UACpD4B,OAAO,CAACI,IAAI,CAACjC,MAAM,CAAC;QACtB;MACF;MACA,OAAO6B,OAAO;IAChB,CAAC,CAAC,OAAOV,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;MAC9D,OAAO,EAAE;IACX;EACF;;EAEA;EACAe,kBAAkBA,CAACC,UAAU,EAAE;IAC7B,IAAI;MACF,MAAMlC,GAAG,GAAG,IAAI,CAACL,aAAa,CAAC,UAAU,EAAEuC,UAAU,CAAC;MACtD,OAAOpB,YAAY,CAACS,OAAO,CAACvB,GAAG,CAAC,KAAK,IAAI;IAC3C,CAAC,CAAC,OAAOkB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrD,OAAO,KAAK;IACd;EACF;;EAEA;EACAiB,qBAAqBA,CAACC,aAAa,EAAE;IACnC,IAAI;MACF,MAAMpC,GAAG,GAAG,IAAI,CAACL,aAAa,CAAC,aAAa,EAAEyC,aAAa,CAAC;MAC5D,OAAOtB,YAAY,CAACS,OAAO,CAACvB,GAAG,CAAC,KAAK,IAAI;IAC3C,CAAC,CAAC,OAAOkB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxD,OAAO,KAAK;IACd;EACF;;EAEA;EACAmB,yBAAyBA,CAACH,UAAU,EAAE;IACpC,IAAI;MACF,MAAMI,UAAU,GAAG,IAAI,CAACX,iBAAiB,CAAC,CAAC;MAC3C,OAAOW,UAAU,CACdC,MAAM,CAACxC,MAAM;QAAA,IAAAyC,iBAAA;QAAA,OAAIzC,MAAM,CAACH,IAAI,KAAK,aAAa,IAAI,EAAA4C,iBAAA,GAAAzC,MAAM,CAACS,SAAS,cAAAgC,iBAAA,uBAAhBA,iBAAA,CAAkBN,UAAU,MAAKA,UAAU;MAAA,EAAC,CAC9FO,GAAG,CAAC1C,MAAM;QAAA,IAAA2C,kBAAA;QAAA,QAAAA,kBAAA,GAAI3C,MAAM,CAACS,SAAS,cAAAkC,kBAAA,uBAAhBA,kBAAA,CAAkBN,aAAa;MAAA,EAAC,CAC9CG,MAAM,CAAC1C,EAAE,IAAIA,EAAE,KAAK,IAAI,IAAIA,EAAE,KAAK8C,SAAS,CAAC;IAClD,CAAC,CAAC,OAAOzB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;MAC/D,OAAO,EAAE;IACX;EACF;;EAEA;EACA0B,oBAAoBA,CAACV,UAAU,EAAEE,aAAa,GAAG,IAAI,EAAE;IACrD,MAAMS,MAAM,GAAG,EAAE;IAEjB,IAAIT,aAAa,EAAE;MACjB;MACA,IAAI,IAAI,CAACH,kBAAkB,CAACC,UAAU,CAAC,EAAE;QACvCW,MAAM,CAACb,IAAI,CAAC,0FAA0F,CAAC;MACzG;;MAEA;MACA,IAAI,IAAI,CAACG,qBAAqB,CAACC,aAAa,CAAC,EAAE;QAC7CS,MAAM,CAACb,IAAI,CAAC,sFAAsF,CAAC;MACrG;IACF,CAAC,MAAM,IAAIE,UAAU,EAAE;MACrB;MACA,MAAMY,sBAAsB,GAAG,IAAI,CAACT,yBAAyB,CAACH,UAAU,CAAC;MACzE,IAAIY,sBAAsB,CAAChB,MAAM,GAAG,CAAC,EAAE;QACrCe,MAAM,CAACb,IAAI,CAAC,mFAAmF,CAAC;MAClG;;MAEA;MACA,IAAI,IAAI,CAACC,kBAAkB,CAACC,UAAU,CAAC,EAAE;QACvCW,MAAM,CAACb,IAAI,CAAC,yCAAyC,CAAC;MACxD;IACF;IAEA,OAAO;MACLe,OAAO,EAAEF,MAAM,CAACf,MAAM,KAAK,CAAC;MAC5Be;IACF,CAAC;EACH;;EAEA;EACAG,yBAAyBA,CAACC,UAAU,EAAEf,UAAU,EAAEE,aAAa,GAAG,IAAI,EAAE;IACtE;IACA,IAAIA,aAAa,EAAE;MACjB,MAAMc,eAAe,GAAG,IAAI,CAAC7B,cAAc,CAAC,aAAa,EAAEe,aAAa,CAAC;MACzE,IAAIc,eAAe,EAAE;QACnB,OAAOA,eAAe;MACxB;IACF;;IAEA;IACA,MAAMC,YAAY,GAAG,IAAI,CAAC9B,cAAc,CAAC,UAAU,EAAEa,UAAU,CAAC;IAChE,IAAIiB,YAAY,EAAE;MAChB,OAAOA,YAAY;IACrB;;IAEA;IACA,OAAO,IAAI,CAACC,oBAAoB,CAAC,CAAC;EACpC;;EAEA;EACAA,oBAAoBA,CAAA,EAAG;IACrB,OAAO;MACLvD,EAAE,EAAE,SAAS;MACbD,IAAI,EAAE,SAAS;MACfO,IAAI,EAAE,qBAAqB;MAC3BC,WAAW,EAAE,qCAAqC;MAClDC,MAAM,EAAEf,kBAAkB,CAAC,CAAC;MAC5BgB,QAAQ,EAAE+C,MAAM,CAACC,IAAI,CAACjE,sBAAsB,CAAC;MAC7CkB,QAAQ,EAAE;QACRgD,YAAY,EAAE,IAAI;QAClBC,sBAAsB,EAAE,IAAI;QAC5BC,gBAAgB,EAAE;MACpB,CAAC;MACDhD,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MACnCC,SAAS,EAAE,IAAIF,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MACnCE,OAAO,EAAE;IACX,CAAC;EACH;;EAEA;EACA6C,0BAA0BA,CAACC,cAAc,EAAEpD,QAAQ,GAAG,CAAC,CAAC,EAAE;IACxD,MAAMR,MAAM,GAAG;MACbM,MAAM,EAAEsD,cAAc;MACtBrD,QAAQ,EAAE,IAAI,CAACsD,qBAAqB,CAACD,cAAc,CAAC;MACpDpD,QAAQ,EAAE;QACRgD,YAAY,EAAE,IAAI;QAClBC,sBAAsB,EAAE,IAAI;QAC5BC,gBAAgB,EAAE,IAAI;QACtB,GAAGlD;MACL;IACF,CAAC;IAED,OAAOR,MAAM;EACf;;EAEA;EACA6D,qBAAqBA,CAACvD,MAAM,EAAE;IAC5B,MAAMC,QAAQ,GAAG,CAAC,CAAC;IAEnBD,MAAM,CAACwD,OAAO,CAACC,KAAK,IAAI;MACtB,MAAMC,UAAU,GAAGD,KAAK,CAACE,OAAO;MAChC,IAAI,CAAC1D,QAAQ,CAACyD,UAAU,CAAC,EAAE;QAAA,IAAAE,qBAAA;QACzB3D,QAAQ,CAACyD,UAAU,CAAC,GAAG;UACrB/D,GAAG,EAAE+D,UAAU;UACfG,KAAK,EAAE,EAAAD,qBAAA,GAAA5E,sBAAsB,CAAC0E,UAAU,CAAC,cAAAE,qBAAA,uBAAlCA,qBAAA,CAAoCC,KAAK,KAAIH,UAAU;UAC9D1D,MAAM,EAAE;QACV,CAAC;MACH;MACAC,QAAQ,CAACyD,UAAU,CAAC,CAAC1D,MAAM,CAAC2B,IAAI,CAAC8B,KAAK,CAAC;IACzC,CAAC,CAAC;IAEF,OAAOT,MAAM,CAACc,MAAM,CAAC7D,QAAQ,CAAC;EAChC;;EAEA;EACA8D,kBAAkBA,CAACrE,MAAM,EAAE;IACzB,MAAM8C,MAAM,GAAG,EAAE;IAEjB,IAAI,CAAC9C,MAAM,CAACI,IAAI,IAAIJ,MAAM,CAACI,IAAI,CAACkE,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MAC7CxB,MAAM,CAACb,IAAI,CAAC,uBAAuB,CAAC;IACtC;IAEA,IAAI,CAACjC,MAAM,CAACM,MAAM,IAAIN,MAAM,CAACM,MAAM,CAACyB,MAAM,KAAK,CAAC,EAAE;MAChDe,MAAM,CAACb,IAAI,CAAC,qCAAqC,CAAC;IACpD;;IAEA;IACA,MAAMsC,cAAc,GAAG,CAAC,MAAM,EAAE,cAAc,EAAE,QAAQ,CAAC;IACzD,MAAMC,iBAAiB,GAAGxE,MAAM,CAACM,MAAM,CAACoC,GAAG,CAAC+B,CAAC,IAAIA,CAAC,CAACxE,GAAG,CAAC;IAEvDsE,cAAc,CAACT,OAAO,CAACY,aAAa,IAAI;MACtC,IAAI,CAACF,iBAAiB,CAACG,QAAQ,CAACD,aAAa,CAAC,EAAE;QAC9C5B,MAAM,CAACb,IAAI,CAAC,mBAAmByC,aAAa,oBAAoB,CAAC;MACnE;IACF,CAAC,CAAC;IAEF,OAAO;MACL1B,OAAO,EAAEF,MAAM,CAACf,MAAM,KAAK,CAAC;MAC5Be;IACF,CAAC;EACH;;EAEA;EACA8B,gBAAgBA,CAAC/E,IAAI,EAAEC,EAAE,EAAE;IACzB,MAAME,MAAM,GAAG,IAAI,CAACsB,cAAc,CAACzB,IAAI,EAAEC,EAAE,CAAC;IAC5C,IAAI,CAACE,MAAM,EAAE;MACX,MAAM,IAAIqB,KAAK,CAAC,8BAA8B,CAAC;IACjD;IAEA,MAAMwD,UAAU,GAAG;MACjB,GAAG7E,MAAM;MACT8E,UAAU,EAAE,IAAInE,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MACpCmE,aAAa,EAAE;IACjB,CAAC;IAED,OAAO9D,IAAI,CAACC,SAAS,CAAC2D,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC;EAC5C;;EAEA;EACAG,gBAAgBA,CAACC,UAAU,EAAEpF,IAAI,EAAEC,EAAE,EAAE;IACrC,IAAI;MACF,MAAME,MAAM,GAAGiB,IAAI,CAACQ,KAAK,CAACwD,UAAU,CAAC;;MAErC;MACA,MAAMC,UAAU,GAAG,IAAI,CAACb,kBAAkB,CAACrE,MAAM,CAAC;MAClD,IAAI,CAACkF,UAAU,CAAClC,OAAO,EAAE;QACvB,MAAM,IAAI3B,KAAK,CAAC,0BAA0B6D,UAAU,CAACpC,MAAM,CAACqC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;MAC3E;;MAEA;MACAnF,MAAM,CAACH,IAAI,GAAGA,IAAI;MAClBG,MAAM,CAACG,YAAY,GAAGL,EAAE;MACxBE,MAAM,CAACoF,UAAU,GAAG,IAAIzE,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MAC5CZ,MAAM,CAACa,SAAS,GAAG,IAAIF,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MAE3C,OAAO,IAAI,CAACb,cAAc,CAACF,IAAI,EAAEC,EAAE,EAAEE,MAAM,CAAC;IAC9C,CAAC,CAAC,OAAOmB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC3D,MAAM,IAAIE,KAAK,CAAC,qCAAqC,CAAC;IACxD;EACF;;EAEA;EACAgE,eAAeA,CAACC,UAAU,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,OAAO,EAAE;IACnE,MAAMC,YAAY,GAAG,IAAI,CAACrE,cAAc,CAACgE,UAAU,EAAEC,QAAQ,CAAC;IAC9D,IAAI,CAACI,YAAY,EAAE;MACjB,MAAM,IAAItE,KAAK,CAAC,qCAAqC,CAAC;IACxD;IAEA,MAAMuE,YAAY,GAAG;MACnB,GAAGD,YAAY;MACfvF,IAAI,EAAEsF,OAAO,IAAI,GAAGC,YAAY,CAACvF,IAAI,SAAS;MAC9CP,IAAI,EAAE2F,UAAU;MAChBrF,YAAY,EAAEsF,QAAQ;MACtBI,UAAU,EAAE,GAAGP,UAAU,IAAIC,QAAQ,EAAE;MACvC7E,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MACnCC,SAAS,EAAE,IAAIF,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;IACpC,CAAC;IAED,OAAO,IAAI,CAACb,cAAc,CAACyF,UAAU,EAAEC,QAAQ,EAAEG,YAAY,CAAC;EAChE;;EAEA;EACAE,iBAAiBA,CAAA,EAAG;IAClB,MAAMvD,UAAU,GAAG,IAAI,CAACX,iBAAiB,CAAC,CAAC;IAE3C,OAAO;MACLmE,UAAU,EAAExD,UAAU,CAACR,MAAM;MAC7BiE,aAAa,EAAEzD,UAAU,CAACC,MAAM,CAACyD,CAAC,IAAIA,CAAC,CAACpG,IAAI,KAAK,UAAU,CAAC,CAACkC,MAAM;MACnEmE,gBAAgB,EAAE3D,UAAU,CAACC,MAAM,CAACyD,CAAC,IAAIA,CAAC,CAACpG,IAAI,KAAK,aAAa,CAAC,CAACkC,MAAM;MACzEoE,iBAAiB,EAAE5D,UAAU,CAACR,MAAM,GAAG,CAAC,GACpCqE,IAAI,CAACC,KAAK,CAAC9D,UAAU,CAAC+D,MAAM,CAAC,CAACC,GAAG,EAAEN,CAAC;QAAA,IAAAO,SAAA;QAAA,OAAKD,GAAG,IAAI,EAAAC,SAAA,GAAAP,CAAC,CAAC3F,MAAM,cAAAkG,SAAA,uBAARA,SAAA,CAAUzE,MAAM,KAAI,CAAC,CAAC;MAAA,GAAE,CAAC,CAAC,GAAGQ,UAAU,CAACR,MAAM,CAAC,GAC/F,CAAC;MACL0E,cAAc,EAAE,IAAI,CAACC,iBAAiB,CAACnE,UAAU,CAAC;MAClDoE,gBAAgB,EAAEpE,UAAU,CACzBqE,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAInG,IAAI,CAACmG,CAAC,CAACjG,SAAS,CAAC,GAAG,IAAIF,IAAI,CAACkG,CAAC,CAAChG,SAAS,CAAC,CAAC,CAC7DkG,KAAK,CAAC,CAAC,EAAE,CAAC;IACf,CAAC;EACH;;EAEA;EACAL,iBAAiBA,CAAC7E,OAAO,EAAE;IACzB,MAAMmF,UAAU,GAAG,CAAC,CAAC;IAErBnF,OAAO,CAACiC,OAAO,CAAC9D,MAAM,IAAI;MACxB,IAAIA,MAAM,CAACM,MAAM,EAAE;QACjBN,MAAM,CAACM,MAAM,CAACwD,OAAO,CAACC,KAAK,IAAI;UAC7BiD,UAAU,CAACjD,KAAK,CAAC9D,GAAG,CAAC,GAAG,CAAC+G,UAAU,CAACjD,KAAK,CAAC9D,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC;QAC1D,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;IAEF,OAAOqD,MAAM,CAAC2D,OAAO,CAACD,UAAU,CAAC,CAC9BJ,IAAI,CAAC,CAAC,GAAEC,CAAC,CAAC,EAAE,GAAEC,CAAC,CAAC,KAAKA,CAAC,GAAGD,CAAC,CAAC,CAC3BE,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CACZrE,GAAG,CAAC,CAAC,CAACzC,GAAG,EAAEiH,KAAK,CAAC,MAAM;MAAEnD,KAAK,EAAE9D,GAAG;MAAEkH,KAAK,EAAED;IAAM,CAAC,CAAC,CAAC;EAC1D;;EAEA;EACAtF,iBAAiBA,CAAA,EAAG;IAClB,IAAI;MACF,MAAMwF,KAAK,GAAG,EAAE;MAChB,KAAK,IAAItF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGf,YAAY,CAACgB,MAAM,EAAED,CAAC,EAAE,EAAE;QAC5C,MAAM7B,GAAG,GAAGc,YAAY,CAACd,GAAG,CAAC6B,CAAC,CAAC;QAC/B,IAAI7B,GAAG,IAAIA,GAAG,CAAC+B,UAAU,CAAC,IAAI,CAACtC,aAAa,CAAC,EAAE;UAC7C,MAAM6B,MAAM,GAAGR,YAAY,CAACS,OAAO,CAACvB,GAAG,CAAC;UACxC,IAAIsB,MAAM,EAAE;YAAA,IAAA8F,cAAA;YACV,MAAMrH,MAAM,GAAGiB,IAAI,CAACQ,KAAK,CAACF,MAAM,CAAC;YACjC6F,KAAK,CAACnF,IAAI,CAAC;cACThC,GAAG;cACH,GAAGD,MAAM;cACTsH,OAAO,EAAE;gBACPC,UAAU,EAAE,EAAAF,cAAA,GAAArH,MAAM,CAACM,MAAM,cAAA+G,cAAA,uBAAbA,cAAA,CAAetF,MAAM,KAAI,CAAC;gBACtClC,IAAI,EAAEG,MAAM,CAACH,IAAI;gBACjBM,YAAY,EAAEH,MAAM,CAACG,YAAY;gBACjCO,SAAS,EAAEV,MAAM,CAACU,SAAS;gBAC3BG,SAAS,EAAEb,MAAM,CAACa;cACpB;YACF,CAAC,CAAC;UACJ;QACF;MACF;;MAEA;MACA,OAAOuG,KAAK,CAACR,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAInG,IAAI,CAACmG,CAAC,CAACjG,SAAS,CAAC,GAAG,IAAIF,IAAI,CAACkG,CAAC,CAAChG,SAAS,CAAC,CAAC;IAC5E,CAAC,CAAC,OAAOM,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;MAC1D,OAAO,EAAE;IACX;EACF;;EAEA;EACAO,gBAAgBA,CAAC7B,IAAI,EAAEC,EAAE,EAAE;IACzB,IAAI;MACF,MAAMG,GAAG,GAAG,IAAI,CAACL,aAAa,CAACC,IAAI,EAAEC,EAAE,CAAC;MACxCiB,YAAY,CAACY,UAAU,CAAC1B,GAAG,CAAC;MAC5B,OAAO,IAAI;IACb,CAAC,CAAC,OAAOkB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;MAC1D,OAAO,KAAK;IACd;EACF;;EAEA;EACAqG,mBAAmBA,CAAA,EAAG;IACpB,IAAI;MACF,MAAMC,YAAY,GAAG,EAAE;MACvB,KAAK,IAAI3F,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGf,YAAY,CAACgB,MAAM,EAAED,CAAC,EAAE,EAAE;QAC5C,MAAM7B,GAAG,GAAGc,YAAY,CAACd,GAAG,CAAC6B,CAAC,CAAC;QAC/B,IAAI7B,GAAG,IAAIA,GAAG,CAAC+B,UAAU,CAAC,IAAI,CAACtC,aAAa,CAAC,EAAE;UAC7C+H,YAAY,CAACxF,IAAI,CAAChC,GAAG,CAAC;QACxB;MACF;MAEAwH,YAAY,CAAC3D,OAAO,CAAC7D,GAAG,IAAIc,YAAY,CAACY,UAAU,CAAC1B,GAAG,CAAC,CAAC;MACzD,OAAO,IAAI;IACb,CAAC,CAAC,OAAOkB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC3D,OAAO,KAAK;IACd;EACF;AACF;AAEA,eAAe,IAAI3B,iBAAiB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}