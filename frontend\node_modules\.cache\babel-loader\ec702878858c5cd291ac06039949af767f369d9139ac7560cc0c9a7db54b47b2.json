{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Advanced\\\\Advanced CRM filed all use\\\\frontend\\\\src\\\\components\\\\import\\\\ImportProgress.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport './ImportProgress.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ImportProgress = ({\n  importJob,\n  onCancel,\n  error\n}) => {\n  _s();\n  var _importJob$processedR, _importJob$totalRows, _importJob$successful, _importJob$failedRows, _importJob$skippedRow, _importJob$updatedRow;\n  const [progress, setProgress] = useState(0);\n  const [timeElapsed, setTimeElapsed] = useState(0);\n  const [estimatedTimeRemaining, setEstimatedTimeRemaining] = useState(null);\n  useEffect(() => {\n    if (importJob) {\n      const progressPercentage = importJob.totalRows > 0 ? Math.round(importJob.processedRows / importJob.totalRows * 100) : 0;\n      setProgress(progressPercentage);\n    }\n  }, [importJob]);\n  useEffect(() => {\n    const timer = setInterval(() => {\n      if (importJob !== null && importJob !== void 0 && importJob.startedAt) {\n        const elapsed = Math.floor((Date.now() - new Date(importJob.startedAt).getTime()) / 1000);\n        setTimeElapsed(elapsed);\n\n        // Calculate estimated time remaining\n        if (importJob.processedRows > 0 && importJob.totalRows > 0) {\n          const rate = importJob.processedRows / elapsed;\n          const remaining = (importJob.totalRows - importJob.processedRows) / rate;\n          setEstimatedTimeRemaining(Math.ceil(remaining));\n        }\n      }\n    }, 1000);\n    return () => clearInterval(timer);\n  }, [importJob]);\n  const formatTime = seconds => {\n    if (seconds < 60) return `${seconds}s`;\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = seconds % 60;\n    return `${minutes}m ${remainingSeconds}s`;\n  };\n\n  // Convert status to string (handles both enum numbers and strings)\n  const getStatusString = status => {\n    if (typeof status === 'string') {\n      return status;\n    }\n\n    // Handle enum values\n    const statusMap = {\n      1: 'Pending',\n      2: 'Processing',\n      3: 'Completed',\n      4: 'Failed',\n      5: 'Cancelled'\n    };\n    return statusMap[status] || 'Unknown';\n  };\n  const getStatusColor = status => {\n    const statusString = getStatusString(status);\n    switch (statusString) {\n      case 'Processing':\n        return '#007bff';\n      case 'Completed':\n        return '#28a745';\n      case 'Failed':\n        return '#dc3545';\n      case 'Cancelled':\n        return '#6c757d';\n      default:\n        return '#6c757d';\n    }\n  };\n  const getStatusIcon = status => {\n    const statusString = getStatusString(status);\n    switch (statusString) {\n      case 'Processing':\n        return '⏳';\n      case 'Completed':\n        return '✅';\n      case 'Failed':\n        return '❌';\n      case 'Cancelled':\n        return '⏹️';\n      default:\n        return '⏳';\n    }\n  };\n  if (!importJob) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"import-progress\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"progress-loading\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading-spinner\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Starting import...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 88,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 86,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 85,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"import-progress\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"progress-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"Import Progress\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 97,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"job-info\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"job-id\",\n          children: [\"Job ID: \", importJob.jobId]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 99,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"job-status\",\n          style: {\n            color: getStatusColor(importJob.status)\n          },\n          children: [getStatusIcon(importJob.status), \" \", importJob.status]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 100,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 98,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 96,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"progress-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"progress-bar-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"progress-bar\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"progress-fill\",\n            style: {\n              width: `${progress}%`,\n              backgroundColor: getStatusColor(importJob.status)\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 113,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 112,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"progress-text\",\n          children: [progress, \"% Complete\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 111,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"progress-details\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"detail-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"detail-label\",\n            children: \"Processed:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 128,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"detail-value\",\n            children: [((_importJob$processedR = importJob.processedRows) === null || _importJob$processedR === void 0 ? void 0 : _importJob$processedR.toLocaleString()) || 0, \" / \", ((_importJob$totalRows = importJob.totalRows) === null || _importJob$totalRows === void 0 ? void 0 : _importJob$totalRows.toLocaleString()) || 0]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 127,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"detail-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"detail-label\",\n            children: \"Time Elapsed:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 134,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"detail-value\",\n            children: formatTime(timeElapsed)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 135,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 133,\n          columnNumber: 11\n        }, this), estimatedTimeRemaining && importJob.status === 'Processing' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"detail-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"detail-label\",\n            children: \"Est. Remaining:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 139,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"detail-value\",\n            children: formatTime(estimatedTimeRemaining)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 140,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 138,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 126,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 110,\n      columnNumber: 7\n    }, this), importJob.currentOperation && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"current-operation\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"operation-icon\",\n        children: \"\\uD83D\\uDD04\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 149,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"operation-text\",\n        children: importJob.currentOperation\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 150,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 148,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"progress-stats\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stats-grid\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card success\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-icon\",\n            children: \"\\u2705\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 158,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat-value\",\n              children: ((_importJob$successful = importJob.successfulRows) === null || _importJob$successful === void 0 ? void 0 : _importJob$successful.toLocaleString()) || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat-label\",\n              children: \"Successful\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 161,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card error\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-icon\",\n            children: \"\\u274C\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 166,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat-value\",\n              children: ((_importJob$failedRows = importJob.failedRows) === null || _importJob$failedRows === void 0 ? void 0 : _importJob$failedRows.toLocaleString()) || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 168,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat-label\",\n              children: \"Failed\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 169,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 165,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card warning\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-icon\",\n            children: \"\\u23ED\\uFE0F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat-value\",\n              children: ((_importJob$skippedRow = importJob.skippedRows) === null || _importJob$skippedRow === void 0 ? void 0 : _importJob$skippedRow.toLocaleString()) || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat-label\",\n              children: \"Skipped\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 177,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 173,\n          columnNumber: 11\n        }, this), importJob.updatedRows > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card info\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-icon\",\n            children: \"\\uD83D\\uDD04\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 183,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat-value\",\n              children: ((_importJob$updatedRow = importJob.updatedRows) === null || _importJob$updatedRow === void 0 ? void 0 : _importJob$updatedRow.toLocaleString()) || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 185,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat-label\",\n              children: \"Updated\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 186,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 184,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 182,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 156,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 155,\n      columnNumber: 7\n    }, this), importJob.errors && importJob.errors.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"recent-errors\",\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        children: \"Recent Errors\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 196,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"errors-list\",\n        children: [importJob.errors.slice(0, 5).map((error, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"error-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"error-row\",\n            children: [\"Row \", error.rowNumber]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"error-field\",\n            children: error.fieldName\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"error-message\",\n            children: error.errorMessage\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 202,\n            columnNumber: 17\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 199,\n          columnNumber: 15\n        }, this)), importJob.errors.length > 5 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"more-errors\",\n          children: [\"+\", importJob.errors.length - 5, \" more errors\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 197,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 195,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"processing-log\",\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        children: \"Processing Log\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 216,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"log-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"log-entry\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"log-time\",\n            children: new Date(importJob.startedAt).toLocaleTimeString()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 219,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"log-message\",\n            children: \"Import started\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 220,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 218,\n          columnNumber: 11\n        }, this), importJob.status === 'Processing' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"log-entry current\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"log-time\",\n            children: new Date().toLocaleTimeString()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 224,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"log-message\",\n            children: importJob.currentOperation || 'Processing...'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 225,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 223,\n          columnNumber: 13\n        }, this), importJob.completedAt && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"log-entry\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"log-time\",\n            children: new Date(importJob.completedAt).toLocaleTimeString()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 230,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"log-message\",\n            children: [\"Import \", importJob.status.toLowerCase()]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 229,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 217,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 215,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"progress-actions\",\n      children: [importJob.status === 'Processing' && /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: onCancel,\n        className: \"btn btn-outline\",\n        children: \"Cancel Import\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 240,\n        columnNumber: 11\n      }, this), (importJob.status === 'Completed' || importJob.status === 'Failed' || importJob.status === 'Cancelled') && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"completion-message\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: `completion-icon ${importJob.status.toLowerCase()}`,\n          children: getStatusIcon(importJob.status)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 247,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"completion-text\",\n          children: [\"Import \", importJob.status.toLowerCase(), importJob.status === 'Completed' && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"completion-details\",\n            children: [\"Successfully processed \", importJob.successfulRows, \" records\", importJob.failedRows > 0 && ` with ${importJob.failedRows} errors`]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 253,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 250,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 246,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 238,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"progress-error\",\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"error-icon\",\n        children: \"\\u26A0\\uFE0F\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 265,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"error-message\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 266,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 264,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 95,\n    columnNumber: 5\n  }, this);\n};\n_s(ImportProgress, \"xVUnoFKWotreKC3vltRi2XfirzA=\");\n_c = ImportProgress;\nexport default ImportProgress;\nvar _c;\n$RefreshReg$(_c, \"ImportProgress\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "jsxDEV", "_jsxDEV", "ImportProgress", "importJob", "onCancel", "error", "_s", "_importJob$processedR", "_importJob$totalRows", "_importJob$successful", "_importJob$failedRows", "_importJob$skippedRow", "_importJob$updatedRow", "progress", "setProgress", "timeElapsed", "setTimeElapsed", "estimatedTimeRemaining", "setEstimatedTimeRemaining", "progressPercentage", "totalRows", "Math", "round", "processedRows", "timer", "setInterval", "startedAt", "elapsed", "floor", "Date", "now", "getTime", "rate", "remaining", "ceil", "clearInterval", "formatTime", "seconds", "minutes", "remainingSeconds", "getStatusString", "status", "statusMap", "getStatusColor", "statusString", "getStatusIcon", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "jobId", "style", "color", "width", "backgroundColor", "toLocaleString", "currentOperation", "successfulRows", "failedRows", "skippedRows", "updatedRows", "errors", "length", "slice", "map", "index", "rowNumber", "fieldName", "errorMessage", "toLocaleTimeString", "completedAt", "toLowerCase", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/Advanced/Advanced CRM filed all use/frontend/src/components/import/ImportProgress.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport './ImportProgress.css';\n\nconst ImportProgress = ({ importJob, onCancel, error }) => {\n  const [progress, setProgress] = useState(0);\n  const [timeElapsed, setTimeElapsed] = useState(0);\n  const [estimatedTimeRemaining, setEstimatedTimeRemaining] = useState(null);\n\n  useEffect(() => {\n    if (importJob) {\n      const progressPercentage = importJob.totalRows > 0 \n        ? Math.round((importJob.processedRows / importJob.totalRows) * 100)\n        : 0;\n      setProgress(progressPercentage);\n    }\n  }, [importJob]);\n\n  useEffect(() => {\n    const timer = setInterval(() => {\n      if (importJob?.startedAt) {\n        const elapsed = Math.floor((Date.now() - new Date(importJob.startedAt).getTime()) / 1000);\n        setTimeElapsed(elapsed);\n\n        // Calculate estimated time remaining\n        if (importJob.processedRows > 0 && importJob.totalRows > 0) {\n          const rate = importJob.processedRows / elapsed;\n          const remaining = (importJob.totalRows - importJob.processedRows) / rate;\n          setEstimatedTimeRemaining(Math.ceil(remaining));\n        }\n      }\n    }, 1000);\n\n    return () => clearInterval(timer);\n  }, [importJob]);\n\n  const formatTime = (seconds) => {\n    if (seconds < 60) return `${seconds}s`;\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = seconds % 60;\n    return `${minutes}m ${remainingSeconds}s`;\n  };\n\n  // Convert status to string (handles both enum numbers and strings)\n  const getStatusString = (status) => {\n    if (typeof status === 'string') {\n      return status;\n    }\n\n    // Handle enum values\n    const statusMap = {\n      1: 'Pending',\n      2: 'Processing',\n      3: 'Completed',\n      4: 'Failed',\n      5: 'Cancelled'\n    };\n\n    return statusMap[status] || 'Unknown';\n  };\n\n  const getStatusColor = (status) => {\n    const statusString = getStatusString(status);\n    switch (statusString) {\n      case 'Processing': return '#007bff';\n      case 'Completed': return '#28a745';\n      case 'Failed': return '#dc3545';\n      case 'Cancelled': return '#6c757d';\n      default: return '#6c757d';\n    }\n  };\n\n  const getStatusIcon = (status) => {\n    const statusString = getStatusString(status);\n    switch (statusString) {\n      case 'Processing': return '⏳';\n      case 'Completed': return '✅';\n      case 'Failed': return '❌';\n      case 'Cancelled': return '⏹️';\n      default: return '⏳';\n    }\n  };\n\n  if (!importJob) {\n    return (\n      <div className=\"import-progress\">\n        <div className=\"progress-loading\">\n          <div className=\"loading-spinner\"></div>\n          <p>Starting import...</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"import-progress\">\n      <div className=\"progress-header\">\n        <h3>Import Progress</h3>\n        <div className=\"job-info\">\n          <span className=\"job-id\">Job ID: {importJob.jobId}</span>\n          <span \n            className=\"job-status\"\n            style={{ color: getStatusColor(importJob.status) }}\n          >\n            {getStatusIcon(importJob.status)} {importJob.status}\n          </span>\n        </div>\n      </div>\n\n      {/* Progress Bar */}\n      <div className=\"progress-section\">\n        <div className=\"progress-bar-container\">\n          <div className=\"progress-bar\">\n            <div \n              className=\"progress-fill\"\n              style={{ \n                width: `${progress}%`,\n                backgroundColor: getStatusColor(importJob.status)\n              }}\n            ></div>\n          </div>\n          <div className=\"progress-text\">\n            {progress}% Complete\n          </div>\n        </div>\n\n        <div className=\"progress-details\">\n          <div className=\"detail-item\">\n            <span className=\"detail-label\">Processed:</span>\n            <span className=\"detail-value\">\n              {importJob.processedRows?.toLocaleString() || 0} / {importJob.totalRows?.toLocaleString() || 0}\n            </span>\n          </div>\n          <div className=\"detail-item\">\n            <span className=\"detail-label\">Time Elapsed:</span>\n            <span className=\"detail-value\">{formatTime(timeElapsed)}</span>\n          </div>\n          {estimatedTimeRemaining && importJob.status === 'Processing' && (\n            <div className=\"detail-item\">\n              <span className=\"detail-label\">Est. Remaining:</span>\n              <span className=\"detail-value\">{formatTime(estimatedTimeRemaining)}</span>\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Current Operation */}\n      {importJob.currentOperation && (\n        <div className=\"current-operation\">\n          <div className=\"operation-icon\">🔄</div>\n          <div className=\"operation-text\">{importJob.currentOperation}</div>\n        </div>\n      )}\n\n      {/* Statistics */}\n      <div className=\"progress-stats\">\n        <div className=\"stats-grid\">\n          <div className=\"stat-card success\">\n            <div className=\"stat-icon\">✅</div>\n            <div className=\"stat-content\">\n              <div className=\"stat-value\">{importJob.successfulRows?.toLocaleString() || 0}</div>\n              <div className=\"stat-label\">Successful</div>\n            </div>\n          </div>\n          \n          <div className=\"stat-card error\">\n            <div className=\"stat-icon\">❌</div>\n            <div className=\"stat-content\">\n              <div className=\"stat-value\">{importJob.failedRows?.toLocaleString() || 0}</div>\n              <div className=\"stat-label\">Failed</div>\n            </div>\n          </div>\n          \n          <div className=\"stat-card warning\">\n            <div className=\"stat-icon\">⏭️</div>\n            <div className=\"stat-content\">\n              <div className=\"stat-value\">{importJob.skippedRows?.toLocaleString() || 0}</div>\n              <div className=\"stat-label\">Skipped</div>\n            </div>\n          </div>\n          \n          {importJob.updatedRows > 0 && (\n            <div className=\"stat-card info\">\n              <div className=\"stat-icon\">🔄</div>\n              <div className=\"stat-content\">\n                <div className=\"stat-value\">{importJob.updatedRows?.toLocaleString() || 0}</div>\n                <div className=\"stat-label\">Updated</div>\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Recent Errors */}\n      {importJob.errors && importJob.errors.length > 0 && (\n        <div className=\"recent-errors\">\n          <h4>Recent Errors</h4>\n          <div className=\"errors-list\">\n            {importJob.errors.slice(0, 5).map((error, index) => (\n              <div key={index} className=\"error-item\">\n                <div className=\"error-row\">Row {error.rowNumber}</div>\n                <div className=\"error-field\">{error.fieldName}</div>\n                <div className=\"error-message\">{error.errorMessage}</div>\n              </div>\n            ))}\n            {importJob.errors.length > 5 && (\n              <div className=\"more-errors\">\n                +{importJob.errors.length - 5} more errors\n              </div>\n            )}\n          </div>\n        </div>\n      )}\n\n      {/* Processing Log */}\n      <div className=\"processing-log\">\n        <h4>Processing Log</h4>\n        <div className=\"log-container\">\n          <div className=\"log-entry\">\n            <span className=\"log-time\">{new Date(importJob.startedAt).toLocaleTimeString()}</span>\n            <span className=\"log-message\">Import started</span>\n          </div>\n          {importJob.status === 'Processing' && (\n            <div className=\"log-entry current\">\n              <span className=\"log-time\">{new Date().toLocaleTimeString()}</span>\n              <span className=\"log-message\">{importJob.currentOperation || 'Processing...'}</span>\n            </div>\n          )}\n          {importJob.completedAt && (\n            <div className=\"log-entry\">\n              <span className=\"log-time\">{new Date(importJob.completedAt).toLocaleTimeString()}</span>\n              <span className=\"log-message\">Import {importJob.status.toLowerCase()}</span>\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Actions */}\n      <div className=\"progress-actions\">\n        {importJob.status === 'Processing' && (\n          <button onClick={onCancel} className=\"btn btn-outline\">\n            Cancel Import\n          </button>\n        )}\n        \n        {(importJob.status === 'Completed' || importJob.status === 'Failed' || importJob.status === 'Cancelled') && (\n          <div className=\"completion-message\">\n            <div className={`completion-icon ${importJob.status.toLowerCase()}`}>\n              {getStatusIcon(importJob.status)}\n            </div>\n            <div className=\"completion-text\">\n              Import {importJob.status.toLowerCase()}\n              {importJob.status === 'Completed' && (\n                <div className=\"completion-details\">\n                  Successfully processed {importJob.successfulRows} records\n                  {importJob.failedRows > 0 && ` with ${importJob.failedRows} errors`}\n                </div>\n              )}\n            </div>\n          </div>\n        )}\n      </div>\n\n      {error && (\n        <div className=\"progress-error\">\n          <span className=\"error-icon\">⚠️</span>\n          <span className=\"error-message\">{error}</span>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default ImportProgress;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAO,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9B,MAAMC,cAAc,GAAGA,CAAC;EAAEC,SAAS;EAAEC,QAAQ;EAAEC;AAAM,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,oBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA;EACzD,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGhB,QAAQ,CAAC,CAAC,CAAC;EAC3C,MAAM,CAACiB,WAAW,EAAEC,cAAc,CAAC,GAAGlB,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACmB,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGpB,QAAQ,CAAC,IAAI,CAAC;EAE1EC,SAAS,CAAC,MAAM;IACd,IAAII,SAAS,EAAE;MACb,MAAMgB,kBAAkB,GAAGhB,SAAS,CAACiB,SAAS,GAAG,CAAC,GAC9CC,IAAI,CAACC,KAAK,CAAEnB,SAAS,CAACoB,aAAa,GAAGpB,SAAS,CAACiB,SAAS,GAAI,GAAG,CAAC,GACjE,CAAC;MACLN,WAAW,CAACK,kBAAkB,CAAC;IACjC;EACF,CAAC,EAAE,CAAChB,SAAS,CAAC,CAAC;EAEfJ,SAAS,CAAC,MAAM;IACd,MAAMyB,KAAK,GAAGC,WAAW,CAAC,MAAM;MAC9B,IAAItB,SAAS,aAATA,SAAS,eAATA,SAAS,CAAEuB,SAAS,EAAE;QACxB,MAAMC,OAAO,GAAGN,IAAI,CAACO,KAAK,CAAC,CAACC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAID,IAAI,CAAC1B,SAAS,CAACuB,SAAS,CAAC,CAACK,OAAO,CAAC,CAAC,IAAI,IAAI,CAAC;QACzFf,cAAc,CAACW,OAAO,CAAC;;QAEvB;QACA,IAAIxB,SAAS,CAACoB,aAAa,GAAG,CAAC,IAAIpB,SAAS,CAACiB,SAAS,GAAG,CAAC,EAAE;UAC1D,MAAMY,IAAI,GAAG7B,SAAS,CAACoB,aAAa,GAAGI,OAAO;UAC9C,MAAMM,SAAS,GAAG,CAAC9B,SAAS,CAACiB,SAAS,GAAGjB,SAAS,CAACoB,aAAa,IAAIS,IAAI;UACxEd,yBAAyB,CAACG,IAAI,CAACa,IAAI,CAACD,SAAS,CAAC,CAAC;QACjD;MACF;IACF,CAAC,EAAE,IAAI,CAAC;IAER,OAAO,MAAME,aAAa,CAACX,KAAK,CAAC;EACnC,CAAC,EAAE,CAACrB,SAAS,CAAC,CAAC;EAEf,MAAMiC,UAAU,GAAIC,OAAO,IAAK;IAC9B,IAAIA,OAAO,GAAG,EAAE,EAAE,OAAO,GAAGA,OAAO,GAAG;IACtC,MAAMC,OAAO,GAAGjB,IAAI,CAACO,KAAK,CAACS,OAAO,GAAG,EAAE,CAAC;IACxC,MAAME,gBAAgB,GAAGF,OAAO,GAAG,EAAE;IACrC,OAAO,GAAGC,OAAO,KAAKC,gBAAgB,GAAG;EAC3C,CAAC;;EAED;EACA,MAAMC,eAAe,GAAIC,MAAM,IAAK;IAClC,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;MAC9B,OAAOA,MAAM;IACf;;IAEA;IACA,MAAMC,SAAS,GAAG;MAChB,CAAC,EAAE,SAAS;MACZ,CAAC,EAAE,YAAY;MACf,CAAC,EAAE,WAAW;MACd,CAAC,EAAE,QAAQ;MACX,CAAC,EAAE;IACL,CAAC;IAED,OAAOA,SAAS,CAACD,MAAM,CAAC,IAAI,SAAS;EACvC,CAAC;EAED,MAAME,cAAc,GAAIF,MAAM,IAAK;IACjC,MAAMG,YAAY,GAAGJ,eAAe,CAACC,MAAM,CAAC;IAC5C,QAAQG,YAAY;MAClB,KAAK,YAAY;QAAE,OAAO,SAAS;MACnC,KAAK,WAAW;QAAE,OAAO,SAAS;MAClC,KAAK,QAAQ;QAAE,OAAO,SAAS;MAC/B,KAAK,WAAW;QAAE,OAAO,SAAS;MAClC;QAAS,OAAO,SAAS;IAC3B;EACF,CAAC;EAED,MAAMC,aAAa,GAAIJ,MAAM,IAAK;IAChC,MAAMG,YAAY,GAAGJ,eAAe,CAACC,MAAM,CAAC;IAC5C,QAAQG,YAAY;MAClB,KAAK,YAAY;QAAE,OAAO,GAAG;MAC7B,KAAK,WAAW;QAAE,OAAO,GAAG;MAC5B,KAAK,QAAQ;QAAE,OAAO,GAAG;MACzB,KAAK,WAAW;QAAE,OAAO,IAAI;MAC7B;QAAS,OAAO,GAAG;IACrB;EACF,CAAC;EAED,IAAI,CAACzC,SAAS,EAAE;IACd,oBACEF,OAAA;MAAK6C,SAAS,EAAC,iBAAiB;MAAAC,QAAA,eAC9B9C,OAAA;QAAK6C,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/B9C,OAAA;UAAK6C,SAAS,EAAC;QAAiB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACvClD,OAAA;UAAA8C,QAAA,EAAG;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACElD,OAAA;IAAK6C,SAAS,EAAC,iBAAiB;IAAAC,QAAA,gBAC9B9C,OAAA;MAAK6C,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9B9C,OAAA;QAAA8C,QAAA,EAAI;MAAe;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACxBlD,OAAA;QAAK6C,SAAS,EAAC,UAAU;QAAAC,QAAA,gBACvB9C,OAAA;UAAM6C,SAAS,EAAC,QAAQ;UAAAC,QAAA,GAAC,UAAQ,EAAC5C,SAAS,CAACiD,KAAK;QAAA;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACzDlD,OAAA;UACE6C,SAAS,EAAC,YAAY;UACtBO,KAAK,EAAE;YAAEC,KAAK,EAAEX,cAAc,CAACxC,SAAS,CAACsC,MAAM;UAAE,CAAE;UAAAM,QAAA,GAElDF,aAAa,CAAC1C,SAAS,CAACsC,MAAM,CAAC,EAAC,GAAC,EAACtC,SAAS,CAACsC,MAAM;QAAA;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNlD,OAAA;MAAK6C,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/B9C,OAAA;QAAK6C,SAAS,EAAC,wBAAwB;QAAAC,QAAA,gBACrC9C,OAAA;UAAK6C,SAAS,EAAC,cAAc;UAAAC,QAAA,eAC3B9C,OAAA;YACE6C,SAAS,EAAC,eAAe;YACzBO,KAAK,EAAE;cACLE,KAAK,EAAE,GAAG1C,QAAQ,GAAG;cACrB2C,eAAe,EAAEb,cAAc,CAACxC,SAAS,CAACsC,MAAM;YAClD;UAAE;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACNlD,OAAA;UAAK6C,SAAS,EAAC,eAAe;UAAAC,QAAA,GAC3BlC,QAAQ,EAAC,YACZ;QAAA;UAAAmC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENlD,OAAA;QAAK6C,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/B9C,OAAA;UAAK6C,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1B9C,OAAA;YAAM6C,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAC;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAChDlD,OAAA;YAAM6C,SAAS,EAAC,cAAc;YAAAC,QAAA,GAC3B,EAAAxC,qBAAA,GAAAJ,SAAS,CAACoB,aAAa,cAAAhB,qBAAA,uBAAvBA,qBAAA,CAAyBkD,cAAc,CAAC,CAAC,KAAI,CAAC,EAAC,KAAG,EAAC,EAAAjD,oBAAA,GAAAL,SAAS,CAACiB,SAAS,cAAAZ,oBAAA,uBAAnBA,oBAAA,CAAqBiD,cAAc,CAAC,CAAC,KAAI,CAAC;UAAA;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1F,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACNlD,OAAA;UAAK6C,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1B9C,OAAA;YAAM6C,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAC;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACnDlD,OAAA;YAAM6C,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAEX,UAAU,CAACrB,WAAW;UAAC;YAAAiC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5D,CAAC,EACLlC,sBAAsB,IAAId,SAAS,CAACsC,MAAM,KAAK,YAAY,iBAC1DxC,OAAA;UAAK6C,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1B9C,OAAA;YAAM6C,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAC;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACrDlD,OAAA;YAAM6C,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAEX,UAAU,CAACnB,sBAAsB;UAAC;YAAA+B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvE,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLhD,SAAS,CAACuD,gBAAgB,iBACzBzD,OAAA;MAAK6C,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChC9C,OAAA;QAAK6C,SAAS,EAAC,gBAAgB;QAAAC,QAAA,EAAC;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACxClD,OAAA;QAAK6C,SAAS,EAAC,gBAAgB;QAAAC,QAAA,EAAE5C,SAAS,CAACuD;MAAgB;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/D,CACN,eAGDlD,OAAA;MAAK6C,SAAS,EAAC,gBAAgB;MAAAC,QAAA,eAC7B9C,OAAA;QAAK6C,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzB9C,OAAA;UAAK6C,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC9C,OAAA;YAAK6C,SAAS,EAAC,WAAW;YAAAC,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAClClD,OAAA;YAAK6C,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3B9C,OAAA;cAAK6C,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAE,EAAAtC,qBAAA,GAAAN,SAAS,CAACwD,cAAc,cAAAlD,qBAAA,uBAAxBA,qBAAA,CAA0BgD,cAAc,CAAC,CAAC,KAAI;YAAC;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACnFlD,OAAA;cAAK6C,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENlD,OAAA;UAAK6C,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9B9C,OAAA;YAAK6C,SAAS,EAAC,WAAW;YAAAC,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAClClD,OAAA;YAAK6C,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3B9C,OAAA;cAAK6C,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAE,EAAArC,qBAAA,GAAAP,SAAS,CAACyD,UAAU,cAAAlD,qBAAA,uBAApBA,qBAAA,CAAsB+C,cAAc,CAAC,CAAC,KAAI;YAAC;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC/ElD,OAAA;cAAK6C,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENlD,OAAA;UAAK6C,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC9C,OAAA;YAAK6C,SAAS,EAAC,WAAW;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACnClD,OAAA;YAAK6C,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3B9C,OAAA;cAAK6C,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAE,EAAApC,qBAAA,GAAAR,SAAS,CAAC0D,WAAW,cAAAlD,qBAAA,uBAArBA,qBAAA,CAAuB8C,cAAc,CAAC,CAAC,KAAI;YAAC;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAChFlD,OAAA;cAAK6C,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAELhD,SAAS,CAAC2D,WAAW,GAAG,CAAC,iBACxB7D,OAAA;UAAK6C,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7B9C,OAAA;YAAK6C,SAAS,EAAC,WAAW;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACnClD,OAAA;YAAK6C,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3B9C,OAAA;cAAK6C,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAE,EAAAnC,qBAAA,GAAAT,SAAS,CAAC2D,WAAW,cAAAlD,qBAAA,uBAArBA,qBAAA,CAAuB6C,cAAc,CAAC,CAAC,KAAI;YAAC;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAChFlD,OAAA;cAAK6C,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLhD,SAAS,CAAC4D,MAAM,IAAI5D,SAAS,CAAC4D,MAAM,CAACC,MAAM,GAAG,CAAC,iBAC9C/D,OAAA;MAAK6C,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC5B9C,OAAA;QAAA8C,QAAA,EAAI;MAAa;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACtBlD,OAAA;QAAK6C,SAAS,EAAC,aAAa;QAAAC,QAAA,GACzB5C,SAAS,CAAC4D,MAAM,CAACE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAC,CAAC7D,KAAK,EAAE8D,KAAK,kBAC7ClE,OAAA;UAAiB6C,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACrC9C,OAAA;YAAK6C,SAAS,EAAC,WAAW;YAAAC,QAAA,GAAC,MAAI,EAAC1C,KAAK,CAAC+D,SAAS;UAAA;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACtDlD,OAAA;YAAK6C,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAE1C,KAAK,CAACgE;UAAS;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACpDlD,OAAA;YAAK6C,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAE1C,KAAK,CAACiE;UAAY;YAAAtB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA,GAHjDgB,KAAK;UAAAnB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAIV,CACN,CAAC,EACDhD,SAAS,CAAC4D,MAAM,CAACC,MAAM,GAAG,CAAC,iBAC1B/D,OAAA;UAAK6C,SAAS,EAAC,aAAa;UAAAC,QAAA,GAAC,GAC1B,EAAC5C,SAAS,CAAC4D,MAAM,CAACC,MAAM,GAAG,CAAC,EAAC,cAChC;QAAA;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGDlD,OAAA;MAAK6C,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7B9C,OAAA;QAAA8C,QAAA,EAAI;MAAc;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACvBlD,OAAA;QAAK6C,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5B9C,OAAA;UAAK6C,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxB9C,OAAA;YAAM6C,SAAS,EAAC,UAAU;YAAAC,QAAA,EAAE,IAAIlB,IAAI,CAAC1B,SAAS,CAACuB,SAAS,CAAC,CAAC6C,kBAAkB,CAAC;UAAC;YAAAvB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACtFlD,OAAA;YAAM6C,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD,CAAC,EACLhD,SAAS,CAACsC,MAAM,KAAK,YAAY,iBAChCxC,OAAA;UAAK6C,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC9C,OAAA;YAAM6C,SAAS,EAAC,UAAU;YAAAC,QAAA,EAAE,IAAIlB,IAAI,CAAC,CAAC,CAAC0C,kBAAkB,CAAC;UAAC;YAAAvB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACnElD,OAAA;YAAM6C,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAE5C,SAAS,CAACuD,gBAAgB,IAAI;UAAe;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjF,CACN,EACAhD,SAAS,CAACqE,WAAW,iBACpBvE,OAAA;UAAK6C,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxB9C,OAAA;YAAM6C,SAAS,EAAC,UAAU;YAAAC,QAAA,EAAE,IAAIlB,IAAI,CAAC1B,SAAS,CAACqE,WAAW,CAAC,CAACD,kBAAkB,CAAC;UAAC;YAAAvB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACxFlD,OAAA;YAAM6C,SAAS,EAAC,aAAa;YAAAC,QAAA,GAAC,SAAO,EAAC5C,SAAS,CAACsC,MAAM,CAACgC,WAAW,CAAC,CAAC;UAAA;YAAAzB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzE,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNlD,OAAA;MAAK6C,SAAS,EAAC,kBAAkB;MAAAC,QAAA,GAC9B5C,SAAS,CAACsC,MAAM,KAAK,YAAY,iBAChCxC,OAAA;QAAQyE,OAAO,EAAEtE,QAAS;QAAC0C,SAAS,EAAC,iBAAiB;QAAAC,QAAA,EAAC;MAEvD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CACT,EAEA,CAAChD,SAAS,CAACsC,MAAM,KAAK,WAAW,IAAItC,SAAS,CAACsC,MAAM,KAAK,QAAQ,IAAItC,SAAS,CAACsC,MAAM,KAAK,WAAW,kBACrGxC,OAAA;QAAK6C,SAAS,EAAC,oBAAoB;QAAAC,QAAA,gBACjC9C,OAAA;UAAK6C,SAAS,EAAE,mBAAmB3C,SAAS,CAACsC,MAAM,CAACgC,WAAW,CAAC,CAAC,EAAG;UAAA1B,QAAA,EACjEF,aAAa,CAAC1C,SAAS,CAACsC,MAAM;QAAC;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC,eACNlD,OAAA;UAAK6C,SAAS,EAAC,iBAAiB;UAAAC,QAAA,GAAC,SACxB,EAAC5C,SAAS,CAACsC,MAAM,CAACgC,WAAW,CAAC,CAAC,EACrCtE,SAAS,CAACsC,MAAM,KAAK,WAAW,iBAC/BxC,OAAA;YAAK6C,SAAS,EAAC,oBAAoB;YAAAC,QAAA,GAAC,yBACX,EAAC5C,SAAS,CAACwD,cAAc,EAAC,UACjD,EAACxD,SAAS,CAACyD,UAAU,GAAG,CAAC,IAAI,SAASzD,SAAS,CAACyD,UAAU,SAAS;UAAA;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChE,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAEL9C,KAAK,iBACJJ,OAAA;MAAK6C,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7B9C,OAAA;QAAM6C,SAAS,EAAC,YAAY;QAAAC,QAAA,EAAC;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACtClD,OAAA;QAAM6C,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAE1C;MAAK;QAAA2C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3C,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC7C,EAAA,CA3QIJ,cAAc;AAAAyE,EAAA,GAAdzE,cAAc;AA6QpB,eAAeA,cAAc;AAAC,IAAAyE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}