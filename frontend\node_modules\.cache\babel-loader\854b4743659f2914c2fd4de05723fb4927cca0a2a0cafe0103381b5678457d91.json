{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Advanced\\\\Advanced CRM filed all use\\\\frontend\\\\src\\\\components\\\\forms\\\\FormBuilder.js\",\n  _s = $RefreshSig$();\nimport { useState, useEffect } from 'react';\nimport { PersonFieldDefinitions, getAllPersonFields } from '../../constants/personConstants';\nimport formConfigService from '../../services/formConfigService';\nimport apiService from '../../services/apiService';\nimport FieldConfigModal from './FieldConfigModal';\nimport FormPreview from './FormPreview';\nimport './FormBuilder.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst FormBuilder = ({\n  onSave,\n  onCancel,\n  initialConfig = null\n}) => {\n  _s();\n  const [selectedHierarchy, setSelectedHierarchy] = useState({});\n  const [formName, setFormName] = useState('');\n  const [formDescription, setFormDescription] = useState('');\n  const [availableFields, setAvailableFields] = useState([]);\n  const [selectedFields, setSelectedFields] = useState([]);\n  const [currentSection, setCurrentSection] = useState('all');\n  const [searchTerm, setSearchTerm] = useState('');\n  const [showPreview, setShowPreview] = useState(false);\n  const [showFieldConfig, setShowFieldConfig] = useState(false);\n  const [configField, setConfigField] = useState(null);\n  const [errors, setErrors] = useState({});\n  const [saving, setSaving] = useState(false);\n  const [savedForms, setSavedForms] = useState([]);\n  const [showSavedForms, setShowSavedForms] = useState(false);\n\n  // State for dropdown data\n  const [divisions, setDivisions] = useState([]);\n  const [categories, setCategories] = useState([]);\n  const [subCategories, setSubCategories] = useState([]);\n  const [loading, setLoading] = useState({\n    divisions: false,\n    categories: false,\n    subCategories: false\n  });\n  const [existingFormInfo, setExistingFormInfo] = useState(null);\n  useEffect(() => {\n    initializeFields();\n    loadSavedForms();\n    if (initialConfig) {\n      loadInitialConfig(initialConfig);\n    }\n  }, [initialConfig]);\n  const initializeFields = () => {\n    const allFields = getAllPersonFields();\n    setAvailableFields(allFields);\n  };\n  const loadSavedForms = () => {\n    const forms = formConfigService.getAllFormConfigs();\n    setSavedForms(forms);\n  };\n  const loadInitialConfig = config => {\n    setFormName(config.name || '');\n    setFormDescription(config.description || '');\n\n    // Deduplicate fields when loading initial config\n    const fields = config.fields || [];\n    const deduplicatedFields = deduplicateFields(fields);\n    setSelectedFields(deduplicatedFields);\n    if (config.type === 'division' && config.associatedId) {\n      // Load division info for hierarchy selector\n      setSelectedHierarchy({\n        divisionId: config.associatedId\n      });\n    } else if (config.type === 'category' && config.associatedId) {\n      // Load category info for hierarchy selector\n      setSelectedHierarchy({\n        categoryId: config.associatedId\n      });\n    } else if (config.type === 'subcategory' && config.associatedId) {\n      // Load subcategory info for hierarchy selector\n      setSelectedHierarchy({\n        subCategoryId: config.associatedId\n      });\n    }\n\n    // If hierarchy is stored in config, use that\n    if (config.hierarchy) {\n      setSelectedHierarchy(config.hierarchy);\n    }\n  };\n\n  // Load divisions on component mount\n  useEffect(() => {\n    loadDivisions();\n  }, []);\n\n  // Validate form creation rules when hierarchy changes\n  useEffect(() => {\n    if (selectedHierarchy.categoryId) {\n      const formValidation = formConfigService.validateFormCreation(selectedHierarchy.categoryId, selectedHierarchy.subCategoryId);\n      const formInfo = formConfigService.getExistingFormInfo(selectedHierarchy.categoryId, selectedHierarchy.subCategoryId);\n      setExistingFormInfo(formInfo);\n      if (!formValidation.isValid) {\n        setErrors(prev => ({\n          ...prev,\n          formCreation: formValidation.errors.join('. ')\n        }));\n      } else {\n        setErrors(prev => {\n          const newErrors = {\n            ...prev\n          };\n          delete newErrors.formCreation;\n          return newErrors;\n        });\n      }\n    } else {\n      setExistingFormInfo(null);\n      setErrors(prev => {\n        const newErrors = {\n          ...prev\n        };\n        delete newErrors.formCreation;\n        return newErrors;\n      });\n    }\n  }, [selectedHierarchy.categoryId, selectedHierarchy.subCategoryId]);\n  const loadDivisions = async () => {\n    setLoading(prev => ({\n      ...prev,\n      divisions: true\n    }));\n    try {\n      const response = await apiService.getDivisions();\n      setDivisions(response.data || []);\n    } catch (error) {\n      console.error('Error loading divisions:', error);\n    } finally {\n      setLoading(prev => ({\n        ...prev,\n        divisions: false\n      }));\n    }\n  };\n  const loadCategories = async divisionId => {\n    if (!divisionId) {\n      setCategories([]);\n      setSubCategories([]);\n      return;\n    }\n    setLoading(prev => ({\n      ...prev,\n      categories: true\n    }));\n    try {\n      const response = await apiService.getCategoriesByDivision(divisionId);\n      setCategories(response.data || []);\n    } catch (error) {\n      console.error('Error loading categories:', error);\n      setCategories([]);\n    } finally {\n      setLoading(prev => ({\n        ...prev,\n        categories: false\n      }));\n    }\n  };\n  const loadSubCategories = async categoryId => {\n    if (!categoryId) {\n      setSubCategories([]);\n      return;\n    }\n    setLoading(prev => ({\n      ...prev,\n      subCategories: true\n    }));\n    try {\n      const response = await apiService.getSubCategoriesByCategory(categoryId);\n      setSubCategories(response.data || []);\n    } catch (error) {\n      console.error('Error loading subcategories:', error);\n      setSubCategories([]);\n    } finally {\n      setLoading(prev => ({\n        ...prev,\n        subCategories: false\n      }));\n    }\n  };\n  const handleDivisionChange = e => {\n    const divisionId = e.target.value;\n    const division = divisions.find(d => d.id === parseInt(divisionId));\n    setSelectedHierarchy({\n      divisionId: divisionId || null,\n      categoryId: null,\n      subCategoryId: null,\n      division: division || null,\n      category: null,\n      subCategory: null\n    });\n    setCategories([]);\n    setSubCategories([]);\n    if (divisionId) {\n      loadCategories(divisionId);\n    }\n  };\n  const handleCategoryChange = e => {\n    const categoryId = e.target.value;\n    const category = categories.find(c => c.id === parseInt(categoryId));\n    setSelectedHierarchy(prev => ({\n      ...prev,\n      categoryId: categoryId || null,\n      subCategoryId: null,\n      category: category || null,\n      subCategory: null\n    }));\n    setSubCategories([]);\n    if (categoryId) {\n      loadSubCategories(categoryId);\n    }\n  };\n  const handleSubCategoryChange = e => {\n    const subCategoryId = e.target.value;\n    const subCategory = subCategories.find(sc => sc.id === parseInt(subCategoryId));\n    setSelectedHierarchy(prev => ({\n      ...prev,\n      subCategoryId: subCategoryId || null,\n      subCategory: subCategory || null\n    }));\n  };\n  const handleFieldToggle = field => {\n    const isSelected = selectedFields.some(f => f.key === field.key);\n    if (isSelected) {\n      // Remove all instances of this field key (in case there are duplicates)\n      setSelectedFields(prev => prev.filter(f => f.key !== field.key));\n    } else {\n      // Add field only if it doesn't already exist\n      setSelectedFields(prev => {\n        const exists = prev.some(f => f.key === field.key);\n        if (exists) {\n          console.warn(`Field ${field.key} already exists in selectedFields, skipping duplicate`);\n          return prev;\n        }\n        return [...prev, {\n          ...field\n        }];\n      });\n    }\n  };\n  const handleFieldConfig = field => {\n    setConfigField(field);\n    setShowFieldConfig(true);\n  };\n  const handleFieldConfigSave = updatedField => {\n    setSelectedFields(prev => prev.map(f => f.key === updatedField.key ? updatedField : f));\n    setShowFieldConfig(false);\n    setConfigField(null);\n  };\n  const handleFieldMove = (fromIndex, toIndex) => {\n    const newFields = [...selectedFields];\n    const [movedField] = newFields.splice(fromIndex, 1);\n    newFields.splice(toIndex, 0, movedField);\n    setSelectedFields(newFields);\n  };\n  const handleSave = async () => {\n    const validation = validateForm();\n    if (!validation.isValid) {\n      setErrors(validation.errors);\n      return;\n    }\n    setSaving(true);\n    try {\n      // Ensure complete hierarchy information is included\n      const completeHierarchy = {\n        ...selectedHierarchy,\n        // Include division info (required)\n        divisionId: selectedHierarchy.divisionId,\n        division: selectedHierarchy.division,\n        // Include category info (required)\n        categoryId: selectedHierarchy.categoryId,\n        category: selectedHierarchy.category,\n        // Include subcategory info if selected (optional)\n        subCategoryId: selectedHierarchy.subCategoryId || null,\n        subCategory: selectedHierarchy.subCategory || null\n      };\n      const config = {\n        name: formName,\n        description: formDescription,\n        fields: selectedFields,\n        hierarchy: completeHierarchy,\n        settings: {\n          showSections: true,\n          allowConditionalFields: true,\n          validateOnChange: true\n        }\n      };\n      let savedConfig;\n      // Save form with the most specific level selected, but include complete hierarchy\n      if (selectedHierarchy.subCategoryId) {\n        savedConfig = formConfigService.saveFormConfig('subcategory', selectedHierarchy.subCategoryId, config);\n      } else if (selectedHierarchy.categoryId) {\n        savedConfig = formConfigService.saveFormConfig('category', selectedHierarchy.categoryId, config);\n      } else {\n        throw new Error('Please select both division and category');\n      }\n\n      // Reload saved forms list\n      loadSavedForms();\n      if (onSave) {\n        onSave(savedConfig);\n      } else {\n        // Show success message for standalone usage\n        alert('Form configuration saved successfully!');\n        // Reset form\n        setFormName('');\n        setFormDescription('');\n        setSelectedFields([]);\n        setSelectedHierarchy({});\n      }\n    } catch (error) {\n      console.error('Error saving form:', error);\n      setErrors({\n        general: error.message\n      });\n    } finally {\n      setSaving(false);\n    }\n  };\n  const validateForm = () => {\n    const errors = {};\n    if (!formName.trim()) {\n      errors.formName = 'Form name is required';\n    }\n\n    // Division and category are required, subcategory is optional\n    if (!selectedHierarchy.divisionId) {\n      errors.hierarchy = 'Please select a division';\n    } else if (!selectedHierarchy.categoryId) {\n      errors.hierarchy = 'Please select a category';\n    } else if (selectedHierarchy.subCategoryId && !selectedHierarchy.categoryId) {\n      errors.hierarchy = 'Cannot select subcategory without selecting category';\n    }\n\n    // Validate form creation rules\n    if (selectedHierarchy.categoryId) {\n      const formValidation = formConfigService.validateFormCreation(selectedHierarchy.categoryId, selectedHierarchy.subCategoryId);\n      if (!formValidation.isValid) {\n        errors.formCreation = formValidation.errors.join('. ');\n      }\n    }\n    if (selectedFields.length === 0) {\n      errors.fields = 'Please select at least one field';\n    }\n\n    // Check required fields\n    const requiredFields = ['name', 'mobileNumber', 'nature'];\n    const selectedFieldKeys = selectedFields.map(f => f.key);\n    const missingRequired = requiredFields.filter(rf => !selectedFieldKeys.includes(rf));\n    if (missingRequired.length > 0) {\n      errors.requiredFields = `Required fields missing: ${missingRequired.join(', ')}`;\n    }\n    return {\n      isValid: Object.keys(errors).length === 0,\n      errors\n    };\n  };\n  const getFilteredFields = () => {\n    let filtered = availableFields;\n\n    // Filter by section\n    if (currentSection !== 'all') {\n      filtered = filtered.filter(field => field.section === currentSection);\n    }\n\n    // Filter by search term\n    if (searchTerm) {\n      filtered = filtered.filter(field => field.label.toLowerCase().includes(searchTerm.toLowerCase()) || field.key.toLowerCase().includes(searchTerm.toLowerCase()));\n    }\n    return filtered;\n  };\n  const getSectionCounts = () => {\n    const counts = {\n      all: availableFields.length\n    };\n    Object.keys(PersonFieldDefinitions).forEach(sectionKey => {\n      counts[sectionKey] = availableFields.filter(f => f.section === sectionKey).length;\n    });\n    return counts;\n  };\n  const sectionCounts = getSectionCounts();\n  const filteredFields = getFilteredFields();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"form-builder\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"form-builder-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Form Builder\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 414,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-actions\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          onClick: () => setShowPreview(true),\n          className: \"btn btn-secondary\",\n          disabled: selectedFields.length === 0,\n          children: \"Preview Form\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 416,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          onClick: handleSave,\n          className: \"btn btn-primary\",\n          disabled: saving || errors.formCreation,\n          children: saving ? 'Saving...' : 'Save Form'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 424,\n          columnNumber: 11\n        }, this), onCancel && /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          onClick: onCancel,\n          className: \"btn btn-outline\",\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 433,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 415,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 413,\n      columnNumber: 7\n    }, this), errors.general && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"alert alert-error\",\n      children: errors.general\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 445,\n      columnNumber: 9\n    }, this), Object.keys(errors).length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"validation-errors\",\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        children: \"Please fix the following issues:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 451,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n        children: Object.entries(errors).map(([key, message]) => /*#__PURE__*/_jsxDEV(\"li\", {\n          children: message\n        }, key, false, {\n          fileName: _jsxFileName,\n          lineNumber: 454,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 452,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 450,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"form-builder-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"config-panel\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"config-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"section-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: [\"Saved Forms (\", savedForms.length, \")\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 466,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              className: \"btn-link\",\n              onClick: () => setShowSavedForms(!showSavedForms),\n              children: showSavedForms ? 'Hide' : 'Show'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 467,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 465,\n            columnNumber: 13\n          }, this), showSavedForms && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"saved-forms-list\",\n            children: savedForms.length === 0 ? /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"no-forms-message\",\n              children: \"No saved forms yet. Create your first form below!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 479,\n              columnNumber: 19\n            }, this) : savedForms.map(form => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"saved-form-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-item-header\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  children: form.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 484,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"form-type-badge\",\n                  children: form.type\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 485,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 483,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"form-description\",\n                children: form.description || 'No description'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 487,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-meta\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [form.summary.fieldCount, \" fields\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 489,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"\\u2022\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 490,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [\"Updated \", new Date(form.updatedAt).toLocaleDateString()]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 491,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 488,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-actions\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"button\",\n                  className: \"btn-small btn-outline\",\n                  onClick: () => loadInitialConfig(form),\n                  children: \"Load\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 494,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"button\",\n                  className: \"btn-small btn-danger\",\n                  onClick: () => {\n                    if (window.confirm('Are you sure you want to delete this form?')) {\n                      formConfigService.deleteFormConfig(form.type, form.associatedId);\n                      loadSavedForms();\n                    }\n                  },\n                  children: \"Delete\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 501,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 493,\n                columnNumber: 23\n              }, this)]\n            }, form.key, true, {\n              fileName: _jsxFileName,\n              lineNumber: 482,\n              columnNumber: 21\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 477,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 464,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"config-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Form Configuration\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 523,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"Form Name *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 526,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: formName,\n              onChange: e => setFormName(e.target.value),\n              placeholder: \"Enter form name\",\n              className: errors.formName ? 'error' : ''\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 527,\n              columnNumber: 15\n            }, this), errors.formName && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"error-message\",\n              children: errors.formName\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 534,\n              columnNumber: 35\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 525,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"Description\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 538,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n              value: formDescription,\n              onChange: e => setFormDescription(e.target.value),\n              placeholder: \"Enter form description\",\n              rows: \"3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 539,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 537,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 522,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"config-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: [\"Selected Fields (\", selectedFields.length, \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 552,\n            columnNumber: 13\n          }, this), errors.fields && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"error-message\",\n            children: errors.fields\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 553,\n            columnNumber: 31\n          }, this), errors.requiredFields && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"error-message\",\n            children: errors.requiredFields\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 554,\n            columnNumber: 39\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"selected-fields\",\n            children: selectedFields.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"empty-state\",\n              children: \"No fields selected. Choose fields from the available fields panel.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 558,\n              columnNumber: 17\n            }, this) : selectedFields.map((field, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"selected-field\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"field-info\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"field-label\",\n                  children: field.label\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 565,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"field-type\",\n                  children: field.type\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 566,\n                  columnNumber: 23\n                }, this), field.required && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"required-badge\",\n                  children: \"Required\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 567,\n                  columnNumber: 42\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 564,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"field-actions\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"button\",\n                  onClick: () => handleFieldConfig(field),\n                  className: \"btn-icon\",\n                  title: \"Configure field\",\n                  children: \"\\u2699\\uFE0F\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 570,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"button\",\n                  onClick: () => handleFieldMove(index, Math.max(0, index - 1)),\n                  className: \"btn-icon\",\n                  disabled: index === 0,\n                  title: \"Move up\",\n                  children: \"\\u2191\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 578,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"button\",\n                  onClick: () => handleFieldMove(index, Math.min(selectedFields.length - 1, index + 1)),\n                  className: \"btn-icon\",\n                  disabled: index === selectedFields.length - 1,\n                  title: \"Move down\",\n                  children: \"\\u2193\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 587,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"button\",\n                  onClick: () => handleFieldToggle(field),\n                  className: \"btn-icon remove\",\n                  title: \"Remove field\",\n                  children: \"\\u2715\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 596,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 569,\n                columnNumber: 21\n              }, this)]\n            }, field.key, true, {\n              fileName: _jsxFileName,\n              lineNumber: 563,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 556,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 551,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 462,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"fields-panel\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"fields-header\",\n          children: /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Available Fields\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 615,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 614,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hierarchy-selection\",\n          style: {\n            backgroundColor: '#f8f9fa',\n            border: '1px solid #e1e5e9',\n            borderRadius: '8px',\n            padding: '1.5rem',\n            margin: '1rem 0'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            style: {\n              margin: '0 0 1rem 0',\n              color: '#495057'\n            },\n            children: \"Associate with Division & Category (Required) / SubCategory (Optional) *\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 626,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '0.875rem',\n              color: '#6c757d',\n              marginBottom: '1rem',\n              padding: '0.75rem',\n              backgroundColor: '#f8f9fa',\n              borderRadius: '4px',\n              border: '1px solid #e9ecef'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Form Creation Rules:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 638,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              style: {\n                margin: '0.5rem 0 0 1rem',\n                paddingLeft: '1rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"If you create a form for a category, you cannot create forms for its subcategories\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 640,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Each subcategory can have only one form\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 641,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"If subcategories already have forms, you cannot create a form for the parent category\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 642,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 639,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 629,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"hierarchy-dropdowns\",\n            style: {\n              display: 'grid',\n              gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n              gap: '1rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                style: {\n                  display: 'block',\n                  marginBottom: '0.5rem',\n                  fontWeight: '500',\n                  color: '#495057'\n                },\n                children: \"Division *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 653,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: selectedHierarchy.divisionId || '',\n                onChange: handleDivisionChange,\n                disabled: loading.divisions,\n                className: errors.divisionId ? 'error' : '',\n                style: {\n                  width: '100%',\n                  padding: '0.75rem',\n                  border: '1px solid #ced4da',\n                  borderRadius: '4px',\n                  fontSize: '1rem',\n                  backgroundColor: 'white'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Select Division\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 675,\n                  columnNumber: 19\n                }, this), divisions.map(division => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: division.id,\n                  children: division.name\n                }, division.id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 677,\n                  columnNumber: 21\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 661,\n                columnNumber: 17\n              }, this), errors.divisionId && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  color: '#dc3545',\n                  fontSize: '0.875rem',\n                  marginTop: '0.25rem'\n                },\n                children: errors.divisionId\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 683,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 652,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                style: {\n                  display: 'block',\n                  marginBottom: '0.5rem',\n                  fontWeight: '500',\n                  color: '#495057'\n                },\n                children: \"Category *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 691,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: selectedHierarchy.categoryId || '',\n                onChange: handleCategoryChange,\n                disabled: !selectedHierarchy.divisionId || loading.categories,\n                className: errors.categoryId ? 'error' : '',\n                style: {\n                  width: '100%',\n                  padding: '0.75rem',\n                  border: '1px solid #ced4da',\n                  borderRadius: '4px',\n                  fontSize: '1rem',\n                  backgroundColor: 'white'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: !selectedHierarchy.divisionId ? 'Select Division first' : 'Select Category'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 713,\n                  columnNumber: 19\n                }, this), categories.map(category => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: category.id,\n                  children: category.name\n                }, category.id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 717,\n                  columnNumber: 21\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 699,\n                columnNumber: 17\n              }, this), errors.categoryId && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  color: '#dc3545',\n                  fontSize: '0.875rem',\n                  marginTop: '0.25rem'\n                },\n                children: errors.categoryId\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 723,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 690,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                style: {\n                  display: 'block',\n                  marginBottom: '0.5rem',\n                  fontWeight: '500',\n                  color: '#495057'\n                },\n                children: \"SubCategory (Optional)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 731,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: selectedHierarchy.subCategoryId || '',\n                onChange: handleSubCategoryChange,\n                disabled: !selectedHierarchy.categoryId || loading.subCategories,\n                style: {\n                  width: '100%',\n                  padding: '0.75rem',\n                  border: '1px solid #ced4da',\n                  borderRadius: '4px',\n                  fontSize: '1rem',\n                  backgroundColor: 'white'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: !selectedHierarchy.categoryId ? 'Select Category first' : 'Select SubCategory (Optional)'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 752,\n                  columnNumber: 19\n                }, this), subCategories.map(subCategory => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: subCategory.id,\n                  children: subCategory.name\n                }, subCategory.id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 756,\n                  columnNumber: 21\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 739,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 730,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 646,\n            columnNumber: 13\n          }, this), errors.hierarchy && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              color: '#dc3545',\n              fontSize: '0.875rem',\n              marginTop: '0.5rem'\n            },\n            children: errors.hierarchy\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 765,\n            columnNumber: 15\n          }, this), errors.formCreation && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              color: '#dc3545',\n              fontSize: '0.875rem',\n              marginTop: '0.5rem',\n              fontWeight: 'bold'\n            },\n            children: [\"\\u26A0\\uFE0F \", errors.formCreation]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 770,\n            columnNumber: 15\n          }, this), existingFormInfo && existingFormInfo.existingForms.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginTop: '1rem',\n              padding: '0.75rem',\n              backgroundColor: '#fff3cd',\n              border: '1px solid #ffeaa7',\n              borderRadius: '4px',\n              fontSize: '0.875rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\uD83D\\uDCCB Existing Forms:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 785,\n              columnNumber: 17\n            }, this), existingFormInfo.existingForms.map((form, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginTop: '0.5rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: [form.type === 'category' ? 'Category' : 'SubCategory', \" Form:\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 788,\n                columnNumber: 21\n              }, this), \" \", form.name, form.description && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  color: '#6c757d'\n                },\n                children: form.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 789,\n                columnNumber: 42\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 787,\n              columnNumber: 19\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 777,\n            columnNumber: 15\n          }, this), existingFormInfo && existingFormInfo.subCategoriesWithForms.length > 0 && !selectedHierarchy.subCategoryId && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginTop: '1rem',\n              padding: '0.75rem',\n              backgroundColor: '#f8d7da',\n              border: '1px solid #f5c6cb',\n              borderRadius: '4px',\n              fontSize: '0.875rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u26A0\\uFE0F Warning:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 805,\n              columnNumber: 17\n            }, this), \" This category has \", existingFormInfo.subCategoriesWithForms.length, \" subcategory form(s). You cannot create a form for this category.\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 797,\n            columnNumber: 15\n          }, this), selectedHierarchy.categoryId && !errors.formCreation && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginTop: '1rem',\n              padding: '0.75rem',\n              backgroundColor: '#d4edda',\n              border: '1px solid #c3e6cb',\n              borderRadius: '4px',\n              fontSize: '0.875rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u2705 Valid Selection:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 820,\n              columnNumber: 17\n            }, this), \" You can create a form for this \", selectedHierarchy.subCategoryId ? 'subcategory' : 'category', \".\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 812,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 619,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"fields-controls\",\n          children: /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            placeholder: \"Search fields...\",\n            value: searchTerm,\n            onChange: e => setSearchTerm(e.target.value),\n            className: \"search-input\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 827,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 826,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"section-tabs\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            onClick: () => setCurrentSection('all'),\n            className: `section-tab ${currentSection === 'all' ? 'active' : ''}`,\n            children: [\"All (\", sectionCounts.all, \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 838,\n            columnNumber: 13\n          }, this), Object.entries(PersonFieldDefinitions).map(([sectionKey, section]) => /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            onClick: () => setCurrentSection(sectionKey),\n            className: `section-tab ${currentSection === sectionKey ? 'active' : ''}`,\n            children: [section.title, \" (\", sectionCounts[sectionKey], \")\"]\n          }, sectionKey, true, {\n            fileName: _jsxFileName,\n            lineNumber: 846,\n            columnNumber: 15\n          }, this))]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 837,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"fields-list\",\n          children: filteredFields.map(field => {\n            const isSelected = selectedFields.some(f => f.key === field.key);\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `field-item ${isSelected ? 'selected' : ''}`,\n              onClick: () => handleFieldToggle(field),\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"field-checkbox\",\n                children: /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"checkbox\",\n                  checked: isSelected,\n                  onChange: () => handleFieldToggle(field)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 868,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 867,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"field-details\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"field-name\",\n                  children: field.label\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 875,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"field-meta\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"field-type\",\n                    children: field.type\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 877,\n                    columnNumber: 23\n                  }, this), field.required && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"required-badge\",\n                    children: \"Required\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 878,\n                    columnNumber: 42\n                  }, this), field.conditional && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"conditional-badge\",\n                    children: \"Conditional\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 879,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 876,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 874,\n                columnNumber: 19\n              }, this)]\n            }, field.key, true, {\n              fileName: _jsxFileName,\n              lineNumber: 862,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 858,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 613,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 460,\n      columnNumber: 7\n    }, this), showFieldConfig && configField && /*#__PURE__*/_jsxDEV(FieldConfigModal, {\n      field: configField,\n      onSave: handleFieldConfigSave,\n      onCancel: () => {\n        setShowFieldConfig(false);\n        setConfigField(null);\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 891,\n      columnNumber: 9\n    }, this), showPreview && /*#__PURE__*/_jsxDEV(FormPreview, {\n      fields: selectedFields,\n      formName: formName,\n      onClose: () => setShowPreview(false)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 902,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 412,\n    columnNumber: 5\n  }, this);\n};\n_s(FormBuilder, \"0DqQGpKgpUZO5u8m04b/qhfBuMA=\");\n_c = FormBuilder;\nexport default FormBuilder;\nvar _c;\n$RefreshReg$(_c, \"FormBuilder\");", "map": {"version": 3, "names": ["useState", "useEffect", "PersonFieldDefinitions", "<PERSON><PERSON><PERSON><PERSON><PERSON>F<PERSON>s", "formConfigService", "apiService", "FieldConfigModal", "FormPreview", "jsxDEV", "_jsxDEV", "FormBuilder", "onSave", "onCancel", "initialConfig", "_s", "selectedHierarchy", "setSelectedHierarchy", "formName", "setFormName", "formDescription", "setFormDescription", "availableFields", "setAvailableFields", "<PERSON><PERSON><PERSON>s", "setSelectedFields", "currentSection", "setCurrentSection", "searchTerm", "setSearchTerm", "showPreview", "setShowPreview", "showFieldConfig", "setShowFieldConfig", "config<PERSON><PERSON>", "setConfigField", "errors", "setErrors", "saving", "setSaving", "savedForms", "setSavedForms", "showSavedForms", "setShowSavedForms", "divisions", "setDivisions", "categories", "setCategories", "subCategories", "setSubCategories", "loading", "setLoading", "existingFormInfo", "setExistingFormInfo", "initializeFields", "loadSavedForms", "loadInitialConfig", "allFields", "forms", "getAllFormConfigs", "config", "name", "description", "fields", "deduplicatedFields", "deduplicateFields", "type", "associatedId", "divisionId", "categoryId", "subCategoryId", "hierarchy", "loadDivisions", "formValidation", "validateFormCreation", "formInfo", "getExistingFormInfo", "<PERSON><PERSON><PERSON><PERSON>", "prev", "formCreation", "join", "newErrors", "response", "getDivisions", "data", "error", "console", "loadCategories", "getCategoriesByDivision", "loadSubCategories", "getSubCategoriesByCategory", "handleDivisionChange", "e", "target", "value", "division", "find", "d", "id", "parseInt", "category", "subCategory", "handleCategoryChange", "c", "handleSubCategoryChange", "sc", "handleFieldToggle", "field", "isSelected", "some", "f", "key", "filter", "exists", "warn", "handleFieldConfig", "handleFieldConfigSave", "updatedField", "map", "handleFieldMove", "fromIndex", "toIndex", "new<PERSON>ields", "movedField", "splice", "handleSave", "validation", "validateForm", "completeHierarchy", "settings", "showSections", "allowConditionalFields", "validateOnChange", "savedConfig", "saveFormConfig", "Error", "alert", "general", "message", "trim", "length", "requiredFields", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "missingRequired", "rf", "includes", "Object", "keys", "getFilteredFields", "filtered", "section", "label", "toLowerCase", "getSectionCounts", "counts", "all", "for<PERSON>ach", "sectionKey", "sectionCounts", "filteredFields", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "disabled", "entries", "form", "summary", "fieldCount", "Date", "updatedAt", "toLocaleDateString", "window", "confirm", "deleteFormConfig", "onChange", "placeholder", "rows", "index", "required", "title", "Math", "max", "min", "style", "backgroundColor", "border", "borderRadius", "padding", "margin", "color", "fontSize", "marginBottom", "paddingLeft", "display", "gridTemplateColumns", "gap", "fontWeight", "width", "marginTop", "existingForms", "subCategoriesWithForms", "checked", "conditional", "onClose", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/Advanced/Advanced CRM filed all use/frontend/src/components/forms/FormBuilder.js"], "sourcesContent": ["import { useState, useEffect } from 'react';\nimport { PersonFieldDefinitions, getAllPersonFields } from '../../constants/personConstants';\nimport formConfigService from '../../services/formConfigService';\nimport apiService from '../../services/apiService';\nimport FieldConfigModal from './FieldConfigModal';\nimport FormPreview from './FormPreview';\nimport './FormBuilder.css';\n\nconst FormBuilder = ({ onSave, onCancel, initialConfig = null }) => {\n  const [selectedHierarchy, setSelectedHierarchy] = useState({});\n  const [formName, setFormName] = useState('');\n  const [formDescription, setFormDescription] = useState('');\n  const [availableFields, setAvailableFields] = useState([]);\n  const [selectedFields, setSelectedFields] = useState([]);\n  const [currentSection, setCurrentSection] = useState('all');\n  const [searchTerm, setSearchTerm] = useState('');\n  const [showPreview, setShowPreview] = useState(false);\n  const [showFieldConfig, setShowFieldConfig] = useState(false);\n  const [configField, setConfigField] = useState(null);\n  const [errors, setErrors] = useState({});\n  const [saving, setSaving] = useState(false);\n  const [savedForms, setSavedForms] = useState([]);\n  const [showSavedForms, setShowSavedForms] = useState(false);\n\n  // State for dropdown data\n  const [divisions, setDivisions] = useState([]);\n  const [categories, setCategories] = useState([]);\n  const [subCategories, setSubCategories] = useState([]);\n  const [loading, setLoading] = useState({\n    divisions: false,\n    categories: false,\n    subCategories: false\n  });\n  const [existingFormInfo, setExistingFormInfo] = useState(null);\n\n  useEffect(() => {\n    initializeFields();\n    loadSavedForms();\n    if (initialConfig) {\n      loadInitialConfig(initialConfig);\n    }\n  }, [initialConfig]);\n\n  const initializeFields = () => {\n    const allFields = getAllPersonFields();\n    setAvailableFields(allFields);\n  };\n\n  const loadSavedForms = () => {\n    const forms = formConfigService.getAllFormConfigs();\n    setSavedForms(forms);\n  };\n\n  const loadInitialConfig = (config) => {\n    setFormName(config.name || '');\n    setFormDescription(config.description || '');\n\n    // Deduplicate fields when loading initial config\n    const fields = config.fields || [];\n    const deduplicatedFields = deduplicateFields(fields);\n    setSelectedFields(deduplicatedFields);\n    \n    if (config.type === 'division' && config.associatedId) {\n      // Load division info for hierarchy selector\n      setSelectedHierarchy({\n        divisionId: config.associatedId\n      });\n    } else if (config.type === 'category' && config.associatedId) {\n      // Load category info for hierarchy selector\n      setSelectedHierarchy({\n        categoryId: config.associatedId\n      });\n    } else if (config.type === 'subcategory' && config.associatedId) {\n      // Load subcategory info for hierarchy selector\n      setSelectedHierarchy({\n        subCategoryId: config.associatedId\n      });\n    }\n\n    // If hierarchy is stored in config, use that\n    if (config.hierarchy) {\n      setSelectedHierarchy(config.hierarchy);\n    }\n  };\n\n  // Load divisions on component mount\n  useEffect(() => {\n    loadDivisions();\n  }, []);\n\n  // Validate form creation rules when hierarchy changes\n  useEffect(() => {\n    if (selectedHierarchy.categoryId) {\n      const formValidation = formConfigService.validateFormCreation(\n        selectedHierarchy.categoryId,\n        selectedHierarchy.subCategoryId\n      );\n\n      const formInfo = formConfigService.getExistingFormInfo(\n        selectedHierarchy.categoryId,\n        selectedHierarchy.subCategoryId\n      );\n      setExistingFormInfo(formInfo);\n\n      if (!formValidation.isValid) {\n        setErrors(prev => ({\n          ...prev,\n          formCreation: formValidation.errors.join('. ')\n        }));\n      } else {\n        setErrors(prev => {\n          const newErrors = { ...prev };\n          delete newErrors.formCreation;\n          return newErrors;\n        });\n      }\n    } else {\n      setExistingFormInfo(null);\n      setErrors(prev => {\n        const newErrors = { ...prev };\n        delete newErrors.formCreation;\n        return newErrors;\n      });\n    }\n  }, [selectedHierarchy.categoryId, selectedHierarchy.subCategoryId]);\n\n  const loadDivisions = async () => {\n    setLoading(prev => ({ ...prev, divisions: true }));\n    try {\n      const response = await apiService.getDivisions();\n      setDivisions(response.data || []);\n    } catch (error) {\n      console.error('Error loading divisions:', error);\n    } finally {\n      setLoading(prev => ({ ...prev, divisions: false }));\n    }\n  };\n\n  const loadCategories = async (divisionId) => {\n    if (!divisionId) {\n      setCategories([]);\n      setSubCategories([]);\n      return;\n    }\n\n    setLoading(prev => ({ ...prev, categories: true }));\n    try {\n      const response = await apiService.getCategoriesByDivision(divisionId);\n      setCategories(response.data || []);\n    } catch (error) {\n      console.error('Error loading categories:', error);\n      setCategories([]);\n    } finally {\n      setLoading(prev => ({ ...prev, categories: false }));\n    }\n  };\n\n  const loadSubCategories = async (categoryId) => {\n    if (!categoryId) {\n      setSubCategories([]);\n      return;\n    }\n\n    setLoading(prev => ({ ...prev, subCategories: true }));\n    try {\n      const response = await apiService.getSubCategoriesByCategory(categoryId);\n      setSubCategories(response.data || []);\n    } catch (error) {\n      console.error('Error loading subcategories:', error);\n      setSubCategories([]);\n    } finally {\n      setLoading(prev => ({ ...prev, subCategories: false }));\n    }\n  };\n\n  const handleDivisionChange = (e) => {\n    const divisionId = e.target.value;\n    const division = divisions.find(d => d.id === parseInt(divisionId));\n\n    setSelectedHierarchy({\n      divisionId: divisionId || null,\n      categoryId: null,\n      subCategoryId: null,\n      division: division || null,\n      category: null,\n      subCategory: null\n    });\n\n    setCategories([]);\n    setSubCategories([]);\n\n    if (divisionId) {\n      loadCategories(divisionId);\n    }\n  };\n\n  const handleCategoryChange = (e) => {\n    const categoryId = e.target.value;\n    const category = categories.find(c => c.id === parseInt(categoryId));\n\n    setSelectedHierarchy(prev => ({\n      ...prev,\n      categoryId: categoryId || null,\n      subCategoryId: null,\n      category: category || null,\n      subCategory: null\n    }));\n\n    setSubCategories([]);\n\n    if (categoryId) {\n      loadSubCategories(categoryId);\n    }\n  };\n\n  const handleSubCategoryChange = (e) => {\n    const subCategoryId = e.target.value;\n    const subCategory = subCategories.find(sc => sc.id === parseInt(subCategoryId));\n\n    setSelectedHierarchy(prev => ({\n      ...prev,\n      subCategoryId: subCategoryId || null,\n      subCategory: subCategory || null\n    }));\n  };\n\n  const handleFieldToggle = (field) => {\n    const isSelected = selectedFields.some(f => f.key === field.key);\n\n    if (isSelected) {\n      // Remove all instances of this field key (in case there are duplicates)\n      setSelectedFields(prev => prev.filter(f => f.key !== field.key));\n    } else {\n      // Add field only if it doesn't already exist\n      setSelectedFields(prev => {\n        const exists = prev.some(f => f.key === field.key);\n        if (exists) {\n          console.warn(`Field ${field.key} already exists in selectedFields, skipping duplicate`);\n          return prev;\n        }\n        return [...prev, { ...field }];\n      });\n    }\n  };\n\n  const handleFieldConfig = (field) => {\n    setConfigField(field);\n    setShowFieldConfig(true);\n  };\n\n  const handleFieldConfigSave = (updatedField) => {\n    setSelectedFields(prev => \n      prev.map(f => f.key === updatedField.key ? updatedField : f)\n    );\n    setShowFieldConfig(false);\n    setConfigField(null);\n  };\n\n  const handleFieldMove = (fromIndex, toIndex) => {\n    const newFields = [...selectedFields];\n    const [movedField] = newFields.splice(fromIndex, 1);\n    newFields.splice(toIndex, 0, movedField);\n    setSelectedFields(newFields);\n  };\n\n  const handleSave = async () => {\n    const validation = validateForm();\n\n    if (!validation.isValid) {\n      setErrors(validation.errors);\n      return;\n    }\n\n    setSaving(true);\n    try {\n      // Ensure complete hierarchy information is included\n      const completeHierarchy = {\n        ...selectedHierarchy,\n        // Include division info (required)\n        divisionId: selectedHierarchy.divisionId,\n        division: selectedHierarchy.division,\n        // Include category info (required)\n        categoryId: selectedHierarchy.categoryId,\n        category: selectedHierarchy.category,\n        // Include subcategory info if selected (optional)\n        subCategoryId: selectedHierarchy.subCategoryId || null,\n        subCategory: selectedHierarchy.subCategory || null\n      };\n\n      const config = {\n        name: formName,\n        description: formDescription,\n        fields: selectedFields,\n        hierarchy: completeHierarchy,\n        settings: {\n          showSections: true,\n          allowConditionalFields: true,\n          validateOnChange: true\n        }\n      };\n\n      let savedConfig;\n      // Save form with the most specific level selected, but include complete hierarchy\n      if (selectedHierarchy.subCategoryId) {\n        savedConfig = formConfigService.saveFormConfig('subcategory', selectedHierarchy.subCategoryId, config);\n      } else if (selectedHierarchy.categoryId) {\n        savedConfig = formConfigService.saveFormConfig('category', selectedHierarchy.categoryId, config);\n      } else {\n        throw new Error('Please select both division and category');\n      }\n\n      // Reload saved forms list\n      loadSavedForms();\n\n      if (onSave) {\n        onSave(savedConfig);\n      } else {\n        // Show success message for standalone usage\n        alert('Form configuration saved successfully!');\n        // Reset form\n        setFormName('');\n        setFormDescription('');\n        setSelectedFields([]);\n        setSelectedHierarchy({});\n      }\n    } catch (error) {\n      console.error('Error saving form:', error);\n      setErrors({ general: error.message });\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  const validateForm = () => {\n    const errors = {};\n\n    if (!formName.trim()) {\n      errors.formName = 'Form name is required';\n    }\n\n    // Division and category are required, subcategory is optional\n    if (!selectedHierarchy.divisionId) {\n      errors.hierarchy = 'Please select a division';\n    } else if (!selectedHierarchy.categoryId) {\n      errors.hierarchy = 'Please select a category';\n    } else if (selectedHierarchy.subCategoryId && !selectedHierarchy.categoryId) {\n      errors.hierarchy = 'Cannot select subcategory without selecting category';\n    }\n\n    // Validate form creation rules\n    if (selectedHierarchy.categoryId) {\n      const formValidation = formConfigService.validateFormCreation(\n        selectedHierarchy.categoryId,\n        selectedHierarchy.subCategoryId\n      );\n\n      if (!formValidation.isValid) {\n        errors.formCreation = formValidation.errors.join('. ');\n      }\n    }\n\n    if (selectedFields.length === 0) {\n      errors.fields = 'Please select at least one field';\n    }\n\n    // Check required fields\n    const requiredFields = ['name', 'mobileNumber', 'nature'];\n    const selectedFieldKeys = selectedFields.map(f => f.key);\n    const missingRequired = requiredFields.filter(rf => !selectedFieldKeys.includes(rf));\n\n    if (missingRequired.length > 0) {\n      errors.requiredFields = `Required fields missing: ${missingRequired.join(', ')}`;\n    }\n\n    return {\n      isValid: Object.keys(errors).length === 0,\n      errors\n    };\n  };\n\n  const getFilteredFields = () => {\n    let filtered = availableFields;\n\n    // Filter by section\n    if (currentSection !== 'all') {\n      filtered = filtered.filter(field => field.section === currentSection);\n    }\n\n    // Filter by search term\n    if (searchTerm) {\n      filtered = filtered.filter(field => \n        field.label.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        field.key.toLowerCase().includes(searchTerm.toLowerCase())\n      );\n    }\n\n    return filtered;\n  };\n\n  const getSectionCounts = () => {\n    const counts = { all: availableFields.length };\n    Object.keys(PersonFieldDefinitions).forEach(sectionKey => {\n      counts[sectionKey] = availableFields.filter(f => f.section === sectionKey).length;\n    });\n    return counts;\n  };\n\n  const sectionCounts = getSectionCounts();\n  const filteredFields = getFilteredFields();\n\n  return (\n    <div className=\"form-builder\">\n      <div className=\"form-builder-header\">\n        <h2>Form Builder</h2>\n        <div className=\"header-actions\">\n          <button \n            type=\"button\" \n            onClick={() => setShowPreview(true)}\n            className=\"btn btn-secondary\"\n            disabled={selectedFields.length === 0}\n          >\n            Preview Form\n          </button>\n          <button\n            type=\"button\"\n            onClick={handleSave}\n            className=\"btn btn-primary\"\n            disabled={saving || errors.formCreation}\n          >\n            {saving ? 'Saving...' : 'Save Form'}\n          </button>\n          {onCancel && (\n            <button\n              type=\"button\"\n              onClick={onCancel}\n              className=\"btn btn-outline\"\n            >\n              Cancel\n            </button>\n          )}\n        </div>\n      </div>\n\n      {errors.general && (\n        <div className=\"alert alert-error\">{errors.general}</div>\n      )}\n\n      {/* Validation Status */}\n      {Object.keys(errors).length > 0 && (\n        <div className=\"validation-errors\">\n          <h4>Please fix the following issues:</h4>\n          <ul>\n            {Object.entries(errors).map(([key, message]) => (\n              <li key={key}>{message}</li>\n            ))}\n          </ul>\n        </div>\n      )}\n\n      <div className=\"form-builder-content\">\n        {/* Form Configuration Panel */}\n        <div className=\"config-panel\">\n          {/* Saved Forms Section */}\n          <div className=\"config-section\">\n            <div className=\"section-header\">\n              <h3>Saved Forms ({savedForms.length})</h3>\n              <button\n                type=\"button\"\n                className=\"btn-link\"\n                onClick={() => setShowSavedForms(!showSavedForms)}\n              >\n                {showSavedForms ? 'Hide' : 'Show'}\n              </button>\n            </div>\n\n            {showSavedForms && (\n              <div className=\"saved-forms-list\">\n                {savedForms.length === 0 ? (\n                  <p className=\"no-forms-message\">No saved forms yet. Create your first form below!</p>\n                ) : (\n                  savedForms.map((form) => (\n                    <div key={form.key} className=\"saved-form-item\">\n                      <div className=\"form-item-header\">\n                        <h4>{form.name}</h4>\n                        <span className=\"form-type-badge\">{form.type}</span>\n                      </div>\n                      <p className=\"form-description\">{form.description || 'No description'}</p>\n                      <div className=\"form-meta\">\n                        <span>{form.summary.fieldCount} fields</span>\n                        <span>•</span>\n                        <span>Updated {new Date(form.updatedAt).toLocaleDateString()}</span>\n                      </div>\n                      <div className=\"form-actions\">\n                        <button\n                          type=\"button\"\n                          className=\"btn-small btn-outline\"\n                          onClick={() => loadInitialConfig(form)}\n                        >\n                          Load\n                        </button>\n                        <button\n                          type=\"button\"\n                          className=\"btn-small btn-danger\"\n                          onClick={() => {\n                            if (window.confirm('Are you sure you want to delete this form?')) {\n                              formConfigService.deleteFormConfig(form.type, form.associatedId);\n                              loadSavedForms();\n                            }\n                          }}\n                        >\n                          Delete\n                        </button>\n                      </div>\n                    </div>\n                  ))\n                )}\n              </div>\n            )}\n          </div>\n\n          {/* Form Configuration Section */}\n          <div className=\"config-section\">\n            <h3>Form Configuration</h3>\n\n            <div className=\"form-group\">\n              <label>Form Name *</label>\n              <input\n                type=\"text\"\n                value={formName}\n                onChange={(e) => setFormName(e.target.value)}\n                placeholder=\"Enter form name\"\n                className={errors.formName ? 'error' : ''}\n              />\n              {errors.formName && <div className=\"error-message\">{errors.formName}</div>}\n            </div>\n\n            <div className=\"form-group\">\n              <label>Description</label>\n              <textarea\n                value={formDescription}\n                onChange={(e) => setFormDescription(e.target.value)}\n                placeholder=\"Enter form description\"\n                rows=\"3\"\n              />\n            </div>\n\n\n          </div>\n\n          {/* Selected Fields Panel */}\n          <div className=\"config-section\">\n            <h3>Selected Fields ({selectedFields.length})</h3>\n            {errors.fields && <div className=\"error-message\">{errors.fields}</div>}\n            {errors.requiredFields && <div className=\"error-message\">{errors.requiredFields}</div>}\n            \n            <div className=\"selected-fields\">\n              {selectedFields.length === 0 ? (\n                <div className=\"empty-state\">\n                  No fields selected. Choose fields from the available fields panel.\n                </div>\n              ) : (\n                selectedFields.map((field, index) => (\n                  <div key={field.key} className=\"selected-field\">\n                    <div className=\"field-info\">\n                      <span className=\"field-label\">{field.label}</span>\n                      <span className=\"field-type\">{field.type}</span>\n                      {field.required && <span className=\"required-badge\">Required</span>}\n                    </div>\n                    <div className=\"field-actions\">\n                      <button\n                        type=\"button\"\n                        onClick={() => handleFieldConfig(field)}\n                        className=\"btn-icon\"\n                        title=\"Configure field\"\n                      >\n                        ⚙️\n                      </button>\n                      <button\n                        type=\"button\"\n                        onClick={() => handleFieldMove(index, Math.max(0, index - 1))}\n                        className=\"btn-icon\"\n                        disabled={index === 0}\n                        title=\"Move up\"\n                      >\n                        ↑\n                      </button>\n                      <button\n                        type=\"button\"\n                        onClick={() => handleFieldMove(index, Math.min(selectedFields.length - 1, index + 1))}\n                        className=\"btn-icon\"\n                        disabled={index === selectedFields.length - 1}\n                        title=\"Move down\"\n                      >\n                        ↓\n                      </button>\n                      <button\n                        type=\"button\"\n                        onClick={() => handleFieldToggle(field)}\n                        className=\"btn-icon remove\"\n                        title=\"Remove field\"\n                      >\n                        ✕\n                      </button>\n                    </div>\n                  </div>\n                ))\n              )}\n            </div>\n          </div>\n        </div>\n\n        {/* Available Fields Panel */}\n        <div className=\"fields-panel\">\n          <div className=\"fields-header\">\n            <h3>Available Fields</h3>\n          </div>\n\n          {/* Division/Category/SubCategory Selection */}\n          <div className=\"hierarchy-selection\" style={{\n            backgroundColor: '#f8f9fa',\n            border: '1px solid #e1e5e9',\n            borderRadius: '8px',\n            padding: '1.5rem',\n            margin: '1rem 0'\n          }}>\n            <h4 style={{ margin: '0 0 1rem 0', color: '#495057' }}>\n              Associate with Division & Category (Required) / SubCategory (Optional) *\n            </h4>\n            <div style={{\n              fontSize: '0.875rem',\n              color: '#6c757d',\n              marginBottom: '1rem',\n              padding: '0.75rem',\n              backgroundColor: '#f8f9fa',\n              borderRadius: '4px',\n              border: '1px solid #e9ecef'\n            }}>\n              <strong>Form Creation Rules:</strong>\n              <ul style={{ margin: '0.5rem 0 0 1rem', paddingLeft: '1rem' }}>\n                <li>If you create a form for a category, you cannot create forms for its subcategories</li>\n                <li>Each subcategory can have only one form</li>\n                <li>If subcategories already have forms, you cannot create a form for the parent category</li>\n              </ul>\n            </div>\n\n            <div className=\"hierarchy-dropdowns\" style={{\n              display: 'grid',\n              gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n              gap: '1rem'\n            }}>\n              {/* Division Dropdown */}\n              <div className=\"form-group\">\n                <label style={{\n                  display: 'block',\n                  marginBottom: '0.5rem',\n                  fontWeight: '500',\n                  color: '#495057'\n                }}>\n                  Division *\n                </label>\n                <select\n                  value={selectedHierarchy.divisionId || ''}\n                  onChange={handleDivisionChange}\n                  disabled={loading.divisions}\n                  className={errors.divisionId ? 'error' : ''}\n                  style={{\n                    width: '100%',\n                    padding: '0.75rem',\n                    border: '1px solid #ced4da',\n                    borderRadius: '4px',\n                    fontSize: '1rem',\n                    backgroundColor: 'white'\n                  }}\n                >\n                  <option value=\"\">Select Division</option>\n                  {divisions.map(division => (\n                    <option key={division.id} value={division.id}>\n                      {division.name}\n                    </option>\n                  ))}\n                </select>\n                {errors.divisionId && (\n                  <div style={{ color: '#dc3545', fontSize: '0.875rem', marginTop: '0.25rem' }}>\n                    {errors.divisionId}\n                  </div>\n                )}\n              </div>\n\n              {/* Category Dropdown */}\n              <div className=\"form-group\">\n                <label style={{\n                  display: 'block',\n                  marginBottom: '0.5rem',\n                  fontWeight: '500',\n                  color: '#495057'\n                }}>\n                  Category *\n                </label>\n                <select\n                  value={selectedHierarchy.categoryId || ''}\n                  onChange={handleCategoryChange}\n                  disabled={!selectedHierarchy.divisionId || loading.categories}\n                  className={errors.categoryId ? 'error' : ''}\n                  style={{\n                    width: '100%',\n                    padding: '0.75rem',\n                    border: '1px solid #ced4da',\n                    borderRadius: '4px',\n                    fontSize: '1rem',\n                    backgroundColor: 'white'\n                  }}\n                >\n                  <option value=\"\">\n                    {!selectedHierarchy.divisionId ? 'Select Division first' : 'Select Category'}\n                  </option>\n                  {categories.map(category => (\n                    <option key={category.id} value={category.id}>\n                      {category.name}\n                    </option>\n                  ))}\n                </select>\n                {errors.categoryId && (\n                  <div style={{ color: '#dc3545', fontSize: '0.875rem', marginTop: '0.25rem' }}>\n                    {errors.categoryId}\n                  </div>\n                )}\n              </div>\n\n              {/* SubCategory Dropdown */}\n              <div className=\"form-group\">\n                <label style={{\n                  display: 'block',\n                  marginBottom: '0.5rem',\n                  fontWeight: '500',\n                  color: '#495057'\n                }}>\n                  SubCategory (Optional)\n                </label>\n                <select\n                  value={selectedHierarchy.subCategoryId || ''}\n                  onChange={handleSubCategoryChange}\n                  disabled={!selectedHierarchy.categoryId || loading.subCategories}\n                  style={{\n                    width: '100%',\n                    padding: '0.75rem',\n                    border: '1px solid #ced4da',\n                    borderRadius: '4px',\n                    fontSize: '1rem',\n                    backgroundColor: 'white'\n                  }}\n                >\n                  <option value=\"\">\n                    {!selectedHierarchy.categoryId ? 'Select Category first' : 'Select SubCategory (Optional)'}\n                  </option>\n                  {subCategories.map(subCategory => (\n                    <option key={subCategory.id} value={subCategory.id}>\n                      {subCategory.name}\n                    </option>\n                  ))}\n                </select>\n              </div>\n            </div>\n\n            {errors.hierarchy && (\n              <div style={{ color: '#dc3545', fontSize: '0.875rem', marginTop: '0.5rem' }}>\n                {errors.hierarchy}\n              </div>\n            )}\n            {errors.formCreation && (\n              <div style={{ color: '#dc3545', fontSize: '0.875rem', marginTop: '0.5rem', fontWeight: 'bold' }}>\n                ⚠️ {errors.formCreation}\n              </div>\n            )}\n\n            {/* Existing Form Information */}\n            {existingFormInfo && existingFormInfo.existingForms.length > 0 && (\n              <div style={{\n                marginTop: '1rem',\n                padding: '0.75rem',\n                backgroundColor: '#fff3cd',\n                border: '1px solid #ffeaa7',\n                borderRadius: '4px',\n                fontSize: '0.875rem'\n              }}>\n                <strong>📋 Existing Forms:</strong>\n                {existingFormInfo.existingForms.map((form, index) => (\n                  <div key={index} style={{ marginTop: '0.5rem' }}>\n                    <strong>{form.type === 'category' ? 'Category' : 'SubCategory'} Form:</strong> {form.name}\n                    {form.description && <div style={{ color: '#6c757d' }}>{form.description}</div>}\n                  </div>\n                ))}\n              </div>\n            )}\n\n            {/* Subcategories with Forms Warning */}\n            {existingFormInfo && existingFormInfo.subCategoriesWithForms.length > 0 && !selectedHierarchy.subCategoryId && (\n              <div style={{\n                marginTop: '1rem',\n                padding: '0.75rem',\n                backgroundColor: '#f8d7da',\n                border: '1px solid #f5c6cb',\n                borderRadius: '4px',\n                fontSize: '0.875rem'\n              }}>\n                <strong>⚠️ Warning:</strong> This category has {existingFormInfo.subCategoriesWithForms.length} subcategory form(s).\n                You cannot create a form for this category.\n              </div>\n            )}\n\n            {/* Success Message for Valid Selection */}\n            {selectedHierarchy.categoryId && !errors.formCreation && (\n              <div style={{\n                marginTop: '1rem',\n                padding: '0.75rem',\n                backgroundColor: '#d4edda',\n                border: '1px solid #c3e6cb',\n                borderRadius: '4px',\n                fontSize: '0.875rem'\n              }}>\n                <strong>✅ Valid Selection:</strong> You can create a form for this {selectedHierarchy.subCategoryId ? 'subcategory' : 'category'}.\n              </div>\n            )}\n          </div>\n\n          {/* Search and Filter Controls */}\n          <div className=\"fields-controls\">\n            <input\n              type=\"text\"\n              placeholder=\"Search fields...\"\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              className=\"search-input\"\n            />\n          </div>\n\n          {/* Section Tabs */}\n          <div className=\"section-tabs\">\n            <button\n              type=\"button\"\n              onClick={() => setCurrentSection('all')}\n              className={`section-tab ${currentSection === 'all' ? 'active' : ''}`}\n            >\n              All ({sectionCounts.all})\n            </button>\n            {Object.entries(PersonFieldDefinitions).map(([sectionKey, section]) => (\n              <button\n                key={sectionKey}\n                type=\"button\"\n                onClick={() => setCurrentSection(sectionKey)}\n                className={`section-tab ${currentSection === sectionKey ? 'active' : ''}`}\n              >\n                {section.title} ({sectionCounts[sectionKey]})\n              </button>\n            ))}\n          </div>\n\n          {/* Fields List */}\n          <div className=\"fields-list\">\n            {filteredFields.map(field => {\n              const isSelected = selectedFields.some(f => f.key === field.key);\n              return (\n                <div\n                  key={field.key}\n                  className={`field-item ${isSelected ? 'selected' : ''}`}\n                  onClick={() => handleFieldToggle(field)}\n                >\n                  <div className=\"field-checkbox\">\n                    <input\n                      type=\"checkbox\"\n                      checked={isSelected}\n                      onChange={() => handleFieldToggle(field)}\n                    />\n                  </div>\n                  <div className=\"field-details\">\n                    <div className=\"field-name\">{field.label}</div>\n                    <div className=\"field-meta\">\n                      <span className=\"field-type\">{field.type}</span>\n                      {field.required && <span className=\"required-badge\">Required</span>}\n                      {field.conditional && <span className=\"conditional-badge\">Conditional</span>}\n                    </div>\n                  </div>\n                </div>\n              );\n            })}\n          </div>\n        </div>\n      </div>\n\n      {/* Modals */}\n      {showFieldConfig && configField && (\n        <FieldConfigModal\n          field={configField}\n          onSave={handleFieldConfigSave}\n          onCancel={() => {\n            setShowFieldConfig(false);\n            setConfigField(null);\n          }}\n        />\n      )}\n\n      {showPreview && (\n        <FormPreview\n          fields={selectedFields}\n          formName={formName}\n          onClose={() => setShowPreview(false)}\n        />\n      )}\n    </div>\n  );\n};\n\nexport default FormBuilder;\n"], "mappings": ";;AAAA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC3C,SAASC,sBAAsB,EAAEC,kBAAkB,QAAQ,iCAAiC;AAC5F,OAAOC,iBAAiB,MAAM,kCAAkC;AAChE,OAAOC,UAAU,MAAM,2BAA2B;AAClD,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAO,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3B,MAAMC,WAAW,GAAGA,CAAC;EAAEC,MAAM;EAAEC,QAAQ;EAAEC,aAAa,GAAG;AAAK,CAAC,KAAK;EAAAC,EAAA;EAClE,MAAM,CAACC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGhB,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC9D,MAAM,CAACiB,QAAQ,EAAEC,WAAW,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACmB,eAAe,EAAEC,kBAAkB,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACqB,eAAe,EAAEC,kBAAkB,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACuB,cAAc,EAAEC,iBAAiB,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACyB,cAAc,EAAEC,iBAAiB,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAAC2B,UAAU,EAAEC,aAAa,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC6B,WAAW,EAAEC,cAAc,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC+B,eAAe,EAAEC,kBAAkB,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACiC,WAAW,EAAEC,cAAc,CAAC,GAAGlC,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACmC,MAAM,EAAEC,SAAS,CAAC,GAAGpC,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxC,MAAM,CAACqC,MAAM,EAAEC,SAAS,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAACuC,UAAU,EAAEC,aAAa,CAAC,GAAGxC,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACyC,cAAc,EAAEC,iBAAiB,CAAC,GAAG1C,QAAQ,CAAC,KAAK,CAAC;;EAE3D;EACA,MAAM,CAAC2C,SAAS,EAAEC,YAAY,CAAC,GAAG5C,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC6C,UAAU,EAAEC,aAAa,CAAC,GAAG9C,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC+C,aAAa,EAAEC,gBAAgB,CAAC,GAAGhD,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACiD,OAAO,EAAEC,UAAU,CAAC,GAAGlD,QAAQ,CAAC;IACrC2C,SAAS,EAAE,KAAK;IAChBE,UAAU,EAAE,KAAK;IACjBE,aAAa,EAAE;EACjB,CAAC,CAAC;EACF,MAAM,CAACI,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGpD,QAAQ,CAAC,IAAI,CAAC;EAE9DC,SAAS,CAAC,MAAM;IACdoD,gBAAgB,CAAC,CAAC;IAClBC,cAAc,CAAC,CAAC;IAChB,IAAIzC,aAAa,EAAE;MACjB0C,iBAAiB,CAAC1C,aAAa,CAAC;IAClC;EACF,CAAC,EAAE,CAACA,aAAa,CAAC,CAAC;EAEnB,MAAMwC,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,MAAMG,SAAS,GAAGrD,kBAAkB,CAAC,CAAC;IACtCmB,kBAAkB,CAACkC,SAAS,CAAC;EAC/B,CAAC;EAED,MAAMF,cAAc,GAAGA,CAAA,KAAM;IAC3B,MAAMG,KAAK,GAAGrD,iBAAiB,CAACsD,iBAAiB,CAAC,CAAC;IACnDlB,aAAa,CAACiB,KAAK,CAAC;EACtB,CAAC;EAED,MAAMF,iBAAiB,GAAII,MAAM,IAAK;IACpCzC,WAAW,CAACyC,MAAM,CAACC,IAAI,IAAI,EAAE,CAAC;IAC9BxC,kBAAkB,CAACuC,MAAM,CAACE,WAAW,IAAI,EAAE,CAAC;;IAE5C;IACA,MAAMC,MAAM,GAAGH,MAAM,CAACG,MAAM,IAAI,EAAE;IAClC,MAAMC,kBAAkB,GAAGC,iBAAiB,CAACF,MAAM,CAAC;IACpDtC,iBAAiB,CAACuC,kBAAkB,CAAC;IAErC,IAAIJ,MAAM,CAACM,IAAI,KAAK,UAAU,IAAIN,MAAM,CAACO,YAAY,EAAE;MACrD;MACAlD,oBAAoB,CAAC;QACnBmD,UAAU,EAAER,MAAM,CAACO;MACrB,CAAC,CAAC;IACJ,CAAC,MAAM,IAAIP,MAAM,CAACM,IAAI,KAAK,UAAU,IAAIN,MAAM,CAACO,YAAY,EAAE;MAC5D;MACAlD,oBAAoB,CAAC;QACnBoD,UAAU,EAAET,MAAM,CAACO;MACrB,CAAC,CAAC;IACJ,CAAC,MAAM,IAAIP,MAAM,CAACM,IAAI,KAAK,aAAa,IAAIN,MAAM,CAACO,YAAY,EAAE;MAC/D;MACAlD,oBAAoB,CAAC;QACnBqD,aAAa,EAAEV,MAAM,CAACO;MACxB,CAAC,CAAC;IACJ;;IAEA;IACA,IAAIP,MAAM,CAACW,SAAS,EAAE;MACpBtD,oBAAoB,CAAC2C,MAAM,CAACW,SAAS,CAAC;IACxC;EACF,CAAC;;EAED;EACArE,SAAS,CAAC,MAAM;IACdsE,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAtE,SAAS,CAAC,MAAM;IACd,IAAIc,iBAAiB,CAACqD,UAAU,EAAE;MAChC,MAAMI,cAAc,GAAGpE,iBAAiB,CAACqE,oBAAoB,CAC3D1D,iBAAiB,CAACqD,UAAU,EAC5BrD,iBAAiB,CAACsD,aACpB,CAAC;MAED,MAAMK,QAAQ,GAAGtE,iBAAiB,CAACuE,mBAAmB,CACpD5D,iBAAiB,CAACqD,UAAU,EAC5BrD,iBAAiB,CAACsD,aACpB,CAAC;MACDjB,mBAAmB,CAACsB,QAAQ,CAAC;MAE7B,IAAI,CAACF,cAAc,CAACI,OAAO,EAAE;QAC3BxC,SAAS,CAACyC,IAAI,KAAK;UACjB,GAAGA,IAAI;UACPC,YAAY,EAAEN,cAAc,CAACrC,MAAM,CAAC4C,IAAI,CAAC,IAAI;QAC/C,CAAC,CAAC,CAAC;MACL,CAAC,MAAM;QACL3C,SAAS,CAACyC,IAAI,IAAI;UAChB,MAAMG,SAAS,GAAG;YAAE,GAAGH;UAAK,CAAC;UAC7B,OAAOG,SAAS,CAACF,YAAY;UAC7B,OAAOE,SAAS;QAClB,CAAC,CAAC;MACJ;IACF,CAAC,MAAM;MACL5B,mBAAmB,CAAC,IAAI,CAAC;MACzBhB,SAAS,CAACyC,IAAI,IAAI;QAChB,MAAMG,SAAS,GAAG;UAAE,GAAGH;QAAK,CAAC;QAC7B,OAAOG,SAAS,CAACF,YAAY;QAC7B,OAAOE,SAAS;MAClB,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACjE,iBAAiB,CAACqD,UAAU,EAAErD,iBAAiB,CAACsD,aAAa,CAAC,CAAC;EAEnE,MAAME,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChCrB,UAAU,CAAC2B,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAElC,SAAS,EAAE;IAAK,CAAC,CAAC,CAAC;IAClD,IAAI;MACF,MAAMsC,QAAQ,GAAG,MAAM5E,UAAU,CAAC6E,YAAY,CAAC,CAAC;MAChDtC,YAAY,CAACqC,QAAQ,CAACE,IAAI,IAAI,EAAE,CAAC;IACnC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;IAClD,CAAC,SAAS;MACRlC,UAAU,CAAC2B,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAElC,SAAS,EAAE;MAAM,CAAC,CAAC,CAAC;IACrD;EACF,CAAC;EAED,MAAM2C,cAAc,GAAG,MAAOnB,UAAU,IAAK;IAC3C,IAAI,CAACA,UAAU,EAAE;MACfrB,aAAa,CAAC,EAAE,CAAC;MACjBE,gBAAgB,CAAC,EAAE,CAAC;MACpB;IACF;IAEAE,UAAU,CAAC2B,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAEhC,UAAU,EAAE;IAAK,CAAC,CAAC,CAAC;IACnD,IAAI;MACF,MAAMoC,QAAQ,GAAG,MAAM5E,UAAU,CAACkF,uBAAuB,CAACpB,UAAU,CAAC;MACrErB,aAAa,CAACmC,QAAQ,CAACE,IAAI,IAAI,EAAE,CAAC;IACpC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjDtC,aAAa,CAAC,EAAE,CAAC;IACnB,CAAC,SAAS;MACRI,UAAU,CAAC2B,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEhC,UAAU,EAAE;MAAM,CAAC,CAAC,CAAC;IACtD;EACF,CAAC;EAED,MAAM2C,iBAAiB,GAAG,MAAOpB,UAAU,IAAK;IAC9C,IAAI,CAACA,UAAU,EAAE;MACfpB,gBAAgB,CAAC,EAAE,CAAC;MACpB;IACF;IAEAE,UAAU,CAAC2B,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE9B,aAAa,EAAE;IAAK,CAAC,CAAC,CAAC;IACtD,IAAI;MACF,MAAMkC,QAAQ,GAAG,MAAM5E,UAAU,CAACoF,0BAA0B,CAACrB,UAAU,CAAC;MACxEpB,gBAAgB,CAACiC,QAAQ,CAACE,IAAI,IAAI,EAAE,CAAC;IACvC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpDpC,gBAAgB,CAAC,EAAE,CAAC;IACtB,CAAC,SAAS;MACRE,UAAU,CAAC2B,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE9B,aAAa,EAAE;MAAM,CAAC,CAAC,CAAC;IACzD;EACF,CAAC;EAED,MAAM2C,oBAAoB,GAAIC,CAAC,IAAK;IAClC,MAAMxB,UAAU,GAAGwB,CAAC,CAACC,MAAM,CAACC,KAAK;IACjC,MAAMC,QAAQ,GAAGnD,SAAS,CAACoD,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAKC,QAAQ,CAAC/B,UAAU,CAAC,CAAC;IAEnEnD,oBAAoB,CAAC;MACnBmD,UAAU,EAAEA,UAAU,IAAI,IAAI;MAC9BC,UAAU,EAAE,IAAI;MAChBC,aAAa,EAAE,IAAI;MACnByB,QAAQ,EAAEA,QAAQ,IAAI,IAAI;MAC1BK,QAAQ,EAAE,IAAI;MACdC,WAAW,EAAE;IACf,CAAC,CAAC;IAEFtD,aAAa,CAAC,EAAE,CAAC;IACjBE,gBAAgB,CAAC,EAAE,CAAC;IAEpB,IAAImB,UAAU,EAAE;MACdmB,cAAc,CAACnB,UAAU,CAAC;IAC5B;EACF,CAAC;EAED,MAAMkC,oBAAoB,GAAIV,CAAC,IAAK;IAClC,MAAMvB,UAAU,GAAGuB,CAAC,CAACC,MAAM,CAACC,KAAK;IACjC,MAAMM,QAAQ,GAAGtD,UAAU,CAACkD,IAAI,CAACO,CAAC,IAAIA,CAAC,CAACL,EAAE,KAAKC,QAAQ,CAAC9B,UAAU,CAAC,CAAC;IAEpEpD,oBAAoB,CAAC6D,IAAI,KAAK;MAC5B,GAAGA,IAAI;MACPT,UAAU,EAAEA,UAAU,IAAI,IAAI;MAC9BC,aAAa,EAAE,IAAI;MACnB8B,QAAQ,EAAEA,QAAQ,IAAI,IAAI;MAC1BC,WAAW,EAAE;IACf,CAAC,CAAC,CAAC;IAEHpD,gBAAgB,CAAC,EAAE,CAAC;IAEpB,IAAIoB,UAAU,EAAE;MACdoB,iBAAiB,CAACpB,UAAU,CAAC;IAC/B;EACF,CAAC;EAED,MAAMmC,uBAAuB,GAAIZ,CAAC,IAAK;IACrC,MAAMtB,aAAa,GAAGsB,CAAC,CAACC,MAAM,CAACC,KAAK;IACpC,MAAMO,WAAW,GAAGrD,aAAa,CAACgD,IAAI,CAACS,EAAE,IAAIA,EAAE,CAACP,EAAE,KAAKC,QAAQ,CAAC7B,aAAa,CAAC,CAAC;IAE/ErD,oBAAoB,CAAC6D,IAAI,KAAK;MAC5B,GAAGA,IAAI;MACPR,aAAa,EAAEA,aAAa,IAAI,IAAI;MACpC+B,WAAW,EAAEA,WAAW,IAAI;IAC9B,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMK,iBAAiB,GAAIC,KAAK,IAAK;IACnC,MAAMC,UAAU,GAAGpF,cAAc,CAACqF,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,GAAG,KAAKJ,KAAK,CAACI,GAAG,CAAC;IAEhE,IAAIH,UAAU,EAAE;MACd;MACAnF,iBAAiB,CAACqD,IAAI,IAAIA,IAAI,CAACkC,MAAM,CAACF,CAAC,IAAIA,CAAC,CAACC,GAAG,KAAKJ,KAAK,CAACI,GAAG,CAAC,CAAC;IAClE,CAAC,MAAM;MACL;MACAtF,iBAAiB,CAACqD,IAAI,IAAI;QACxB,MAAMmC,MAAM,GAAGnC,IAAI,CAAC+B,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,GAAG,KAAKJ,KAAK,CAACI,GAAG,CAAC;QAClD,IAAIE,MAAM,EAAE;UACV3B,OAAO,CAAC4B,IAAI,CAAC,SAASP,KAAK,CAACI,GAAG,uDAAuD,CAAC;UACvF,OAAOjC,IAAI;QACb;QACA,OAAO,CAAC,GAAGA,IAAI,EAAE;UAAE,GAAG6B;QAAM,CAAC,CAAC;MAChC,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAMQ,iBAAiB,GAAIR,KAAK,IAAK;IACnCxE,cAAc,CAACwE,KAAK,CAAC;IACrB1E,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,MAAMmF,qBAAqB,GAAIC,YAAY,IAAK;IAC9C5F,iBAAiB,CAACqD,IAAI,IACpBA,IAAI,CAACwC,GAAG,CAACR,CAAC,IAAIA,CAAC,CAACC,GAAG,KAAKM,YAAY,CAACN,GAAG,GAAGM,YAAY,GAAGP,CAAC,CAC7D,CAAC;IACD7E,kBAAkB,CAAC,KAAK,CAAC;IACzBE,cAAc,CAAC,IAAI,CAAC;EACtB,CAAC;EAED,MAAMoF,eAAe,GAAGA,CAACC,SAAS,EAAEC,OAAO,KAAK;IAC9C,MAAMC,SAAS,GAAG,CAAC,GAAGlG,cAAc,CAAC;IACrC,MAAM,CAACmG,UAAU,CAAC,GAAGD,SAAS,CAACE,MAAM,CAACJ,SAAS,EAAE,CAAC,CAAC;IACnDE,SAAS,CAACE,MAAM,CAACH,OAAO,EAAE,CAAC,EAAEE,UAAU,CAAC;IACxClG,iBAAiB,CAACiG,SAAS,CAAC;EAC9B,CAAC;EAED,MAAMG,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,MAAMC,UAAU,GAAGC,YAAY,CAAC,CAAC;IAEjC,IAAI,CAACD,UAAU,CAACjD,OAAO,EAAE;MACvBxC,SAAS,CAACyF,UAAU,CAAC1F,MAAM,CAAC;MAC5B;IACF;IAEAG,SAAS,CAAC,IAAI,CAAC;IACf,IAAI;MACF;MACA,MAAMyF,iBAAiB,GAAG;QACxB,GAAGhH,iBAAiB;QACpB;QACAoD,UAAU,EAAEpD,iBAAiB,CAACoD,UAAU;QACxC2B,QAAQ,EAAE/E,iBAAiB,CAAC+E,QAAQ;QACpC;QACA1B,UAAU,EAAErD,iBAAiB,CAACqD,UAAU;QACxC+B,QAAQ,EAAEpF,iBAAiB,CAACoF,QAAQ;QACpC;QACA9B,aAAa,EAAEtD,iBAAiB,CAACsD,aAAa,IAAI,IAAI;QACtD+B,WAAW,EAAErF,iBAAiB,CAACqF,WAAW,IAAI;MAChD,CAAC;MAED,MAAMzC,MAAM,GAAG;QACbC,IAAI,EAAE3C,QAAQ;QACd4C,WAAW,EAAE1C,eAAe;QAC5B2C,MAAM,EAAEvC,cAAc;QACtB+C,SAAS,EAAEyD,iBAAiB;QAC5BC,QAAQ,EAAE;UACRC,YAAY,EAAE,IAAI;UAClBC,sBAAsB,EAAE,IAAI;UAC5BC,gBAAgB,EAAE;QACpB;MACF,CAAC;MAED,IAAIC,WAAW;MACf;MACA,IAAIrH,iBAAiB,CAACsD,aAAa,EAAE;QACnC+D,WAAW,GAAGhI,iBAAiB,CAACiI,cAAc,CAAC,aAAa,EAAEtH,iBAAiB,CAACsD,aAAa,EAAEV,MAAM,CAAC;MACxG,CAAC,MAAM,IAAI5C,iBAAiB,CAACqD,UAAU,EAAE;QACvCgE,WAAW,GAAGhI,iBAAiB,CAACiI,cAAc,CAAC,UAAU,EAAEtH,iBAAiB,CAACqD,UAAU,EAAET,MAAM,CAAC;MAClG,CAAC,MAAM;QACL,MAAM,IAAI2E,KAAK,CAAC,0CAA0C,CAAC;MAC7D;;MAEA;MACAhF,cAAc,CAAC,CAAC;MAEhB,IAAI3C,MAAM,EAAE;QACVA,MAAM,CAACyH,WAAW,CAAC;MACrB,CAAC,MAAM;QACL;QACAG,KAAK,CAAC,wCAAwC,CAAC;QAC/C;QACArH,WAAW,CAAC,EAAE,CAAC;QACfE,kBAAkB,CAAC,EAAE,CAAC;QACtBI,iBAAiB,CAAC,EAAE,CAAC;QACrBR,oBAAoB,CAAC,CAAC,CAAC,CAAC;MAC1B;IACF,CAAC,CAAC,OAAOoE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1ChD,SAAS,CAAC;QAAEoG,OAAO,EAAEpD,KAAK,CAACqD;MAAQ,CAAC,CAAC;IACvC,CAAC,SAAS;MACRnG,SAAS,CAAC,KAAK,CAAC;IAClB;EACF,CAAC;EAED,MAAMwF,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAM3F,MAAM,GAAG,CAAC,CAAC;IAEjB,IAAI,CAAClB,QAAQ,CAACyH,IAAI,CAAC,CAAC,EAAE;MACpBvG,MAAM,CAAClB,QAAQ,GAAG,uBAAuB;IAC3C;;IAEA;IACA,IAAI,CAACF,iBAAiB,CAACoD,UAAU,EAAE;MACjChC,MAAM,CAACmC,SAAS,GAAG,0BAA0B;IAC/C,CAAC,MAAM,IAAI,CAACvD,iBAAiB,CAACqD,UAAU,EAAE;MACxCjC,MAAM,CAACmC,SAAS,GAAG,0BAA0B;IAC/C,CAAC,MAAM,IAAIvD,iBAAiB,CAACsD,aAAa,IAAI,CAACtD,iBAAiB,CAACqD,UAAU,EAAE;MAC3EjC,MAAM,CAACmC,SAAS,GAAG,sDAAsD;IAC3E;;IAEA;IACA,IAAIvD,iBAAiB,CAACqD,UAAU,EAAE;MAChC,MAAMI,cAAc,GAAGpE,iBAAiB,CAACqE,oBAAoB,CAC3D1D,iBAAiB,CAACqD,UAAU,EAC5BrD,iBAAiB,CAACsD,aACpB,CAAC;MAED,IAAI,CAACG,cAAc,CAACI,OAAO,EAAE;QAC3BzC,MAAM,CAAC2C,YAAY,GAAGN,cAAc,CAACrC,MAAM,CAAC4C,IAAI,CAAC,IAAI,CAAC;MACxD;IACF;IAEA,IAAIxD,cAAc,CAACoH,MAAM,KAAK,CAAC,EAAE;MAC/BxG,MAAM,CAAC2B,MAAM,GAAG,kCAAkC;IACpD;;IAEA;IACA,MAAM8E,cAAc,GAAG,CAAC,MAAM,EAAE,cAAc,EAAE,QAAQ,CAAC;IACzD,MAAMC,iBAAiB,GAAGtH,cAAc,CAAC8F,GAAG,CAACR,CAAC,IAAIA,CAAC,CAACC,GAAG,CAAC;IACxD,MAAMgC,eAAe,GAAGF,cAAc,CAAC7B,MAAM,CAACgC,EAAE,IAAI,CAACF,iBAAiB,CAACG,QAAQ,CAACD,EAAE,CAAC,CAAC;IAEpF,IAAID,eAAe,CAACH,MAAM,GAAG,CAAC,EAAE;MAC9BxG,MAAM,CAACyG,cAAc,GAAG,4BAA4BE,eAAe,CAAC/D,IAAI,CAAC,IAAI,CAAC,EAAE;IAClF;IAEA,OAAO;MACLH,OAAO,EAAEqE,MAAM,CAACC,IAAI,CAAC/G,MAAM,CAAC,CAACwG,MAAM,KAAK,CAAC;MACzCxG;IACF,CAAC;EACH,CAAC;EAED,MAAMgH,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,IAAIC,QAAQ,GAAG/H,eAAe;;IAE9B;IACA,IAAII,cAAc,KAAK,KAAK,EAAE;MAC5B2H,QAAQ,GAAGA,QAAQ,CAACrC,MAAM,CAACL,KAAK,IAAIA,KAAK,CAAC2C,OAAO,KAAK5H,cAAc,CAAC;IACvE;;IAEA;IACA,IAAIE,UAAU,EAAE;MACdyH,QAAQ,GAAGA,QAAQ,CAACrC,MAAM,CAACL,KAAK,IAC9BA,KAAK,CAAC4C,KAAK,CAACC,WAAW,CAAC,CAAC,CAACP,QAAQ,CAACrH,UAAU,CAAC4H,WAAW,CAAC,CAAC,CAAC,IAC5D7C,KAAK,CAACI,GAAG,CAACyC,WAAW,CAAC,CAAC,CAACP,QAAQ,CAACrH,UAAU,CAAC4H,WAAW,CAAC,CAAC,CAC3D,CAAC;IACH;IAEA,OAAOH,QAAQ;EACjB,CAAC;EAED,MAAMI,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,MAAMC,MAAM,GAAG;MAAEC,GAAG,EAAErI,eAAe,CAACsH;IAAO,CAAC;IAC9CM,MAAM,CAACC,IAAI,CAAChJ,sBAAsB,CAAC,CAACyJ,OAAO,CAACC,UAAU,IAAI;MACxDH,MAAM,CAACG,UAAU,CAAC,GAAGvI,eAAe,CAAC0F,MAAM,CAACF,CAAC,IAAIA,CAAC,CAACwC,OAAO,KAAKO,UAAU,CAAC,CAACjB,MAAM;IACnF,CAAC,CAAC;IACF,OAAOc,MAAM;EACf,CAAC;EAED,MAAMI,aAAa,GAAGL,gBAAgB,CAAC,CAAC;EACxC,MAAMM,cAAc,GAAGX,iBAAiB,CAAC,CAAC;EAE1C,oBACE1I,OAAA;IAAKsJ,SAAS,EAAC,cAAc;IAAAC,QAAA,gBAC3BvJ,OAAA;MAAKsJ,SAAS,EAAC,qBAAqB;MAAAC,QAAA,gBAClCvJ,OAAA;QAAAuJ,QAAA,EAAI;MAAY;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACrB3J,OAAA;QAAKsJ,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BvJ,OAAA;UACEwD,IAAI,EAAC,QAAQ;UACboG,OAAO,EAAEA,CAAA,KAAMvI,cAAc,CAAC,IAAI,CAAE;UACpCiI,SAAS,EAAC,mBAAmB;UAC7BO,QAAQ,EAAE/I,cAAc,CAACoH,MAAM,KAAK,CAAE;UAAAqB,QAAA,EACvC;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT3J,OAAA;UACEwD,IAAI,EAAC,QAAQ;UACboG,OAAO,EAAEzC,UAAW;UACpBmC,SAAS,EAAC,iBAAiB;UAC3BO,QAAQ,EAAEjI,MAAM,IAAIF,MAAM,CAAC2C,YAAa;UAAAkF,QAAA,EAEvC3H,MAAM,GAAG,WAAW,GAAG;QAAW;UAAA4H,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC,EACRxJ,QAAQ,iBACPH,OAAA;UACEwD,IAAI,EAAC,QAAQ;UACboG,OAAO,EAAEzJ,QAAS;UAClBmJ,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAC5B;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAELjI,MAAM,CAACqG,OAAO,iBACb/H,OAAA;MAAKsJ,SAAS,EAAC,mBAAmB;MAAAC,QAAA,EAAE7H,MAAM,CAACqG;IAAO;MAAAyB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CACzD,EAGAnB,MAAM,CAACC,IAAI,CAAC/G,MAAM,CAAC,CAACwG,MAAM,GAAG,CAAC,iBAC7BlI,OAAA;MAAKsJ,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChCvJ,OAAA;QAAAuJ,QAAA,EAAI;MAAgC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACzC3J,OAAA;QAAAuJ,QAAA,EACGf,MAAM,CAACsB,OAAO,CAACpI,MAAM,CAAC,CAACkF,GAAG,CAAC,CAAC,CAACP,GAAG,EAAE2B,OAAO,CAAC,kBACzChI,OAAA;UAAAuJ,QAAA,EAAevB;QAAO,GAAb3B,GAAG;UAAAmD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAe,CAC5B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CACN,eAED3J,OAAA;MAAKsJ,SAAS,EAAC,sBAAsB;MAAAC,QAAA,gBAEnCvJ,OAAA;QAAKsJ,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAE3BvJ,OAAA;UAAKsJ,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BvJ,OAAA;YAAKsJ,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BvJ,OAAA;cAAAuJ,QAAA,GAAI,eAAa,EAACzH,UAAU,CAACoG,MAAM,EAAC,GAAC;YAAA;cAAAsB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC1C3J,OAAA;cACEwD,IAAI,EAAC,QAAQ;cACb8F,SAAS,EAAC,UAAU;cACpBM,OAAO,EAAEA,CAAA,KAAM3H,iBAAiB,CAAC,CAACD,cAAc,CAAE;cAAAuH,QAAA,EAEjDvH,cAAc,GAAG,MAAM,GAAG;YAAM;cAAAwH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,EAEL3H,cAAc,iBACbhC,OAAA;YAAKsJ,SAAS,EAAC,kBAAkB;YAAAC,QAAA,EAC9BzH,UAAU,CAACoG,MAAM,KAAK,CAAC,gBACtBlI,OAAA;cAAGsJ,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAC;YAAiD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,GAErF7H,UAAU,CAAC8E,GAAG,CAAEmD,IAAI,iBAClB/J,OAAA;cAAoBsJ,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAC7CvJ,OAAA;gBAAKsJ,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,gBAC/BvJ,OAAA;kBAAAuJ,QAAA,EAAKQ,IAAI,CAAC5G;gBAAI;kBAAAqG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACpB3J,OAAA;kBAAMsJ,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,EAAEQ,IAAI,CAACvG;gBAAI;kBAAAgG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD,CAAC,eACN3J,OAAA;gBAAGsJ,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,EAAEQ,IAAI,CAAC3G,WAAW,IAAI;cAAgB;gBAAAoG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC1E3J,OAAA;gBAAKsJ,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxBvJ,OAAA;kBAAAuJ,QAAA,GAAOQ,IAAI,CAACC,OAAO,CAACC,UAAU,EAAC,SAAO;gBAAA;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC7C3J,OAAA;kBAAAuJ,QAAA,EAAM;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACd3J,OAAA;kBAAAuJ,QAAA,GAAM,UAAQ,EAAC,IAAIW,IAAI,CAACH,IAAI,CAACI,SAAS,CAAC,CAACC,kBAAkB,CAAC,CAAC;gBAAA;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjE,CAAC,eACN3J,OAAA;gBAAKsJ,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBAC3BvJ,OAAA;kBACEwD,IAAI,EAAC,QAAQ;kBACb8F,SAAS,EAAC,uBAAuB;kBACjCM,OAAO,EAAEA,CAAA,KAAM9G,iBAAiB,CAACiH,IAAI,CAAE;kBAAAR,QAAA,EACxC;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACT3J,OAAA;kBACEwD,IAAI,EAAC,QAAQ;kBACb8F,SAAS,EAAC,sBAAsB;kBAChCM,OAAO,EAAEA,CAAA,KAAM;oBACb,IAAIS,MAAM,CAACC,OAAO,CAAC,4CAA4C,CAAC,EAAE;sBAChE3K,iBAAiB,CAAC4K,gBAAgB,CAACR,IAAI,CAACvG,IAAI,EAAEuG,IAAI,CAACtG,YAAY,CAAC;sBAChEZ,cAAc,CAAC,CAAC;oBAClB;kBACF,CAAE;kBAAA0G,QAAA,EACH;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA,GA/BEI,IAAI,CAAC1D,GAAG;cAAAmD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAgCb,CACN;UACF;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGN3J,OAAA;UAAKsJ,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BvJ,OAAA;YAAAuJ,QAAA,EAAI;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAE3B3J,OAAA;YAAKsJ,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBvJ,OAAA;cAAAuJ,QAAA,EAAO;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC1B3J,OAAA;cACEwD,IAAI,EAAC,MAAM;cACX4B,KAAK,EAAE5E,QAAS;cAChBgK,QAAQ,EAAGtF,CAAC,IAAKzE,WAAW,CAACyE,CAAC,CAACC,MAAM,CAACC,KAAK,CAAE;cAC7CqF,WAAW,EAAC,iBAAiB;cAC7BnB,SAAS,EAAE5H,MAAM,CAAClB,QAAQ,GAAG,OAAO,GAAG;YAAG;cAAAgJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC,EACDjI,MAAM,CAAClB,QAAQ,iBAAIR,OAAA;cAAKsJ,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAE7H,MAAM,CAAClB;YAAQ;cAAAgJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvE,CAAC,eAEN3J,OAAA;YAAKsJ,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBvJ,OAAA;cAAAuJ,QAAA,EAAO;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC1B3J,OAAA;cACEoF,KAAK,EAAE1E,eAAgB;cACvB8J,QAAQ,EAAGtF,CAAC,IAAKvE,kBAAkB,CAACuE,CAAC,CAACC,MAAM,CAACC,KAAK,CAAE;cACpDqF,WAAW,EAAC,wBAAwB;cACpCC,IAAI,EAAC;YAAG;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAGH,CAAC,eAGN3J,OAAA;UAAKsJ,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BvJ,OAAA;YAAAuJ,QAAA,GAAI,mBAAiB,EAACzI,cAAc,CAACoH,MAAM,EAAC,GAAC;UAAA;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EACjDjI,MAAM,CAAC2B,MAAM,iBAAIrD,OAAA;YAAKsJ,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAE7H,MAAM,CAAC2B;UAAM;YAAAmG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,EACrEjI,MAAM,CAACyG,cAAc,iBAAInI,OAAA;YAAKsJ,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAE7H,MAAM,CAACyG;UAAc;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAEtF3J,OAAA;YAAKsJ,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAC7BzI,cAAc,CAACoH,MAAM,KAAK,CAAC,gBAC1BlI,OAAA;cAAKsJ,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAE7B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,GAEN7I,cAAc,CAAC8F,GAAG,CAAC,CAACX,KAAK,EAAE0E,KAAK,kBAC9B3K,OAAA;cAAqBsJ,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7CvJ,OAAA;gBAAKsJ,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBvJ,OAAA;kBAAMsJ,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAEtD,KAAK,CAAC4C;gBAAK;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAClD3J,OAAA;kBAAMsJ,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAEtD,KAAK,CAACzC;gBAAI;kBAAAgG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,EAC/C1D,KAAK,CAAC2E,QAAQ,iBAAI5K,OAAA;kBAAMsJ,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChE,CAAC,eACN3J,OAAA;gBAAKsJ,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC5BvJ,OAAA;kBACEwD,IAAI,EAAC,QAAQ;kBACboG,OAAO,EAAEA,CAAA,KAAMnD,iBAAiB,CAACR,KAAK,CAAE;kBACxCqD,SAAS,EAAC,UAAU;kBACpBuB,KAAK,EAAC,iBAAiB;kBAAAtB,QAAA,EACxB;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACT3J,OAAA;kBACEwD,IAAI,EAAC,QAAQ;kBACboG,OAAO,EAAEA,CAAA,KAAM/C,eAAe,CAAC8D,KAAK,EAAEG,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEJ,KAAK,GAAG,CAAC,CAAC,CAAE;kBAC9DrB,SAAS,EAAC,UAAU;kBACpBO,QAAQ,EAAEc,KAAK,KAAK,CAAE;kBACtBE,KAAK,EAAC,SAAS;kBAAAtB,QAAA,EAChB;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACT3J,OAAA;kBACEwD,IAAI,EAAC,QAAQ;kBACboG,OAAO,EAAEA,CAAA,KAAM/C,eAAe,CAAC8D,KAAK,EAAEG,IAAI,CAACE,GAAG,CAAClK,cAAc,CAACoH,MAAM,GAAG,CAAC,EAAEyC,KAAK,GAAG,CAAC,CAAC,CAAE;kBACtFrB,SAAS,EAAC,UAAU;kBACpBO,QAAQ,EAAEc,KAAK,KAAK7J,cAAc,CAACoH,MAAM,GAAG,CAAE;kBAC9C2C,KAAK,EAAC,WAAW;kBAAAtB,QAAA,EAClB;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACT3J,OAAA;kBACEwD,IAAI,EAAC,QAAQ;kBACboG,OAAO,EAAEA,CAAA,KAAM5D,iBAAiB,CAACC,KAAK,CAAE;kBACxCqD,SAAS,EAAC,iBAAiB;kBAC3BuB,KAAK,EAAC,cAAc;kBAAAtB,QAAA,EACrB;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA,GAzCE1D,KAAK,CAACI,GAAG;cAAAmD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA0Cd,CACN;UACF;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN3J,OAAA;QAAKsJ,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BvJ,OAAA;UAAKsJ,SAAS,EAAC,eAAe;UAAAC,QAAA,eAC5BvJ,OAAA;YAAAuJ,QAAA,EAAI;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC,eAGN3J,OAAA;UAAKsJ,SAAS,EAAC,qBAAqB;UAAC2B,KAAK,EAAE;YAC1CC,eAAe,EAAE,SAAS;YAC1BC,MAAM,EAAE,mBAAmB;YAC3BC,YAAY,EAAE,KAAK;YACnBC,OAAO,EAAE,QAAQ;YACjBC,MAAM,EAAE;UACV,CAAE;UAAA/B,QAAA,gBACAvJ,OAAA;YAAIiL,KAAK,EAAE;cAAEK,MAAM,EAAE,YAAY;cAAEC,KAAK,EAAE;YAAU,CAAE;YAAAhC,QAAA,EAAC;UAEvD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACL3J,OAAA;YAAKiL,KAAK,EAAE;cACVO,QAAQ,EAAE,UAAU;cACpBD,KAAK,EAAE,SAAS;cAChBE,YAAY,EAAE,MAAM;cACpBJ,OAAO,EAAE,SAAS;cAClBH,eAAe,EAAE,SAAS;cAC1BE,YAAY,EAAE,KAAK;cACnBD,MAAM,EAAE;YACV,CAAE;YAAA5B,QAAA,gBACAvJ,OAAA;cAAAuJ,QAAA,EAAQ;YAAoB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACrC3J,OAAA;cAAIiL,KAAK,EAAE;gBAAEK,MAAM,EAAE,iBAAiB;gBAAEI,WAAW,EAAE;cAAO,CAAE;cAAAnC,QAAA,gBAC5DvJ,OAAA;gBAAAuJ,QAAA,EAAI;cAAkF;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC3F3J,OAAA;gBAAAuJ,QAAA,EAAI;cAAuC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChD3J,OAAA;gBAAAuJ,QAAA,EAAI;cAAqF;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5F,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAEN3J,OAAA;YAAKsJ,SAAS,EAAC,qBAAqB;YAAC2B,KAAK,EAAE;cAC1CU,OAAO,EAAE,MAAM;cACfC,mBAAmB,EAAE,sCAAsC;cAC3DC,GAAG,EAAE;YACP,CAAE;YAAAtC,QAAA,gBAEAvJ,OAAA;cAAKsJ,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBvJ,OAAA;gBAAOiL,KAAK,EAAE;kBACZU,OAAO,EAAE,OAAO;kBAChBF,YAAY,EAAE,QAAQ;kBACtBK,UAAU,EAAE,KAAK;kBACjBP,KAAK,EAAE;gBACT,CAAE;gBAAAhC,QAAA,EAAC;cAEH;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR3J,OAAA;gBACEoF,KAAK,EAAE9E,iBAAiB,CAACoD,UAAU,IAAI,EAAG;gBAC1C8G,QAAQ,EAAEvF,oBAAqB;gBAC/B4E,QAAQ,EAAErH,OAAO,CAACN,SAAU;gBAC5BoH,SAAS,EAAE5H,MAAM,CAACgC,UAAU,GAAG,OAAO,GAAG,EAAG;gBAC5CuH,KAAK,EAAE;kBACLc,KAAK,EAAE,MAAM;kBACbV,OAAO,EAAE,SAAS;kBAClBF,MAAM,EAAE,mBAAmB;kBAC3BC,YAAY,EAAE,KAAK;kBACnBI,QAAQ,EAAE,MAAM;kBAChBN,eAAe,EAAE;gBACnB,CAAE;gBAAA3B,QAAA,gBAEFvJ,OAAA;kBAAQoF,KAAK,EAAC,EAAE;kBAAAmE,QAAA,EAAC;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EACxCzH,SAAS,CAAC0E,GAAG,CAACvB,QAAQ,iBACrBrF,OAAA;kBAA0BoF,KAAK,EAAEC,QAAQ,CAACG,EAAG;kBAAA+D,QAAA,EAC1ClE,QAAQ,CAAClC;gBAAI,GADHkC,QAAQ,CAACG,EAAE;kBAAAgE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEhB,CACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC,EACRjI,MAAM,CAACgC,UAAU,iBAChB1D,OAAA;gBAAKiL,KAAK,EAAE;kBAAEM,KAAK,EAAE,SAAS;kBAAEC,QAAQ,EAAE,UAAU;kBAAEQ,SAAS,EAAE;gBAAU,CAAE;gBAAAzC,QAAA,EAC1E7H,MAAM,CAACgC;cAAU;gBAAA8F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAGN3J,OAAA;cAAKsJ,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBvJ,OAAA;gBAAOiL,KAAK,EAAE;kBACZU,OAAO,EAAE,OAAO;kBAChBF,YAAY,EAAE,QAAQ;kBACtBK,UAAU,EAAE,KAAK;kBACjBP,KAAK,EAAE;gBACT,CAAE;gBAAAhC,QAAA,EAAC;cAEH;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR3J,OAAA;gBACEoF,KAAK,EAAE9E,iBAAiB,CAACqD,UAAU,IAAI,EAAG;gBAC1C6G,QAAQ,EAAE5E,oBAAqB;gBAC/BiE,QAAQ,EAAE,CAACvJ,iBAAiB,CAACoD,UAAU,IAAIlB,OAAO,CAACJ,UAAW;gBAC9DkH,SAAS,EAAE5H,MAAM,CAACiC,UAAU,GAAG,OAAO,GAAG,EAAG;gBAC5CsH,KAAK,EAAE;kBACLc,KAAK,EAAE,MAAM;kBACbV,OAAO,EAAE,SAAS;kBAClBF,MAAM,EAAE,mBAAmB;kBAC3BC,YAAY,EAAE,KAAK;kBACnBI,QAAQ,EAAE,MAAM;kBAChBN,eAAe,EAAE;gBACnB,CAAE;gBAAA3B,QAAA,gBAEFvJ,OAAA;kBAAQoF,KAAK,EAAC,EAAE;kBAAAmE,QAAA,EACb,CAACjJ,iBAAiB,CAACoD,UAAU,GAAG,uBAAuB,GAAG;gBAAiB;kBAAA8F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtE,CAAC,EACRvH,UAAU,CAACwE,GAAG,CAAClB,QAAQ,iBACtB1F,OAAA;kBAA0BoF,KAAK,EAAEM,QAAQ,CAACF,EAAG;kBAAA+D,QAAA,EAC1C7D,QAAQ,CAACvC;gBAAI,GADHuC,QAAQ,CAACF,EAAE;kBAAAgE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEhB,CACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC,EACRjI,MAAM,CAACiC,UAAU,iBAChB3D,OAAA;gBAAKiL,KAAK,EAAE;kBAAEM,KAAK,EAAE,SAAS;kBAAEC,QAAQ,EAAE,UAAU;kBAAEQ,SAAS,EAAE;gBAAU,CAAE;gBAAAzC,QAAA,EAC1E7H,MAAM,CAACiC;cAAU;gBAAA6F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAGN3J,OAAA;cAAKsJ,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBvJ,OAAA;gBAAOiL,KAAK,EAAE;kBACZU,OAAO,EAAE,OAAO;kBAChBF,YAAY,EAAE,QAAQ;kBACtBK,UAAU,EAAE,KAAK;kBACjBP,KAAK,EAAE;gBACT,CAAE;gBAAAhC,QAAA,EAAC;cAEH;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR3J,OAAA;gBACEoF,KAAK,EAAE9E,iBAAiB,CAACsD,aAAa,IAAI,EAAG;gBAC7C4G,QAAQ,EAAE1E,uBAAwB;gBAClC+D,QAAQ,EAAE,CAACvJ,iBAAiB,CAACqD,UAAU,IAAInB,OAAO,CAACF,aAAc;gBACjE2I,KAAK,EAAE;kBACLc,KAAK,EAAE,MAAM;kBACbV,OAAO,EAAE,SAAS;kBAClBF,MAAM,EAAE,mBAAmB;kBAC3BC,YAAY,EAAE,KAAK;kBACnBI,QAAQ,EAAE,MAAM;kBAChBN,eAAe,EAAE;gBACnB,CAAE;gBAAA3B,QAAA,gBAEFvJ,OAAA;kBAAQoF,KAAK,EAAC,EAAE;kBAAAmE,QAAA,EACb,CAACjJ,iBAAiB,CAACqD,UAAU,GAAG,uBAAuB,GAAG;gBAA+B;kBAAA6F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpF,CAAC,EACRrH,aAAa,CAACsE,GAAG,CAACjB,WAAW,iBAC5B3F,OAAA;kBAA6BoF,KAAK,EAAEO,WAAW,CAACH,EAAG;kBAAA+D,QAAA,EAChD5D,WAAW,CAACxC;gBAAI,GADNwC,WAAW,CAACH,EAAE;kBAAAgE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEnB,CACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAELjI,MAAM,CAACmC,SAAS,iBACf7D,OAAA;YAAKiL,KAAK,EAAE;cAAEM,KAAK,EAAE,SAAS;cAAEC,QAAQ,EAAE,UAAU;cAAEQ,SAAS,EAAE;YAAS,CAAE;YAAAzC,QAAA,EACzE7H,MAAM,CAACmC;UAAS;YAAA2F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd,CACN,EACAjI,MAAM,CAAC2C,YAAY,iBAClBrE,OAAA;YAAKiL,KAAK,EAAE;cAAEM,KAAK,EAAE,SAAS;cAAEC,QAAQ,EAAE,UAAU;cAAEQ,SAAS,EAAE,QAAQ;cAAEF,UAAU,EAAE;YAAO,CAAE;YAAAvC,QAAA,GAAC,eAC5F,EAAC7H,MAAM,CAAC2C,YAAY;UAAA;YAAAmF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB,CACN,EAGAjH,gBAAgB,IAAIA,gBAAgB,CAACuJ,aAAa,CAAC/D,MAAM,GAAG,CAAC,iBAC5DlI,OAAA;YAAKiL,KAAK,EAAE;cACVe,SAAS,EAAE,MAAM;cACjBX,OAAO,EAAE,SAAS;cAClBH,eAAe,EAAE,SAAS;cAC1BC,MAAM,EAAE,mBAAmB;cAC3BC,YAAY,EAAE,KAAK;cACnBI,QAAQ,EAAE;YACZ,CAAE;YAAAjC,QAAA,gBACAvJ,OAAA;cAAAuJ,QAAA,EAAQ;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EAClCjH,gBAAgB,CAACuJ,aAAa,CAACrF,GAAG,CAAC,CAACmD,IAAI,EAAEY,KAAK,kBAC9C3K,OAAA;cAAiBiL,KAAK,EAAE;gBAAEe,SAAS,EAAE;cAAS,CAAE;cAAAzC,QAAA,gBAC9CvJ,OAAA;gBAAAuJ,QAAA,GAASQ,IAAI,CAACvG,IAAI,KAAK,UAAU,GAAG,UAAU,GAAG,aAAa,EAAC,QAAM;cAAA;gBAAAgG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACI,IAAI,CAAC5G,IAAI,EACxF4G,IAAI,CAAC3G,WAAW,iBAAIpD,OAAA;gBAAKiL,KAAK,EAAE;kBAAEM,KAAK,EAAE;gBAAU,CAAE;gBAAAhC,QAAA,EAAEQ,IAAI,CAAC3G;cAAW;gBAAAoG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA,GAFvEgB,KAAK;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAGV,CACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CACN,EAGAjH,gBAAgB,IAAIA,gBAAgB,CAACwJ,sBAAsB,CAAChE,MAAM,GAAG,CAAC,IAAI,CAAC5H,iBAAiB,CAACsD,aAAa,iBACzG5D,OAAA;YAAKiL,KAAK,EAAE;cACVe,SAAS,EAAE,MAAM;cACjBX,OAAO,EAAE,SAAS;cAClBH,eAAe,EAAE,SAAS;cAC1BC,MAAM,EAAE,mBAAmB;cAC3BC,YAAY,EAAE,KAAK;cACnBI,QAAQ,EAAE;YACZ,CAAE;YAAAjC,QAAA,gBACAvJ,OAAA;cAAAuJ,QAAA,EAAQ;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,uBAAmB,EAACjH,gBAAgB,CAACwJ,sBAAsB,CAAChE,MAAM,EAAC,mEAEjG;UAAA;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CACN,EAGArJ,iBAAiB,CAACqD,UAAU,IAAI,CAACjC,MAAM,CAAC2C,YAAY,iBACnDrE,OAAA;YAAKiL,KAAK,EAAE;cACVe,SAAS,EAAE,MAAM;cACjBX,OAAO,EAAE,SAAS;cAClBH,eAAe,EAAE,SAAS;cAC1BC,MAAM,EAAE,mBAAmB;cAC3BC,YAAY,EAAE,KAAK;cACnBI,QAAQ,EAAE;YACZ,CAAE;YAAAjC,QAAA,gBACAvJ,OAAA;cAAAuJ,QAAA,EAAQ;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,oCAAgC,EAACrJ,iBAAiB,CAACsD,aAAa,GAAG,aAAa,GAAG,UAAU,EAAC,GACnI;UAAA;YAAA4F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGN3J,OAAA;UAAKsJ,SAAS,EAAC,iBAAiB;UAAAC,QAAA,eAC9BvJ,OAAA;YACEwD,IAAI,EAAC,MAAM;YACXiH,WAAW,EAAC,kBAAkB;YAC9BrF,KAAK,EAAElE,UAAW;YAClBsJ,QAAQ,EAAGtF,CAAC,IAAK/D,aAAa,CAAC+D,CAAC,CAACC,MAAM,CAACC,KAAK,CAAE;YAC/CkE,SAAS,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGN3J,OAAA;UAAKsJ,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BvJ,OAAA;YACEwD,IAAI,EAAC,QAAQ;YACboG,OAAO,EAAEA,CAAA,KAAM3I,iBAAiB,CAAC,KAAK,CAAE;YACxCqI,SAAS,EAAE,eAAetI,cAAc,KAAK,KAAK,GAAG,QAAQ,GAAG,EAAE,EAAG;YAAAuI,QAAA,GACtE,OACM,EAACH,aAAa,CAACH,GAAG,EAAC,GAC1B;UAAA;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EACRnB,MAAM,CAACsB,OAAO,CAACrK,sBAAsB,CAAC,CAACmH,GAAG,CAAC,CAAC,CAACuC,UAAU,EAAEP,OAAO,CAAC,kBAChE5I,OAAA;YAEEwD,IAAI,EAAC,QAAQ;YACboG,OAAO,EAAEA,CAAA,KAAM3I,iBAAiB,CAACkI,UAAU,CAAE;YAC7CG,SAAS,EAAE,eAAetI,cAAc,KAAKmI,UAAU,GAAG,QAAQ,GAAG,EAAE,EAAG;YAAAI,QAAA,GAEzEX,OAAO,CAACiC,KAAK,EAAC,IAAE,EAACzB,aAAa,CAACD,UAAU,CAAC,EAAC,GAC9C;UAAA,GANOA,UAAU;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAMT,CACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGN3J,OAAA;UAAKsJ,SAAS,EAAC,aAAa;UAAAC,QAAA,EACzBF,cAAc,CAACzC,GAAG,CAACX,KAAK,IAAI;YAC3B,MAAMC,UAAU,GAAGpF,cAAc,CAACqF,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,GAAG,KAAKJ,KAAK,CAACI,GAAG,CAAC;YAChE,oBACErG,OAAA;cAEEsJ,SAAS,EAAE,cAAcpD,UAAU,GAAG,UAAU,GAAG,EAAE,EAAG;cACxD0D,OAAO,EAAEA,CAAA,KAAM5D,iBAAiB,CAACC,KAAK,CAAE;cAAAsD,QAAA,gBAExCvJ,OAAA;gBAAKsJ,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,eAC7BvJ,OAAA;kBACEwD,IAAI,EAAC,UAAU;kBACf2I,OAAO,EAAEjG,UAAW;kBACpBsE,QAAQ,EAAEA,CAAA,KAAMxE,iBAAiB,CAACC,KAAK;gBAAE;kBAAAuD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1C;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN3J,OAAA;gBAAKsJ,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC5BvJ,OAAA;kBAAKsJ,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAEtD,KAAK,CAAC4C;gBAAK;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC/C3J,OAAA;kBAAKsJ,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzBvJ,OAAA;oBAAMsJ,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAEtD,KAAK,CAACzC;kBAAI;oBAAAgG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,EAC/C1D,KAAK,CAAC2E,QAAQ,iBAAI5K,OAAA;oBAAMsJ,SAAS,EAAC,gBAAgB;oBAAAC,QAAA,EAAC;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,EAClE1D,KAAK,CAACmG,WAAW,iBAAIpM,OAAA;oBAAMsJ,SAAS,EAAC,mBAAmB;oBAAAC,QAAA,EAAC;kBAAW;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA,GAlBD1D,KAAK,CAACI,GAAG;cAAAmD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAmBX,CAAC;UAEV,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLrI,eAAe,IAAIE,WAAW,iBAC7BxB,OAAA,CAACH,gBAAgB;MACfoG,KAAK,EAAEzE,WAAY;MACnBtB,MAAM,EAAEwG,qBAAsB;MAC9BvG,QAAQ,EAAEA,CAAA,KAAM;QACdoB,kBAAkB,CAAC,KAAK,CAAC;QACzBE,cAAc,CAAC,IAAI,CAAC;MACtB;IAAE;MAAA+H,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACF,EAEAvI,WAAW,iBACVpB,OAAA,CAACF,WAAW;MACVuD,MAAM,EAAEvC,cAAe;MACvBN,QAAQ,EAAEA,QAAS;MACnB6L,OAAO,EAAEA,CAAA,KAAMhL,cAAc,CAAC,KAAK;IAAE;MAAAmI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtC,CACF;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACtJ,EAAA,CAr4BIJ,WAAW;AAAAqM,EAAA,GAAXrM,WAAW;AAu4BjB,eAAeA,WAAW;AAAC,IAAAqM,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}