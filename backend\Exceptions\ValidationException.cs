namespace CrmApi.Exceptions
{
    public class ValidationException : BaseException
    {
        public override int StatusCode => 422;
        public override string ErrorType => "Validation";
        public Dictionary<string, string[]> Errors { get; }

        public ValidationException(string message) : base(message) 
        {
            Errors = new Dictionary<string, string[]>();
        }

        public ValidationException(string message, Dictionary<string, string[]> errors) : base(message) 
        {
            Errors = errors;
        }

        public ValidationException(string message, Exception innerException) : base(message, innerException) 
        {
            Errors = new Dictionary<string, string[]>();
        }
    }
}
