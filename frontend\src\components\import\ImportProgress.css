.import-progress {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  padding: 1rem;
}

.progress-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
}

.loading-spinner {
  width: 3rem;
  height: 3rem;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.progress-header h3 {
  margin: 0;
  color: #495057;
}

.job-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.25rem;
}

.job-id {
  font-size: 0.875rem;
  color: #6c757d;
  font-family: monospace;
}

.job-status {
  font-weight: 600;
  font-size: 1rem;
}

.progress-section {
  background-color: #f8f9fa;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  padding: 1.5rem;
}

.progress-bar-container {
  margin-bottom: 1rem;
}

.progress-bar {
  width: 100%;
  height: 2rem;
  background-color: #e9ecef;
  border-radius: 1rem;
  overflow: hidden;
  position: relative;
}

.progress-fill {
  height: 100%;
  transition: width 0.3s ease;
  border-radius: 1rem;
}

.progress-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-weight: 600;
  color: white;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
  font-size: 0.875rem;
}

.progress-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background-color: white;
  border-radius: 4px;
  border: 1px solid #e1e5e9;
}

.detail-label {
  font-weight: 600;
  color: #495057;
}

.detail-value {
  font-weight: 700;
  color: #007bff;
}

.current-operation {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background-color: #e7f3ff;
  border: 1px solid #b3d7ff;
  border-radius: 8px;
}

.operation-icon {
  font-size: 1.5rem;
  animation: spin 2s linear infinite;
}

.operation-text {
  font-weight: 500;
  color: #0056b3;
}

.progress-stats {
  background-color: #f8f9fa;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  padding: 1.5rem;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
}

.stat-card {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid;
}

.stat-card.success {
  background-color: #d4edda;
  border-color: #c3e6cb;
}

.stat-card.error {
  background-color: #f8d7da;
  border-color: #f5c6cb;
}

.stat-card.warning {
  background-color: #fff3cd;
  border-color: #ffeaa7;
}

.stat-card.info {
  background-color: #d1ecf1;
  border-color: #bee5eb;
}

.stat-icon {
  font-size: 1.5rem;
}

.stat-content {
  display: flex;
  flex-direction: column;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 0.25rem;
}

.stat-label {
  font-size: 0.875rem;
  color: #6c757d;
  font-weight: 500;
}

.recent-errors {
  background-color: #f8f9fa;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  padding: 1.5rem;
}

.recent-errors h4 {
  margin: 0 0 1rem 0;
  color: #495057;
}

.errors-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.error-item {
  display: grid;
  grid-template-columns: auto auto 1fr;
  gap: 1rem;
  padding: 0.75rem;
  background-color: #f8d7da;
  border: 1px solid #f5c6cb;
  border-radius: 4px;
  font-size: 0.875rem;
}

.error-row {
  font-weight: 600;
  color: #721c24;
}

.error-field {
  font-weight: 500;
  color: #721c24;
}

.error-message {
  color: #721c24;
}

.more-errors {
  text-align: center;
  padding: 0.5rem;
  color: #6c757d;
  font-style: italic;
}

.processing-log {
  background-color: #f8f9fa;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  padding: 1.5rem;
}

.processing-log h4 {
  margin: 0 0 1rem 0;
  color: #495057;
}

.log-container {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  max-height: 200px;
  overflow-y: auto;
}

.log-entry {
  display: flex;
  gap: 1rem;
  padding: 0.5rem;
  background-color: white;
  border-radius: 4px;
  border: 1px solid #e1e5e9;
  font-size: 0.875rem;
}

.log-entry.current {
  background-color: #e7f3ff;
  border-color: #b3d7ff;
}

.log-time {
  font-weight: 600;
  color: #6c757d;
  font-family: monospace;
  min-width: 80px;
}

.log-message {
  color: #495057;
}

.progress-actions {
  display: flex;
  justify-content: center;
  gap: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #e1e5e9;
}

.completion-message {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  border-radius: 8px;
  text-align: center;
}

.completion-message.completed {
  background-color: #d4edda;
  border: 1px solid #c3e6cb;
}

.completion-message.failed {
  background-color: #f8d7da;
  border: 1px solid #f5c6cb;
}

.completion-message.cancelled {
  background-color: #e2e3e5;
  border: 1px solid #d6d8db;
}

.completion-icon {
  font-size: 2rem;
}

.completion-text {
  flex: 1;
}

.completion-details {
  margin-top: 0.5rem;
  font-size: 0.875rem;
  color: #6c757d;
}

.progress-error {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 1rem;
  padding: 1rem;
  background-color: #f8d7da;
  border: 1px solid #f5c6cb;
  border-radius: 4px;
  color: #721c24;
  font-weight: 500;
}

.btn {
  padding: 0.75rem 1.5rem;
  border: 1px solid transparent;
  border-radius: 4px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  text-decoration: none;
  display: inline-block;
  transition: all 0.2s ease;
}

.btn-outline {
  background-color: transparent;
  border-color: #6c757d;
  color: #6c757d;
}

.btn-outline:hover {
  background-color: #6c757d;
  color: white;
}

/* Responsive design */
@media (max-width: 768px) {
  .progress-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
  
  .job-info {
    align-items: flex-start;
  }
  
  .progress-details {
    grid-template-columns: 1fr;
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .error-item {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }
  
  .completion-message {
    flex-direction: column;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .progress-actions {
    flex-direction: column;
  }
}
