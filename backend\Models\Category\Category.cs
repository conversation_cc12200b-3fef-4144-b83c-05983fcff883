using System.Text.Json.Serialization;

namespace CrmApi.Models.Category
{
    public class Category
    {
        public int Id { get; set; }
        public int DivisionId { get; set; }
        public string Name { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
        
        [JsonIgnore]
        public Division.Division Division { get; set; } = null!;
        
        public ICollection<SubCategory.SubCategory> SubCategories { get; set; } = new List<SubCategory.SubCategory>();
    }
}
