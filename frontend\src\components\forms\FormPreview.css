.form-preview-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  padding: 1rem;
}

.form-preview-modal {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 1200px;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 1.5rem;
  border-bottom: 1px solid #e1e5e9;
  background-color: #f8f9fa;
}

.preview-title h2 {
  margin: 0 0 0.5rem 0;
  color: #495057;
}

.preview-info {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-name {
  font-weight: 600;
  color: #007bff;
  font-size: 1rem;
}

.field-stats {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.stat {
  font-size: 0.875rem;
  color: #6c757d;
  background-color: #e9ecef;
  padding: 0.25rem 0.5rem;
  border-radius: 3px;
}

.close-button {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #6c757d;
  padding: 0;
  width: 2rem;
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
}

.close-button:hover {
  background-color: #e9ecef;
  color: #495057;
}

.preview-content {
  flex: 1;
  overflow-y: auto;
  padding: 1.5rem;
}

.preview-notice {
  background-color: #e7f3ff;
  border: 1px solid #b3d7ff;
  border-radius: 4px;
  padding: 1rem;
  margin-bottom: 1.5rem;
}

.preview-notice p {
  margin: 0;
  color: #0056b3;
  font-size: 0.875rem;
}

.preview-form {
  margin-bottom: 2rem;
}

.preview-section {
  background-color: #f8f9fa;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}

.preview-section h3 {
  margin: 0 0 1.5rem 0;
  color: #495057;
  font-size: 1.25rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #007bff;
  display: inline-block;
}

.preview-fields {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.field-summary {
  background-color: #f8f9fa;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}

.field-summary h4 {
  margin: 0 0 1rem 0;
  color: #495057;
}

.summary-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem;
  background-color: white;
  border-radius: 4px;
  border: 1px solid #e1e5e9;
}

.summary-label {
  font-weight: 600;
  color: #495057;
  font-size: 0.875rem;
}

.summary-value {
  font-weight: 700;
  color: #007bff;
  font-size: 1rem;
}

.field-list {
  background-color: #f8f9fa;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  padding: 1.5rem;
}

.field-list h4 {
  margin: 0 0 1rem 0;
  color: #495057;
}

.field-items {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 0.75rem;
}

.field-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background-color: white;
  border: 1px solid #e1e5e9;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.field-item.visible {
  border-left: 4px solid #28a745;
}

.field-item.hidden {
  border-left: 4px solid #6c757d;
  opacity: 0.7;
}

.field-name {
  font-weight: 600;
  color: #495057;
}

.field-meta {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.field-type {
  font-size: 0.75rem;
  color: #6c757d;
  background-color: #e9ecef;
  padding: 0.125rem 0.375rem;
  border-radius: 3px;
}

.required-badge {
  background-color: #dc3545;
  color: white;
  padding: 0.125rem 0.375rem;
  border-radius: 3px;
  font-size: 0.75rem;
  font-weight: 600;
}

.conditional-badge {
  background-color: #ffc107;
  color: #212529;
  padding: 0.125rem 0.375rem;
  border-radius: 3px;
  font-size: 0.75rem;
  font-weight: 600;
}

.hidden-badge {
  background-color: #6c757d;
  color: white;
  padding: 0.125rem 0.375rem;
  border-radius: 3px;
  font-size: 0.75rem;
  font-weight: 600;
}

.preview-footer {
  display: flex;
  justify-content: center;
  padding: 1.5rem;
  border-top: 1px solid #e1e5e9;
  background-color: #f8f9fa;
}

.btn {
  padding: 0.75rem 1.5rem;
  border: 1px solid transparent;
  border-radius: 4px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  text-decoration: none;
  display: inline-block;
  transition: all 0.2s ease;
}

.btn-primary {
  background-color: #007bff;
  border-color: #007bff;
  color: white;
}

.btn-primary:hover {
  background-color: #0056b3;
  border-color: #0056b3;
}

/* Responsive design */
@media (max-width: 768px) {
  .form-preview-overlay {
    padding: 0.5rem;
  }
  
  .preview-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  
  .field-stats {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .preview-fields {
    grid-template-columns: 1fr;
  }
  
  .summary-grid {
    grid-template-columns: 1fr;
  }
  
  .field-items {
    grid-template-columns: 1fr;
  }
  
  .field-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
}

@media (max-width: 480px) {
  .preview-header,
  .preview-content,
  .preview-footer {
    padding: 1rem;
  }
  
  .preview-section {
    padding: 1rem;
  }
  
  .field-summary,
  .field-list {
    padding: 1rem;
  }
}
