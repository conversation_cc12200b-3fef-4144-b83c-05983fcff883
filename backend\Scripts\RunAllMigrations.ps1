# PowerShell script to run all migrations and set up the complete database
# This script creates all tables and inserts initial data

Write-Host "=== CRM Database Migration Script ===" -ForegroundColor Cyan
Write-Host "This script will create the complete database schema and insert initial data" -ForegroundColor Yellow
Write-Host ""

# Change to the backend directory
$backendPath = Split-Path -Parent $PSScriptRoot
Set-Location $backendPath

Write-Host "Current directory: $(Get-Location)" -ForegroundColor Yellow
Write-Host ""

# Check if Entity Framework tools are installed
Write-Host "Checking Entity Framework Core tools..." -ForegroundColor Green
try {
    $efVersion = dotnet ef --version
    Write-Host "✓ Entity Framework Core tools version: $efVersion" -ForegroundColor Green
} catch {
    Write-Host "⚠ Entity Framework Core tools not found. Installing..." -ForegroundColor Yellow
    dotnet tool install --global dotnet-ef
    if ($LASTEXITCODE -ne 0) {
        Write-Host "✗ Failed to install EF tools. Please install manually." -ForegroundColor Red
        exit 1
    }
    Write-Host "✓ Entity Framework Core tools installed successfully" -ForegroundColor Green
}
Write-Host ""

# Build the project first
Write-Host "Building the project..." -ForegroundColor Green
dotnet build --configuration Release

if ($LASTEXITCODE -ne 0) {
    Write-Host "✗ Build failed. Please fix compilation errors first." -ForegroundColor Red
    Write-Host ""
    Write-Host "Common issues:" -ForegroundColor Yellow
    Write-Host "- Missing using statements" -ForegroundColor White
    Write-Host "- Syntax errors in migration files" -ForegroundColor White
    Write-Host "- Missing references" -ForegroundColor White
    exit 1
}
Write-Host "✓ Project built successfully" -ForegroundColor Green
Write-Host ""

# Check current database status
Write-Host "Checking current database status..." -ForegroundColor Green
try {
    $migrations = dotnet ef migrations list 2>$null
    if ($migrations) {
        Write-Host "Current migrations:" -ForegroundColor Yellow
        Write-Host $migrations -ForegroundColor White
    } else {
        Write-Host "No existing migrations found" -ForegroundColor Yellow
    }
} catch {
    Write-Host "Could not check existing migrations (this is normal for new databases)" -ForegroundColor Yellow
}
Write-Host ""

# Remove existing migrations if they exist (for clean setup)
Write-Host "Cleaning up existing migrations..." -ForegroundColor Green
if (Test-Path "Migrations") {
    $existingMigrations = Get-ChildItem "Migrations" -Filter "*.cs" | Where-Object { $_.Name -ne "20241201000001_InitialCreate.cs" -and $_.Name -ne "20241201000002_SeedInitialData.cs" }
    if ($existingMigrations) {
        Write-Host "Removing old migration files..." -ForegroundColor Yellow
        $existingMigrations | Remove-Item -Force
        Write-Host "✓ Old migration files removed" -ForegroundColor Green
    }
}

# Drop and recreate database (optional - comment out if you want to preserve data)
Write-Host "Do you want to drop and recreate the database? (This will delete all existing data)" -ForegroundColor Yellow
$response = Read-Host "Enter 'yes' to drop and recreate, or 'no' to update existing database"
if ($response -eq "yes" -or $response -eq "y") {
    Write-Host "Dropping existing database..." -ForegroundColor Yellow
    dotnet ef database drop --force
    Write-Host "✓ Database dropped" -ForegroundColor Green
}
Write-Host ""

# Update the database with all migrations
Write-Host "Applying migrations to database..." -ForegroundColor Green
dotnet ef database update

if ($LASTEXITCODE -eq 0) {
    Write-Host ""
    Write-Host "=== MIGRATION COMPLETED SUCCESSFULLY! ===" -ForegroundColor Green
    Write-Host ""
    Write-Host "Database Schema Created:" -ForegroundColor Cyan
    Write-Host "✓ States table with all 36 Indian states and union territories" -ForegroundColor White
    Write-Host "✓ Divisions table with sample business divisions" -ForegroundColor White
    Write-Host "✓ Categories table with sample categories" -ForegroundColor White
    Write-Host "✓ SubCategories table with sample subcategories" -ForegroundColor White
    Write-Host "✓ Persons table with comprehensive person data structure" -ForegroundColor White
    Write-Host ""
    Write-Host "Features Included:" -ForegroundColor Cyan
    Write-Host "✓ Complete hierarchical structure (Division → Category → SubCategory)" -ForegroundColor White
    Write-Host "✓ Comprehensive person data model with all fields" -ForegroundColor White
    Write-Host "✓ Proper foreign key relationships and constraints" -ForegroundColor White
    Write-Host "✓ Optimized indexes for performance" -ForegroundColor White
    Write-Host "✓ Unique constraints for data integrity" -ForegroundColor White
    Write-Host "✓ Soft delete support for persons" -ForegroundColor White
    Write-Host "✓ Audit fields (created_at, updated_at)" -ForegroundColor White
    Write-Host ""
    Write-Host "Sample Data Inserted:" -ForegroundColor Cyan
    Write-Host "✓ 36 Indian states and union territories" -ForegroundColor White
    Write-Host "✓ 10 business divisions (Real Estate, Construction, Finance, etc.)" -ForegroundColor White
    Write-Host "✓ 15 categories across different divisions" -ForegroundColor White
    Write-Host "✓ 10 subcategories for detailed classification" -ForegroundColor White
    Write-Host ""
    Write-Host "Next Steps:" -ForegroundColor Cyan
    Write-Host "1. Start the API server: dotnet run" -ForegroundColor White
    Write-Host "2. Test the endpoints using Swagger UI" -ForegroundColor White
    Write-Host "3. Use the frontend to create and manage persons" -ForegroundColor White
    Write-Host "4. Import bulk data using the import functionality" -ForegroundColor White
    Write-Host ""
    Write-Host "API Endpoints Available:" -ForegroundColor Cyan
    Write-Host "• GET /api/states - List all states" -ForegroundColor White
    Write-Host "• GET /api/divisions - List all divisions" -ForegroundColor White
    Write-Host "• GET /api/categories - List categories" -ForegroundColor White
    Write-Host "• GET /api/subcategories - List subcategories" -ForegroundColor White
    Write-Host "• POST /api/persons - Create new person" -ForegroundColor White
    Write-Host "• GET /api/persons/search - Search persons" -ForegroundColor White
    Write-Host "• POST /api/import-export/persons/import - Import persons from file" -ForegroundColor White
    Write-Host ""
} else {
    Write-Host ""
    Write-Host "=== MIGRATION FAILED ===" -ForegroundColor Red
    Write-Host ""
    Write-Host "Common issues and solutions:" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "1. Connection String Issues:" -ForegroundColor Cyan
    Write-Host "   - Check appsettings.json for correct database connection" -ForegroundColor White
    Write-Host "   - Ensure SQL Server is running" -ForegroundColor White
    Write-Host "   - Verify database permissions" -ForegroundColor White
    Write-Host ""
    Write-Host "2. Migration Conflicts:" -ForegroundColor Cyan
    Write-Host "   - Remove Migrations folder and recreate: dotnet ef migrations add InitialCreate" -ForegroundColor White
    Write-Host "   - Check for duplicate migration files" -ForegroundColor White
    Write-Host ""
    Write-Host "3. Model Issues:" -ForegroundColor Cyan
    Write-Host "   - Verify all model classes are properly configured" -ForegroundColor White
    Write-Host "   - Check DbContext configuration" -ForegroundColor White
    Write-Host ""
    Write-Host "4. Database Issues:" -ForegroundColor Cyan
    Write-Host "   - Try dropping the database: dotnet ef database drop" -ForegroundColor White
    Write-Host "   - Check SQL Server logs for detailed errors" -ForegroundColor White
    Write-Host ""
    exit 1
}

Write-Host "Migration script completed successfully!" -ForegroundColor Green
Write-Host "Press any key to continue..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
