{"ast": null, "code": "'use strict';\n\nvar globalThis = require('../internals/global-this');\nvar navigator = globalThis.navigator;\nvar userAgent = navigator && navigator.userAgent;\nmodule.exports = userAgent ? String(userAgent) : '';", "map": {"version": 3, "names": ["globalThis", "require", "navigator", "userAgent", "module", "exports", "String"], "sources": ["C:/Users/<USER>/Downloads/Advanced/Advanced CRM filed all use/frontend/node_modules/core-js-pure/internals/environment-user-agent.js"], "sourcesContent": ["'use strict';\nvar globalThis = require('../internals/global-this');\n\nvar navigator = globalThis.navigator;\nvar userAgent = navigator && navigator.userAgent;\n\nmodule.exports = userAgent ? String(userAgent) : '';\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,UAAU,GAAGC,OAAO,CAAC,0BAA0B,CAAC;AAEpD,IAAIC,SAAS,GAAGF,UAAU,CAACE,SAAS;AACpC,IAAIC,SAAS,GAAGD,SAAS,IAAIA,SAAS,CAACC,SAAS;AAEhDC,MAAM,CAACC,OAAO,GAAGF,SAAS,GAAGG,MAAM,CAACH,SAAS,CAAC,GAAG,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}