{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Advanced\\\\Advanced CRM filed all use\\\\frontend\\\\src\\\\components\\\\forms\\\\DynamicPersonForm.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { PersonNatureLabels, GenderLabels, WorkingProfileLabels, getDefaultPersonData } from '../../constants/personConstants';\nimport formConfigService from '../../services/formConfigService';\nimport apiService from '../../services/apiService';\nimport FormField from './FormField';\nimport './DynamicPersonForm.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DynamicPersonForm = ({\n  onSubmit,\n  onCancel,\n  initialData = null,\n  mode = 'create'\n}) => {\n  _s();\n  // Hierarchy state\n  const [divisions, setDivisions] = useState([]);\n  const [categories, setCategories] = useState([]);\n  const [subCategories, setSubCategories] = useState([]);\n\n  // Selection state\n  const [selectedDivision, setSelectedDivision] = useState('');\n  const [selectedCategory, setSelectedCategory] = useState('');\n  const [selectedSubCategory, setSelectedSubCategory] = useState('');\n\n  // Form state\n  const [formConfig, setFormConfig] = useState(null);\n  const [formData, setFormData] = useState(getDefaultPersonData());\n  const [errors, setErrors] = useState({});\n\n  // Loading states\n  const [loading, setLoading] = useState({\n    divisions: false,\n    categories: false,\n    subCategories: false,\n    form: false\n  });\n  const [submitting, setSubmitting] = useState(false);\n\n  // Form availability state\n  const [formAvailability, setFormAvailability] = useState({\n    categoryHasForm: false,\n    subCategoryHasForm: false,\n    showSubCategoryDropdown: false,\n    message: ''\n  });\n\n  // Load divisions on component mount\n  useEffect(() => {\n    loadDivisions();\n\n    // Test API connectivity\n    testApiConnectivity();\n\n    // Load initial data if provided\n    if (initialData) {\n      var _initialData$division, _initialData$category, _initialData$subCateg;\n      setFormData(initialData);\n      setSelectedDivision(((_initialData$division = initialData.divisionId) === null || _initialData$division === void 0 ? void 0 : _initialData$division.toString()) || '');\n      setSelectedCategory(((_initialData$category = initialData.categoryId) === null || _initialData$category === void 0 ? void 0 : _initialData$category.toString()) || '');\n      setSelectedSubCategory(((_initialData$subCateg = initialData.subCategoryId) === null || _initialData$subCateg === void 0 ? void 0 : _initialData$subCateg.toString()) || '');\n    } else {\n      // Load default form for new person creation\n      const defaultForm = formConfigService.getDefaultFormConfig();\n      // Ensure no duplicate fields in default form\n      if (defaultForm.fields) {\n        defaultForm.fields = deduplicateFields(defaultForm.fields);\n      }\n      setFormConfig(defaultForm);\n    }\n  }, [initialData]);\n\n  // Test API connectivity\n  const testApiConnectivity = async () => {\n    try {\n      await apiService.getDivisions();\n    } catch (error) {\n      console.error('API connectivity test failed:', error);\n    }\n  };\n\n  // Deduplicate fields based on field key\n  const deduplicateFields = fields => {\n    const seen = new Set();\n    const deduplicated = [];\n    fields.forEach(field => {\n      if (!seen.has(field.key)) {\n        seen.add(field.key);\n        deduplicated.push(field);\n      } else {\n        console.warn(`DynamicPersonForm: Removing duplicate field: ${field.key}`);\n      }\n    });\n    if (fields.length !== deduplicated.length) {\n      console.log(`DynamicPersonForm: Deduplicated ${fields.length} fields to ${deduplicated.length} unique fields`);\n    }\n    return deduplicated;\n  };\n\n  // Load categories when division changes\n  useEffect(() => {\n    if (selectedDivision) {\n      loadCategories(selectedDivision);\n      // Reset category and subcategory when division changes\n      setSelectedCategory('');\n      setSelectedSubCategory('');\n      setSubCategories([]);\n      setFormConfig(null);\n      setFormAvailability({\n        categoryHasForm: false,\n        subCategoryHasForm: false,\n        showSubCategoryDropdown: false,\n        message: ''\n      });\n    } else {\n      setCategories([]);\n      setSubCategories([]);\n      setSelectedCategory('');\n      setSelectedSubCategory('');\n      setFormConfig(null);\n    }\n  }, [selectedDivision]);\n\n  // Handle category selection and form loading\n  useEffect(() => {\n    if (selectedCategory) {\n      handleCategorySelection(selectedCategory);\n      // Reset subcategory when category changes\n      setSelectedSubCategory('');\n    } else {\n      setSubCategories([]);\n      setSelectedSubCategory('');\n      setFormConfig(null);\n      setFormAvailability({\n        categoryHasForm: false,\n        subCategoryHasForm: false,\n        showSubCategoryDropdown: false,\n        message: ''\n      });\n    }\n  }, [selectedCategory]);\n\n  // Handle subcategory selection and form loading\n  useEffect(() => {\n    if (selectedSubCategory) {\n      handleSubCategorySelection(selectedSubCategory);\n    }\n  }, [selectedSubCategory]);\n\n  // Load divisions from API\n  const loadDivisions = async () => {\n    setLoading(prev => ({\n      ...prev,\n      divisions: true\n    }));\n    try {\n      const response = await apiService.getDivisions();\n      setDivisions(response.data || []);\n    } catch (error) {\n      console.error('Error loading divisions:', error);\n      setErrors(prev => ({\n        ...prev,\n        divisions: 'Failed to load divisions'\n      }));\n    } finally {\n      setLoading(prev => ({\n        ...prev,\n        divisions: false\n      }));\n    }\n  };\n\n  // Load categories by division\n  const loadCategories = async divisionId => {\n    setLoading(prev => ({\n      ...prev,\n      categories: true\n    }));\n    try {\n      const response = await apiService.getCategoriesByDivision(divisionId);\n      setCategories(response.data || []);\n    } catch (error) {\n      console.error('Error loading categories:', error);\n      setErrors(prev => ({\n        ...prev,\n        categories: 'Failed to load categories'\n      }));\n      setCategories([]);\n    } finally {\n      setLoading(prev => ({\n        ...prev,\n        categories: false\n      }));\n    }\n  };\n\n  // Load subcategories by category\n  const loadSubCategories = async categoryId => {\n    setLoading(prev => ({\n      ...prev,\n      subCategories: true\n    }));\n    try {\n      const response = await apiService.getSubCategoriesByCategory(categoryId);\n      setSubCategories(response.data || []);\n    } catch (error) {\n      console.error('Error loading subcategories:', error);\n      setErrors(prev => ({\n        ...prev,\n        subCategories: 'Failed to load subcategories'\n      }));\n      setSubCategories([]);\n    } finally {\n      setLoading(prev => ({\n        ...prev,\n        subCategories: false\n      }));\n    }\n  };\n\n  // Handle category selection and form loading logic\n  const handleCategorySelection = async categoryId => {\n    setLoading(prev => ({\n      ...prev,\n      form: true\n    }));\n    try {\n      // Check if category has a form\n      const categoryHasForm = formConfigService.hasFormForCategory(parseInt(categoryId));\n      if (categoryHasForm) {\n        // Load category form and hide subcategory dropdown\n        const categoryForm = formConfigService.loadFormConfig('category', parseInt(categoryId));\n        // Deduplicate fields in loaded form\n        if (categoryForm && categoryForm.fields) {\n          categoryForm.fields = deduplicateFields(categoryForm.fields);\n        }\n        setFormConfig(categoryForm);\n        setFormAvailability({\n          categoryHasForm: true,\n          subCategoryHasForm: false,\n          showSubCategoryDropdown: false,\n          message: 'Using category-specific form'\n        });\n      } else {\n        // No category form, show subcategory dropdown and load subcategories\n        await loadSubCategories(categoryId);\n        // Load default form as fallback\n        const defaultForm = formConfigService.getDefaultFormConfig();\n        // Deduplicate fields in default form\n        if (defaultForm.fields) {\n          defaultForm.fields = deduplicateFields(defaultForm.fields);\n        }\n        setFormConfig(defaultForm);\n        setFormAvailability({\n          categoryHasForm: false,\n          subCategoryHasForm: false,\n          showSubCategoryDropdown: true,\n          message: 'Using default form. You can select a subcategory if available.'\n        });\n      }\n    } catch (error) {\n      console.error('Error handling category selection:', error);\n      setErrors(prev => ({\n        ...prev,\n        form: 'Error loading form configuration'\n      }));\n    } finally {\n      setLoading(prev => ({\n        ...prev,\n        form: false\n      }));\n    }\n  };\n\n  // Handle subcategory selection and form loading logic\n  const handleSubCategorySelection = async subCategoryId => {\n    setLoading(prev => ({\n      ...prev,\n      form: true\n    }));\n    try {\n      // Check if subcategory has a form\n      const subCategoryHasForm = formConfigService.hasFormForSubCategory(parseInt(subCategoryId));\n      if (subCategoryHasForm) {\n        // Load subcategory form\n        const subCategoryForm = formConfigService.loadFormConfig('subcategory', parseInt(subCategoryId));\n        // Deduplicate fields in loaded form\n        if (subCategoryForm && subCategoryForm.fields) {\n          subCategoryForm.fields = deduplicateFields(subCategoryForm.fields);\n        }\n        setFormConfig(subCategoryForm);\n        setFormAvailability({\n          categoryHasForm: false,\n          subCategoryHasForm: true,\n          showSubCategoryDropdown: true,\n          message: 'Using subcategory-specific form'\n        });\n      } else {\n        // No subcategory form available, use default form\n        const defaultForm = formConfigService.getDefaultFormConfig();\n        // Deduplicate fields in default form\n        if (defaultForm.fields) {\n          defaultForm.fields = deduplicateFields(defaultForm.fields);\n        }\n        setFormConfig(defaultForm);\n        setFormAvailability({\n          categoryHasForm: false,\n          subCategoryHasForm: false,\n          showSubCategoryDropdown: true,\n          message: 'No specific form found for this subcategory. Using default form.'\n        });\n      }\n    } catch (error) {\n      console.error('Error handling subcategory selection:', error);\n      setErrors(prev => ({\n        ...prev,\n        form: 'Error loading form configuration'\n      }));\n    } finally {\n      setLoading(prev => ({\n        ...prev,\n        form: false\n      }));\n    }\n  };\n\n  // Handle dropdown changes\n  const handleDivisionChange = e => {\n    const value = e.target.value;\n    setSelectedDivision(value);\n\n    // Update form data\n    setFormData(prev => ({\n      ...prev,\n      divisionId: value ? parseInt(value) : null,\n      categoryId: null,\n      subCategoryId: null\n    }));\n  };\n  const handleCategoryChange = e => {\n    const value = e.target.value;\n    setSelectedCategory(value);\n\n    // Update form data\n    setFormData(prev => ({\n      ...prev,\n      categoryId: value ? parseInt(value) : null,\n      subCategoryId: null\n    }));\n  };\n  const handleSubCategoryChange = e => {\n    const value = e.target.value;\n    setSelectedSubCategory(value);\n\n    // Update form data\n    setFormData(prev => ({\n      ...prev,\n      subCategoryId: value ? parseInt(value) : null\n    }));\n  };\n  const handleFieldChange = (fieldKey, value) => {\n    setFormData(prev => ({\n      ...prev,\n      [fieldKey]: value\n    }));\n\n    // Clear field error when user starts typing\n    if (errors[fieldKey]) {\n      setErrors(prev => ({\n        ...prev,\n        [fieldKey]: null\n      }));\n    }\n  };\n  const validateForm = () => {\n    const newErrors = {};\n\n    // Validate hierarchy selection (required by backend)\n    if (!selectedDivision) {\n      newErrors.division = 'Division is required';\n    } else if (parseInt(selectedDivision) < 1) {\n      newErrors.division = 'Division ID must be a positive number';\n    }\n    if (!selectedCategory) {\n      newErrors.category = 'Category is required';\n    } else if (parseInt(selectedCategory) < 1) {\n      newErrors.category = 'Category ID must be a positive number';\n    }\n    if (selectedSubCategory && parseInt(selectedSubCategory) < 1) {\n      newErrors.subCategory = 'SubCategory ID must be a positive number';\n    }\n\n    // Validate required fields that match backend requirements\n    if (!formData.name || formData.name.trim() === '') {\n      newErrors.name = 'Name is required';\n    } else if (formData.name.length > 255) {\n      newErrors.name = 'Name cannot exceed 255 characters';\n    }\n    if (!formData.mobileNumber || formData.mobileNumber.trim() === '') {\n      newErrors.mobileNumber = 'Mobile number is required';\n    } else {\n      // Validate mobile number format (matches backend regex)\n      const mobileRegex = /^(\\+91[\\-\\s]?)?[0]?(91)?[789]\\d{9}$/;\n      if (!mobileRegex.test(formData.mobileNumber)) {\n        newErrors.mobileNumber = 'Invalid mobile number format';\n      }\n    }\n    if (!formData.nature) {\n      newErrors.nature = 'Nature is required';\n    } else if (![1, 2, 3, 4].includes(parseInt(formData.nature))) {\n      newErrors.nature = 'Invalid nature value';\n    }\n\n    // Check if form is available (should always have default form as fallback)\n    if (!formConfig) {\n      newErrors.form = 'Form configuration not loaded. Please try again.';\n      return {\n        isValid: false,\n        errors: newErrors\n      };\n    }\n\n    // Validate form fields\n    formConfig.fields.forEach(field => {\n      const value = formData[field.key];\n\n      // Required field validation\n      if (field.required && (!value || typeof value === 'string' && value.trim() === '')) {\n        newErrors[field.key] = `${field.label} is required`;\n        return;\n      }\n\n      // Conditional field validation\n      if (field.conditional && shouldShowField(field)) {\n        if (field.required && (!value || typeof value === 'string' && value.trim() === '')) {\n          newErrors[field.key] = `${field.label} is required`;\n          return;\n        }\n      }\n\n      // Type-specific validation\n      if (value && typeof value === 'string' && value.trim() !== '') {\n        var _field$validation, _field$validation2, _field$validation3;\n        switch (field.type) {\n          case 'email':\n            const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n            if (!emailRegex.test(value)) {\n              newErrors[field.key] = 'Please enter a valid email address';\n            }\n            break;\n          case 'tel':\n            const phoneRegex = /^(\\+91[\\-\\s]?)?[0]?(91)?[789]\\d{9}$/;\n            if (!phoneRegex.test(value)) {\n              newErrors[field.key] = 'Please enter a valid mobile number';\n            }\n            break;\n          case 'url':\n            try {\n              new URL(value);\n            } catch {\n              newErrors[field.key] = 'Please enter a valid URL';\n            }\n            break;\n          case 'number':\n            if (field.validation) {\n              const numValue = parseFloat(value);\n              if (field.validation.min !== undefined && numValue < field.validation.min) {\n                newErrors[field.key] = `Value must be at least ${field.validation.min}`;\n              }\n              if (field.validation.max !== undefined && numValue > field.validation.max) {\n                newErrors[field.key] = `Value must be at most ${field.validation.max}`;\n              }\n            }\n            break;\n        }\n\n        // Pattern validation\n        if ((_field$validation = field.validation) !== null && _field$validation !== void 0 && _field$validation.pattern) {\n          const regex = new RegExp(field.validation.pattern);\n          if (!regex.test(value)) {\n            newErrors[field.key] = `${field.label} format is invalid`;\n          }\n        }\n\n        // Length validation\n        if ((_field$validation2 = field.validation) !== null && _field$validation2 !== void 0 && _field$validation2.minLength && value.length < field.validation.minLength) {\n          newErrors[field.key] = `${field.label} must be at least ${field.validation.minLength} characters`;\n        }\n        if ((_field$validation3 = field.validation) !== null && _field$validation3 !== void 0 && _field$validation3.maxLength && value.length > field.validation.maxLength) {\n          newErrors[field.key] = `${field.label} must be at most ${field.validation.maxLength} characters`;\n        }\n\n        // Backend-specific field validations\n        if (field.key === 'primaryEmailId' && value.length > 255) {\n          newErrors[field.key] = 'Email cannot exceed 255 characters';\n        }\n        if (field.key === 'workingState' && value.length > 100) {\n          newErrors[field.key] = 'Working state cannot exceed 100 characters';\n        }\n        if (field.key === 'domesticState' && value.length > 100) {\n          newErrors[field.key] = 'Domestic state cannot exceed 100 characters';\n        }\n        if (field.key === 'district' && value.length > 100) {\n          newErrors[field.key] = 'District cannot exceed 100 characters';\n        }\n        if (field.key === 'address' && value.length > 500) {\n          newErrors[field.key] = 'Address cannot exceed 500 characters';\n        }\n        if (field.key === 'workingArea' && value.length > 200) {\n          newErrors[field.key] = 'Working area cannot exceed 200 characters';\n        }\n        if (field.key === 'associateName' && value.length > 255) {\n          newErrors[field.key] = 'Associate name cannot exceed 255 characters';\n        }\n        if (field.key === 'associateRelation' && value.length > 100) {\n          newErrors[field.key] = 'Associate relation cannot exceed 100 characters';\n        }\n        if (field.key === 'reraRegistrationNumber' && value.length > 50) {\n          newErrors[field.key] = 'RERA registration number cannot exceed 50 characters';\n        }\n        if (field.key === 'source' && value.length > 200) {\n          newErrors[field.key] = 'Source cannot exceed 200 characters';\n        }\n        if (field.key === 'remarks' && value.length > 1000) {\n          newErrors[field.key] = 'Remarks cannot exceed 1000 characters';\n        }\n        if (field.key === 'firmName' && value.length > 255) {\n          newErrors[field.key] = 'Firm name cannot exceed 255 characters';\n        }\n        if (field.key === 'authorizedPersonName' && value.length > 255) {\n          newErrors[field.key] = 'Authorized person name cannot exceed 255 characters';\n        }\n        if (field.key === 'authorizedPersonEmail' && value.length > 255) {\n          newErrors[field.key] = 'Authorized person email cannot exceed 255 characters';\n        }\n        if (field.key === 'designation' && value.length > 100) {\n          newErrors[field.key] = 'Designation cannot exceed 100 characters';\n        }\n        if (field.key === 'marketingContact' && value.length > 255) {\n          newErrors[field.key] = 'Marketing contact cannot exceed 255 characters';\n        }\n        if (field.key === 'marketingDesignation' && value.length > 100) {\n          newErrors[field.key] = 'Marketing designation cannot exceed 100 characters';\n        }\n        if (field.key === 'placeOfPosting' && value.length > 200) {\n          newErrors[field.key] = 'Place of posting cannot exceed 200 characters';\n        }\n        if (field.key === 'department' && value.length > 100) {\n          newErrors[field.key] = 'Department cannot exceed 100 characters';\n        }\n      }\n\n      // Validate numeric fields\n      if (field.type === 'number' && value) {\n        const numValue = parseFloat(value);\n        if (field.key === 'starRating' && (numValue < 1 || numValue > 5)) {\n          newErrors[field.key] = 'Star rating must be between 1 and 5';\n        }\n        if (['numberOfOffices', 'numberOfBranches', 'totalEmployeeStrength'].includes(field.key) && numValue < 0) {\n          newErrors[field.key] = `${field.label} must be non-negative`;\n        }\n        if (field.key === 'transactionValue' && numValue < 0) {\n          newErrors[field.key] = 'Transaction value must be non-negative';\n        }\n      }\n\n      // Validate associate mobile number format\n      if (field.key === 'associateMobile' && value && value.trim() !== '') {\n        const mobileRegex = /^(\\+91[\\-\\s]?)?[0]?(91)?[789]\\d{9}$/;\n        if (!mobileRegex.test(value)) {\n          newErrors[field.key] = 'Invalid associate mobile number format';\n        }\n      }\n    });\n\n    // Business logic validation\n    if (formData.isMarried && formData.dateOfMarriage && formData.dateOfBirth) {\n      const birthDate = new Date(formData.dateOfBirth);\n      const marriageDate = new Date(formData.dateOfMarriage);\n      if (marriageDate <= birthDate) {\n        newErrors.dateOfMarriage = 'Marriage date must be after birth date';\n      }\n    }\n    return {\n      isValid: Object.keys(newErrors).length === 0,\n      errors: newErrors\n    };\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    const validation = validateForm();\n    if (!validation.isValid) {\n      setErrors(validation.errors);\n      return;\n    }\n    setSubmitting(true);\n    try {\n      // Prepare data for API - match backend model exactly (PascalCase field names)\n      const submitData = {\n        // Required hierarchy fields\n        DivisionId: parseInt(selectedDivision),\n        CategoryId: parseInt(selectedCategory),\n        SubCategoryId: selectedSubCategory ? parseInt(selectedSubCategory) : null,\n        // Required fields\n        Name: formData.name || '',\n        MobileNumber: formData.mobileNumber || '',\n        Nature: formData.nature ? parseInt(formData.nature) : 1,\n        // Default to Business (1) if not provided\n\n        // Optional enum fields\n        Gender: formData.gender ? parseInt(formData.gender) : null,\n        // Contact Information\n        AlternateNumbers: Array.isArray(formData.alternateNumbers) ? formData.alternateNumbers.filter(num => num && num.trim() !== '') : formData.alternateNumbers ? formData.alternateNumbers.split(',').map(s => s.trim()).filter(s => s !== '') : [],\n        AlternateEmailIds: Array.isArray(formData.alternateEmailIds) ? formData.alternateEmailIds.filter(email => email && email.trim() !== '') : formData.alternateEmailIds ? formData.alternateEmailIds.split(',').map(s => s.trim()).filter(s => s !== '') : [],\n        // Personal Information\n        DateOfBirth: formData.dateOfBirth || null,\n        IsMarried: Boolean(formData.isMarried),\n        DateOfMarriage: formData.dateOfMarriage || null,\n        // Location Information\n        WorkingState: formData.workingState || '',\n        DomesticState: formData.domesticState || '',\n        District: formData.district || '',\n        Address: formData.address || '',\n        WorkingArea: formData.workingArea || '',\n        // Associate Information\n        HasAssociate: Boolean(formData.hasAssociate),\n        AssociateName: formData.associateName || '',\n        AssociateRelation: formData.associateRelation || '',\n        AssociateMobile: formData.associateMobile || '',\n        // Digital Presence\n        UsingWebsite: Boolean(formData.usingWebsite),\n        UsingCRMApp: Boolean(formData.usingCRMApp),\n        // Business Information\n        TransactionValue: formData.transactionValue ? parseFloat(formData.transactionValue) : null,\n        RERARegistrationNumber: formData.reraRegistrationNumber || '',\n        WorkingProfiles: Array.isArray(formData.workingProfiles) ? formData.workingProfiles.map(wp => parseInt(wp)).filter(wp => !isNaN(wp)) : [],\n        StarRating: formData.starRating ? parseInt(formData.starRating) : null,\n        Source: formData.source || '',\n        Remarks: formData.remarks || '',\n        // Company Information\n        FirmName: formData.firmName || '',\n        NumberOfOffices: formData.numberOfOffices && !isNaN(parseInt(formData.numberOfOffices)) ? parseInt(formData.numberOfOffices) : null,\n        NumberOfBranches: formData.numberOfBranches && !isNaN(parseInt(formData.numberOfBranches)) ? parseInt(formData.numberOfBranches) : null,\n        TotalEmployeeStrength: formData.totalEmployeeStrength && !isNaN(parseInt(formData.totalEmployeeStrength)) ? parseInt(formData.totalEmployeeStrength) : null,\n        // Authorized Person\n        AuthorizedPersonName: formData.authorizedPersonName || '',\n        Designation: formData.designation || '',\n        // Marketing Information\n        MarketingContact: formData.marketingContact || '',\n        MarketingDesignation: formData.marketingDesignation || '',\n        PlaceOfPosting: formData.placeOfPosting || '',\n        Department: formData.department || ''\n      };\n\n      // Only add optional URL and email fields if they have valid values (not empty strings)\n      // This prevents backend validation errors for empty strings on URL/Email fields\n\n      // Email fields - only add if not empty and valid\n      if (formData.primaryEmailId && formData.primaryEmailId.trim() !== '') {\n        submitData.PrimaryEmailId = formData.primaryEmailId.trim();\n      }\n      if (formData.authorizedPersonEmail && formData.authorizedPersonEmail.trim() !== '') {\n        submitData.AuthorizedPersonEmail = formData.authorizedPersonEmail.trim();\n      }\n\n      // URL fields - only add if not empty and valid\n      if (formData.website && formData.website.trim() !== '') {\n        submitData.Website = formData.website.trim();\n      }\n      if (formData.websiteLink && formData.websiteLink.trim() !== '') {\n        submitData.WebsiteLink = formData.websiteLink.trim();\n      }\n      if (formData.crmAppLink && formData.crmAppLink.trim() !== '') {\n        submitData.CRMAppLink = formData.crmAppLink.trim();\n      }\n\n      // Validate required fields before submission\n      const requiredFieldsCheck = {\n        DivisionId: submitData.DivisionId,\n        CategoryId: submitData.CategoryId,\n        Name: submitData.Name,\n        MobileNumber: submitData.MobileNumber,\n        Nature: submitData.Nature\n      };\n\n      // Check for missing required fields\n      const missingFields = Object.entries(requiredFieldsCheck).filter(([, value]) => !value || value === '' || value === null).map(([key]) => key);\n      if (missingFields.length > 0) {\n        setErrors({\n          general: 'Missing required fields: ' + missingFields.join(', ')\n        });\n        return;\n      }\n      console.log('Submitting data to backend:', JSON.stringify(submitData, null, 2));\n      let result;\n      if (mode === 'create') {\n        result = await apiService.createPerson(submitData);\n      } else {\n        result = await apiService.updatePerson(initialData.id, submitData);\n      }\n      onSubmit(result);\n    } catch (error) {\n      var _error$data, _error$response, _error$response$data, _error$data3, _error$data4, _error$response3, _error$response3$data, _error$response4, _error$response4$data;\n      console.error('Error submitting form:', error);\n      console.error('Error details:', {\n        status: error.status,\n        data: error.data,\n        response: error.response,\n        message: error.message\n      });\n\n      // Enhanced error handling for backend validation errors\n      if ((_error$data = error.data) !== null && _error$data !== void 0 && _error$data.errors || (_error$response = error.response) !== null && _error$response !== void 0 && (_error$response$data = _error$response.data) !== null && _error$response$data !== void 0 && _error$response$data.errors) {\n        var _error$data2, _error$response2, _error$response2$data;\n        // Handle ASP.NET Core ModelState validation errors\n        const validationErrors = ((_error$data2 = error.data) === null || _error$data2 === void 0 ? void 0 : _error$data2.errors) || ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.errors);\n        const backendErrors = {};\n        console.log('Validation errors received:', validationErrors);\n        Object.keys(validationErrors).forEach(key => {\n          const errorMessages = validationErrors[key];\n          // Map backend field names to frontend field names for display\n          const frontendFieldName = mapBackendFieldToFrontend(key);\n          backendErrors[frontendFieldName] = Array.isArray(errorMessages) ? errorMessages.join(', ') : errorMessages;\n        });\n        setErrors(backendErrors);\n      } else if ((_error$data3 = error.data) !== null && _error$data3 !== void 0 && _error$data3.title || (_error$data4 = error.data) !== null && _error$data4 !== void 0 && _error$data4.detail || (_error$response3 = error.response) !== null && _error$response3 !== void 0 && (_error$response3$data = _error$response3.data) !== null && _error$response3$data !== void 0 && _error$response3$data.title || (_error$response4 = error.response) !== null && _error$response4 !== void 0 && (_error$response4$data = _error$response4.data) !== null && _error$response4$data !== void 0 && _error$response4$data.detail) {\n        var _error$data5, _error$data6, _error$response5, _error$response5$data, _error$response6, _error$response6$data;\n        // Handle ProblemDetails format errors\n        const errorMessage = ((_error$data5 = error.data) === null || _error$data5 === void 0 ? void 0 : _error$data5.detail) || ((_error$data6 = error.data) === null || _error$data6 === void 0 ? void 0 : _error$data6.title) || ((_error$response5 = error.response) === null || _error$response5 === void 0 ? void 0 : (_error$response5$data = _error$response5.data) === null || _error$response5$data === void 0 ? void 0 : _error$response5$data.detail) || ((_error$response6 = error.response) === null || _error$response6 === void 0 ? void 0 : (_error$response6$data = _error$response6.data) === null || _error$response6$data === void 0 ? void 0 : _error$response6$data.title);\n        setErrors({\n          general: errorMessage\n        });\n      } else if (error.isValidationError && error.isValidationError()) {\n        const validationErrors = error.getValidationErrors();\n        console.log('ApiError validation errors:', validationErrors);\n        setErrors(validationErrors);\n      } else {\n        var _error$data7, _error$response7, _error$response7$data, _error$data8;\n        // Handle other error formats\n        const errorMessage = ((_error$data7 = error.data) === null || _error$data7 === void 0 ? void 0 : _error$data7.message) || ((_error$response7 = error.response) === null || _error$response7 === void 0 ? void 0 : (_error$response7$data = _error$response7.data) === null || _error$response7$data === void 0 ? void 0 : _error$response7$data.message) || ((_error$data8 = error.data) === null || _error$data8 === void 0 ? void 0 : _error$data8.title) || error.message || 'An error occurred while saving the person. Please check your input and try again.';\n        setErrors({\n          general: errorMessage\n        });\n      }\n    } finally {\n      setSubmitting(false);\n    }\n  };\n\n  // Helper function to map backend field names to frontend field names for error display\n  const mapBackendFieldToFrontend = backendFieldName => {\n    const fieldMapping = {\n      'DivisionId': 'division',\n      'CategoryId': 'category',\n      'SubCategoryId': 'subCategory',\n      'Name': 'name',\n      'MobileNumber': 'mobileNumber',\n      'Nature': 'nature',\n      'Gender': 'gender',\n      'PrimaryEmailId': 'primaryEmailId',\n      'AlternateNumbers': 'alternateNumbers',\n      'AlternateEmailIds': 'alternateEmailIds',\n      'Website': 'website',\n      'DateOfBirth': 'dateOfBirth',\n      'IsMarried': 'isMarried',\n      'DateOfMarriage': 'dateOfMarriage',\n      'WorkingState': 'workingState',\n      'DomesticState': 'domesticState',\n      'District': 'district',\n      'Address': 'address',\n      'WorkingArea': 'workingArea',\n      'HasAssociate': 'hasAssociate',\n      'AssociateName': 'associateName',\n      'AssociateRelation': 'associateRelation',\n      'AssociateMobile': 'associateMobile',\n      'UsingWebsite': 'usingWebsite',\n      'WebsiteLink': 'websiteLink',\n      'UsingCRMApp': 'usingCRMApp',\n      'CRMAppLink': 'crmAppLink',\n      'TransactionValue': 'transactionValue',\n      'RERARegistrationNumber': 'reraRegistrationNumber',\n      'WorkingProfiles': 'workingProfiles',\n      'StarRating': 'starRating',\n      'Source': 'source',\n      'Remarks': 'remarks',\n      'FirmName': 'firmName',\n      'NumberOfOffices': 'numberOfOffices',\n      'NumberOfBranches': 'numberOfBranches',\n      'TotalEmployeeStrength': 'totalEmployeeStrength',\n      'AuthorizedPersonName': 'authorizedPersonName',\n      'AuthorizedPersonEmail': 'authorizedPersonEmail',\n      'Designation': 'designation',\n      'MarketingContact': 'marketingContact',\n      'MarketingDesignation': 'marketingDesignation',\n      'PlaceOfPosting': 'placeOfPosting',\n      'Department': 'department'\n    };\n    return fieldMapping[backendFieldName] || backendFieldName.toLowerCase();\n  };\n  const shouldShowField = field => {\n    if (!field.conditional) return true;\n    const conditionValue = formData[field.conditional.field];\n    const expectedValue = field.conditional.value;\n\n    // Handle boolean conditions\n    if (typeof expectedValue === 'boolean' || expectedValue === 'true' || expectedValue === 'false') {\n      return conditionValue === (expectedValue === true || expectedValue === 'true');\n    }\n    return conditionValue === expectedValue;\n  };\n  const groupFieldsBySections = () => {\n    if (!formConfig || !formConfig.fields) return {};\n    const sections = {};\n    formConfig.fields.forEach(field => {\n      const sectionKey = field.section || 'general';\n      if (!sections[sectionKey]) {\n        sections[sectionKey] = {\n          title: getSectionTitle(sectionKey),\n          fields: []\n        };\n      }\n      sections[sectionKey].fields.push(field);\n    });\n    return sections;\n  };\n  const getSectionTitle = sectionKey => {\n    const titles = {\n      personalInfo: 'Personal Information',\n      contactInfo: 'Contact Information',\n      locationInfo: 'Location Information',\n      businessInfo: 'Business Information',\n      associateInfo: 'Associate Information',\n      digitalPresence: 'Digital Presence',\n      companyInfo: 'Company Information',\n      authorizedPerson: 'Authorized Person',\n      marketingInfo: 'Marketing Information',\n      general: 'General Information'\n    };\n    return titles[sectionKey] || sectionKey;\n  };\n  const sections = formConfig ? groupFieldsBySections() : {};\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"dynamic-person-form\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"form-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: mode === 'create' ? 'Create New Person' : 'Edit Person'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 845,\n        columnNumber: 9\n      }, this), formConfig && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-info\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"form-name\",\n          children: formConfig.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 848,\n          columnNumber: 13\n        }, this), formConfig.description && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"form-description\",\n          children: formConfig.description\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 850,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 847,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 844,\n      columnNumber: 7\n    }, this), errors.general && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"alert alert-error\",\n      children: errors.general\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 857,\n      columnNumber: 9\n    }, this), Object.keys(errors).length > 0 && !errors.general && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"alert alert-error\",\n      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n        children: \"Please fix the following errors:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 863,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n        style: {\n          margin: '8px 0 0 20px'\n        },\n        children: Object.entries(errors).filter(([key]) => key !== 'general').map(([key, message]) => /*#__PURE__*/_jsxDEV(\"li\", {\n          children: message\n        }, key, false, {\n          fileName: _jsxFileName,\n          lineNumber: 868,\n          columnNumber: 17\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 864,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 862,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmit,\n      className: \"person-form\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Division & Category Selection\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 877,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"form-label\",\n            children: [\"Division \", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"required\",\n              children: \"*\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 882,\n              columnNumber: 24\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 881,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: selectedDivision,\n            onChange: handleDivisionChange,\n            disabled: loading.divisions,\n            className: `form-select ${errors.division ? 'error' : ''}`,\n            required: true,\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"\",\n              children: loading.divisions ? 'Loading divisions...' : 'Select Division'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 891,\n              columnNumber: 15\n            }, this), divisions.map(division => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: division.id,\n              children: division.name\n            }, division.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 895,\n              columnNumber: 17\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 884,\n            columnNumber: 13\n          }, this), errors.division && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"error-message\",\n            children: errors.division\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 901,\n            columnNumber: 15\n          }, this), errors.divisions && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"error-message\",\n            children: errors.divisions\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 904,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 880,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"form-label\",\n            children: [\"Category \", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"required\",\n              children: \"*\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 911,\n              columnNumber: 24\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 910,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: selectedCategory,\n            onChange: handleCategoryChange,\n            disabled: !selectedDivision || loading.categories,\n            className: `form-select ${errors.category ? 'error' : ''}`,\n            required: true,\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"\",\n              children: !selectedDivision ? 'Select Division first' : loading.categories ? 'Loading categories...' : 'Select Category'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 920,\n              columnNumber: 15\n            }, this), categories.map(category => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: category.id,\n              children: category.name\n            }, category.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 929,\n              columnNumber: 17\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 913,\n            columnNumber: 13\n          }, this), errors.category && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"error-message\",\n            children: errors.category\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 935,\n            columnNumber: 15\n          }, this), errors.categories && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"error-message\",\n            children: errors.categories\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 938,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 909,\n          columnNumber: 11\n        }, this), formAvailability.showSubCategoryDropdown && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"form-label\",\n            children: \"Subcategory (Optional)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 945,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: selectedSubCategory,\n            onChange: handleSubCategoryChange,\n            disabled: !selectedCategory || loading.subCategories,\n            className: \"form-select\",\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"\",\n              children: !selectedCategory ? 'Select Category first' : loading.subCategories ? 'Loading subcategories...' : 'Select Subcategory (Optional)'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 954,\n              columnNumber: 17\n            }, this), subCategories.map(subCategory => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: subCategory.id,\n              children: subCategory.name\n            }, subCategory.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 963,\n              columnNumber: 19\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 948,\n            columnNumber: 15\n          }, this), errors.subCategories && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"error-message\",\n            children: errors.subCategories\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 969,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 944,\n          columnNumber: 13\n        }, this), loading.form && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading-message\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Loading form configuration...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 977,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 976,\n          columnNumber: 13\n        }, this), formAvailability.message && !loading.form && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `status-message ${formAvailability.categoryHasForm || formAvailability.subCategoryHasForm ? 'success' : formAvailability.message.includes('No form') ? 'error' : 'info'}`,\n          children: formAvailability.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 982,\n          columnNumber: 13\n        }, this), errors.form && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"error-message\",\n          children: errors.form\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 994,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 876,\n        columnNumber: 9\n      }, this), formConfig && Object.entries(sections).map(([sectionKey, section]) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: section.title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1001,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-fields\",\n          children: section.fields.filter(field => shouldShowField(field)).map((field, fieldIndex) => /*#__PURE__*/_jsxDEV(FormField, {\n            field: field,\n            value: formData[field.key],\n            onChange: value => handleFieldChange(field.key, value),\n            error: errors[field.key]\n          }, `${sectionKey}-${field.key}-${fieldIndex}`, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1006,\n            columnNumber: 19\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1002,\n          columnNumber: 13\n        }, this)]\n      }, sectionKey, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1000,\n        columnNumber: 11\n      }, this)), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-actions\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          onClick: onCancel,\n          className: \"btn btn-outline\",\n          disabled: submitting,\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1022,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          className: \"btn btn-primary\",\n          disabled: submitting || !selectedDivision || !selectedCategory,\n          children: submitting ? mode === 'create' ? 'Creating...' : 'Updating...' : mode === 'create' ? 'Create Person' : 'Update Person'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1030,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1021,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 874,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 843,\n    columnNumber: 5\n  }, this);\n};\n_s(DynamicPersonForm, \"HQTZ+LW5pRnY6eFU2EO5G2IvcY0=\");\n_c = DynamicPersonForm;\nexport default DynamicPersonForm;\nvar _c;\n$RefreshReg$(_c, \"DynamicPersonForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "PersonNatureLabels", "<PERSON><PERSON><PERSON><PERSON>", "WorkingProfileLabels", "getDefaultPersonData", "formConfigService", "apiService", "FormField", "jsxDEV", "_jsxDEV", "DynamicPersonForm", "onSubmit", "onCancel", "initialData", "mode", "_s", "divisions", "setDivisions", "categories", "setCategories", "subCategories", "setSubCategories", "selectedDivision", "setSelectedDivision", "selectedCate<PERSON><PERSON>", "setSelectedCategory", "selectedSubCategory", "setSelectedSubCategory", "formConfig", "setFormConfig", "formData", "setFormData", "errors", "setErrors", "loading", "setLoading", "form", "submitting", "setSubmitting", "formAvailability", "setFormAvailability", "categoryHasForm", "subCategoryHasForm", "showSubCategoryDropdown", "message", "loadDivisions", "testApiConnectivity", "_initialData$division", "_initialData$category", "_initialData$subCateg", "divisionId", "toString", "categoryId", "subCategoryId", "defaultForm", "getDefaultFormConfig", "fields", "deduplicateFields", "getDivisions", "error", "console", "seen", "Set", "deduplicated", "for<PERSON>ach", "field", "has", "key", "add", "push", "warn", "length", "log", "loadCategories", "handleCategorySelection", "handleSubCategorySelection", "prev", "response", "data", "getCategoriesByDivision", "loadSubCategories", "getSubCategoriesByCategory", "hasFormForCategory", "parseInt", "categoryForm", "loadFormConfig", "hasFormForSubCategory", "subCategoryForm", "handleDivisionChange", "e", "value", "target", "handleCategoryChange", "handleSubCategoryChange", "handleFieldChange", "<PERSON><PERSON><PERSON>", "validateForm", "newErrors", "division", "category", "subCategory", "name", "trim", "mobileNumber", "mobileRegex", "test", "nature", "includes", "<PERSON><PERSON><PERSON><PERSON>", "required", "label", "conditional", "shouldShowField", "_field$validation", "_field$validation2", "_field$validation3", "type", "emailRegex", "phoneRegex", "URL", "validation", "numValue", "parseFloat", "min", "undefined", "max", "pattern", "regex", "RegExp", "<PERSON><PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "dateOfMarriage", "dateOfBirth", "birthDate", "Date", "marriageDate", "Object", "keys", "handleSubmit", "preventDefault", "submitData", "DivisionId", "CategoryId", "SubCategoryId", "Name", "MobileNumber", "Nature", "Gender", "gender", "AlternateNumbers", "Array", "isArray", "alternateNumbers", "filter", "num", "split", "map", "s", "AlternateEmailIds", "alternateEmailIds", "email", "DateOfBirth", "<PERSON><PERSON><PERSON><PERSON>", "Boolean", "DateOfMarriage", "WorkingState", "workingState", "DomesticState", "domesticState", "District", "district", "Address", "address", "WorkingArea", "workingArea", "HasAssociate", "hasAssociate", "<PERSON><PERSON><PERSON>", "associate<PERSON><PERSON>", "AssociateRelation", "associateRelation", "AssociateMobile", "associate<PERSON><PERSON><PERSON>", "UsingWebsite", "usingWebsite", "UsingCRMApp", "usingCRMApp", "TransactionValue", "transactionValue", "RERARegistrationNumber", "reraRegistrationNumber", "WorkingProfiles", "workingProfiles", "wp", "isNaN", "StarRating", "starRating", "Source", "source", "Remarks", "remarks", "FirmName", "firmName", "NumberOfOffices", "numberOfOffices", "NumberOfBranches", "numberOfBranches", "TotalEmployeeStrength", "totalEmployeeStrength", "AuthorizedPersonName", "authorizedPersonName", "Designation", "designation", "MarketingContact", "marketingContact", "MarketingDesignation", "marketingDesignation", "PlaceOfPosting", "placeOfPosting", "Department", "department", "primaryEmailId", "PrimaryEmailId", "authorizedPersonEmail", "AuthorizedPersonEmail", "website", "Website", "websiteLink", "WebsiteLink", "crmAppLink", "CRMAppLink", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "missingFields", "entries", "general", "join", "JSON", "stringify", "result", "create<PERSON>erson", "update<PERSON><PERSON>", "id", "_error$data", "_error$response", "_error$response$data", "_error$data3", "_error$data4", "_error$response3", "_error$response3$data", "_error$response4", "_error$response4$data", "status", "_error$data2", "_error$response2", "_error$response2$data", "validationErrors", "backendErrors", "errorMessages", "frontendFieldName", "mapBackendFieldToFrontend", "title", "detail", "_error$data5", "_error$data6", "_error$response5", "_error$response5$data", "_error$response6", "_error$response6$data", "errorMessage", "isValidationError", "getValidationErrors", "_error$data7", "_error$response7", "_error$response7$data", "_error$data8", "backendFieldName", "fieldMapping", "toLowerCase", "conditionValue", "expectedValue", "groupFieldsBySections", "sections", "sectionKey", "section", "getSectionTitle", "titles", "personalInfo", "contactInfo", "locationInfo", "businessInfo", "associateInfo", "digitalPresence", "companyInfo", "<PERSON><PERSON><PERSON>", "marketingInfo", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "description", "style", "margin", "onChange", "disabled", "fieldIndex", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/Advanced/Advanced CRM filed all use/frontend/src/components/forms/DynamicPersonForm.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { PersonNatureLabels, GenderLabels, WorkingProfileLabels, getDefaultPersonData } from '../../constants/personConstants';\nimport formConfigService from '../../services/formConfigService';\nimport apiService from '../../services/apiService';\nimport FormField from './FormField';\nimport './DynamicPersonForm.css';\n\nconst DynamicPersonForm = ({ onSubmit, onCancel, initialData = null, mode = 'create' }) => {\n  // Hierarchy state\n  const [divisions, setDivisions] = useState([]);\n  const [categories, setCategories] = useState([]);\n  const [subCategories, setSubCategories] = useState([]);\n\n  // Selection state\n  const [selectedDivision, setSelectedDivision] = useState('');\n  const [selectedCategory, setSelectedCategory] = useState('');\n  const [selectedSubCategory, setSelectedSubCategory] = useState('');\n\n  // Form state\n  const [formConfig, setFormConfig] = useState(null);\n  const [formData, setFormData] = useState(getDefaultPersonData());\n  const [errors, setErrors] = useState({});\n\n  // Loading states\n  const [loading, setLoading] = useState({\n    divisions: false,\n    categories: false,\n    subCategories: false,\n    form: false\n  });\n  const [submitting, setSubmitting] = useState(false);\n\n  // Form availability state\n  const [formAvailability, setFormAvailability] = useState({\n    categoryHasForm: false,\n    subCategoryHasForm: false,\n    showSubCategoryDropdown: false,\n    message: ''\n  });\n\n  // Load divisions on component mount\n  useEffect(() => {\n    loadDivisions();\n\n    // Test API connectivity\n    testApiConnectivity();\n\n    // Load initial data if provided\n    if (initialData) {\n      setFormData(initialData);\n      setSelectedDivision(initialData.divisionId?.toString() || '');\n      setSelectedCategory(initialData.categoryId?.toString() || '');\n      setSelectedSubCategory(initialData.subCategoryId?.toString() || '');\n    } else {\n      // Load default form for new person creation\n      const defaultForm = formConfigService.getDefaultFormConfig();\n      // Ensure no duplicate fields in default form\n      if (defaultForm.fields) {\n        defaultForm.fields = deduplicateFields(defaultForm.fields);\n      }\n      setFormConfig(defaultForm);\n    }\n  }, [initialData]);\n\n  // Test API connectivity\n  const testApiConnectivity = async () => {\n    try {\n      await apiService.getDivisions();\n    } catch (error) {\n      console.error('API connectivity test failed:', error);\n    }\n  };\n\n  // Deduplicate fields based on field key\n  const deduplicateFields = (fields) => {\n    const seen = new Set();\n    const deduplicated = [];\n\n    fields.forEach(field => {\n      if (!seen.has(field.key)) {\n        seen.add(field.key);\n        deduplicated.push(field);\n      } else {\n        console.warn(`DynamicPersonForm: Removing duplicate field: ${field.key}`);\n      }\n    });\n\n    if (fields.length !== deduplicated.length) {\n      console.log(`DynamicPersonForm: Deduplicated ${fields.length} fields to ${deduplicated.length} unique fields`);\n    }\n\n    return deduplicated;\n  };\n\n  // Load categories when division changes\n  useEffect(() => {\n    if (selectedDivision) {\n      loadCategories(selectedDivision);\n      // Reset category and subcategory when division changes\n      setSelectedCategory('');\n      setSelectedSubCategory('');\n      setSubCategories([]);\n      setFormConfig(null);\n      setFormAvailability({\n        categoryHasForm: false,\n        subCategoryHasForm: false,\n        showSubCategoryDropdown: false,\n        message: ''\n      });\n    } else {\n      setCategories([]);\n      setSubCategories([]);\n      setSelectedCategory('');\n      setSelectedSubCategory('');\n      setFormConfig(null);\n    }\n  }, [selectedDivision]);\n\n  // Handle category selection and form loading\n  useEffect(() => {\n    if (selectedCategory) {\n      handleCategorySelection(selectedCategory);\n      // Reset subcategory when category changes\n      setSelectedSubCategory('');\n    } else {\n      setSubCategories([]);\n      setSelectedSubCategory('');\n      setFormConfig(null);\n      setFormAvailability({\n        categoryHasForm: false,\n        subCategoryHasForm: false,\n        showSubCategoryDropdown: false,\n        message: ''\n      });\n    }\n  }, [selectedCategory]);\n\n  // Handle subcategory selection and form loading\n  useEffect(() => {\n    if (selectedSubCategory) {\n      handleSubCategorySelection(selectedSubCategory);\n    }\n  }, [selectedSubCategory]);\n\n  // Load divisions from API\n  const loadDivisions = async () => {\n    setLoading(prev => ({ ...prev, divisions: true }));\n    try {\n      const response = await apiService.getDivisions();\n      setDivisions(response.data || []);\n    } catch (error) {\n      console.error('Error loading divisions:', error);\n      setErrors(prev => ({ ...prev, divisions: 'Failed to load divisions' }));\n    } finally {\n      setLoading(prev => ({ ...prev, divisions: false }));\n    }\n  };\n\n  // Load categories by division\n  const loadCategories = async (divisionId) => {\n    setLoading(prev => ({ ...prev, categories: true }));\n    try {\n      const response = await apiService.getCategoriesByDivision(divisionId);\n      setCategories(response.data || []);\n    } catch (error) {\n      console.error('Error loading categories:', error);\n      setErrors(prev => ({ ...prev, categories: 'Failed to load categories' }));\n      setCategories([]);\n    } finally {\n      setLoading(prev => ({ ...prev, categories: false }));\n    }\n  };\n\n  // Load subcategories by category\n  const loadSubCategories = async (categoryId) => {\n    setLoading(prev => ({ ...prev, subCategories: true }));\n    try {\n      const response = await apiService.getSubCategoriesByCategory(categoryId);\n      setSubCategories(response.data || []);\n    } catch (error) {\n      console.error('Error loading subcategories:', error);\n      setErrors(prev => ({ ...prev, subCategories: 'Failed to load subcategories' }));\n      setSubCategories([]);\n    } finally {\n      setLoading(prev => ({ ...prev, subCategories: false }));\n    }\n  };\n\n  // Handle category selection and form loading logic\n  const handleCategorySelection = async (categoryId) => {\n    setLoading(prev => ({ ...prev, form: true }));\n\n    try {\n      // Check if category has a form\n      const categoryHasForm = formConfigService.hasFormForCategory(parseInt(categoryId));\n\n      if (categoryHasForm) {\n        // Load category form and hide subcategory dropdown\n        const categoryForm = formConfigService.loadFormConfig('category', parseInt(categoryId));\n        // Deduplicate fields in loaded form\n        if (categoryForm && categoryForm.fields) {\n          categoryForm.fields = deduplicateFields(categoryForm.fields);\n        }\n        setFormConfig(categoryForm);\n        setFormAvailability({\n          categoryHasForm: true,\n          subCategoryHasForm: false,\n          showSubCategoryDropdown: false,\n          message: 'Using category-specific form'\n        });\n      } else {\n        // No category form, show subcategory dropdown and load subcategories\n        await loadSubCategories(categoryId);\n        // Load default form as fallback\n        const defaultForm = formConfigService.getDefaultFormConfig();\n        // Deduplicate fields in default form\n        if (defaultForm.fields) {\n          defaultForm.fields = deduplicateFields(defaultForm.fields);\n        }\n        setFormConfig(defaultForm);\n        setFormAvailability({\n          categoryHasForm: false,\n          subCategoryHasForm: false,\n          showSubCategoryDropdown: true,\n          message: 'Using default form. You can select a subcategory if available.'\n        });\n      }\n    } catch (error) {\n      console.error('Error handling category selection:', error);\n      setErrors(prev => ({ ...prev, form: 'Error loading form configuration' }));\n    } finally {\n      setLoading(prev => ({ ...prev, form: false }));\n    }\n  };\n\n  // Handle subcategory selection and form loading logic\n  const handleSubCategorySelection = async (subCategoryId) => {\n    setLoading(prev => ({ ...prev, form: true }));\n\n    try {\n      // Check if subcategory has a form\n      const subCategoryHasForm = formConfigService.hasFormForSubCategory(parseInt(subCategoryId));\n\n      if (subCategoryHasForm) {\n        // Load subcategory form\n        const subCategoryForm = formConfigService.loadFormConfig('subcategory', parseInt(subCategoryId));\n        // Deduplicate fields in loaded form\n        if (subCategoryForm && subCategoryForm.fields) {\n          subCategoryForm.fields = deduplicateFields(subCategoryForm.fields);\n        }\n        setFormConfig(subCategoryForm);\n        setFormAvailability({\n          categoryHasForm: false,\n          subCategoryHasForm: true,\n          showSubCategoryDropdown: true,\n          message: 'Using subcategory-specific form'\n        });\n      } else {\n        // No subcategory form available, use default form\n        const defaultForm = formConfigService.getDefaultFormConfig();\n        // Deduplicate fields in default form\n        if (defaultForm.fields) {\n          defaultForm.fields = deduplicateFields(defaultForm.fields);\n        }\n        setFormConfig(defaultForm);\n        setFormAvailability({\n          categoryHasForm: false,\n          subCategoryHasForm: false,\n          showSubCategoryDropdown: true,\n          message: 'No specific form found for this subcategory. Using default form.'\n        });\n      }\n    } catch (error) {\n      console.error('Error handling subcategory selection:', error);\n      setErrors(prev => ({ ...prev, form: 'Error loading form configuration' }));\n    } finally {\n      setLoading(prev => ({ ...prev, form: false }));\n    }\n  };\n\n  // Handle dropdown changes\n  const handleDivisionChange = (e) => {\n    const value = e.target.value;\n    setSelectedDivision(value);\n\n    // Update form data\n    setFormData(prev => ({\n      ...prev,\n      divisionId: value ? parseInt(value) : null,\n      categoryId: null,\n      subCategoryId: null\n    }));\n  };\n\n  const handleCategoryChange = (e) => {\n    const value = e.target.value;\n    setSelectedCategory(value);\n\n    // Update form data\n    setFormData(prev => ({\n      ...prev,\n      categoryId: value ? parseInt(value) : null,\n      subCategoryId: null\n    }));\n  };\n\n  const handleSubCategoryChange = (e) => {\n    const value = e.target.value;\n    setSelectedSubCategory(value);\n\n    // Update form data\n    setFormData(prev => ({\n      ...prev,\n      subCategoryId: value ? parseInt(value) : null\n    }));\n  };\n\n  const handleFieldChange = (fieldKey, value) => {\n    setFormData(prev => ({\n      ...prev,\n      [fieldKey]: value\n    }));\n\n    // Clear field error when user starts typing\n    if (errors[fieldKey]) {\n      setErrors(prev => ({\n        ...prev,\n        [fieldKey]: null\n      }));\n    }\n  };\n\n  const validateForm = () => {\n    const newErrors = {};\n\n    // Validate hierarchy selection (required by backend)\n    if (!selectedDivision) {\n      newErrors.division = 'Division is required';\n    } else if (parseInt(selectedDivision) < 1) {\n      newErrors.division = 'Division ID must be a positive number';\n    }\n\n    if (!selectedCategory) {\n      newErrors.category = 'Category is required';\n    } else if (parseInt(selectedCategory) < 1) {\n      newErrors.category = 'Category ID must be a positive number';\n    }\n\n    if (selectedSubCategory && parseInt(selectedSubCategory) < 1) {\n      newErrors.subCategory = 'SubCategory ID must be a positive number';\n    }\n\n    // Validate required fields that match backend requirements\n    if (!formData.name || formData.name.trim() === '') {\n      newErrors.name = 'Name is required';\n    } else if (formData.name.length > 255) {\n      newErrors.name = 'Name cannot exceed 255 characters';\n    }\n\n    if (!formData.mobileNumber || formData.mobileNumber.trim() === '') {\n      newErrors.mobileNumber = 'Mobile number is required';\n    } else {\n      // Validate mobile number format (matches backend regex)\n      const mobileRegex = /^(\\+91[\\-\\s]?)?[0]?(91)?[789]\\d{9}$/;\n      if (!mobileRegex.test(formData.mobileNumber)) {\n        newErrors.mobileNumber = 'Invalid mobile number format';\n      }\n    }\n\n    if (!formData.nature) {\n      newErrors.nature = 'Nature is required';\n    } else if (![1, 2, 3, 4].includes(parseInt(formData.nature))) {\n      newErrors.nature = 'Invalid nature value';\n    }\n\n    // Check if form is available (should always have default form as fallback)\n    if (!formConfig) {\n      newErrors.form = 'Form configuration not loaded. Please try again.';\n      return { isValid: false, errors: newErrors };\n    }\n\n\n\n    // Validate form fields\n    formConfig.fields.forEach(field => {\n      const value = formData[field.key];\n      \n      // Required field validation\n      if (field.required && (!value || (typeof value === 'string' && value.trim() === ''))) {\n        newErrors[field.key] = `${field.label} is required`;\n        return;\n      }\n\n      // Conditional field validation\n      if (field.conditional && shouldShowField(field)) {\n        if (field.required && (!value || (typeof value === 'string' && value.trim() === ''))) {\n          newErrors[field.key] = `${field.label} is required`;\n          return;\n        }\n      }\n\n      // Type-specific validation\n      if (value && typeof value === 'string' && value.trim() !== '') {\n        switch (field.type) {\n          case 'email':\n            const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n            if (!emailRegex.test(value)) {\n              newErrors[field.key] = 'Please enter a valid email address';\n            }\n            break;\n          case 'tel':\n            const phoneRegex = /^(\\+91[\\-\\s]?)?[0]?(91)?[789]\\d{9}$/;\n            if (!phoneRegex.test(value)) {\n              newErrors[field.key] = 'Please enter a valid mobile number';\n            }\n            break;\n          case 'url':\n            try {\n              new URL(value);\n            } catch {\n              newErrors[field.key] = 'Please enter a valid URL';\n            }\n            break;\n          case 'number':\n            if (field.validation) {\n              const numValue = parseFloat(value);\n              if (field.validation.min !== undefined && numValue < field.validation.min) {\n                newErrors[field.key] = `Value must be at least ${field.validation.min}`;\n              }\n              if (field.validation.max !== undefined && numValue > field.validation.max) {\n                newErrors[field.key] = `Value must be at most ${field.validation.max}`;\n              }\n            }\n            break;\n        }\n\n        // Pattern validation\n        if (field.validation?.pattern) {\n          const regex = new RegExp(field.validation.pattern);\n          if (!regex.test(value)) {\n            newErrors[field.key] = `${field.label} format is invalid`;\n          }\n        }\n\n        // Length validation\n        if (field.validation?.minLength && value.length < field.validation.minLength) {\n          newErrors[field.key] = `${field.label} must be at least ${field.validation.minLength} characters`;\n        }\n        if (field.validation?.maxLength && value.length > field.validation.maxLength) {\n          newErrors[field.key] = `${field.label} must be at most ${field.validation.maxLength} characters`;\n        }\n\n        // Backend-specific field validations\n        if (field.key === 'primaryEmailId' && value.length > 255) {\n          newErrors[field.key] = 'Email cannot exceed 255 characters';\n        }\n        if (field.key === 'workingState' && value.length > 100) {\n          newErrors[field.key] = 'Working state cannot exceed 100 characters';\n        }\n        if (field.key === 'domesticState' && value.length > 100) {\n          newErrors[field.key] = 'Domestic state cannot exceed 100 characters';\n        }\n        if (field.key === 'district' && value.length > 100) {\n          newErrors[field.key] = 'District cannot exceed 100 characters';\n        }\n        if (field.key === 'address' && value.length > 500) {\n          newErrors[field.key] = 'Address cannot exceed 500 characters';\n        }\n        if (field.key === 'workingArea' && value.length > 200) {\n          newErrors[field.key] = 'Working area cannot exceed 200 characters';\n        }\n        if (field.key === 'associateName' && value.length > 255) {\n          newErrors[field.key] = 'Associate name cannot exceed 255 characters';\n        }\n        if (field.key === 'associateRelation' && value.length > 100) {\n          newErrors[field.key] = 'Associate relation cannot exceed 100 characters';\n        }\n        if (field.key === 'reraRegistrationNumber' && value.length > 50) {\n          newErrors[field.key] = 'RERA registration number cannot exceed 50 characters';\n        }\n        if (field.key === 'source' && value.length > 200) {\n          newErrors[field.key] = 'Source cannot exceed 200 characters';\n        }\n        if (field.key === 'remarks' && value.length > 1000) {\n          newErrors[field.key] = 'Remarks cannot exceed 1000 characters';\n        }\n        if (field.key === 'firmName' && value.length > 255) {\n          newErrors[field.key] = 'Firm name cannot exceed 255 characters';\n        }\n        if (field.key === 'authorizedPersonName' && value.length > 255) {\n          newErrors[field.key] = 'Authorized person name cannot exceed 255 characters';\n        }\n        if (field.key === 'authorizedPersonEmail' && value.length > 255) {\n          newErrors[field.key] = 'Authorized person email cannot exceed 255 characters';\n        }\n        if (field.key === 'designation' && value.length > 100) {\n          newErrors[field.key] = 'Designation cannot exceed 100 characters';\n        }\n        if (field.key === 'marketingContact' && value.length > 255) {\n          newErrors[field.key] = 'Marketing contact cannot exceed 255 characters';\n        }\n        if (field.key === 'marketingDesignation' && value.length > 100) {\n          newErrors[field.key] = 'Marketing designation cannot exceed 100 characters';\n        }\n        if (field.key === 'placeOfPosting' && value.length > 200) {\n          newErrors[field.key] = 'Place of posting cannot exceed 200 characters';\n        }\n        if (field.key === 'department' && value.length > 100) {\n          newErrors[field.key] = 'Department cannot exceed 100 characters';\n        }\n      }\n\n      // Validate numeric fields\n      if (field.type === 'number' && value) {\n        const numValue = parseFloat(value);\n        if (field.key === 'starRating' && (numValue < 1 || numValue > 5)) {\n          newErrors[field.key] = 'Star rating must be between 1 and 5';\n        }\n        if (['numberOfOffices', 'numberOfBranches', 'totalEmployeeStrength'].includes(field.key) && numValue < 0) {\n          newErrors[field.key] = `${field.label} must be non-negative`;\n        }\n        if (field.key === 'transactionValue' && numValue < 0) {\n          newErrors[field.key] = 'Transaction value must be non-negative';\n        }\n      }\n\n      // Validate associate mobile number format\n      if (field.key === 'associateMobile' && value && value.trim() !== '') {\n        const mobileRegex = /^(\\+91[\\-\\s]?)?[0]?(91)?[789]\\d{9}$/;\n        if (!mobileRegex.test(value)) {\n          newErrors[field.key] = 'Invalid associate mobile number format';\n        }\n      }\n    });\n\n    // Business logic validation\n    if (formData.isMarried && formData.dateOfMarriage && formData.dateOfBirth) {\n      const birthDate = new Date(formData.dateOfBirth);\n      const marriageDate = new Date(formData.dateOfMarriage);\n      if (marriageDate <= birthDate) {\n        newErrors.dateOfMarriage = 'Marriage date must be after birth date';\n      }\n    }\n\n    return {\n      isValid: Object.keys(newErrors).length === 0,\n      errors: newErrors\n    };\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    \n    const validation = validateForm();\n    if (!validation.isValid) {\n      setErrors(validation.errors);\n      return;\n    }\n\n    setSubmitting(true);\n    try {\n      // Prepare data for API - match backend model exactly (PascalCase field names)\n      const submitData = {\n        // Required hierarchy fields\n        DivisionId: parseInt(selectedDivision),\n        CategoryId: parseInt(selectedCategory),\n        SubCategoryId: selectedSubCategory ? parseInt(selectedSubCategory) : null,\n\n        // Required fields\n        Name: formData.name || '',\n        MobileNumber: formData.mobileNumber || '',\n        Nature: formData.nature ? parseInt(formData.nature) : 1, // Default to Business (1) if not provided\n\n        // Optional enum fields\n        Gender: formData.gender ? parseInt(formData.gender) : null,\n\n        // Contact Information\n        AlternateNumbers: Array.isArray(formData.alternateNumbers)\n          ? formData.alternateNumbers.filter(num => num && num.trim() !== '')\n          : formData.alternateNumbers ? formData.alternateNumbers.split(',').map(s => s.trim()).filter(s => s !== '') : [],\n        AlternateEmailIds: Array.isArray(formData.alternateEmailIds)\n          ? formData.alternateEmailIds.filter(email => email && email.trim() !== '')\n          : formData.alternateEmailIds ? formData.alternateEmailIds.split(',').map(s => s.trim()).filter(s => s !== '') : [],\n\n        // Personal Information\n        DateOfBirth: formData.dateOfBirth || null,\n        IsMarried: Boolean(formData.isMarried),\n        DateOfMarriage: formData.dateOfMarriage || null,\n\n        // Location Information\n        WorkingState: formData.workingState || '',\n        DomesticState: formData.domesticState || '',\n        District: formData.district || '',\n        Address: formData.address || '',\n        WorkingArea: formData.workingArea || '',\n\n        // Associate Information\n        HasAssociate: Boolean(formData.hasAssociate),\n        AssociateName: formData.associateName || '',\n        AssociateRelation: formData.associateRelation || '',\n        AssociateMobile: formData.associateMobile || '',\n\n        // Digital Presence\n        UsingWebsite: Boolean(formData.usingWebsite),\n        UsingCRMApp: Boolean(formData.usingCRMApp),\n\n        // Business Information\n        TransactionValue: formData.transactionValue ? parseFloat(formData.transactionValue) : null,\n        RERARegistrationNumber: formData.reraRegistrationNumber || '',\n        WorkingProfiles: Array.isArray(formData.workingProfiles)\n          ? formData.workingProfiles.map(wp => parseInt(wp)).filter(wp => !isNaN(wp))\n          : [],\n        StarRating: formData.starRating ? parseInt(formData.starRating) : null,\n        Source: formData.source || '',\n        Remarks: formData.remarks || '',\n\n        // Company Information\n        FirmName: formData.firmName || '',\n        NumberOfOffices: formData.numberOfOffices && !isNaN(parseInt(formData.numberOfOffices)) ? parseInt(formData.numberOfOffices) : null,\n        NumberOfBranches: formData.numberOfBranches && !isNaN(parseInt(formData.numberOfBranches)) ? parseInt(formData.numberOfBranches) : null,\n        TotalEmployeeStrength: formData.totalEmployeeStrength && !isNaN(parseInt(formData.totalEmployeeStrength)) ? parseInt(formData.totalEmployeeStrength) : null,\n\n        // Authorized Person\n        AuthorizedPersonName: formData.authorizedPersonName || '',\n        Designation: formData.designation || '',\n\n        // Marketing Information\n        MarketingContact: formData.marketingContact || '',\n        MarketingDesignation: formData.marketingDesignation || '',\n        PlaceOfPosting: formData.placeOfPosting || '',\n        Department: formData.department || ''\n      };\n\n      // Only add optional URL and email fields if they have valid values (not empty strings)\n      // This prevents backend validation errors for empty strings on URL/Email fields\n\n      // Email fields - only add if not empty and valid\n      if (formData.primaryEmailId && formData.primaryEmailId.trim() !== '') {\n        submitData.PrimaryEmailId = formData.primaryEmailId.trim();\n      }\n\n      if (formData.authorizedPersonEmail && formData.authorizedPersonEmail.trim() !== '') {\n        submitData.AuthorizedPersonEmail = formData.authorizedPersonEmail.trim();\n      }\n\n      // URL fields - only add if not empty and valid\n      if (formData.website && formData.website.trim() !== '') {\n        submitData.Website = formData.website.trim();\n      }\n\n      if (formData.websiteLink && formData.websiteLink.trim() !== '') {\n        submitData.WebsiteLink = formData.websiteLink.trim();\n      }\n\n      if (formData.crmAppLink && formData.crmAppLink.trim() !== '') {\n        submitData.CRMAppLink = formData.crmAppLink.trim();\n      }\n\n\n\n      // Validate required fields before submission\n      const requiredFieldsCheck = {\n        DivisionId: submitData.DivisionId,\n        CategoryId: submitData.CategoryId,\n        Name: submitData.Name,\n        MobileNumber: submitData.MobileNumber,\n        Nature: submitData.Nature\n      };\n\n      // Check for missing required fields\n      const missingFields = Object.entries(requiredFieldsCheck)\n        .filter(([, value]) => !value || value === '' || value === null)\n        .map(([key]) => key);\n\n      if (missingFields.length > 0) {\n        setErrors({ general: 'Missing required fields: ' + missingFields.join(', ') });\n        return;\n      }\n\n      console.log('Submitting data to backend:', JSON.stringify(submitData, null, 2));\n\n      let result;\n      if (mode === 'create') {\n        result = await apiService.createPerson(submitData);\n      } else {\n        result = await apiService.updatePerson(initialData.id, submitData);\n      }\n\n      onSubmit(result);\n    } catch (error) {\n      console.error('Error submitting form:', error);\n      console.error('Error details:', {\n        status: error.status,\n        data: error.data,\n        response: error.response,\n        message: error.message\n      });\n\n      // Enhanced error handling for backend validation errors\n      if (error.data?.errors || error.response?.data?.errors) {\n        // Handle ASP.NET Core ModelState validation errors\n        const validationErrors = error.data?.errors || error.response?.data?.errors;\n        const backendErrors = {};\n\n        console.log('Validation errors received:', validationErrors);\n\n        Object.keys(validationErrors).forEach(key => {\n          const errorMessages = validationErrors[key];\n          // Map backend field names to frontend field names for display\n          const frontendFieldName = mapBackendFieldToFrontend(key);\n          backendErrors[frontendFieldName] = Array.isArray(errorMessages)\n            ? errorMessages.join(', ')\n            : errorMessages;\n        });\n        setErrors(backendErrors);\n      } else if (error.data?.title || error.data?.detail || error.response?.data?.title || error.response?.data?.detail) {\n        // Handle ProblemDetails format errors\n        const errorMessage = error.data?.detail || error.data?.title || error.response?.data?.detail || error.response?.data?.title;\n        setErrors({ general: errorMessage });\n      } else if (error.isValidationError && error.isValidationError()) {\n        const validationErrors = error.getValidationErrors();\n        console.log('ApiError validation errors:', validationErrors);\n        setErrors(validationErrors);\n      } else {\n        // Handle other error formats\n        const errorMessage = error.data?.message ||\n                           error.response?.data?.message ||\n                           error.data?.title ||\n                           error.message ||\n                           'An error occurred while saving the person. Please check your input and try again.';\n        setErrors({ general: errorMessage });\n      }\n    } finally {\n      setSubmitting(false);\n    }\n  };\n\n  // Helper function to map backend field names to frontend field names for error display\n  const mapBackendFieldToFrontend = (backendFieldName) => {\n    const fieldMapping = {\n      'DivisionId': 'division',\n      'CategoryId': 'category',\n      'SubCategoryId': 'subCategory',\n      'Name': 'name',\n      'MobileNumber': 'mobileNumber',\n      'Nature': 'nature',\n      'Gender': 'gender',\n      'PrimaryEmailId': 'primaryEmailId',\n      'AlternateNumbers': 'alternateNumbers',\n      'AlternateEmailIds': 'alternateEmailIds',\n      'Website': 'website',\n      'DateOfBirth': 'dateOfBirth',\n      'IsMarried': 'isMarried',\n      'DateOfMarriage': 'dateOfMarriage',\n      'WorkingState': 'workingState',\n      'DomesticState': 'domesticState',\n      'District': 'district',\n      'Address': 'address',\n      'WorkingArea': 'workingArea',\n      'HasAssociate': 'hasAssociate',\n      'AssociateName': 'associateName',\n      'AssociateRelation': 'associateRelation',\n      'AssociateMobile': 'associateMobile',\n      'UsingWebsite': 'usingWebsite',\n      'WebsiteLink': 'websiteLink',\n      'UsingCRMApp': 'usingCRMApp',\n      'CRMAppLink': 'crmAppLink',\n      'TransactionValue': 'transactionValue',\n      'RERARegistrationNumber': 'reraRegistrationNumber',\n      'WorkingProfiles': 'workingProfiles',\n      'StarRating': 'starRating',\n      'Source': 'source',\n      'Remarks': 'remarks',\n      'FirmName': 'firmName',\n      'NumberOfOffices': 'numberOfOffices',\n      'NumberOfBranches': 'numberOfBranches',\n      'TotalEmployeeStrength': 'totalEmployeeStrength',\n      'AuthorizedPersonName': 'authorizedPersonName',\n      'AuthorizedPersonEmail': 'authorizedPersonEmail',\n      'Designation': 'designation',\n      'MarketingContact': 'marketingContact',\n      'MarketingDesignation': 'marketingDesignation',\n      'PlaceOfPosting': 'placeOfPosting',\n      'Department': 'department'\n    };\n\n    return fieldMapping[backendFieldName] || backendFieldName.toLowerCase();\n  };\n\n  const shouldShowField = (field) => {\n    if (!field.conditional) return true;\n\n    const conditionValue = formData[field.conditional.field];\n    const expectedValue = field.conditional.value;\n\n    // Handle boolean conditions\n    if (typeof expectedValue === 'boolean' || expectedValue === 'true' || expectedValue === 'false') {\n      return conditionValue === (expectedValue === true || expectedValue === 'true');\n    }\n\n    return conditionValue === expectedValue;\n  };\n\n  const groupFieldsBySections = () => {\n    if (!formConfig || !formConfig.fields) return {};\n\n    const sections = {};\n    formConfig.fields.forEach((field) => {\n      const sectionKey = field.section || 'general';\n      if (!sections[sectionKey]) {\n        sections[sectionKey] = {\n          title: getSectionTitle(sectionKey),\n          fields: []\n        };\n      }\n      sections[sectionKey].fields.push(field);\n    });\n\n    return sections;\n  };\n\n  const getSectionTitle = (sectionKey) => {\n    const titles = {\n      personalInfo: 'Personal Information',\n      contactInfo: 'Contact Information',\n      locationInfo: 'Location Information',\n      businessInfo: 'Business Information',\n      associateInfo: 'Associate Information',\n      digitalPresence: 'Digital Presence',\n      companyInfo: 'Company Information',\n      authorizedPerson: 'Authorized Person',\n      marketingInfo: 'Marketing Information',\n      general: 'General Information'\n    };\n    return titles[sectionKey] || sectionKey;\n  };\n\n  const sections = formConfig ? groupFieldsBySections() : {};\n\n\n\n  return (\n    <div className=\"dynamic-person-form\">\n      <div className=\"form-header\">\n        <h2>{mode === 'create' ? 'Create New Person' : 'Edit Person'}</h2>\n        {formConfig && (\n          <div className=\"form-info\">\n            <span className=\"form-name\">{formConfig.name}</span>\n            {formConfig.description && (\n              <span className=\"form-description\">{formConfig.description}</span>\n            )}\n          </div>\n        )}\n      </div>\n\n      {errors.general && (\n        <div className=\"alert alert-error\">{errors.general}</div>\n      )}\n\n      {/* Display validation error summary */}\n      {Object.keys(errors).length > 0 && !errors.general && (\n        <div className=\"alert alert-error\">\n          <strong>Please fix the following errors:</strong>\n          <ul style={{ margin: '8px 0 0 20px' }}>\n            {Object.entries(errors)\n              .filter(([key]) => key !== 'general')\n              .map(([key, message]) => (\n                <li key={key}>{message}</li>\n              ))}\n          </ul>\n        </div>\n      )}\n\n      <form onSubmit={handleSubmit} className=\"person-form\">\n        {/* Hierarchical Dropdowns */}\n        <div className=\"form-section\">\n          <h3>Division & Category Selection</h3>\n\n          {/* Division Dropdown */}\n          <div className=\"form-group\">\n            <label className=\"form-label\">\n              Division <span className=\"required\">*</span>\n            </label>\n            <select\n              value={selectedDivision}\n              onChange={handleDivisionChange}\n              disabled={loading.divisions}\n              className={`form-select ${errors.division ? 'error' : ''}`}\n              required\n            >\n              <option value=\"\">\n                {loading.divisions ? 'Loading divisions...' : 'Select Division'}\n              </option>\n              {divisions.map(division => (\n                <option key={division.id} value={division.id}>\n                  {division.name}\n                </option>\n              ))}\n            </select>\n            {errors.division && (\n              <div className=\"error-message\">{errors.division}</div>\n            )}\n            {errors.divisions && (\n              <div className=\"error-message\">{errors.divisions}</div>\n            )}\n          </div>\n\n          {/* Category Dropdown */}\n          <div className=\"form-group\">\n            <label className=\"form-label\">\n              Category <span className=\"required\">*</span>\n            </label>\n            <select\n              value={selectedCategory}\n              onChange={handleCategoryChange}\n              disabled={!selectedDivision || loading.categories}\n              className={`form-select ${errors.category ? 'error' : ''}`}\n              required\n            >\n              <option value=\"\">\n                {!selectedDivision\n                  ? 'Select Division first'\n                  : loading.categories\n                    ? 'Loading categories...'\n                    : 'Select Category'\n                }\n              </option>\n              {categories.map(category => (\n                <option key={category.id} value={category.id}>\n                  {category.name}\n                </option>\n              ))}\n            </select>\n            {errors.category && (\n              <div className=\"error-message\">{errors.category}</div>\n            )}\n            {errors.categories && (\n              <div className=\"error-message\">{errors.categories}</div>\n            )}\n          </div>\n\n          {/* Subcategory Dropdown - Only show if category doesn't have a form */}\n          {formAvailability.showSubCategoryDropdown && (\n            <div className=\"form-group\">\n              <label className=\"form-label\">\n                Subcategory (Optional)\n              </label>\n              <select\n                value={selectedSubCategory}\n                onChange={handleSubCategoryChange}\n                disabled={!selectedCategory || loading.subCategories}\n                className=\"form-select\"\n              >\n                <option value=\"\">\n                  {!selectedCategory\n                    ? 'Select Category first'\n                    : loading.subCategories\n                      ? 'Loading subcategories...'\n                      : 'Select Subcategory (Optional)'\n                  }\n                </option>\n                {subCategories.map(subCategory => (\n                  <option key={subCategory.id} value={subCategory.id}>\n                    {subCategory.name}\n                  </option>\n                ))}\n              </select>\n              {errors.subCategories && (\n                <div className=\"error-message\">{errors.subCategories}</div>\n              )}\n            </div>\n          )}\n\n          {/* Form Status Messages */}\n          {loading.form && (\n            <div className=\"loading-message\">\n              <span>Loading form configuration...</span>\n            </div>\n          )}\n\n          {formAvailability.message && !loading.form && (\n            <div className={`status-message ${\n              formAvailability.categoryHasForm || formAvailability.subCategoryHasForm\n                ? 'success'\n                : formAvailability.message.includes('No form')\n                  ? 'error'\n                  : 'info'\n            }`}>\n              {formAvailability.message}\n            </div>\n          )}\n\n          {errors.form && (\n            <div className=\"error-message\">{errors.form}</div>\n          )}\n        </div>\n\n        {/* Dynamic Form Fields - Only show when form is available */}\n        {formConfig && Object.entries(sections).map(([sectionKey, section]) => (\n          <div key={sectionKey} className=\"form-section\">\n            <h3>{section.title}</h3>\n            <div className=\"form-fields\">\n              {section.fields\n                .filter(field => shouldShowField(field))\n                .map((field, fieldIndex) => (\n                  <FormField\n                    key={`${sectionKey}-${field.key}-${fieldIndex}`}\n                    field={field}\n                    value={formData[field.key]}\n                    onChange={(value) => handleFieldChange(field.key, value)}\n                    error={errors[field.key]}\n                  />\n                ))}\n            </div>\n          </div>\n        ))}\n\n\n\n        {/* Form Actions */}\n        <div className=\"form-actions\">\n          <button\n            type=\"button\"\n            onClick={onCancel}\n            className=\"btn btn-outline\"\n            disabled={submitting}\n          >\n            Cancel\n          </button>\n          <button\n            type=\"submit\"\n            className=\"btn btn-primary\"\n            disabled={submitting || !selectedDivision || !selectedCategory}\n          >\n            {submitting\n              ? (mode === 'create' ? 'Creating...' : 'Updating...')\n              : (mode === 'create' ? 'Create Person' : 'Update Person')\n            }\n          </button>\n        </div>\n      </form>\n    </div>\n  );\n};\n\nexport default DynamicPersonForm;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,kBAAkB,EAAEC,YAAY,EAAEC,oBAAoB,EAAEC,oBAAoB,QAAQ,iCAAiC;AAC9H,OAAOC,iBAAiB,MAAM,kCAAkC;AAChE,OAAOC,UAAU,MAAM,2BAA2B;AAClD,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAO,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjC,MAAMC,iBAAiB,GAAGA,CAAC;EAAEC,QAAQ;EAAEC,QAAQ;EAAEC,WAAW,GAAG,IAAI;EAAEC,IAAI,GAAG;AAAS,CAAC,KAAK;EAAAC,EAAA;EACzF;EACA,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACmB,UAAU,EAAEC,aAAa,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACqB,aAAa,EAAEC,gBAAgB,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;;EAEtD;EACA,MAAM,CAACuB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACyB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAAC2B,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;;EAElE;EACA,MAAM,CAAC6B,UAAU,EAAEC,aAAa,CAAC,GAAG9B,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAAC+B,QAAQ,EAAEC,WAAW,CAAC,GAAGhC,QAAQ,CAACK,oBAAoB,CAAC,CAAC,CAAC;EAChE,MAAM,CAAC4B,MAAM,EAAEC,SAAS,CAAC,GAAGlC,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAExC;EACA,MAAM,CAACmC,OAAO,EAAEC,UAAU,CAAC,GAAGpC,QAAQ,CAAC;IACrCiB,SAAS,EAAE,KAAK;IAChBE,UAAU,EAAE,KAAK;IACjBE,aAAa,EAAE,KAAK;IACpBgB,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGvC,QAAQ,CAAC,KAAK,CAAC;;EAEnD;EACA,MAAM,CAACwC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGzC,QAAQ,CAAC;IACvD0C,eAAe,EAAE,KAAK;IACtBC,kBAAkB,EAAE,KAAK;IACzBC,uBAAuB,EAAE,KAAK;IAC9BC,OAAO,EAAE;EACX,CAAC,CAAC;;EAEF;EACA5C,SAAS,CAAC,MAAM;IACd6C,aAAa,CAAC,CAAC;;IAEf;IACAC,mBAAmB,CAAC,CAAC;;IAErB;IACA,IAAIjC,WAAW,EAAE;MAAA,IAAAkC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA;MACflB,WAAW,CAAClB,WAAW,CAAC;MACxBU,mBAAmB,CAAC,EAAAwB,qBAAA,GAAAlC,WAAW,CAACqC,UAAU,cAAAH,qBAAA,uBAAtBA,qBAAA,CAAwBI,QAAQ,CAAC,CAAC,KAAI,EAAE,CAAC;MAC7D1B,mBAAmB,CAAC,EAAAuB,qBAAA,GAAAnC,WAAW,CAACuC,UAAU,cAAAJ,qBAAA,uBAAtBA,qBAAA,CAAwBG,QAAQ,CAAC,CAAC,KAAI,EAAE,CAAC;MAC7DxB,sBAAsB,CAAC,EAAAsB,qBAAA,GAAApC,WAAW,CAACwC,aAAa,cAAAJ,qBAAA,uBAAzBA,qBAAA,CAA2BE,QAAQ,CAAC,CAAC,KAAI,EAAE,CAAC;IACrE,CAAC,MAAM;MACL;MACA,MAAMG,WAAW,GAAGjD,iBAAiB,CAACkD,oBAAoB,CAAC,CAAC;MAC5D;MACA,IAAID,WAAW,CAACE,MAAM,EAAE;QACtBF,WAAW,CAACE,MAAM,GAAGC,iBAAiB,CAACH,WAAW,CAACE,MAAM,CAAC;MAC5D;MACA3B,aAAa,CAACyB,WAAW,CAAC;IAC5B;EACF,CAAC,EAAE,CAACzC,WAAW,CAAC,CAAC;;EAEjB;EACA,MAAMiC,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACF,MAAMxC,UAAU,CAACoD,YAAY,CAAC,CAAC;IACjC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;IACvD;EACF,CAAC;;EAED;EACA,MAAMF,iBAAiB,GAAID,MAAM,IAAK;IACpC,MAAMK,IAAI,GAAG,IAAIC,GAAG,CAAC,CAAC;IACtB,MAAMC,YAAY,GAAG,EAAE;IAEvBP,MAAM,CAACQ,OAAO,CAACC,KAAK,IAAI;MACtB,IAAI,CAACJ,IAAI,CAACK,GAAG,CAACD,KAAK,CAACE,GAAG,CAAC,EAAE;QACxBN,IAAI,CAACO,GAAG,CAACH,KAAK,CAACE,GAAG,CAAC;QACnBJ,YAAY,CAACM,IAAI,CAACJ,KAAK,CAAC;MAC1B,CAAC,MAAM;QACLL,OAAO,CAACU,IAAI,CAAC,gDAAgDL,KAAK,CAACE,GAAG,EAAE,CAAC;MAC3E;IACF,CAAC,CAAC;IAEF,IAAIX,MAAM,CAACe,MAAM,KAAKR,YAAY,CAACQ,MAAM,EAAE;MACzCX,OAAO,CAACY,GAAG,CAAC,mCAAmChB,MAAM,CAACe,MAAM,cAAcR,YAAY,CAACQ,MAAM,gBAAgB,CAAC;IAChH;IAEA,OAAOR,YAAY;EACrB,CAAC;;EAED;EACA/D,SAAS,CAAC,MAAM;IACd,IAAIsB,gBAAgB,EAAE;MACpBmD,cAAc,CAACnD,gBAAgB,CAAC;MAChC;MACAG,mBAAmB,CAAC,EAAE,CAAC;MACvBE,sBAAsB,CAAC,EAAE,CAAC;MAC1BN,gBAAgB,CAAC,EAAE,CAAC;MACpBQ,aAAa,CAAC,IAAI,CAAC;MACnBW,mBAAmB,CAAC;QAClBC,eAAe,EAAE,KAAK;QACtBC,kBAAkB,EAAE,KAAK;QACzBC,uBAAuB,EAAE,KAAK;QAC9BC,OAAO,EAAE;MACX,CAAC,CAAC;IACJ,CAAC,MAAM;MACLzB,aAAa,CAAC,EAAE,CAAC;MACjBE,gBAAgB,CAAC,EAAE,CAAC;MACpBI,mBAAmB,CAAC,EAAE,CAAC;MACvBE,sBAAsB,CAAC,EAAE,CAAC;MAC1BE,aAAa,CAAC,IAAI,CAAC;IACrB;EACF,CAAC,EAAE,CAACP,gBAAgB,CAAC,CAAC;;EAEtB;EACAtB,SAAS,CAAC,MAAM;IACd,IAAIwB,gBAAgB,EAAE;MACpBkD,uBAAuB,CAAClD,gBAAgB,CAAC;MACzC;MACAG,sBAAsB,CAAC,EAAE,CAAC;IAC5B,CAAC,MAAM;MACLN,gBAAgB,CAAC,EAAE,CAAC;MACpBM,sBAAsB,CAAC,EAAE,CAAC;MAC1BE,aAAa,CAAC,IAAI,CAAC;MACnBW,mBAAmB,CAAC;QAClBC,eAAe,EAAE,KAAK;QACtBC,kBAAkB,EAAE,KAAK;QACzBC,uBAAuB,EAAE,KAAK;QAC9BC,OAAO,EAAE;MACX,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACpB,gBAAgB,CAAC,CAAC;;EAEtB;EACAxB,SAAS,CAAC,MAAM;IACd,IAAI0B,mBAAmB,EAAE;MACvBiD,0BAA0B,CAACjD,mBAAmB,CAAC;IACjD;EACF,CAAC,EAAE,CAACA,mBAAmB,CAAC,CAAC;;EAEzB;EACA,MAAMmB,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChCV,UAAU,CAACyC,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE5D,SAAS,EAAE;IAAK,CAAC,CAAC,CAAC;IAClD,IAAI;MACF,MAAM6D,QAAQ,GAAG,MAAMvE,UAAU,CAACoD,YAAY,CAAC,CAAC;MAChDzC,YAAY,CAAC4D,QAAQ,CAACC,IAAI,IAAI,EAAE,CAAC;IACnC,CAAC,CAAC,OAAOnB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChD1B,SAAS,CAAC2C,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE5D,SAAS,EAAE;MAA2B,CAAC,CAAC,CAAC;IACzE,CAAC,SAAS;MACRmB,UAAU,CAACyC,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE5D,SAAS,EAAE;MAAM,CAAC,CAAC,CAAC;IACrD;EACF,CAAC;;EAED;EACA,MAAMyD,cAAc,GAAG,MAAOvB,UAAU,IAAK;IAC3Cf,UAAU,CAACyC,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE1D,UAAU,EAAE;IAAK,CAAC,CAAC,CAAC;IACnD,IAAI;MACF,MAAM2D,QAAQ,GAAG,MAAMvE,UAAU,CAACyE,uBAAuB,CAAC7B,UAAU,CAAC;MACrE/B,aAAa,CAAC0D,QAAQ,CAACC,IAAI,IAAI,EAAE,CAAC;IACpC,CAAC,CAAC,OAAOnB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD1B,SAAS,CAAC2C,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE1D,UAAU,EAAE;MAA4B,CAAC,CAAC,CAAC;MACzEC,aAAa,CAAC,EAAE,CAAC;IACnB,CAAC,SAAS;MACRgB,UAAU,CAACyC,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE1D,UAAU,EAAE;MAAM,CAAC,CAAC,CAAC;IACtD;EACF,CAAC;;EAED;EACA,MAAM8D,iBAAiB,GAAG,MAAO5B,UAAU,IAAK;IAC9CjB,UAAU,CAACyC,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAExD,aAAa,EAAE;IAAK,CAAC,CAAC,CAAC;IACtD,IAAI;MACF,MAAMyD,QAAQ,GAAG,MAAMvE,UAAU,CAAC2E,0BAA0B,CAAC7B,UAAU,CAAC;MACxE/B,gBAAgB,CAACwD,QAAQ,CAACC,IAAI,IAAI,EAAE,CAAC;IACvC,CAAC,CAAC,OAAOnB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD1B,SAAS,CAAC2C,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAExD,aAAa,EAAE;MAA+B,CAAC,CAAC,CAAC;MAC/EC,gBAAgB,CAAC,EAAE,CAAC;IACtB,CAAC,SAAS;MACRc,UAAU,CAACyC,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAExD,aAAa,EAAE;MAAM,CAAC,CAAC,CAAC;IACzD;EACF,CAAC;;EAED;EACA,MAAMsD,uBAAuB,GAAG,MAAOtB,UAAU,IAAK;IACpDjB,UAAU,CAACyC,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAExC,IAAI,EAAE;IAAK,CAAC,CAAC,CAAC;IAE7C,IAAI;MACF;MACA,MAAMK,eAAe,GAAGpC,iBAAiB,CAAC6E,kBAAkB,CAACC,QAAQ,CAAC/B,UAAU,CAAC,CAAC;MAElF,IAAIX,eAAe,EAAE;QACnB;QACA,MAAM2C,YAAY,GAAG/E,iBAAiB,CAACgF,cAAc,CAAC,UAAU,EAAEF,QAAQ,CAAC/B,UAAU,CAAC,CAAC;QACvF;QACA,IAAIgC,YAAY,IAAIA,YAAY,CAAC5B,MAAM,EAAE;UACvC4B,YAAY,CAAC5B,MAAM,GAAGC,iBAAiB,CAAC2B,YAAY,CAAC5B,MAAM,CAAC;QAC9D;QACA3B,aAAa,CAACuD,YAAY,CAAC;QAC3B5C,mBAAmB,CAAC;UAClBC,eAAe,EAAE,IAAI;UACrBC,kBAAkB,EAAE,KAAK;UACzBC,uBAAuB,EAAE,KAAK;UAC9BC,OAAO,EAAE;QACX,CAAC,CAAC;MACJ,CAAC,MAAM;QACL;QACA,MAAMoC,iBAAiB,CAAC5B,UAAU,CAAC;QACnC;QACA,MAAME,WAAW,GAAGjD,iBAAiB,CAACkD,oBAAoB,CAAC,CAAC;QAC5D;QACA,IAAID,WAAW,CAACE,MAAM,EAAE;UACtBF,WAAW,CAACE,MAAM,GAAGC,iBAAiB,CAACH,WAAW,CAACE,MAAM,CAAC;QAC5D;QACA3B,aAAa,CAACyB,WAAW,CAAC;QAC1Bd,mBAAmB,CAAC;UAClBC,eAAe,EAAE,KAAK;UACtBC,kBAAkB,EAAE,KAAK;UACzBC,uBAAuB,EAAE,IAAI;UAC7BC,OAAO,EAAE;QACX,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAOe,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;MAC1D1B,SAAS,CAAC2C,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAExC,IAAI,EAAE;MAAmC,CAAC,CAAC,CAAC;IAC5E,CAAC,SAAS;MACRD,UAAU,CAACyC,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAExC,IAAI,EAAE;MAAM,CAAC,CAAC,CAAC;IAChD;EACF,CAAC;;EAED;EACA,MAAMuC,0BAA0B,GAAG,MAAOtB,aAAa,IAAK;IAC1DlB,UAAU,CAACyC,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAExC,IAAI,EAAE;IAAK,CAAC,CAAC,CAAC;IAE7C,IAAI;MACF;MACA,MAAMM,kBAAkB,GAAGrC,iBAAiB,CAACiF,qBAAqB,CAACH,QAAQ,CAAC9B,aAAa,CAAC,CAAC;MAE3F,IAAIX,kBAAkB,EAAE;QACtB;QACA,MAAM6C,eAAe,GAAGlF,iBAAiB,CAACgF,cAAc,CAAC,aAAa,EAAEF,QAAQ,CAAC9B,aAAa,CAAC,CAAC;QAChG;QACA,IAAIkC,eAAe,IAAIA,eAAe,CAAC/B,MAAM,EAAE;UAC7C+B,eAAe,CAAC/B,MAAM,GAAGC,iBAAiB,CAAC8B,eAAe,CAAC/B,MAAM,CAAC;QACpE;QACA3B,aAAa,CAAC0D,eAAe,CAAC;QAC9B/C,mBAAmB,CAAC;UAClBC,eAAe,EAAE,KAAK;UACtBC,kBAAkB,EAAE,IAAI;UACxBC,uBAAuB,EAAE,IAAI;UAC7BC,OAAO,EAAE;QACX,CAAC,CAAC;MACJ,CAAC,MAAM;QACL;QACA,MAAMU,WAAW,GAAGjD,iBAAiB,CAACkD,oBAAoB,CAAC,CAAC;QAC5D;QACA,IAAID,WAAW,CAACE,MAAM,EAAE;UACtBF,WAAW,CAACE,MAAM,GAAGC,iBAAiB,CAACH,WAAW,CAACE,MAAM,CAAC;QAC5D;QACA3B,aAAa,CAACyB,WAAW,CAAC;QAC1Bd,mBAAmB,CAAC;UAClBC,eAAe,EAAE,KAAK;UACtBC,kBAAkB,EAAE,KAAK;UACzBC,uBAAuB,EAAE,IAAI;UAC7BC,OAAO,EAAE;QACX,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAOe,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;MAC7D1B,SAAS,CAAC2C,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAExC,IAAI,EAAE;MAAmC,CAAC,CAAC,CAAC;IAC5E,CAAC,SAAS;MACRD,UAAU,CAACyC,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAExC,IAAI,EAAE;MAAM,CAAC,CAAC,CAAC;IAChD;EACF,CAAC;;EAED;EACA,MAAMoD,oBAAoB,GAAIC,CAAC,IAAK;IAClC,MAAMC,KAAK,GAAGD,CAAC,CAACE,MAAM,CAACD,KAAK;IAC5BnE,mBAAmB,CAACmE,KAAK,CAAC;;IAE1B;IACA3D,WAAW,CAAC6C,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP1B,UAAU,EAAEwC,KAAK,GAAGP,QAAQ,CAACO,KAAK,CAAC,GAAG,IAAI;MAC1CtC,UAAU,EAAE,IAAI;MAChBC,aAAa,EAAE;IACjB,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMuC,oBAAoB,GAAIH,CAAC,IAAK;IAClC,MAAMC,KAAK,GAAGD,CAAC,CAACE,MAAM,CAACD,KAAK;IAC5BjE,mBAAmB,CAACiE,KAAK,CAAC;;IAE1B;IACA3D,WAAW,CAAC6C,IAAI,KAAK;MACnB,GAAGA,IAAI;MACPxB,UAAU,EAAEsC,KAAK,GAAGP,QAAQ,CAACO,KAAK,CAAC,GAAG,IAAI;MAC1CrC,aAAa,EAAE;IACjB,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMwC,uBAAuB,GAAIJ,CAAC,IAAK;IACrC,MAAMC,KAAK,GAAGD,CAAC,CAACE,MAAM,CAACD,KAAK;IAC5B/D,sBAAsB,CAAC+D,KAAK,CAAC;;IAE7B;IACA3D,WAAW,CAAC6C,IAAI,KAAK;MACnB,GAAGA,IAAI;MACPvB,aAAa,EAAEqC,KAAK,GAAGP,QAAQ,CAACO,KAAK,CAAC,GAAG;IAC3C,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMI,iBAAiB,GAAGA,CAACC,QAAQ,EAAEL,KAAK,KAAK;IAC7C3D,WAAW,CAAC6C,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACmB,QAAQ,GAAGL;IACd,CAAC,CAAC,CAAC;;IAEH;IACA,IAAI1D,MAAM,CAAC+D,QAAQ,CAAC,EAAE;MACpB9D,SAAS,CAAC2C,IAAI,KAAK;QACjB,GAAGA,IAAI;QACP,CAACmB,QAAQ,GAAG;MACd,CAAC,CAAC,CAAC;IACL;EACF,CAAC;EAED,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,SAAS,GAAG,CAAC,CAAC;;IAEpB;IACA,IAAI,CAAC3E,gBAAgB,EAAE;MACrB2E,SAAS,CAACC,QAAQ,GAAG,sBAAsB;IAC7C,CAAC,MAAM,IAAIf,QAAQ,CAAC7D,gBAAgB,CAAC,GAAG,CAAC,EAAE;MACzC2E,SAAS,CAACC,QAAQ,GAAG,uCAAuC;IAC9D;IAEA,IAAI,CAAC1E,gBAAgB,EAAE;MACrByE,SAAS,CAACE,QAAQ,GAAG,sBAAsB;IAC7C,CAAC,MAAM,IAAIhB,QAAQ,CAAC3D,gBAAgB,CAAC,GAAG,CAAC,EAAE;MACzCyE,SAAS,CAACE,QAAQ,GAAG,uCAAuC;IAC9D;IAEA,IAAIzE,mBAAmB,IAAIyD,QAAQ,CAACzD,mBAAmB,CAAC,GAAG,CAAC,EAAE;MAC5DuE,SAAS,CAACG,WAAW,GAAG,0CAA0C;IACpE;;IAEA;IACA,IAAI,CAACtE,QAAQ,CAACuE,IAAI,IAAIvE,QAAQ,CAACuE,IAAI,CAACC,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MACjDL,SAAS,CAACI,IAAI,GAAG,kBAAkB;IACrC,CAAC,MAAM,IAAIvE,QAAQ,CAACuE,IAAI,CAAC9B,MAAM,GAAG,GAAG,EAAE;MACrC0B,SAAS,CAACI,IAAI,GAAG,mCAAmC;IACtD;IAEA,IAAI,CAACvE,QAAQ,CAACyE,YAAY,IAAIzE,QAAQ,CAACyE,YAAY,CAACD,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MACjEL,SAAS,CAACM,YAAY,GAAG,2BAA2B;IACtD,CAAC,MAAM;MACL;MACA,MAAMC,WAAW,GAAG,qCAAqC;MACzD,IAAI,CAACA,WAAW,CAACC,IAAI,CAAC3E,QAAQ,CAACyE,YAAY,CAAC,EAAE;QAC5CN,SAAS,CAACM,YAAY,GAAG,8BAA8B;MACzD;IACF;IAEA,IAAI,CAACzE,QAAQ,CAAC4E,MAAM,EAAE;MACpBT,SAAS,CAACS,MAAM,GAAG,oBAAoB;IACzC,CAAC,MAAM,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAACC,QAAQ,CAACxB,QAAQ,CAACrD,QAAQ,CAAC4E,MAAM,CAAC,CAAC,EAAE;MAC5DT,SAAS,CAACS,MAAM,GAAG,sBAAsB;IAC3C;;IAEA;IACA,IAAI,CAAC9E,UAAU,EAAE;MACfqE,SAAS,CAAC7D,IAAI,GAAG,kDAAkD;MACnE,OAAO;QAAEwE,OAAO,EAAE,KAAK;QAAE5E,MAAM,EAAEiE;MAAU,CAAC;IAC9C;;IAIA;IACArE,UAAU,CAAC4B,MAAM,CAACQ,OAAO,CAACC,KAAK,IAAI;MACjC,MAAMyB,KAAK,GAAG5D,QAAQ,CAACmC,KAAK,CAACE,GAAG,CAAC;;MAEjC;MACA,IAAIF,KAAK,CAAC4C,QAAQ,KAAK,CAACnB,KAAK,IAAK,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACY,IAAI,CAAC,CAAC,KAAK,EAAG,CAAC,EAAE;QACpFL,SAAS,CAAChC,KAAK,CAACE,GAAG,CAAC,GAAG,GAAGF,KAAK,CAAC6C,KAAK,cAAc;QACnD;MACF;;MAEA;MACA,IAAI7C,KAAK,CAAC8C,WAAW,IAAIC,eAAe,CAAC/C,KAAK,CAAC,EAAE;QAC/C,IAAIA,KAAK,CAAC4C,QAAQ,KAAK,CAACnB,KAAK,IAAK,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACY,IAAI,CAAC,CAAC,KAAK,EAAG,CAAC,EAAE;UACpFL,SAAS,CAAChC,KAAK,CAACE,GAAG,CAAC,GAAG,GAAGF,KAAK,CAAC6C,KAAK,cAAc;UACnD;QACF;MACF;;MAEA;MACA,IAAIpB,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACY,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;QAAA,IAAAW,iBAAA,EAAAC,kBAAA,EAAAC,kBAAA;QAC7D,QAAQlD,KAAK,CAACmD,IAAI;UAChB,KAAK,OAAO;YACV,MAAMC,UAAU,GAAG,4BAA4B;YAC/C,IAAI,CAACA,UAAU,CAACZ,IAAI,CAACf,KAAK,CAAC,EAAE;cAC3BO,SAAS,CAAChC,KAAK,CAACE,GAAG,CAAC,GAAG,oCAAoC;YAC7D;YACA;UACF,KAAK,KAAK;YACR,MAAMmD,UAAU,GAAG,qCAAqC;YACxD,IAAI,CAACA,UAAU,CAACb,IAAI,CAACf,KAAK,CAAC,EAAE;cAC3BO,SAAS,CAAChC,KAAK,CAACE,GAAG,CAAC,GAAG,oCAAoC;YAC7D;YACA;UACF,KAAK,KAAK;YACR,IAAI;cACF,IAAIoD,GAAG,CAAC7B,KAAK,CAAC;YAChB,CAAC,CAAC,MAAM;cACNO,SAAS,CAAChC,KAAK,CAACE,GAAG,CAAC,GAAG,0BAA0B;YACnD;YACA;UACF,KAAK,QAAQ;YACX,IAAIF,KAAK,CAACuD,UAAU,EAAE;cACpB,MAAMC,QAAQ,GAAGC,UAAU,CAAChC,KAAK,CAAC;cAClC,IAAIzB,KAAK,CAACuD,UAAU,CAACG,GAAG,KAAKC,SAAS,IAAIH,QAAQ,GAAGxD,KAAK,CAACuD,UAAU,CAACG,GAAG,EAAE;gBACzE1B,SAAS,CAAChC,KAAK,CAACE,GAAG,CAAC,GAAG,0BAA0BF,KAAK,CAACuD,UAAU,CAACG,GAAG,EAAE;cACzE;cACA,IAAI1D,KAAK,CAACuD,UAAU,CAACK,GAAG,KAAKD,SAAS,IAAIH,QAAQ,GAAGxD,KAAK,CAACuD,UAAU,CAACK,GAAG,EAAE;gBACzE5B,SAAS,CAAChC,KAAK,CAACE,GAAG,CAAC,GAAG,yBAAyBF,KAAK,CAACuD,UAAU,CAACK,GAAG,EAAE;cACxE;YACF;YACA;QACJ;;QAEA;QACA,KAAAZ,iBAAA,GAAIhD,KAAK,CAACuD,UAAU,cAAAP,iBAAA,eAAhBA,iBAAA,CAAkBa,OAAO,EAAE;UAC7B,MAAMC,KAAK,GAAG,IAAIC,MAAM,CAAC/D,KAAK,CAACuD,UAAU,CAACM,OAAO,CAAC;UAClD,IAAI,CAACC,KAAK,CAACtB,IAAI,CAACf,KAAK,CAAC,EAAE;YACtBO,SAAS,CAAChC,KAAK,CAACE,GAAG,CAAC,GAAG,GAAGF,KAAK,CAAC6C,KAAK,oBAAoB;UAC3D;QACF;;QAEA;QACA,IAAI,CAAAI,kBAAA,GAAAjD,KAAK,CAACuD,UAAU,cAAAN,kBAAA,eAAhBA,kBAAA,CAAkBe,SAAS,IAAIvC,KAAK,CAACnB,MAAM,GAAGN,KAAK,CAACuD,UAAU,CAACS,SAAS,EAAE;UAC5EhC,SAAS,CAAChC,KAAK,CAACE,GAAG,CAAC,GAAG,GAAGF,KAAK,CAAC6C,KAAK,qBAAqB7C,KAAK,CAACuD,UAAU,CAACS,SAAS,aAAa;QACnG;QACA,IAAI,CAAAd,kBAAA,GAAAlD,KAAK,CAACuD,UAAU,cAAAL,kBAAA,eAAhBA,kBAAA,CAAkBe,SAAS,IAAIxC,KAAK,CAACnB,MAAM,GAAGN,KAAK,CAACuD,UAAU,CAACU,SAAS,EAAE;UAC5EjC,SAAS,CAAChC,KAAK,CAACE,GAAG,CAAC,GAAG,GAAGF,KAAK,CAAC6C,KAAK,oBAAoB7C,KAAK,CAACuD,UAAU,CAACU,SAAS,aAAa;QAClG;;QAEA;QACA,IAAIjE,KAAK,CAACE,GAAG,KAAK,gBAAgB,IAAIuB,KAAK,CAACnB,MAAM,GAAG,GAAG,EAAE;UACxD0B,SAAS,CAAChC,KAAK,CAACE,GAAG,CAAC,GAAG,oCAAoC;QAC7D;QACA,IAAIF,KAAK,CAACE,GAAG,KAAK,cAAc,IAAIuB,KAAK,CAACnB,MAAM,GAAG,GAAG,EAAE;UACtD0B,SAAS,CAAChC,KAAK,CAACE,GAAG,CAAC,GAAG,4CAA4C;QACrE;QACA,IAAIF,KAAK,CAACE,GAAG,KAAK,eAAe,IAAIuB,KAAK,CAACnB,MAAM,GAAG,GAAG,EAAE;UACvD0B,SAAS,CAAChC,KAAK,CAACE,GAAG,CAAC,GAAG,6CAA6C;QACtE;QACA,IAAIF,KAAK,CAACE,GAAG,KAAK,UAAU,IAAIuB,KAAK,CAACnB,MAAM,GAAG,GAAG,EAAE;UAClD0B,SAAS,CAAChC,KAAK,CAACE,GAAG,CAAC,GAAG,uCAAuC;QAChE;QACA,IAAIF,KAAK,CAACE,GAAG,KAAK,SAAS,IAAIuB,KAAK,CAACnB,MAAM,GAAG,GAAG,EAAE;UACjD0B,SAAS,CAAChC,KAAK,CAACE,GAAG,CAAC,GAAG,sCAAsC;QAC/D;QACA,IAAIF,KAAK,CAACE,GAAG,KAAK,aAAa,IAAIuB,KAAK,CAACnB,MAAM,GAAG,GAAG,EAAE;UACrD0B,SAAS,CAAChC,KAAK,CAACE,GAAG,CAAC,GAAG,2CAA2C;QACpE;QACA,IAAIF,KAAK,CAACE,GAAG,KAAK,eAAe,IAAIuB,KAAK,CAACnB,MAAM,GAAG,GAAG,EAAE;UACvD0B,SAAS,CAAChC,KAAK,CAACE,GAAG,CAAC,GAAG,6CAA6C;QACtE;QACA,IAAIF,KAAK,CAACE,GAAG,KAAK,mBAAmB,IAAIuB,KAAK,CAACnB,MAAM,GAAG,GAAG,EAAE;UAC3D0B,SAAS,CAAChC,KAAK,CAACE,GAAG,CAAC,GAAG,iDAAiD;QAC1E;QACA,IAAIF,KAAK,CAACE,GAAG,KAAK,wBAAwB,IAAIuB,KAAK,CAACnB,MAAM,GAAG,EAAE,EAAE;UAC/D0B,SAAS,CAAChC,KAAK,CAACE,GAAG,CAAC,GAAG,sDAAsD;QAC/E;QACA,IAAIF,KAAK,CAACE,GAAG,KAAK,QAAQ,IAAIuB,KAAK,CAACnB,MAAM,GAAG,GAAG,EAAE;UAChD0B,SAAS,CAAChC,KAAK,CAACE,GAAG,CAAC,GAAG,qCAAqC;QAC9D;QACA,IAAIF,KAAK,CAACE,GAAG,KAAK,SAAS,IAAIuB,KAAK,CAACnB,MAAM,GAAG,IAAI,EAAE;UAClD0B,SAAS,CAAChC,KAAK,CAACE,GAAG,CAAC,GAAG,uCAAuC;QAChE;QACA,IAAIF,KAAK,CAACE,GAAG,KAAK,UAAU,IAAIuB,KAAK,CAACnB,MAAM,GAAG,GAAG,EAAE;UAClD0B,SAAS,CAAChC,KAAK,CAACE,GAAG,CAAC,GAAG,wCAAwC;QACjE;QACA,IAAIF,KAAK,CAACE,GAAG,KAAK,sBAAsB,IAAIuB,KAAK,CAACnB,MAAM,GAAG,GAAG,EAAE;UAC9D0B,SAAS,CAAChC,KAAK,CAACE,GAAG,CAAC,GAAG,qDAAqD;QAC9E;QACA,IAAIF,KAAK,CAACE,GAAG,KAAK,uBAAuB,IAAIuB,KAAK,CAACnB,MAAM,GAAG,GAAG,EAAE;UAC/D0B,SAAS,CAAChC,KAAK,CAACE,GAAG,CAAC,GAAG,sDAAsD;QAC/E;QACA,IAAIF,KAAK,CAACE,GAAG,KAAK,aAAa,IAAIuB,KAAK,CAACnB,MAAM,GAAG,GAAG,EAAE;UACrD0B,SAAS,CAAChC,KAAK,CAACE,GAAG,CAAC,GAAG,0CAA0C;QACnE;QACA,IAAIF,KAAK,CAACE,GAAG,KAAK,kBAAkB,IAAIuB,KAAK,CAACnB,MAAM,GAAG,GAAG,EAAE;UAC1D0B,SAAS,CAAChC,KAAK,CAACE,GAAG,CAAC,GAAG,gDAAgD;QACzE;QACA,IAAIF,KAAK,CAACE,GAAG,KAAK,sBAAsB,IAAIuB,KAAK,CAACnB,MAAM,GAAG,GAAG,EAAE;UAC9D0B,SAAS,CAAChC,KAAK,CAACE,GAAG,CAAC,GAAG,oDAAoD;QAC7E;QACA,IAAIF,KAAK,CAACE,GAAG,KAAK,gBAAgB,IAAIuB,KAAK,CAACnB,MAAM,GAAG,GAAG,EAAE;UACxD0B,SAAS,CAAChC,KAAK,CAACE,GAAG,CAAC,GAAG,+CAA+C;QACxE;QACA,IAAIF,KAAK,CAACE,GAAG,KAAK,YAAY,IAAIuB,KAAK,CAACnB,MAAM,GAAG,GAAG,EAAE;UACpD0B,SAAS,CAAChC,KAAK,CAACE,GAAG,CAAC,GAAG,yCAAyC;QAClE;MACF;;MAEA;MACA,IAAIF,KAAK,CAACmD,IAAI,KAAK,QAAQ,IAAI1B,KAAK,EAAE;QACpC,MAAM+B,QAAQ,GAAGC,UAAU,CAAChC,KAAK,CAAC;QAClC,IAAIzB,KAAK,CAACE,GAAG,KAAK,YAAY,KAAKsD,QAAQ,GAAG,CAAC,IAAIA,QAAQ,GAAG,CAAC,CAAC,EAAE;UAChExB,SAAS,CAAChC,KAAK,CAACE,GAAG,CAAC,GAAG,qCAAqC;QAC9D;QACA,IAAI,CAAC,iBAAiB,EAAE,kBAAkB,EAAE,uBAAuB,CAAC,CAACwC,QAAQ,CAAC1C,KAAK,CAACE,GAAG,CAAC,IAAIsD,QAAQ,GAAG,CAAC,EAAE;UACxGxB,SAAS,CAAChC,KAAK,CAACE,GAAG,CAAC,GAAG,GAAGF,KAAK,CAAC6C,KAAK,uBAAuB;QAC9D;QACA,IAAI7C,KAAK,CAACE,GAAG,KAAK,kBAAkB,IAAIsD,QAAQ,GAAG,CAAC,EAAE;UACpDxB,SAAS,CAAChC,KAAK,CAACE,GAAG,CAAC,GAAG,wCAAwC;QACjE;MACF;;MAEA;MACA,IAAIF,KAAK,CAACE,GAAG,KAAK,iBAAiB,IAAIuB,KAAK,IAAIA,KAAK,CAACY,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;QACnE,MAAME,WAAW,GAAG,qCAAqC;QACzD,IAAI,CAACA,WAAW,CAACC,IAAI,CAACf,KAAK,CAAC,EAAE;UAC5BO,SAAS,CAAChC,KAAK,CAACE,GAAG,CAAC,GAAG,wCAAwC;QACjE;MACF;IACF,CAAC,CAAC;;IAEF;IACA,IAAIrC,QAAQ,CAACqG,SAAS,IAAIrG,QAAQ,CAACsG,cAAc,IAAItG,QAAQ,CAACuG,WAAW,EAAE;MACzE,MAAMC,SAAS,GAAG,IAAIC,IAAI,CAACzG,QAAQ,CAACuG,WAAW,CAAC;MAChD,MAAMG,YAAY,GAAG,IAAID,IAAI,CAACzG,QAAQ,CAACsG,cAAc,CAAC;MACtD,IAAII,YAAY,IAAIF,SAAS,EAAE;QAC7BrC,SAAS,CAACmC,cAAc,GAAG,wCAAwC;MACrE;IACF;IAEA,OAAO;MACLxB,OAAO,EAAE6B,MAAM,CAACC,IAAI,CAACzC,SAAS,CAAC,CAAC1B,MAAM,KAAK,CAAC;MAC5CvC,MAAM,EAAEiE;IACV,CAAC;EACH,CAAC;EAED,MAAM0C,YAAY,GAAG,MAAOlD,CAAC,IAAK;IAChCA,CAAC,CAACmD,cAAc,CAAC,CAAC;IAElB,MAAMpB,UAAU,GAAGxB,YAAY,CAAC,CAAC;IACjC,IAAI,CAACwB,UAAU,CAACZ,OAAO,EAAE;MACvB3E,SAAS,CAACuF,UAAU,CAACxF,MAAM,CAAC;MAC5B;IACF;IAEAM,aAAa,CAAC,IAAI,CAAC;IACnB,IAAI;MACF;MACA,MAAMuG,UAAU,GAAG;QACjB;QACAC,UAAU,EAAE3D,QAAQ,CAAC7D,gBAAgB,CAAC;QACtCyH,UAAU,EAAE5D,QAAQ,CAAC3D,gBAAgB,CAAC;QACtCwH,aAAa,EAAEtH,mBAAmB,GAAGyD,QAAQ,CAACzD,mBAAmB,CAAC,GAAG,IAAI;QAEzE;QACAuH,IAAI,EAAEnH,QAAQ,CAACuE,IAAI,IAAI,EAAE;QACzB6C,YAAY,EAAEpH,QAAQ,CAACyE,YAAY,IAAI,EAAE;QACzC4C,MAAM,EAAErH,QAAQ,CAAC4E,MAAM,GAAGvB,QAAQ,CAACrD,QAAQ,CAAC4E,MAAM,CAAC,GAAG,CAAC;QAAE;;QAEzD;QACA0C,MAAM,EAAEtH,QAAQ,CAACuH,MAAM,GAAGlE,QAAQ,CAACrD,QAAQ,CAACuH,MAAM,CAAC,GAAG,IAAI;QAE1D;QACAC,gBAAgB,EAAEC,KAAK,CAACC,OAAO,CAAC1H,QAAQ,CAAC2H,gBAAgB,CAAC,GACtD3H,QAAQ,CAAC2H,gBAAgB,CAACC,MAAM,CAACC,GAAG,IAAIA,GAAG,IAAIA,GAAG,CAACrD,IAAI,CAAC,CAAC,KAAK,EAAE,CAAC,GACjExE,QAAQ,CAAC2H,gBAAgB,GAAG3H,QAAQ,CAAC2H,gBAAgB,CAACG,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACxD,IAAI,CAAC,CAAC,CAAC,CAACoD,MAAM,CAACI,CAAC,IAAIA,CAAC,KAAK,EAAE,CAAC,GAAG,EAAE;QAClHC,iBAAiB,EAAER,KAAK,CAACC,OAAO,CAAC1H,QAAQ,CAACkI,iBAAiB,CAAC,GACxDlI,QAAQ,CAACkI,iBAAiB,CAACN,MAAM,CAACO,KAAK,IAAIA,KAAK,IAAIA,KAAK,CAAC3D,IAAI,CAAC,CAAC,KAAK,EAAE,CAAC,GACxExE,QAAQ,CAACkI,iBAAiB,GAAGlI,QAAQ,CAACkI,iBAAiB,CAACJ,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACxD,IAAI,CAAC,CAAC,CAAC,CAACoD,MAAM,CAACI,CAAC,IAAIA,CAAC,KAAK,EAAE,CAAC,GAAG,EAAE;QAEpH;QACAI,WAAW,EAAEpI,QAAQ,CAACuG,WAAW,IAAI,IAAI;QACzC8B,SAAS,EAAEC,OAAO,CAACtI,QAAQ,CAACqG,SAAS,CAAC;QACtCkC,cAAc,EAAEvI,QAAQ,CAACsG,cAAc,IAAI,IAAI;QAE/C;QACAkC,YAAY,EAAExI,QAAQ,CAACyI,YAAY,IAAI,EAAE;QACzCC,aAAa,EAAE1I,QAAQ,CAAC2I,aAAa,IAAI,EAAE;QAC3CC,QAAQ,EAAE5I,QAAQ,CAAC6I,QAAQ,IAAI,EAAE;QACjCC,OAAO,EAAE9I,QAAQ,CAAC+I,OAAO,IAAI,EAAE;QAC/BC,WAAW,EAAEhJ,QAAQ,CAACiJ,WAAW,IAAI,EAAE;QAEvC;QACAC,YAAY,EAAEZ,OAAO,CAACtI,QAAQ,CAACmJ,YAAY,CAAC;QAC5CC,aAAa,EAAEpJ,QAAQ,CAACqJ,aAAa,IAAI,EAAE;QAC3CC,iBAAiB,EAAEtJ,QAAQ,CAACuJ,iBAAiB,IAAI,EAAE;QACnDC,eAAe,EAAExJ,QAAQ,CAACyJ,eAAe,IAAI,EAAE;QAE/C;QACAC,YAAY,EAAEpB,OAAO,CAACtI,QAAQ,CAAC2J,YAAY,CAAC;QAC5CC,WAAW,EAAEtB,OAAO,CAACtI,QAAQ,CAAC6J,WAAW,CAAC;QAE1C;QACAC,gBAAgB,EAAE9J,QAAQ,CAAC+J,gBAAgB,GAAGnE,UAAU,CAAC5F,QAAQ,CAAC+J,gBAAgB,CAAC,GAAG,IAAI;QAC1FC,sBAAsB,EAAEhK,QAAQ,CAACiK,sBAAsB,IAAI,EAAE;QAC7DC,eAAe,EAAEzC,KAAK,CAACC,OAAO,CAAC1H,QAAQ,CAACmK,eAAe,CAAC,GACpDnK,QAAQ,CAACmK,eAAe,CAACpC,GAAG,CAACqC,EAAE,IAAI/G,QAAQ,CAAC+G,EAAE,CAAC,CAAC,CAACxC,MAAM,CAACwC,EAAE,IAAI,CAACC,KAAK,CAACD,EAAE,CAAC,CAAC,GACzE,EAAE;QACNE,UAAU,EAAEtK,QAAQ,CAACuK,UAAU,GAAGlH,QAAQ,CAACrD,QAAQ,CAACuK,UAAU,CAAC,GAAG,IAAI;QACtEC,MAAM,EAAExK,QAAQ,CAACyK,MAAM,IAAI,EAAE;QAC7BC,OAAO,EAAE1K,QAAQ,CAAC2K,OAAO,IAAI,EAAE;QAE/B;QACAC,QAAQ,EAAE5K,QAAQ,CAAC6K,QAAQ,IAAI,EAAE;QACjCC,eAAe,EAAE9K,QAAQ,CAAC+K,eAAe,IAAI,CAACV,KAAK,CAAChH,QAAQ,CAACrD,QAAQ,CAAC+K,eAAe,CAAC,CAAC,GAAG1H,QAAQ,CAACrD,QAAQ,CAAC+K,eAAe,CAAC,GAAG,IAAI;QACnIC,gBAAgB,EAAEhL,QAAQ,CAACiL,gBAAgB,IAAI,CAACZ,KAAK,CAAChH,QAAQ,CAACrD,QAAQ,CAACiL,gBAAgB,CAAC,CAAC,GAAG5H,QAAQ,CAACrD,QAAQ,CAACiL,gBAAgB,CAAC,GAAG,IAAI;QACvIC,qBAAqB,EAAElL,QAAQ,CAACmL,qBAAqB,IAAI,CAACd,KAAK,CAAChH,QAAQ,CAACrD,QAAQ,CAACmL,qBAAqB,CAAC,CAAC,GAAG9H,QAAQ,CAACrD,QAAQ,CAACmL,qBAAqB,CAAC,GAAG,IAAI;QAE3J;QACAC,oBAAoB,EAAEpL,QAAQ,CAACqL,oBAAoB,IAAI,EAAE;QACzDC,WAAW,EAAEtL,QAAQ,CAACuL,WAAW,IAAI,EAAE;QAEvC;QACAC,gBAAgB,EAAExL,QAAQ,CAACyL,gBAAgB,IAAI,EAAE;QACjDC,oBAAoB,EAAE1L,QAAQ,CAAC2L,oBAAoB,IAAI,EAAE;QACzDC,cAAc,EAAE5L,QAAQ,CAAC6L,cAAc,IAAI,EAAE;QAC7CC,UAAU,EAAE9L,QAAQ,CAAC+L,UAAU,IAAI;MACrC,CAAC;;MAED;MACA;;MAEA;MACA,IAAI/L,QAAQ,CAACgM,cAAc,IAAIhM,QAAQ,CAACgM,cAAc,CAACxH,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;QACpEuC,UAAU,CAACkF,cAAc,GAAGjM,QAAQ,CAACgM,cAAc,CAACxH,IAAI,CAAC,CAAC;MAC5D;MAEA,IAAIxE,QAAQ,CAACkM,qBAAqB,IAAIlM,QAAQ,CAACkM,qBAAqB,CAAC1H,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;QAClFuC,UAAU,CAACoF,qBAAqB,GAAGnM,QAAQ,CAACkM,qBAAqB,CAAC1H,IAAI,CAAC,CAAC;MAC1E;;MAEA;MACA,IAAIxE,QAAQ,CAACoM,OAAO,IAAIpM,QAAQ,CAACoM,OAAO,CAAC5H,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;QACtDuC,UAAU,CAACsF,OAAO,GAAGrM,QAAQ,CAACoM,OAAO,CAAC5H,IAAI,CAAC,CAAC;MAC9C;MAEA,IAAIxE,QAAQ,CAACsM,WAAW,IAAItM,QAAQ,CAACsM,WAAW,CAAC9H,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;QAC9DuC,UAAU,CAACwF,WAAW,GAAGvM,QAAQ,CAACsM,WAAW,CAAC9H,IAAI,CAAC,CAAC;MACtD;MAEA,IAAIxE,QAAQ,CAACwM,UAAU,IAAIxM,QAAQ,CAACwM,UAAU,CAAChI,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;QAC5DuC,UAAU,CAAC0F,UAAU,GAAGzM,QAAQ,CAACwM,UAAU,CAAChI,IAAI,CAAC,CAAC;MACpD;;MAIA;MACA,MAAMkI,mBAAmB,GAAG;QAC1B1F,UAAU,EAAED,UAAU,CAACC,UAAU;QACjCC,UAAU,EAAEF,UAAU,CAACE,UAAU;QACjCE,IAAI,EAAEJ,UAAU,CAACI,IAAI;QACrBC,YAAY,EAAEL,UAAU,CAACK,YAAY;QACrCC,MAAM,EAAEN,UAAU,CAACM;MACrB,CAAC;;MAED;MACA,MAAMsF,aAAa,GAAGhG,MAAM,CAACiG,OAAO,CAACF,mBAAmB,CAAC,CACtD9E,MAAM,CAAC,CAAC,GAAGhE,KAAK,CAAC,KAAK,CAACA,KAAK,IAAIA,KAAK,KAAK,EAAE,IAAIA,KAAK,KAAK,IAAI,CAAC,CAC/DmE,GAAG,CAAC,CAAC,CAAC1F,GAAG,CAAC,KAAKA,GAAG,CAAC;MAEtB,IAAIsK,aAAa,CAAClK,MAAM,GAAG,CAAC,EAAE;QAC5BtC,SAAS,CAAC;UAAE0M,OAAO,EAAE,2BAA2B,GAAGF,aAAa,CAACG,IAAI,CAAC,IAAI;QAAE,CAAC,CAAC;QAC9E;MACF;MAEAhL,OAAO,CAACY,GAAG,CAAC,6BAA6B,EAAEqK,IAAI,CAACC,SAAS,CAACjG,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;MAE/E,IAAIkG,MAAM;MACV,IAAIjO,IAAI,KAAK,QAAQ,EAAE;QACrBiO,MAAM,GAAG,MAAMzO,UAAU,CAAC0O,YAAY,CAACnG,UAAU,CAAC;MACpD,CAAC,MAAM;QACLkG,MAAM,GAAG,MAAMzO,UAAU,CAAC2O,YAAY,CAACpO,WAAW,CAACqO,EAAE,EAAErG,UAAU,CAAC;MACpE;MAEAlI,QAAQ,CAACoO,MAAM,CAAC;IAClB,CAAC,CAAC,OAAOpL,KAAK,EAAE;MAAA,IAAAwL,WAAA,EAAAC,eAAA,EAAAC,oBAAA,EAAAC,YAAA,EAAAC,YAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA;MACd/L,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9CC,OAAO,CAACD,KAAK,CAAC,gBAAgB,EAAE;QAC9BiM,MAAM,EAAEjM,KAAK,CAACiM,MAAM;QACpB9K,IAAI,EAAEnB,KAAK,CAACmB,IAAI;QAChBD,QAAQ,EAAElB,KAAK,CAACkB,QAAQ;QACxBjC,OAAO,EAAEe,KAAK,CAACf;MACjB,CAAC,CAAC;;MAEF;MACA,IAAI,CAAAuM,WAAA,GAAAxL,KAAK,CAACmB,IAAI,cAAAqK,WAAA,eAAVA,WAAA,CAAYnN,MAAM,KAAAoN,eAAA,GAAIzL,KAAK,CAACkB,QAAQ,cAAAuK,eAAA,gBAAAC,oBAAA,GAAdD,eAAA,CAAgBtK,IAAI,cAAAuK,oBAAA,eAApBA,oBAAA,CAAsBrN,MAAM,EAAE;QAAA,IAAA6N,YAAA,EAAAC,gBAAA,EAAAC,qBAAA;QACtD;QACA,MAAMC,gBAAgB,GAAG,EAAAH,YAAA,GAAAlM,KAAK,CAACmB,IAAI,cAAA+K,YAAA,uBAAVA,YAAA,CAAY7N,MAAM,OAAA8N,gBAAA,GAAInM,KAAK,CAACkB,QAAQ,cAAAiL,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBhL,IAAI,cAAAiL,qBAAA,uBAApBA,qBAAA,CAAsB/N,MAAM;QAC3E,MAAMiO,aAAa,GAAG,CAAC,CAAC;QAExBrM,OAAO,CAACY,GAAG,CAAC,6BAA6B,EAAEwL,gBAAgB,CAAC;QAE5DvH,MAAM,CAACC,IAAI,CAACsH,gBAAgB,CAAC,CAAChM,OAAO,CAACG,GAAG,IAAI;UAC3C,MAAM+L,aAAa,GAAGF,gBAAgB,CAAC7L,GAAG,CAAC;UAC3C;UACA,MAAMgM,iBAAiB,GAAGC,yBAAyB,CAACjM,GAAG,CAAC;UACxD8L,aAAa,CAACE,iBAAiB,CAAC,GAAG5G,KAAK,CAACC,OAAO,CAAC0G,aAAa,CAAC,GAC3DA,aAAa,CAACtB,IAAI,CAAC,IAAI,CAAC,GACxBsB,aAAa;QACnB,CAAC,CAAC;QACFjO,SAAS,CAACgO,aAAa,CAAC;MAC1B,CAAC,MAAM,IAAI,CAAAX,YAAA,GAAA3L,KAAK,CAACmB,IAAI,cAAAwK,YAAA,eAAVA,YAAA,CAAYe,KAAK,KAAAd,YAAA,GAAI5L,KAAK,CAACmB,IAAI,cAAAyK,YAAA,eAAVA,YAAA,CAAYe,MAAM,KAAAd,gBAAA,GAAI7L,KAAK,CAACkB,QAAQ,cAAA2K,gBAAA,gBAAAC,qBAAA,GAAdD,gBAAA,CAAgB1K,IAAI,cAAA2K,qBAAA,eAApBA,qBAAA,CAAsBY,KAAK,KAAAX,gBAAA,GAAI/L,KAAK,CAACkB,QAAQ,cAAA6K,gBAAA,gBAAAC,qBAAA,GAAdD,gBAAA,CAAgB5K,IAAI,cAAA6K,qBAAA,eAApBA,qBAAA,CAAsBW,MAAM,EAAE;QAAA,IAAAC,YAAA,EAAAC,YAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA;QACjH;QACA,MAAMC,YAAY,GAAG,EAAAN,YAAA,GAAA5M,KAAK,CAACmB,IAAI,cAAAyL,YAAA,uBAAVA,YAAA,CAAYD,MAAM,OAAAE,YAAA,GAAI7M,KAAK,CAACmB,IAAI,cAAA0L,YAAA,uBAAVA,YAAA,CAAYH,KAAK,OAAAI,gBAAA,GAAI9M,KAAK,CAACkB,QAAQ,cAAA4L,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB3L,IAAI,cAAA4L,qBAAA,uBAApBA,qBAAA,CAAsBJ,MAAM,OAAAK,gBAAA,GAAIhN,KAAK,CAACkB,QAAQ,cAAA8L,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB7L,IAAI,cAAA8L,qBAAA,uBAApBA,qBAAA,CAAsBP,KAAK;QAC3HpO,SAAS,CAAC;UAAE0M,OAAO,EAAEkC;QAAa,CAAC,CAAC;MACtC,CAAC,MAAM,IAAIlN,KAAK,CAACmN,iBAAiB,IAAInN,KAAK,CAACmN,iBAAiB,CAAC,CAAC,EAAE;QAC/D,MAAMd,gBAAgB,GAAGrM,KAAK,CAACoN,mBAAmB,CAAC,CAAC;QACpDnN,OAAO,CAACY,GAAG,CAAC,6BAA6B,EAAEwL,gBAAgB,CAAC;QAC5D/N,SAAS,CAAC+N,gBAAgB,CAAC;MAC7B,CAAC,MAAM;QAAA,IAAAgB,YAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,YAAA;QACL;QACA,MAAMN,YAAY,GAAG,EAAAG,YAAA,GAAArN,KAAK,CAACmB,IAAI,cAAAkM,YAAA,uBAAVA,YAAA,CAAYpO,OAAO,OAAAqO,gBAAA,GACrBtN,KAAK,CAACkB,QAAQ,cAAAoM,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBnM,IAAI,cAAAoM,qBAAA,uBAApBA,qBAAA,CAAsBtO,OAAO,OAAAuO,YAAA,GAC7BxN,KAAK,CAACmB,IAAI,cAAAqM,YAAA,uBAAVA,YAAA,CAAYd,KAAK,KACjB1M,KAAK,CAACf,OAAO,IACb,mFAAmF;QACtGX,SAAS,CAAC;UAAE0M,OAAO,EAAEkC;QAAa,CAAC,CAAC;MACtC;IACF,CAAC,SAAS;MACRvO,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;;EAED;EACA,MAAM8N,yBAAyB,GAAIgB,gBAAgB,IAAK;IACtD,MAAMC,YAAY,GAAG;MACnB,YAAY,EAAE,UAAU;MACxB,YAAY,EAAE,UAAU;MACxB,eAAe,EAAE,aAAa;MAC9B,MAAM,EAAE,MAAM;MACd,cAAc,EAAE,cAAc;MAC9B,QAAQ,EAAE,QAAQ;MAClB,QAAQ,EAAE,QAAQ;MAClB,gBAAgB,EAAE,gBAAgB;MAClC,kBAAkB,EAAE,kBAAkB;MACtC,mBAAmB,EAAE,mBAAmB;MACxC,SAAS,EAAE,SAAS;MACpB,aAAa,EAAE,aAAa;MAC5B,WAAW,EAAE,WAAW;MACxB,gBAAgB,EAAE,gBAAgB;MAClC,cAAc,EAAE,cAAc;MAC9B,eAAe,EAAE,eAAe;MAChC,UAAU,EAAE,UAAU;MACtB,SAAS,EAAE,SAAS;MACpB,aAAa,EAAE,aAAa;MAC5B,cAAc,EAAE,cAAc;MAC9B,eAAe,EAAE,eAAe;MAChC,mBAAmB,EAAE,mBAAmB;MACxC,iBAAiB,EAAE,iBAAiB;MACpC,cAAc,EAAE,cAAc;MAC9B,aAAa,EAAE,aAAa;MAC5B,aAAa,EAAE,aAAa;MAC5B,YAAY,EAAE,YAAY;MAC1B,kBAAkB,EAAE,kBAAkB;MACtC,wBAAwB,EAAE,wBAAwB;MAClD,iBAAiB,EAAE,iBAAiB;MACpC,YAAY,EAAE,YAAY;MAC1B,QAAQ,EAAE,QAAQ;MAClB,SAAS,EAAE,SAAS;MACpB,UAAU,EAAE,UAAU;MACtB,iBAAiB,EAAE,iBAAiB;MACpC,kBAAkB,EAAE,kBAAkB;MACtC,uBAAuB,EAAE,uBAAuB;MAChD,sBAAsB,EAAE,sBAAsB;MAC9C,uBAAuB,EAAE,uBAAuB;MAChD,aAAa,EAAE,aAAa;MAC5B,kBAAkB,EAAE,kBAAkB;MACtC,sBAAsB,EAAE,sBAAsB;MAC9C,gBAAgB,EAAE,gBAAgB;MAClC,YAAY,EAAE;IAChB,CAAC;IAED,OAAOA,YAAY,CAACD,gBAAgB,CAAC,IAAIA,gBAAgB,CAACE,WAAW,CAAC,CAAC;EACzE,CAAC;EAED,MAAMtK,eAAe,GAAI/C,KAAK,IAAK;IACjC,IAAI,CAACA,KAAK,CAAC8C,WAAW,EAAE,OAAO,IAAI;IAEnC,MAAMwK,cAAc,GAAGzP,QAAQ,CAACmC,KAAK,CAAC8C,WAAW,CAAC9C,KAAK,CAAC;IACxD,MAAMuN,aAAa,GAAGvN,KAAK,CAAC8C,WAAW,CAACrB,KAAK;;IAE7C;IACA,IAAI,OAAO8L,aAAa,KAAK,SAAS,IAAIA,aAAa,KAAK,MAAM,IAAIA,aAAa,KAAK,OAAO,EAAE;MAC/F,OAAOD,cAAc,MAAMC,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,MAAM,CAAC;IAChF;IAEA,OAAOD,cAAc,KAAKC,aAAa;EACzC,CAAC;EAED,MAAMC,qBAAqB,GAAGA,CAAA,KAAM;IAClC,IAAI,CAAC7P,UAAU,IAAI,CAACA,UAAU,CAAC4B,MAAM,EAAE,OAAO,CAAC,CAAC;IAEhD,MAAMkO,QAAQ,GAAG,CAAC,CAAC;IACnB9P,UAAU,CAAC4B,MAAM,CAACQ,OAAO,CAAEC,KAAK,IAAK;MACnC,MAAM0N,UAAU,GAAG1N,KAAK,CAAC2N,OAAO,IAAI,SAAS;MAC7C,IAAI,CAACF,QAAQ,CAACC,UAAU,CAAC,EAAE;QACzBD,QAAQ,CAACC,UAAU,CAAC,GAAG;UACrBtB,KAAK,EAAEwB,eAAe,CAACF,UAAU,CAAC;UAClCnO,MAAM,EAAE;QACV,CAAC;MACH;MACAkO,QAAQ,CAACC,UAAU,CAAC,CAACnO,MAAM,CAACa,IAAI,CAACJ,KAAK,CAAC;IACzC,CAAC,CAAC;IAEF,OAAOyN,QAAQ;EACjB,CAAC;EAED,MAAMG,eAAe,GAAIF,UAAU,IAAK;IACtC,MAAMG,MAAM,GAAG;MACbC,YAAY,EAAE,sBAAsB;MACpCC,WAAW,EAAE,qBAAqB;MAClCC,YAAY,EAAE,sBAAsB;MACpCC,YAAY,EAAE,sBAAsB;MACpCC,aAAa,EAAE,uBAAuB;MACtCC,eAAe,EAAE,kBAAkB;MACnCC,WAAW,EAAE,qBAAqB;MAClCC,gBAAgB,EAAE,mBAAmB;MACrCC,aAAa,EAAE,uBAAuB;MACtC5D,OAAO,EAAE;IACX,CAAC;IACD,OAAOmD,MAAM,CAACH,UAAU,CAAC,IAAIA,UAAU;EACzC,CAAC;EAED,MAAMD,QAAQ,GAAG9P,UAAU,GAAG6P,qBAAqB,CAAC,CAAC,GAAG,CAAC,CAAC;EAI1D,oBACEhR,OAAA;IAAK+R,SAAS,EAAC,qBAAqB;IAAAC,QAAA,gBAClChS,OAAA;MAAK+R,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BhS,OAAA;QAAAgS,QAAA,EAAK3R,IAAI,KAAK,QAAQ,GAAG,mBAAmB,GAAG;MAAa;QAAA4R,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,EACjEjR,UAAU,iBACTnB,OAAA;QAAK+R,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBhS,OAAA;UAAM+R,SAAS,EAAC,WAAW;UAAAC,QAAA,EAAE7Q,UAAU,CAACyE;QAAI;UAAAqM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,EACnDjR,UAAU,CAACkR,WAAW,iBACrBrS,OAAA;UAAM+R,SAAS,EAAC,kBAAkB;UAAAC,QAAA,EAAE7Q,UAAU,CAACkR;QAAW;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAClE;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAEL7Q,MAAM,CAAC2M,OAAO,iBACblO,OAAA;MAAK+R,SAAS,EAAC,mBAAmB;MAAAC,QAAA,EAAEzQ,MAAM,CAAC2M;IAAO;MAAA+D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CACzD,EAGApK,MAAM,CAACC,IAAI,CAAC1G,MAAM,CAAC,CAACuC,MAAM,GAAG,CAAC,IAAI,CAACvC,MAAM,CAAC2M,OAAO,iBAChDlO,OAAA;MAAK+R,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChChS,OAAA;QAAAgS,QAAA,EAAQ;MAAgC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACjDpS,OAAA;QAAIsS,KAAK,EAAE;UAAEC,MAAM,EAAE;QAAe,CAAE;QAAAP,QAAA,EACnChK,MAAM,CAACiG,OAAO,CAAC1M,MAAM,CAAC,CACpB0H,MAAM,CAAC,CAAC,CAACvF,GAAG,CAAC,KAAKA,GAAG,KAAK,SAAS,CAAC,CACpC0F,GAAG,CAAC,CAAC,CAAC1F,GAAG,EAAEvB,OAAO,CAAC,kBAClBnC,OAAA;UAAAgS,QAAA,EAAe7P;QAAO,GAAbuB,GAAG;UAAAuO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAe,CAC5B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CACN,eAEDpS,OAAA;MAAME,QAAQ,EAAEgI,YAAa;MAAC6J,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAEnDhS,OAAA;QAAK+R,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BhS,OAAA;UAAAgS,QAAA,EAAI;QAA6B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAGtCpS,OAAA;UAAK+R,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBhS,OAAA;YAAO+R,SAAS,EAAC,YAAY;YAAAC,QAAA,GAAC,WACnB,eAAAhS,OAAA;cAAM+R,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC,eACRpS,OAAA;YACEiF,KAAK,EAAEpE,gBAAiB;YACxB2R,QAAQ,EAAEzN,oBAAqB;YAC/B0N,QAAQ,EAAEhR,OAAO,CAAClB,SAAU;YAC5BwR,SAAS,EAAE,eAAexQ,MAAM,CAACkE,QAAQ,GAAG,OAAO,GAAG,EAAE,EAAG;YAC3DW,QAAQ;YAAA4L,QAAA,gBAERhS,OAAA;cAAQiF,KAAK,EAAC,EAAE;cAAA+M,QAAA,EACbvQ,OAAO,CAAClB,SAAS,GAAG,sBAAsB,GAAG;YAAiB;cAAA0R,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzD,CAAC,EACR7R,SAAS,CAAC6I,GAAG,CAAC3D,QAAQ,iBACrBzF,OAAA;cAA0BiF,KAAK,EAAEQ,QAAQ,CAACgJ,EAAG;cAAAuD,QAAA,EAC1CvM,QAAQ,CAACG;YAAI,GADHH,QAAQ,CAACgJ,EAAE;cAAAwD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEhB,CACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,EACR7Q,MAAM,CAACkE,QAAQ,iBACdzF,OAAA;YAAK+R,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAEzQ,MAAM,CAACkE;UAAQ;YAAAwM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACtD,EACA7Q,MAAM,CAAChB,SAAS,iBACfP,OAAA;YAAK+R,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAEzQ,MAAM,CAAChB;UAAS;YAAA0R,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACvD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGNpS,OAAA;UAAK+R,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBhS,OAAA;YAAO+R,SAAS,EAAC,YAAY;YAAAC,QAAA,GAAC,WACnB,eAAAhS,OAAA;cAAM+R,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC,eACRpS,OAAA;YACEiF,KAAK,EAAElE,gBAAiB;YACxByR,QAAQ,EAAErN,oBAAqB;YAC/BsN,QAAQ,EAAE,CAAC5R,gBAAgB,IAAIY,OAAO,CAAChB,UAAW;YAClDsR,SAAS,EAAE,eAAexQ,MAAM,CAACmE,QAAQ,GAAG,OAAO,GAAG,EAAE,EAAG;YAC3DU,QAAQ;YAAA4L,QAAA,gBAERhS,OAAA;cAAQiF,KAAK,EAAC,EAAE;cAAA+M,QAAA,EACb,CAACnR,gBAAgB,GACd,uBAAuB,GACvBY,OAAO,CAAChB,UAAU,GAChB,uBAAuB,GACvB;YAAiB;cAAAwR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEjB,CAAC,EACR3R,UAAU,CAAC2I,GAAG,CAAC1D,QAAQ,iBACtB1F,OAAA;cAA0BiF,KAAK,EAAES,QAAQ,CAAC+I,EAAG;cAAAuD,QAAA,EAC1CtM,QAAQ,CAACE;YAAI,GADHF,QAAQ,CAAC+I,EAAE;cAAAwD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEhB,CACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,EACR7Q,MAAM,CAACmE,QAAQ,iBACd1F,OAAA;YAAK+R,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAEzQ,MAAM,CAACmE;UAAQ;YAAAuM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACtD,EACA7Q,MAAM,CAACd,UAAU,iBAChBT,OAAA;YAAK+R,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAEzQ,MAAM,CAACd;UAAU;YAAAwR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACxD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,EAGLtQ,gBAAgB,CAACI,uBAAuB,iBACvClC,OAAA;UAAK+R,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBhS,OAAA;YAAO+R,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAE9B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRpS,OAAA;YACEiF,KAAK,EAAEhE,mBAAoB;YAC3BuR,QAAQ,EAAEpN,uBAAwB;YAClCqN,QAAQ,EAAE,CAAC1R,gBAAgB,IAAIU,OAAO,CAACd,aAAc;YACrDoR,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAEvBhS,OAAA;cAAQiF,KAAK,EAAC,EAAE;cAAA+M,QAAA,EACb,CAACjR,gBAAgB,GACd,uBAAuB,GACvBU,OAAO,CAACd,aAAa,GACnB,0BAA0B,GAC1B;YAA+B;cAAAsR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAE/B,CAAC,EACRzR,aAAa,CAACyI,GAAG,CAACzD,WAAW,iBAC5B3F,OAAA;cAA6BiF,KAAK,EAAEU,WAAW,CAAC8I,EAAG;cAAAuD,QAAA,EAChDrM,WAAW,CAACC;YAAI,GADND,WAAW,CAAC8I,EAAE;cAAAwD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEnB,CACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,EACR7Q,MAAM,CAACZ,aAAa,iBACnBX,OAAA;YAAK+R,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAEzQ,MAAM,CAACZ;UAAa;YAAAsR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAC3D;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN,EAGA3Q,OAAO,CAACE,IAAI,iBACX3B,OAAA;UAAK+R,SAAS,EAAC,iBAAiB;UAAAC,QAAA,eAC9BhS,OAAA;YAAAgS,QAAA,EAAM;UAA6B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CACN,EAEAtQ,gBAAgB,CAACK,OAAO,IAAI,CAACV,OAAO,CAACE,IAAI,iBACxC3B,OAAA;UAAK+R,SAAS,EAAE,kBACdjQ,gBAAgB,CAACE,eAAe,IAAIF,gBAAgB,CAACG,kBAAkB,GACnE,SAAS,GACTH,gBAAgB,CAACK,OAAO,CAAC+D,QAAQ,CAAC,SAAS,CAAC,GAC1C,OAAO,GACP,MAAM,EACX;UAAA8L,QAAA,EACAlQ,gBAAgB,CAACK;QAAO;UAAA8P,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CACN,EAEA7Q,MAAM,CAACI,IAAI,iBACV3B,OAAA;UAAK+R,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAEzQ,MAAM,CAACI;QAAI;UAAAsQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAClD;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,EAGLjR,UAAU,IAAI6G,MAAM,CAACiG,OAAO,CAACgD,QAAQ,CAAC,CAAC7H,GAAG,CAAC,CAAC,CAAC8H,UAAU,EAAEC,OAAO,CAAC,kBAChEnR,OAAA;QAAsB+R,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC5ChS,OAAA;UAAAgS,QAAA,EAAKb,OAAO,CAACvB;QAAK;UAAAqC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACxBpS,OAAA;UAAK+R,SAAS,EAAC,aAAa;UAAAC,QAAA,EACzBb,OAAO,CAACpO,MAAM,CACZkG,MAAM,CAACzF,KAAK,IAAI+C,eAAe,CAAC/C,KAAK,CAAC,CAAC,CACvC4F,GAAG,CAAC,CAAC5F,KAAK,EAAEkP,UAAU,kBACrB1S,OAAA,CAACF,SAAS;YAER0D,KAAK,EAAEA,KAAM;YACbyB,KAAK,EAAE5D,QAAQ,CAACmC,KAAK,CAACE,GAAG,CAAE;YAC3B8O,QAAQ,EAAGvN,KAAK,IAAKI,iBAAiB,CAAC7B,KAAK,CAACE,GAAG,EAAEuB,KAAK,CAAE;YACzD/B,KAAK,EAAE3B,MAAM,CAACiC,KAAK,CAACE,GAAG;UAAE,GAJpB,GAAGwN,UAAU,IAAI1N,KAAK,CAACE,GAAG,IAAIgP,UAAU,EAAE;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAKhD,CACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA,GAdElB,UAAU;QAAAe,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAef,CACN,CAAC,eAKFpS,OAAA;QAAK+R,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BhS,OAAA;UACE2G,IAAI,EAAC,QAAQ;UACbgM,OAAO,EAAExS,QAAS;UAClB4R,SAAS,EAAC,iBAAiB;UAC3BU,QAAQ,EAAE7Q,UAAW;UAAAoQ,QAAA,EACtB;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTpS,OAAA;UACE2G,IAAI,EAAC,QAAQ;UACboL,SAAS,EAAC,iBAAiB;UAC3BU,QAAQ,EAAE7Q,UAAU,IAAI,CAACf,gBAAgB,IAAI,CAACE,gBAAiB;UAAAiR,QAAA,EAE9DpQ,UAAU,GACNvB,IAAI,KAAK,QAAQ,GAAG,aAAa,GAAG,aAAa,GACjDA,IAAI,KAAK,QAAQ,GAAG,eAAe,GAAG;QAAgB;UAAA4R,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAErD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAAC9R,EAAA,CA5gCIL,iBAAiB;AAAA2S,EAAA,GAAjB3S,iBAAiB;AA8gCvB,eAAeA,iBAAiB;AAAC,IAAA2S,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}