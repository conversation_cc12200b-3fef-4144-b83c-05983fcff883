{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Advanced\\\\Advanced CRM filed all use\\\\frontend\\\\src\\\\components\\\\forms\\\\DynamicPersonForm.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { PersonNatureLabels, GenderLabels, WorkingProfileLabels, getDefaultPersonData } from '../../constants/personConstants';\nimport formConfigService from '../../services/formConfigService';\nimport apiService from '../../services/apiService';\nimport FormField from './FormField';\nimport './DynamicPersonForm.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DynamicPersonForm = ({\n  onSubmit,\n  onCancel,\n  initialData = null,\n  mode = 'create'\n}) => {\n  _s();\n  // Hierarchy state\n  const [divisions, setDivisions] = useState([]);\n  const [categories, setCategories] = useState([]);\n  const [subCategories, setSubCategories] = useState([]);\n\n  // Selection state\n  const [selectedDivision, setSelectedDivision] = useState('');\n  const [selectedCategory, setSelectedCategory] = useState('');\n  const [selectedSubCategory, setSelectedSubCategory] = useState('');\n\n  // Form state\n  const [formConfig, setFormConfig] = useState(null);\n  const [formData, setFormData] = useState(getDefaultPersonData());\n  const [errors, setErrors] = useState({});\n\n  // Loading states\n  const [loading, setLoading] = useState({\n    divisions: false,\n    categories: false,\n    subCategories: false,\n    form: false\n  });\n  const [submitting, setSubmitting] = useState(false);\n\n  // Form availability state\n  const [formAvailability, setFormAvailability] = useState({\n    categoryHasForm: false,\n    subCategoryHasForm: false,\n    showSubCategoryDropdown: false,\n    message: ''\n  });\n\n  // Load divisions on component mount\n  useEffect(() => {\n    loadDivisions();\n\n    // Test API connectivity\n    testApiConnectivity();\n\n    // Load initial data if provided\n    if (initialData) {\n      var _initialData$division, _initialData$category, _initialData$subCateg;\n      setFormData(initialData);\n      setSelectedDivision(((_initialData$division = initialData.divisionId) === null || _initialData$division === void 0 ? void 0 : _initialData$division.toString()) || '');\n      setSelectedCategory(((_initialData$category = initialData.categoryId) === null || _initialData$category === void 0 ? void 0 : _initialData$category.toString()) || '');\n      setSelectedSubCategory(((_initialData$subCateg = initialData.subCategoryId) === null || _initialData$subCateg === void 0 ? void 0 : _initialData$subCateg.toString()) || '');\n    } else {\n      // Load default form for new person creation\n      const defaultForm = formConfigService.getDefaultFormConfig();\n      // Ensure no duplicate fields in default form\n      if (defaultForm.fields) {\n        defaultForm.fields = deduplicateFields(defaultForm.fields);\n      }\n      setFormConfig(defaultForm);\n    }\n  }, [initialData]);\n\n  // Test API connectivity\n  const testApiConnectivity = async () => {\n    try {\n      console.log('Testing API connectivity...');\n      const response = await apiService.getDivisions();\n      console.log('API test successful:', response);\n    } catch (error) {\n      console.error('API connectivity test failed:', error);\n    }\n  };\n\n  // Load categories when division changes\n  useEffect(() => {\n    if (selectedDivision) {\n      loadCategories(selectedDivision);\n      // Reset category and subcategory when division changes\n      setSelectedCategory('');\n      setSelectedSubCategory('');\n      setSubCategories([]);\n      setFormConfig(null);\n      setFormAvailability({\n        categoryHasForm: false,\n        subCategoryHasForm: false,\n        showSubCategoryDropdown: false,\n        message: ''\n      });\n    } else {\n      setCategories([]);\n      setSubCategories([]);\n      setSelectedCategory('');\n      setSelectedSubCategory('');\n      setFormConfig(null);\n    }\n  }, [selectedDivision]);\n\n  // Handle category selection and form loading\n  useEffect(() => {\n    if (selectedCategory) {\n      handleCategorySelection(selectedCategory);\n      // Reset subcategory when category changes\n      setSelectedSubCategory('');\n    } else {\n      setSubCategories([]);\n      setSelectedSubCategory('');\n      setFormConfig(null);\n      setFormAvailability({\n        categoryHasForm: false,\n        subCategoryHasForm: false,\n        showSubCategoryDropdown: false,\n        message: ''\n      });\n    }\n  }, [selectedCategory]);\n\n  // Handle subcategory selection and form loading\n  useEffect(() => {\n    if (selectedSubCategory) {\n      handleSubCategorySelection(selectedSubCategory);\n    }\n  }, [selectedSubCategory]);\n\n  // Load divisions from API\n  const loadDivisions = async () => {\n    setLoading(prev => ({\n      ...prev,\n      divisions: true\n    }));\n    try {\n      const response = await apiService.getDivisions();\n      setDivisions(response.data || []);\n    } catch (error) {\n      console.error('Error loading divisions:', error);\n      setErrors(prev => ({\n        ...prev,\n        divisions: 'Failed to load divisions'\n      }));\n    } finally {\n      setLoading(prev => ({\n        ...prev,\n        divisions: false\n      }));\n    }\n  };\n\n  // Load categories by division\n  const loadCategories = async divisionId => {\n    setLoading(prev => ({\n      ...prev,\n      categories: true\n    }));\n    try {\n      const response = await apiService.getCategoriesByDivision(divisionId);\n      setCategories(response.data || []);\n    } catch (error) {\n      console.error('Error loading categories:', error);\n      setErrors(prev => ({\n        ...prev,\n        categories: 'Failed to load categories'\n      }));\n      setCategories([]);\n    } finally {\n      setLoading(prev => ({\n        ...prev,\n        categories: false\n      }));\n    }\n  };\n\n  // Load subcategories by category\n  const loadSubCategories = async categoryId => {\n    setLoading(prev => ({\n      ...prev,\n      subCategories: true\n    }));\n    try {\n      const response = await apiService.getSubCategoriesByCategory(categoryId);\n      setSubCategories(response.data || []);\n    } catch (error) {\n      console.error('Error loading subcategories:', error);\n      setErrors(prev => ({\n        ...prev,\n        subCategories: 'Failed to load subcategories'\n      }));\n      setSubCategories([]);\n    } finally {\n      setLoading(prev => ({\n        ...prev,\n        subCategories: false\n      }));\n    }\n  };\n\n  // Handle category selection and form loading logic\n  const handleCategorySelection = async categoryId => {\n    setLoading(prev => ({\n      ...prev,\n      form: true\n    }));\n    try {\n      // Check if category has a form\n      const categoryHasForm = formConfigService.hasFormForCategory(parseInt(categoryId));\n      if (categoryHasForm) {\n        // Load category form and hide subcategory dropdown\n        const categoryForm = formConfigService.loadFormConfig('category', parseInt(categoryId));\n        setFormConfig(categoryForm);\n        setFormAvailability({\n          categoryHasForm: true,\n          subCategoryHasForm: false,\n          showSubCategoryDropdown: false,\n          message: 'Using category-specific form'\n        });\n      } else {\n        // No category form, show subcategory dropdown and load subcategories\n        await loadSubCategories(categoryId);\n        // Load default form as fallback\n        const defaultForm = formConfigService.getDefaultFormConfig();\n        setFormConfig(defaultForm);\n        setFormAvailability({\n          categoryHasForm: false,\n          subCategoryHasForm: false,\n          showSubCategoryDropdown: true,\n          message: 'Using default form. You can select a subcategory if available.'\n        });\n      }\n    } catch (error) {\n      console.error('Error handling category selection:', error);\n      setErrors(prev => ({\n        ...prev,\n        form: 'Error loading form configuration'\n      }));\n    } finally {\n      setLoading(prev => ({\n        ...prev,\n        form: false\n      }));\n    }\n  };\n\n  // Handle subcategory selection and form loading logic\n  const handleSubCategorySelection = async subCategoryId => {\n    setLoading(prev => ({\n      ...prev,\n      form: true\n    }));\n    try {\n      // Check if subcategory has a form\n      const subCategoryHasForm = formConfigService.hasFormForSubCategory(parseInt(subCategoryId));\n      if (subCategoryHasForm) {\n        // Load subcategory form\n        const subCategoryForm = formConfigService.loadFormConfig('subcategory', parseInt(subCategoryId));\n        setFormConfig(subCategoryForm);\n        setFormAvailability({\n          categoryHasForm: false,\n          subCategoryHasForm: true,\n          showSubCategoryDropdown: true,\n          message: 'Using subcategory-specific form'\n        });\n      } else {\n        // No subcategory form available, use default form\n        const defaultForm = formConfigService.getDefaultFormConfig();\n        setFormConfig(defaultForm);\n        setFormAvailability({\n          categoryHasForm: false,\n          subCategoryHasForm: false,\n          showSubCategoryDropdown: true,\n          message: 'No specific form found for this subcategory. Using default form.'\n        });\n      }\n    } catch (error) {\n      console.error('Error handling subcategory selection:', error);\n      setErrors(prev => ({\n        ...prev,\n        form: 'Error loading form configuration'\n      }));\n    } finally {\n      setLoading(prev => ({\n        ...prev,\n        form: false\n      }));\n    }\n  };\n\n  // Handle dropdown changes\n  const handleDivisionChange = e => {\n    const value = e.target.value;\n    setSelectedDivision(value);\n\n    // Update form data\n    setFormData(prev => ({\n      ...prev,\n      divisionId: value ? parseInt(value) : null,\n      categoryId: null,\n      subCategoryId: null\n    }));\n  };\n  const handleCategoryChange = e => {\n    const value = e.target.value;\n    setSelectedCategory(value);\n\n    // Update form data\n    setFormData(prev => ({\n      ...prev,\n      categoryId: value ? parseInt(value) : null,\n      subCategoryId: null\n    }));\n  };\n  const handleSubCategoryChange = e => {\n    const value = e.target.value;\n    setSelectedSubCategory(value);\n\n    // Update form data\n    setFormData(prev => ({\n      ...prev,\n      subCategoryId: value ? parseInt(value) : null\n    }));\n  };\n  const handleFieldChange = (fieldKey, value) => {\n    setFormData(prev => ({\n      ...prev,\n      [fieldKey]: value\n    }));\n\n    // Clear field error when user starts typing\n    if (errors[fieldKey]) {\n      setErrors(prev => ({\n        ...prev,\n        [fieldKey]: null\n      }));\n    }\n  };\n  const validateForm = () => {\n    const newErrors = {};\n\n    // Validate hierarchy selection\n    if (!selectedDivision) {\n      newErrors.division = 'Division is required';\n    }\n    if (!selectedCategory) {\n      newErrors.category = 'Category is required';\n    }\n\n    // Check if form is available (should always have default form as fallback)\n    if (!formConfig) {\n      newErrors.form = 'Form configuration not loaded. Please try again.';\n      return {\n        isValid: false,\n        errors: newErrors\n      };\n    }\n\n    // Validate form fields\n    formConfig.fields.forEach(field => {\n      const value = formData[field.key];\n\n      // Required field validation\n      if (field.required && (!value || typeof value === 'string' && value.trim() === '')) {\n        newErrors[field.key] = `${field.label} is required`;\n        return;\n      }\n\n      // Conditional field validation\n      if (field.conditional && shouldShowField(field)) {\n        if (field.required && (!value || typeof value === 'string' && value.trim() === '')) {\n          newErrors[field.key] = `${field.label} is required`;\n          return;\n        }\n      }\n\n      // Type-specific validation\n      if (value && typeof value === 'string' && value.trim() !== '') {\n        var _field$validation, _field$validation2, _field$validation3;\n        switch (field.type) {\n          case 'email':\n            const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n            if (!emailRegex.test(value)) {\n              newErrors[field.key] = 'Please enter a valid email address';\n            }\n            break;\n          case 'tel':\n            const phoneRegex = /^(\\+91[\\-\\s]?)?[0]?(91)?[789]\\d{9}$/;\n            if (!phoneRegex.test(value)) {\n              newErrors[field.key] = 'Please enter a valid mobile number';\n            }\n            break;\n          case 'url':\n            try {\n              new URL(value);\n            } catch {\n              newErrors[field.key] = 'Please enter a valid URL';\n            }\n            break;\n          case 'number':\n            if (field.validation) {\n              const numValue = parseFloat(value);\n              if (field.validation.min !== undefined && numValue < field.validation.min) {\n                newErrors[field.key] = `Value must be at least ${field.validation.min}`;\n              }\n              if (field.validation.max !== undefined && numValue > field.validation.max) {\n                newErrors[field.key] = `Value must be at most ${field.validation.max}`;\n              }\n            }\n            break;\n        }\n\n        // Pattern validation\n        if ((_field$validation = field.validation) !== null && _field$validation !== void 0 && _field$validation.pattern) {\n          const regex = new RegExp(field.validation.pattern);\n          if (!regex.test(value)) {\n            newErrors[field.key] = `${field.label} format is invalid`;\n          }\n        }\n\n        // Length validation\n        if ((_field$validation2 = field.validation) !== null && _field$validation2 !== void 0 && _field$validation2.minLength && value.length < field.validation.minLength) {\n          newErrors[field.key] = `${field.label} must be at least ${field.validation.minLength} characters`;\n        }\n        if ((_field$validation3 = field.validation) !== null && _field$validation3 !== void 0 && _field$validation3.maxLength && value.length > field.validation.maxLength) {\n          newErrors[field.key] = `${field.label} must be at most ${field.validation.maxLength} characters`;\n        }\n      }\n    });\n\n    // Business logic validation\n    if (formData.isMarried && formData.dateOfMarriage && formData.dateOfBirth) {\n      const birthDate = new Date(formData.dateOfBirth);\n      const marriageDate = new Date(formData.dateOfMarriage);\n      if (marriageDate <= birthDate) {\n        newErrors.dateOfMarriage = 'Marriage date must be after birth date';\n      }\n    }\n    return {\n      isValid: Object.keys(newErrors).length === 0,\n      errors: newErrors\n    };\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    const validation = validateForm();\n    if (!validation.isValid) {\n      console.log('Validation failed:', validation.errors);\n      setErrors(validation.errors);\n      return;\n    }\n    setSubmitting(true);\n    try {\n      // Prepare data for API - match backend model exactly\n      const submitData = {\n        // Required hierarchy fields\n        divisionId: parseInt(selectedDivision),\n        categoryId: parseInt(selectedCategory),\n        subCategoryId: selectedSubCategory ? parseInt(selectedSubCategory) : null,\n        // Required fields\n        name: formData.name || '',\n        mobileNumber: formData.mobileNumber || '',\n        nature: formData.nature ? parseInt(formData.nature) : null,\n        // Optional enum fields\n        gender: formData.gender ? parseInt(formData.gender) : null,\n        // Contact Information\n        alternateNumbers: Array.isArray(formData.alternateNumbers) ? formData.alternateNumbers : formData.alternateNumbers ? formData.alternateNumbers.split(',').map(s => s.trim()) : [],\n        primaryEmailId: formData.primaryEmailId || '',\n        alternateEmailIds: Array.isArray(formData.alternateEmailIds) ? formData.alternateEmailIds : formData.alternateEmailIds ? formData.alternateEmailIds.split(',').map(s => s.trim()) : [],\n        website: formData.website || '',\n        // Personal Information\n        dateOfBirth: formData.dateOfBirth || null,\n        isMarried: formData.isMarried || false,\n        dateOfMarriage: formData.dateOfMarriage || null,\n        // Location Information\n        workingState: formData.workingState || '',\n        domesticState: formData.domesticState || '',\n        district: formData.district || '',\n        address: formData.address || '',\n        workingArea: formData.workingArea || '',\n        // Associate Information\n        hasAssociate: formData.hasAssociate || false,\n        associateName: formData.associateName || '',\n        associateRelation: formData.associateRelation || '',\n        associateMobile: formData.associateMobile || '',\n        // Digital Presence\n        usingWebsite: formData.usingWebsite || false,\n        websiteLink: formData.websiteLink || '',\n        usingCRMApp: formData.usingCRMApp || false,\n        crmAppLink: formData.crmAppLink || '',\n        // Business Information\n        transactionValue: formData.transactionValue ? parseFloat(formData.transactionValue) : null,\n        reraRegistrationNumber: formData.reraRegistrationNumber || '',\n        workingProfiles: Array.isArray(formData.workingProfiles) ? formData.workingProfiles.map(wp => parseInt(wp)) : [],\n        starRating: formData.starRating ? parseInt(formData.starRating) : null,\n        source: formData.source || '',\n        remarks: formData.remarks || '',\n        // Company Information\n        firmName: formData.firmName || '',\n        numberOfOffices: formData.numberOfOffices ? parseInt(formData.numberOfOffices) : null,\n        numberOfBranches: formData.numberOfBranches ? parseInt(formData.numberOfBranches) : null,\n        totalEmployeeStrength: formData.totalEmployeeStrength ? parseInt(formData.totalEmployeeStrength) : null,\n        // Authorized Person\n        authorizedPersonName: formData.authorizedPersonName || '',\n        authorizedPersonEmail: formData.authorizedPersonEmail || '',\n        designation: formData.designation || '',\n        // Marketing Information\n        marketingContact: formData.marketingContact || '',\n        marketingDesignation: formData.marketingDesignation || '',\n        placeOfPosting: formData.placeOfPosting || '',\n        department: formData.department || ''\n      };\n\n      // Debug logging\n      console.log('Submitting person data:', submitData);\n      console.log('Selected Division:', selectedDivision);\n      console.log('Selected Category:', selectedCategory);\n      console.log('Selected SubCategory:', selectedSubCategory);\n\n      // Validate required fields before submission\n      const requiredFieldsCheck = {\n        divisionId: submitData.divisionId,\n        categoryId: submitData.categoryId,\n        name: submitData.name,\n        mobileNumber: submitData.mobileNumber,\n        nature: submitData.nature\n      };\n      console.log('Required fields check:', requiredFieldsCheck);\n\n      // Check for missing required fields\n      const missingFields = Object.entries(requiredFieldsCheck).filter(([, value]) => !value || value === '' || value === null).map(([key]) => key);\n      if (missingFields.length > 0) {\n        console.error('Missing required fields:', missingFields);\n        setErrors({\n          general: 'Missing required fields: ' + missingFields.join(', ')\n        });\n        return;\n      }\n      let result;\n      if (mode === 'create') {\n        result = await apiService.createPerson(submitData);\n      } else {\n        result = await apiService.updatePerson(initialData.id, submitData);\n      }\n      onSubmit(result);\n    } catch (error) {\n      var _error$data;\n      console.error('Error submitting form:', error);\n      console.error('Error data:', error.data);\n      console.error('Full error object:', error);\n      if ((_error$data = error.data) !== null && _error$data !== void 0 && _error$data.errors) {\n        // Handle validation errors from backend\n        const backendErrors = {};\n        Object.keys(error.data.errors).forEach(key => {\n          const errorMessages = error.data.errors[key];\n          backendErrors[key.toLowerCase()] = Array.isArray(errorMessages) ? errorMessages.join(', ') : errorMessages;\n        });\n        console.log('Backend validation errors:', backendErrors);\n        setErrors(backendErrors);\n      } else if (error.isValidationError && error.isValidationError()) {\n        setErrors(error.getValidationErrors());\n      } else {\n        var _error$data2, _error$data3;\n        const errorMessage = ((_error$data2 = error.data) === null || _error$data2 === void 0 ? void 0 : _error$data2.message) || ((_error$data3 = error.data) === null || _error$data3 === void 0 ? void 0 : _error$data3.title) || error.message || `HTTP ${error.status || 'Unknown'} - An error occurred while saving`;\n        console.log('General error message:', errorMessage);\n        setErrors({\n          general: errorMessage\n        });\n      }\n    } finally {\n      setSubmitting(false);\n    }\n  };\n  const shouldShowField = field => {\n    if (!field.conditional) return true;\n    const conditionValue = formData[field.conditional.field];\n    const expectedValue = field.conditional.value;\n\n    // Handle boolean conditions\n    if (typeof expectedValue === 'boolean' || expectedValue === 'true' || expectedValue === 'false') {\n      return conditionValue === (expectedValue === true || expectedValue === 'true');\n    }\n    return conditionValue === expectedValue;\n  };\n  const groupFieldsBySections = () => {\n    if (!formConfig || !formConfig.fields) return {};\n\n    // Debug: Check for duplicate fields\n    const fieldKeys = formConfig.fields.map(field => field.key);\n    const duplicateKeys = fieldKeys.filter((key, index) => fieldKeys.indexOf(key) !== index);\n    if (duplicateKeys.length > 0) {\n      console.warn('Duplicate field keys found:', duplicateKeys);\n      console.log('All fields:', formConfig.fields);\n    }\n    const sections = {};\n    formConfig.fields.forEach(field => {\n      const sectionKey = field.section || 'general';\n      if (!sections[sectionKey]) {\n        sections[sectionKey] = {\n          title: getSectionTitle(sectionKey),\n          fields: []\n        };\n      }\n      sections[sectionKey].fields.push(field);\n    });\n    return sections;\n  };\n  const getSectionTitle = sectionKey => {\n    const titles = {\n      personalInfo: 'Personal Information',\n      contactInfo: 'Contact Information',\n      locationInfo: 'Location Information',\n      businessInfo: 'Business Information',\n      associateInfo: 'Associate Information',\n      digitalPresence: 'Digital Presence',\n      companyInfo: 'Company Information',\n      authorizedPerson: 'Authorized Person',\n      marketingInfo: 'Marketing Information',\n      general: 'General Information'\n    };\n    return titles[sectionKey] || sectionKey;\n  };\n  const sections = formConfig ? groupFieldsBySections() : {};\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"dynamic-person-form\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"form-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: mode === 'create' ? 'Create New Person' : 'Edit Person'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 621,\n        columnNumber: 9\n      }, this), formConfig && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-info\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"form-name\",\n          children: formConfig.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 624,\n          columnNumber: 13\n        }, this), formConfig.description && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"form-description\",\n          children: formConfig.description\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 626,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 623,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 620,\n      columnNumber: 7\n    }, this), errors.general && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"alert alert-error\",\n      children: errors.general\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 633,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmit,\n      className: \"person-form\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Division & Category Selection\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 639,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"form-label\",\n            children: [\"Division \", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"required\",\n              children: \"*\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 644,\n              columnNumber: 24\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 643,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: selectedDivision,\n            onChange: handleDivisionChange,\n            disabled: loading.divisions,\n            className: `form-select ${errors.division ? 'error' : ''}`,\n            required: true,\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"\",\n              children: loading.divisions ? 'Loading divisions...' : 'Select Division'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 653,\n              columnNumber: 15\n            }, this), divisions.map(division => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: division.id,\n              children: division.name\n            }, division.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 657,\n              columnNumber: 17\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 646,\n            columnNumber: 13\n          }, this), errors.division && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"error-message\",\n            children: errors.division\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 663,\n            columnNumber: 15\n          }, this), errors.divisions && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"error-message\",\n            children: errors.divisions\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 666,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 642,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"form-label\",\n            children: [\"Category \", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"required\",\n              children: \"*\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 673,\n              columnNumber: 24\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 672,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: selectedCategory,\n            onChange: handleCategoryChange,\n            disabled: !selectedDivision || loading.categories,\n            className: `form-select ${errors.category ? 'error' : ''}`,\n            required: true,\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"\",\n              children: !selectedDivision ? 'Select Division first' : loading.categories ? 'Loading categories...' : 'Select Category'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 682,\n              columnNumber: 15\n            }, this), categories.map(category => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: category.id,\n              children: category.name\n            }, category.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 691,\n              columnNumber: 17\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 675,\n            columnNumber: 13\n          }, this), errors.category && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"error-message\",\n            children: errors.category\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 697,\n            columnNumber: 15\n          }, this), errors.categories && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"error-message\",\n            children: errors.categories\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 700,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 671,\n          columnNumber: 11\n        }, this), formAvailability.showSubCategoryDropdown && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"form-label\",\n            children: \"Subcategory (Optional)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 707,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: selectedSubCategory,\n            onChange: handleSubCategoryChange,\n            disabled: !selectedCategory || loading.subCategories,\n            className: \"form-select\",\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"\",\n              children: !selectedCategory ? 'Select Category first' : loading.subCategories ? 'Loading subcategories...' : 'Select Subcategory (Optional)'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 716,\n              columnNumber: 17\n            }, this), subCategories.map(subCategory => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: subCategory.id,\n              children: subCategory.name\n            }, subCategory.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 725,\n              columnNumber: 19\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 710,\n            columnNumber: 15\n          }, this), errors.subCategories && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"error-message\",\n            children: errors.subCategories\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 731,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 706,\n          columnNumber: 13\n        }, this), loading.form && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading-message\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Loading form configuration...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 739,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 738,\n          columnNumber: 13\n        }, this), formAvailability.message && !loading.form && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `status-message ${formAvailability.categoryHasForm || formAvailability.subCategoryHasForm ? 'success' : formAvailability.message.includes('No form') ? 'error' : 'info'}`,\n          children: formAvailability.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 744,\n          columnNumber: 13\n        }, this), errors.form && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"error-message\",\n          children: errors.form\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 756,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 638,\n        columnNumber: 9\n      }, this), formConfig && Object.entries(sections).map(([sectionKey, section]) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: section.title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 763,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-fields\",\n          children: section.fields.filter(field => shouldShowField(field)).map((field, fieldIndex) => /*#__PURE__*/_jsxDEV(FormField, {\n            field: field,\n            value: formData[field.key],\n            onChange: value => handleFieldChange(field.key, value),\n            error: errors[field.key]\n          }, `${sectionKey}-${field.key}-${fieldIndex}`, false, {\n            fileName: _jsxFileName,\n            lineNumber: 768,\n            columnNumber: 19\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 764,\n          columnNumber: 13\n        }, this)]\n      }, sectionKey, true, {\n        fileName: _jsxFileName,\n        lineNumber: 762,\n        columnNumber: 11\n      }, this)), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-actions\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          onClick: onCancel,\n          className: \"btn btn-outline\",\n          disabled: submitting,\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 784,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          className: \"btn btn-primary\",\n          disabled: submitting || !selectedDivision || !selectedCategory,\n          children: submitting ? mode === 'create' ? 'Creating...' : 'Updating...' : mode === 'create' ? 'Create Person' : 'Update Person'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 792,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 783,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 636,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 619,\n    columnNumber: 5\n  }, this);\n};\n_s(DynamicPersonForm, \"HQTZ+LW5pRnY6eFU2EO5G2IvcY0=\");\n_c = DynamicPersonForm;\nexport default DynamicPersonForm;\nvar _c;\n$RefreshReg$(_c, \"DynamicPersonForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "PersonNatureLabels", "<PERSON><PERSON><PERSON><PERSON>", "WorkingProfileLabels", "getDefaultPersonData", "formConfigService", "apiService", "FormField", "jsxDEV", "_jsxDEV", "DynamicPersonForm", "onSubmit", "onCancel", "initialData", "mode", "_s", "divisions", "setDivisions", "categories", "setCategories", "subCategories", "setSubCategories", "selectedDivision", "setSelectedDivision", "selectedCate<PERSON><PERSON>", "setSelectedCategory", "selectedSubCategory", "setSelectedSubCategory", "formConfig", "setFormConfig", "formData", "setFormData", "errors", "setErrors", "loading", "setLoading", "form", "submitting", "setSubmitting", "formAvailability", "setFormAvailability", "categoryHasForm", "subCategoryHasForm", "showSubCategoryDropdown", "message", "loadDivisions", "testApiConnectivity", "_initialData$division", "_initialData$category", "_initialData$subCateg", "divisionId", "toString", "categoryId", "subCategoryId", "defaultForm", "getDefaultFormConfig", "fields", "deduplicateFields", "console", "log", "response", "getDivisions", "error", "loadCategories", "handleCategorySelection", "handleSubCategorySelection", "prev", "data", "getCategoriesByDivision", "loadSubCategories", "getSubCategoriesByCategory", "hasFormForCategory", "parseInt", "categoryForm", "loadFormConfig", "hasFormForSubCategory", "subCategoryForm", "handleDivisionChange", "e", "value", "target", "handleCategoryChange", "handleSubCategoryChange", "handleFieldChange", "<PERSON><PERSON><PERSON>", "validateForm", "newErrors", "division", "category", "<PERSON><PERSON><PERSON><PERSON>", "for<PERSON>ach", "field", "key", "required", "trim", "label", "conditional", "shouldShowField", "_field$validation", "_field$validation2", "_field$validation3", "type", "emailRegex", "test", "phoneRegex", "URL", "validation", "numValue", "parseFloat", "min", "undefined", "max", "pattern", "regex", "RegExp", "<PERSON><PERSON><PERSON><PERSON>", "length", "max<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "dateOfMarriage", "dateOfBirth", "birthDate", "Date", "marriageDate", "Object", "keys", "handleSubmit", "preventDefault", "submitData", "name", "mobileNumber", "nature", "gender", "alternateNumbers", "Array", "isArray", "split", "map", "s", "primaryEmailId", "alternateEmailIds", "website", "workingState", "domesticState", "district", "address", "workingArea", "hasAssociate", "associate<PERSON><PERSON>", "associateRelation", "associate<PERSON><PERSON><PERSON>", "usingWebsite", "websiteLink", "usingCRMApp", "crmAppLink", "transactionValue", "reraRegistrationNumber", "workingProfiles", "wp", "starRating", "source", "remarks", "firmName", "numberOfOffices", "numberOfBranches", "totalEmployeeStrength", "authorizedPersonName", "authorizedPersonEmail", "designation", "marketingContact", "marketingDesignation", "placeOfPosting", "department", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "missingFields", "entries", "filter", "general", "join", "result", "create<PERSON>erson", "update<PERSON><PERSON>", "id", "_error$data", "backendErrors", "errorMessages", "toLowerCase", "isValidationError", "getValidationErrors", "_error$data2", "_error$data3", "errorMessage", "title", "status", "conditionValue", "expectedValue", "groupFieldsBySections", "fieldKeys", "duplicateKeys", "index", "indexOf", "warn", "sections", "sectionKey", "section", "getSectionTitle", "push", "titles", "personalInfo", "contactInfo", "locationInfo", "businessInfo", "associateInfo", "digitalPresence", "companyInfo", "<PERSON><PERSON><PERSON>", "marketingInfo", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "description", "onChange", "disabled", "subCategory", "includes", "fieldIndex", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/Advanced/Advanced CRM filed all use/frontend/src/components/forms/DynamicPersonForm.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { PersonNatureLabels, GenderLabels, WorkingProfileLabels, getDefaultPersonData } from '../../constants/personConstants';\nimport formConfigService from '../../services/formConfigService';\nimport apiService from '../../services/apiService';\nimport FormField from './FormField';\nimport './DynamicPersonForm.css';\n\nconst DynamicPersonForm = ({ onSubmit, onCancel, initialData = null, mode = 'create' }) => {\n  // Hierarchy state\n  const [divisions, setDivisions] = useState([]);\n  const [categories, setCategories] = useState([]);\n  const [subCategories, setSubCategories] = useState([]);\n\n  // Selection state\n  const [selectedDivision, setSelectedDivision] = useState('');\n  const [selectedCategory, setSelectedCategory] = useState('');\n  const [selectedSubCategory, setSelectedSubCategory] = useState('');\n\n  // Form state\n  const [formConfig, setFormConfig] = useState(null);\n  const [formData, setFormData] = useState(getDefaultPersonData());\n  const [errors, setErrors] = useState({});\n\n  // Loading states\n  const [loading, setLoading] = useState({\n    divisions: false,\n    categories: false,\n    subCategories: false,\n    form: false\n  });\n  const [submitting, setSubmitting] = useState(false);\n\n  // Form availability state\n  const [formAvailability, setFormAvailability] = useState({\n    categoryHasForm: false,\n    subCategoryHasForm: false,\n    showSubCategoryDropdown: false,\n    message: ''\n  });\n\n  // Load divisions on component mount\n  useEffect(() => {\n    loadDivisions();\n\n    // Test API connectivity\n    testApiConnectivity();\n\n    // Load initial data if provided\n    if (initialData) {\n      setFormData(initialData);\n      setSelectedDivision(initialData.divisionId?.toString() || '');\n      setSelectedCategory(initialData.categoryId?.toString() || '');\n      setSelectedSubCategory(initialData.subCategoryId?.toString() || '');\n    } else {\n      // Load default form for new person creation\n      const defaultForm = formConfigService.getDefaultFormConfig();\n      // Ensure no duplicate fields in default form\n      if (defaultForm.fields) {\n        defaultForm.fields = deduplicateFields(defaultForm.fields);\n      }\n      setFormConfig(defaultForm);\n    }\n  }, [initialData]);\n\n  // Test API connectivity\n  const testApiConnectivity = async () => {\n    try {\n      console.log('Testing API connectivity...');\n      const response = await apiService.getDivisions();\n      console.log('API test successful:', response);\n    } catch (error) {\n      console.error('API connectivity test failed:', error);\n    }\n  };\n\n  // Load categories when division changes\n  useEffect(() => {\n    if (selectedDivision) {\n      loadCategories(selectedDivision);\n      // Reset category and subcategory when division changes\n      setSelectedCategory('');\n      setSelectedSubCategory('');\n      setSubCategories([]);\n      setFormConfig(null);\n      setFormAvailability({\n        categoryHasForm: false,\n        subCategoryHasForm: false,\n        showSubCategoryDropdown: false,\n        message: ''\n      });\n    } else {\n      setCategories([]);\n      setSubCategories([]);\n      setSelectedCategory('');\n      setSelectedSubCategory('');\n      setFormConfig(null);\n    }\n  }, [selectedDivision]);\n\n  // Handle category selection and form loading\n  useEffect(() => {\n    if (selectedCategory) {\n      handleCategorySelection(selectedCategory);\n      // Reset subcategory when category changes\n      setSelectedSubCategory('');\n    } else {\n      setSubCategories([]);\n      setSelectedSubCategory('');\n      setFormConfig(null);\n      setFormAvailability({\n        categoryHasForm: false,\n        subCategoryHasForm: false,\n        showSubCategoryDropdown: false,\n        message: ''\n      });\n    }\n  }, [selectedCategory]);\n\n  // Handle subcategory selection and form loading\n  useEffect(() => {\n    if (selectedSubCategory) {\n      handleSubCategorySelection(selectedSubCategory);\n    }\n  }, [selectedSubCategory]);\n\n  // Load divisions from API\n  const loadDivisions = async () => {\n    setLoading(prev => ({ ...prev, divisions: true }));\n    try {\n      const response = await apiService.getDivisions();\n      setDivisions(response.data || []);\n    } catch (error) {\n      console.error('Error loading divisions:', error);\n      setErrors(prev => ({ ...prev, divisions: 'Failed to load divisions' }));\n    } finally {\n      setLoading(prev => ({ ...prev, divisions: false }));\n    }\n  };\n\n  // Load categories by division\n  const loadCategories = async (divisionId) => {\n    setLoading(prev => ({ ...prev, categories: true }));\n    try {\n      const response = await apiService.getCategoriesByDivision(divisionId);\n      setCategories(response.data || []);\n    } catch (error) {\n      console.error('Error loading categories:', error);\n      setErrors(prev => ({ ...prev, categories: 'Failed to load categories' }));\n      setCategories([]);\n    } finally {\n      setLoading(prev => ({ ...prev, categories: false }));\n    }\n  };\n\n  // Load subcategories by category\n  const loadSubCategories = async (categoryId) => {\n    setLoading(prev => ({ ...prev, subCategories: true }));\n    try {\n      const response = await apiService.getSubCategoriesByCategory(categoryId);\n      setSubCategories(response.data || []);\n    } catch (error) {\n      console.error('Error loading subcategories:', error);\n      setErrors(prev => ({ ...prev, subCategories: 'Failed to load subcategories' }));\n      setSubCategories([]);\n    } finally {\n      setLoading(prev => ({ ...prev, subCategories: false }));\n    }\n  };\n\n  // Handle category selection and form loading logic\n  const handleCategorySelection = async (categoryId) => {\n    setLoading(prev => ({ ...prev, form: true }));\n\n    try {\n      // Check if category has a form\n      const categoryHasForm = formConfigService.hasFormForCategory(parseInt(categoryId));\n\n      if (categoryHasForm) {\n        // Load category form and hide subcategory dropdown\n        const categoryForm = formConfigService.loadFormConfig('category', parseInt(categoryId));\n        setFormConfig(categoryForm);\n        setFormAvailability({\n          categoryHasForm: true,\n          subCategoryHasForm: false,\n          showSubCategoryDropdown: false,\n          message: 'Using category-specific form'\n        });\n      } else {\n        // No category form, show subcategory dropdown and load subcategories\n        await loadSubCategories(categoryId);\n        // Load default form as fallback\n        const defaultForm = formConfigService.getDefaultFormConfig();\n        setFormConfig(defaultForm);\n        setFormAvailability({\n          categoryHasForm: false,\n          subCategoryHasForm: false,\n          showSubCategoryDropdown: true,\n          message: 'Using default form. You can select a subcategory if available.'\n        });\n      }\n    } catch (error) {\n      console.error('Error handling category selection:', error);\n      setErrors(prev => ({ ...prev, form: 'Error loading form configuration' }));\n    } finally {\n      setLoading(prev => ({ ...prev, form: false }));\n    }\n  };\n\n  // Handle subcategory selection and form loading logic\n  const handleSubCategorySelection = async (subCategoryId) => {\n    setLoading(prev => ({ ...prev, form: true }));\n\n    try {\n      // Check if subcategory has a form\n      const subCategoryHasForm = formConfigService.hasFormForSubCategory(parseInt(subCategoryId));\n\n      if (subCategoryHasForm) {\n        // Load subcategory form\n        const subCategoryForm = formConfigService.loadFormConfig('subcategory', parseInt(subCategoryId));\n        setFormConfig(subCategoryForm);\n        setFormAvailability({\n          categoryHasForm: false,\n          subCategoryHasForm: true,\n          showSubCategoryDropdown: true,\n          message: 'Using subcategory-specific form'\n        });\n      } else {\n        // No subcategory form available, use default form\n        const defaultForm = formConfigService.getDefaultFormConfig();\n        setFormConfig(defaultForm);\n        setFormAvailability({\n          categoryHasForm: false,\n          subCategoryHasForm: false,\n          showSubCategoryDropdown: true,\n          message: 'No specific form found for this subcategory. Using default form.'\n        });\n      }\n    } catch (error) {\n      console.error('Error handling subcategory selection:', error);\n      setErrors(prev => ({ ...prev, form: 'Error loading form configuration' }));\n    } finally {\n      setLoading(prev => ({ ...prev, form: false }));\n    }\n  };\n\n  // Handle dropdown changes\n  const handleDivisionChange = (e) => {\n    const value = e.target.value;\n    setSelectedDivision(value);\n\n    // Update form data\n    setFormData(prev => ({\n      ...prev,\n      divisionId: value ? parseInt(value) : null,\n      categoryId: null,\n      subCategoryId: null\n    }));\n  };\n\n  const handleCategoryChange = (e) => {\n    const value = e.target.value;\n    setSelectedCategory(value);\n\n    // Update form data\n    setFormData(prev => ({\n      ...prev,\n      categoryId: value ? parseInt(value) : null,\n      subCategoryId: null\n    }));\n  };\n\n  const handleSubCategoryChange = (e) => {\n    const value = e.target.value;\n    setSelectedSubCategory(value);\n\n    // Update form data\n    setFormData(prev => ({\n      ...prev,\n      subCategoryId: value ? parseInt(value) : null\n    }));\n  };\n\n  const handleFieldChange = (fieldKey, value) => {\n    setFormData(prev => ({\n      ...prev,\n      [fieldKey]: value\n    }));\n\n    // Clear field error when user starts typing\n    if (errors[fieldKey]) {\n      setErrors(prev => ({\n        ...prev,\n        [fieldKey]: null\n      }));\n    }\n  };\n\n  const validateForm = () => {\n    const newErrors = {};\n\n    // Validate hierarchy selection\n    if (!selectedDivision) {\n      newErrors.division = 'Division is required';\n    }\n    if (!selectedCategory) {\n      newErrors.category = 'Category is required';\n    }\n\n    // Check if form is available (should always have default form as fallback)\n    if (!formConfig) {\n      newErrors.form = 'Form configuration not loaded. Please try again.';\n      return { isValid: false, errors: newErrors };\n    }\n\n\n\n    // Validate form fields\n    formConfig.fields.forEach(field => {\n      const value = formData[field.key];\n      \n      // Required field validation\n      if (field.required && (!value || (typeof value === 'string' && value.trim() === ''))) {\n        newErrors[field.key] = `${field.label} is required`;\n        return;\n      }\n\n      // Conditional field validation\n      if (field.conditional && shouldShowField(field)) {\n        if (field.required && (!value || (typeof value === 'string' && value.trim() === ''))) {\n          newErrors[field.key] = `${field.label} is required`;\n          return;\n        }\n      }\n\n      // Type-specific validation\n      if (value && typeof value === 'string' && value.trim() !== '') {\n        switch (field.type) {\n          case 'email':\n            const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n            if (!emailRegex.test(value)) {\n              newErrors[field.key] = 'Please enter a valid email address';\n            }\n            break;\n          case 'tel':\n            const phoneRegex = /^(\\+91[\\-\\s]?)?[0]?(91)?[789]\\d{9}$/;\n            if (!phoneRegex.test(value)) {\n              newErrors[field.key] = 'Please enter a valid mobile number';\n            }\n            break;\n          case 'url':\n            try {\n              new URL(value);\n            } catch {\n              newErrors[field.key] = 'Please enter a valid URL';\n            }\n            break;\n          case 'number':\n            if (field.validation) {\n              const numValue = parseFloat(value);\n              if (field.validation.min !== undefined && numValue < field.validation.min) {\n                newErrors[field.key] = `Value must be at least ${field.validation.min}`;\n              }\n              if (field.validation.max !== undefined && numValue > field.validation.max) {\n                newErrors[field.key] = `Value must be at most ${field.validation.max}`;\n              }\n            }\n            break;\n        }\n\n        // Pattern validation\n        if (field.validation?.pattern) {\n          const regex = new RegExp(field.validation.pattern);\n          if (!regex.test(value)) {\n            newErrors[field.key] = `${field.label} format is invalid`;\n          }\n        }\n\n        // Length validation\n        if (field.validation?.minLength && value.length < field.validation.minLength) {\n          newErrors[field.key] = `${field.label} must be at least ${field.validation.minLength} characters`;\n        }\n        if (field.validation?.maxLength && value.length > field.validation.maxLength) {\n          newErrors[field.key] = `${field.label} must be at most ${field.validation.maxLength} characters`;\n        }\n      }\n    });\n\n    // Business logic validation\n    if (formData.isMarried && formData.dateOfMarriage && formData.dateOfBirth) {\n      const birthDate = new Date(formData.dateOfBirth);\n      const marriageDate = new Date(formData.dateOfMarriage);\n      if (marriageDate <= birthDate) {\n        newErrors.dateOfMarriage = 'Marriage date must be after birth date';\n      }\n    }\n\n    return {\n      isValid: Object.keys(newErrors).length === 0,\n      errors: newErrors\n    };\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    \n    const validation = validateForm();\n    if (!validation.isValid) {\n      console.log('Validation failed:', validation.errors);\n      setErrors(validation.errors);\n      return;\n    }\n\n    setSubmitting(true);\n    try {\n      // Prepare data for API - match backend model exactly\n      const submitData = {\n        // Required hierarchy fields\n        divisionId: parseInt(selectedDivision),\n        categoryId: parseInt(selectedCategory),\n        subCategoryId: selectedSubCategory ? parseInt(selectedSubCategory) : null,\n\n        // Required fields\n        name: formData.name || '',\n        mobileNumber: formData.mobileNumber || '',\n        nature: formData.nature ? parseInt(formData.nature) : null,\n\n        // Optional enum fields\n        gender: formData.gender ? parseInt(formData.gender) : null,\n\n        // Contact Information\n        alternateNumbers: Array.isArray(formData.alternateNumbers)\n          ? formData.alternateNumbers\n          : formData.alternateNumbers ? formData.alternateNumbers.split(',').map(s => s.trim()) : [],\n        primaryEmailId: formData.primaryEmailId || '',\n        alternateEmailIds: Array.isArray(formData.alternateEmailIds)\n          ? formData.alternateEmailIds\n          : formData.alternateEmailIds ? formData.alternateEmailIds.split(',').map(s => s.trim()) : [],\n        website: formData.website || '',\n\n        // Personal Information\n        dateOfBirth: formData.dateOfBirth || null,\n        isMarried: formData.isMarried || false,\n        dateOfMarriage: formData.dateOfMarriage || null,\n\n        // Location Information\n        workingState: formData.workingState || '',\n        domesticState: formData.domesticState || '',\n        district: formData.district || '',\n        address: formData.address || '',\n        workingArea: formData.workingArea || '',\n\n        // Associate Information\n        hasAssociate: formData.hasAssociate || false,\n        associateName: formData.associateName || '',\n        associateRelation: formData.associateRelation || '',\n        associateMobile: formData.associateMobile || '',\n\n        // Digital Presence\n        usingWebsite: formData.usingWebsite || false,\n        websiteLink: formData.websiteLink || '',\n        usingCRMApp: formData.usingCRMApp || false,\n        crmAppLink: formData.crmAppLink || '',\n\n        // Business Information\n        transactionValue: formData.transactionValue ? parseFloat(formData.transactionValue) : null,\n        reraRegistrationNumber: formData.reraRegistrationNumber || '',\n        workingProfiles: Array.isArray(formData.workingProfiles)\n          ? formData.workingProfiles.map(wp => parseInt(wp))\n          : [],\n        starRating: formData.starRating ? parseInt(formData.starRating) : null,\n        source: formData.source || '',\n        remarks: formData.remarks || '',\n\n        // Company Information\n        firmName: formData.firmName || '',\n        numberOfOffices: formData.numberOfOffices ? parseInt(formData.numberOfOffices) : null,\n        numberOfBranches: formData.numberOfBranches ? parseInt(formData.numberOfBranches) : null,\n        totalEmployeeStrength: formData.totalEmployeeStrength ? parseInt(formData.totalEmployeeStrength) : null,\n\n        // Authorized Person\n        authorizedPersonName: formData.authorizedPersonName || '',\n        authorizedPersonEmail: formData.authorizedPersonEmail || '',\n        designation: formData.designation || '',\n\n        // Marketing Information\n        marketingContact: formData.marketingContact || '',\n        marketingDesignation: formData.marketingDesignation || '',\n        placeOfPosting: formData.placeOfPosting || '',\n        department: formData.department || ''\n      };\n\n      // Debug logging\n      console.log('Submitting person data:', submitData);\n      console.log('Selected Division:', selectedDivision);\n      console.log('Selected Category:', selectedCategory);\n      console.log('Selected SubCategory:', selectedSubCategory);\n\n      // Validate required fields before submission\n      const requiredFieldsCheck = {\n        divisionId: submitData.divisionId,\n        categoryId: submitData.categoryId,\n        name: submitData.name,\n        mobileNumber: submitData.mobileNumber,\n        nature: submitData.nature\n      };\n      console.log('Required fields check:', requiredFieldsCheck);\n\n      // Check for missing required fields\n      const missingFields = Object.entries(requiredFieldsCheck)\n        .filter(([, value]) => !value || value === '' || value === null)\n        .map(([key]) => key);\n\n      if (missingFields.length > 0) {\n        console.error('Missing required fields:', missingFields);\n        setErrors({ general: 'Missing required fields: ' + missingFields.join(', ') });\n        return;\n      }\n\n      let result;\n      if (mode === 'create') {\n        result = await apiService.createPerson(submitData);\n      } else {\n        result = await apiService.updatePerson(initialData.id, submitData);\n      }\n\n      onSubmit(result);\n    } catch (error) {\n      console.error('Error submitting form:', error);\n      console.error('Error data:', error.data);\n      console.error('Full error object:', error);\n\n      if (error.data?.errors) {\n        // Handle validation errors from backend\n        const backendErrors = {};\n        Object.keys(error.data.errors).forEach(key => {\n          const errorMessages = error.data.errors[key];\n          backendErrors[key.toLowerCase()] = Array.isArray(errorMessages)\n            ? errorMessages.join(', ')\n            : errorMessages;\n        });\n        console.log('Backend validation errors:', backendErrors);\n        setErrors(backendErrors);\n      } else if (error.isValidationError && error.isValidationError()) {\n        setErrors(error.getValidationErrors());\n      } else {\n        const errorMessage = error.data?.message ||\n                           error.data?.title ||\n                           error.message ||\n                           `HTTP ${error.status || 'Unknown'} - An error occurred while saving`;\n        console.log('General error message:', errorMessage);\n        setErrors({ general: errorMessage });\n      }\n    } finally {\n      setSubmitting(false);\n    }\n  };\n\n  const shouldShowField = (field) => {\n    if (!field.conditional) return true;\n    \n    const conditionValue = formData[field.conditional.field];\n    const expectedValue = field.conditional.value;\n    \n    // Handle boolean conditions\n    if (typeof expectedValue === 'boolean' || expectedValue === 'true' || expectedValue === 'false') {\n      return conditionValue === (expectedValue === true || expectedValue === 'true');\n    }\n    \n    return conditionValue === expectedValue;\n  };\n\n  const groupFieldsBySections = () => {\n    if (!formConfig || !formConfig.fields) return {};\n\n    // Debug: Check for duplicate fields\n    const fieldKeys = formConfig.fields.map(field => field.key);\n    const duplicateKeys = fieldKeys.filter((key, index) => fieldKeys.indexOf(key) !== index);\n    if (duplicateKeys.length > 0) {\n      console.warn('Duplicate field keys found:', duplicateKeys);\n      console.log('All fields:', formConfig.fields);\n    }\n\n    const sections = {};\n    formConfig.fields.forEach((field) => {\n      const sectionKey = field.section || 'general';\n      if (!sections[sectionKey]) {\n        sections[sectionKey] = {\n          title: getSectionTitle(sectionKey),\n          fields: []\n        };\n      }\n      sections[sectionKey].fields.push(field);\n    });\n\n    return sections;\n  };\n\n  const getSectionTitle = (sectionKey) => {\n    const titles = {\n      personalInfo: 'Personal Information',\n      contactInfo: 'Contact Information',\n      locationInfo: 'Location Information',\n      businessInfo: 'Business Information',\n      associateInfo: 'Associate Information',\n      digitalPresence: 'Digital Presence',\n      companyInfo: 'Company Information',\n      authorizedPerson: 'Authorized Person',\n      marketingInfo: 'Marketing Information',\n      general: 'General Information'\n    };\n    return titles[sectionKey] || sectionKey;\n  };\n\n  const sections = formConfig ? groupFieldsBySections() : {};\n\n\n\n  return (\n    <div className=\"dynamic-person-form\">\n      <div className=\"form-header\">\n        <h2>{mode === 'create' ? 'Create New Person' : 'Edit Person'}</h2>\n        {formConfig && (\n          <div className=\"form-info\">\n            <span className=\"form-name\">{formConfig.name}</span>\n            {formConfig.description && (\n              <span className=\"form-description\">{formConfig.description}</span>\n            )}\n          </div>\n        )}\n      </div>\n\n      {errors.general && (\n        <div className=\"alert alert-error\">{errors.general}</div>\n      )}\n\n      <form onSubmit={handleSubmit} className=\"person-form\">\n        {/* Hierarchical Dropdowns */}\n        <div className=\"form-section\">\n          <h3>Division & Category Selection</h3>\n\n          {/* Division Dropdown */}\n          <div className=\"form-group\">\n            <label className=\"form-label\">\n              Division <span className=\"required\">*</span>\n            </label>\n            <select\n              value={selectedDivision}\n              onChange={handleDivisionChange}\n              disabled={loading.divisions}\n              className={`form-select ${errors.division ? 'error' : ''}`}\n              required\n            >\n              <option value=\"\">\n                {loading.divisions ? 'Loading divisions...' : 'Select Division'}\n              </option>\n              {divisions.map(division => (\n                <option key={division.id} value={division.id}>\n                  {division.name}\n                </option>\n              ))}\n            </select>\n            {errors.division && (\n              <div className=\"error-message\">{errors.division}</div>\n            )}\n            {errors.divisions && (\n              <div className=\"error-message\">{errors.divisions}</div>\n            )}\n          </div>\n\n          {/* Category Dropdown */}\n          <div className=\"form-group\">\n            <label className=\"form-label\">\n              Category <span className=\"required\">*</span>\n            </label>\n            <select\n              value={selectedCategory}\n              onChange={handleCategoryChange}\n              disabled={!selectedDivision || loading.categories}\n              className={`form-select ${errors.category ? 'error' : ''}`}\n              required\n            >\n              <option value=\"\">\n                {!selectedDivision\n                  ? 'Select Division first'\n                  : loading.categories\n                    ? 'Loading categories...'\n                    : 'Select Category'\n                }\n              </option>\n              {categories.map(category => (\n                <option key={category.id} value={category.id}>\n                  {category.name}\n                </option>\n              ))}\n            </select>\n            {errors.category && (\n              <div className=\"error-message\">{errors.category}</div>\n            )}\n            {errors.categories && (\n              <div className=\"error-message\">{errors.categories}</div>\n            )}\n          </div>\n\n          {/* Subcategory Dropdown - Only show if category doesn't have a form */}\n          {formAvailability.showSubCategoryDropdown && (\n            <div className=\"form-group\">\n              <label className=\"form-label\">\n                Subcategory (Optional)\n              </label>\n              <select\n                value={selectedSubCategory}\n                onChange={handleSubCategoryChange}\n                disabled={!selectedCategory || loading.subCategories}\n                className=\"form-select\"\n              >\n                <option value=\"\">\n                  {!selectedCategory\n                    ? 'Select Category first'\n                    : loading.subCategories\n                      ? 'Loading subcategories...'\n                      : 'Select Subcategory (Optional)'\n                  }\n                </option>\n                {subCategories.map(subCategory => (\n                  <option key={subCategory.id} value={subCategory.id}>\n                    {subCategory.name}\n                  </option>\n                ))}\n              </select>\n              {errors.subCategories && (\n                <div className=\"error-message\">{errors.subCategories}</div>\n              )}\n            </div>\n          )}\n\n          {/* Form Status Messages */}\n          {loading.form && (\n            <div className=\"loading-message\">\n              <span>Loading form configuration...</span>\n            </div>\n          )}\n\n          {formAvailability.message && !loading.form && (\n            <div className={`status-message ${\n              formAvailability.categoryHasForm || formAvailability.subCategoryHasForm\n                ? 'success'\n                : formAvailability.message.includes('No form')\n                  ? 'error'\n                  : 'info'\n            }`}>\n              {formAvailability.message}\n            </div>\n          )}\n\n          {errors.form && (\n            <div className=\"error-message\">{errors.form}</div>\n          )}\n        </div>\n\n        {/* Dynamic Form Fields - Only show when form is available */}\n        {formConfig && Object.entries(sections).map(([sectionKey, section]) => (\n          <div key={sectionKey} className=\"form-section\">\n            <h3>{section.title}</h3>\n            <div className=\"form-fields\">\n              {section.fields\n                .filter(field => shouldShowField(field))\n                .map((field, fieldIndex) => (\n                  <FormField\n                    key={`${sectionKey}-${field.key}-${fieldIndex}`}\n                    field={field}\n                    value={formData[field.key]}\n                    onChange={(value) => handleFieldChange(field.key, value)}\n                    error={errors[field.key]}\n                  />\n                ))}\n            </div>\n          </div>\n        ))}\n\n\n\n        {/* Form Actions */}\n        <div className=\"form-actions\">\n          <button\n            type=\"button\"\n            onClick={onCancel}\n            className=\"btn btn-outline\"\n            disabled={submitting}\n          >\n            Cancel\n          </button>\n          <button\n            type=\"submit\"\n            className=\"btn btn-primary\"\n            disabled={submitting || !selectedDivision || !selectedCategory}\n          >\n            {submitting\n              ? (mode === 'create' ? 'Creating...' : 'Updating...')\n              : (mode === 'create' ? 'Create Person' : 'Update Person')\n            }\n          </button>\n        </div>\n      </form>\n    </div>\n  );\n};\n\nexport default DynamicPersonForm;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,kBAAkB,EAAEC,YAAY,EAAEC,oBAAoB,EAAEC,oBAAoB,QAAQ,iCAAiC;AAC9H,OAAOC,iBAAiB,MAAM,kCAAkC;AAChE,OAAOC,UAAU,MAAM,2BAA2B;AAClD,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAO,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjC,MAAMC,iBAAiB,GAAGA,CAAC;EAAEC,QAAQ;EAAEC,QAAQ;EAAEC,WAAW,GAAG,IAAI;EAAEC,IAAI,GAAG;AAAS,CAAC,KAAK;EAAAC,EAAA;EACzF;EACA,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACmB,UAAU,EAAEC,aAAa,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACqB,aAAa,EAAEC,gBAAgB,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;;EAEtD;EACA,MAAM,CAACuB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACyB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAAC2B,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;;EAElE;EACA,MAAM,CAAC6B,UAAU,EAAEC,aAAa,CAAC,GAAG9B,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAAC+B,QAAQ,EAAEC,WAAW,CAAC,GAAGhC,QAAQ,CAACK,oBAAoB,CAAC,CAAC,CAAC;EAChE,MAAM,CAAC4B,MAAM,EAAEC,SAAS,CAAC,GAAGlC,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAExC;EACA,MAAM,CAACmC,OAAO,EAAEC,UAAU,CAAC,GAAGpC,QAAQ,CAAC;IACrCiB,SAAS,EAAE,KAAK;IAChBE,UAAU,EAAE,KAAK;IACjBE,aAAa,EAAE,KAAK;IACpBgB,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGvC,QAAQ,CAAC,KAAK,CAAC;;EAEnD;EACA,MAAM,CAACwC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGzC,QAAQ,CAAC;IACvD0C,eAAe,EAAE,KAAK;IACtBC,kBAAkB,EAAE,KAAK;IACzBC,uBAAuB,EAAE,KAAK;IAC9BC,OAAO,EAAE;EACX,CAAC,CAAC;;EAEF;EACA5C,SAAS,CAAC,MAAM;IACd6C,aAAa,CAAC,CAAC;;IAEf;IACAC,mBAAmB,CAAC,CAAC;;IAErB;IACA,IAAIjC,WAAW,EAAE;MAAA,IAAAkC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA;MACflB,WAAW,CAAClB,WAAW,CAAC;MACxBU,mBAAmB,CAAC,EAAAwB,qBAAA,GAAAlC,WAAW,CAACqC,UAAU,cAAAH,qBAAA,uBAAtBA,qBAAA,CAAwBI,QAAQ,CAAC,CAAC,KAAI,EAAE,CAAC;MAC7D1B,mBAAmB,CAAC,EAAAuB,qBAAA,GAAAnC,WAAW,CAACuC,UAAU,cAAAJ,qBAAA,uBAAtBA,qBAAA,CAAwBG,QAAQ,CAAC,CAAC,KAAI,EAAE,CAAC;MAC7DxB,sBAAsB,CAAC,EAAAsB,qBAAA,GAAApC,WAAW,CAACwC,aAAa,cAAAJ,qBAAA,uBAAzBA,qBAAA,CAA2BE,QAAQ,CAAC,CAAC,KAAI,EAAE,CAAC;IACrE,CAAC,MAAM;MACL;MACA,MAAMG,WAAW,GAAGjD,iBAAiB,CAACkD,oBAAoB,CAAC,CAAC;MAC5D;MACA,IAAID,WAAW,CAACE,MAAM,EAAE;QACtBF,WAAW,CAACE,MAAM,GAAGC,iBAAiB,CAACH,WAAW,CAACE,MAAM,CAAC;MAC5D;MACA3B,aAAa,CAACyB,WAAW,CAAC;IAC5B;EACF,CAAC,EAAE,CAACzC,WAAW,CAAC,CAAC;;EAEjB;EACA,MAAMiC,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACFY,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;MAC1C,MAAMC,QAAQ,GAAG,MAAMtD,UAAU,CAACuD,YAAY,CAAC,CAAC;MAChDH,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEC,QAAQ,CAAC;IAC/C,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdJ,OAAO,CAACI,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;IACvD;EACF,CAAC;;EAED;EACA9D,SAAS,CAAC,MAAM;IACd,IAAIsB,gBAAgB,EAAE;MACpByC,cAAc,CAACzC,gBAAgB,CAAC;MAChC;MACAG,mBAAmB,CAAC,EAAE,CAAC;MACvBE,sBAAsB,CAAC,EAAE,CAAC;MAC1BN,gBAAgB,CAAC,EAAE,CAAC;MACpBQ,aAAa,CAAC,IAAI,CAAC;MACnBW,mBAAmB,CAAC;QAClBC,eAAe,EAAE,KAAK;QACtBC,kBAAkB,EAAE,KAAK;QACzBC,uBAAuB,EAAE,KAAK;QAC9BC,OAAO,EAAE;MACX,CAAC,CAAC;IACJ,CAAC,MAAM;MACLzB,aAAa,CAAC,EAAE,CAAC;MACjBE,gBAAgB,CAAC,EAAE,CAAC;MACpBI,mBAAmB,CAAC,EAAE,CAAC;MACvBE,sBAAsB,CAAC,EAAE,CAAC;MAC1BE,aAAa,CAAC,IAAI,CAAC;IACrB;EACF,CAAC,EAAE,CAACP,gBAAgB,CAAC,CAAC;;EAEtB;EACAtB,SAAS,CAAC,MAAM;IACd,IAAIwB,gBAAgB,EAAE;MACpBwC,uBAAuB,CAACxC,gBAAgB,CAAC;MACzC;MACAG,sBAAsB,CAAC,EAAE,CAAC;IAC5B,CAAC,MAAM;MACLN,gBAAgB,CAAC,EAAE,CAAC;MACpBM,sBAAsB,CAAC,EAAE,CAAC;MAC1BE,aAAa,CAAC,IAAI,CAAC;MACnBW,mBAAmB,CAAC;QAClBC,eAAe,EAAE,KAAK;QACtBC,kBAAkB,EAAE,KAAK;QACzBC,uBAAuB,EAAE,KAAK;QAC9BC,OAAO,EAAE;MACX,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACpB,gBAAgB,CAAC,CAAC;;EAEtB;EACAxB,SAAS,CAAC,MAAM;IACd,IAAI0B,mBAAmB,EAAE;MACvBuC,0BAA0B,CAACvC,mBAAmB,CAAC;IACjD;EACF,CAAC,EAAE,CAACA,mBAAmB,CAAC,CAAC;;EAEzB;EACA,MAAMmB,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChCV,UAAU,CAAC+B,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAElD,SAAS,EAAE;IAAK,CAAC,CAAC,CAAC;IAClD,IAAI;MACF,MAAM4C,QAAQ,GAAG,MAAMtD,UAAU,CAACuD,YAAY,CAAC,CAAC;MAChD5C,YAAY,CAAC2C,QAAQ,CAACO,IAAI,IAAI,EAAE,CAAC;IACnC,CAAC,CAAC,OAAOL,KAAK,EAAE;MACdJ,OAAO,CAACI,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChD7B,SAAS,CAACiC,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAElD,SAAS,EAAE;MAA2B,CAAC,CAAC,CAAC;IACzE,CAAC,SAAS;MACRmB,UAAU,CAAC+B,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAElD,SAAS,EAAE;MAAM,CAAC,CAAC,CAAC;IACrD;EACF,CAAC;;EAED;EACA,MAAM+C,cAAc,GAAG,MAAOb,UAAU,IAAK;IAC3Cf,UAAU,CAAC+B,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAEhD,UAAU,EAAE;IAAK,CAAC,CAAC,CAAC;IACnD,IAAI;MACF,MAAM0C,QAAQ,GAAG,MAAMtD,UAAU,CAAC8D,uBAAuB,CAAClB,UAAU,CAAC;MACrE/B,aAAa,CAACyC,QAAQ,CAACO,IAAI,IAAI,EAAE,CAAC;IACpC,CAAC,CAAC,OAAOL,KAAK,EAAE;MACdJ,OAAO,CAACI,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD7B,SAAS,CAACiC,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEhD,UAAU,EAAE;MAA4B,CAAC,CAAC,CAAC;MACzEC,aAAa,CAAC,EAAE,CAAC;IACnB,CAAC,SAAS;MACRgB,UAAU,CAAC+B,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEhD,UAAU,EAAE;MAAM,CAAC,CAAC,CAAC;IACtD;EACF,CAAC;;EAED;EACA,MAAMmD,iBAAiB,GAAG,MAAOjB,UAAU,IAAK;IAC9CjB,UAAU,CAAC+B,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE9C,aAAa,EAAE;IAAK,CAAC,CAAC,CAAC;IACtD,IAAI;MACF,MAAMwC,QAAQ,GAAG,MAAMtD,UAAU,CAACgE,0BAA0B,CAAClB,UAAU,CAAC;MACxE/B,gBAAgB,CAACuC,QAAQ,CAACO,IAAI,IAAI,EAAE,CAAC;IACvC,CAAC,CAAC,OAAOL,KAAK,EAAE;MACdJ,OAAO,CAACI,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD7B,SAAS,CAACiC,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE9C,aAAa,EAAE;MAA+B,CAAC,CAAC,CAAC;MAC/EC,gBAAgB,CAAC,EAAE,CAAC;IACtB,CAAC,SAAS;MACRc,UAAU,CAAC+B,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE9C,aAAa,EAAE;MAAM,CAAC,CAAC,CAAC;IACzD;EACF,CAAC;;EAED;EACA,MAAM4C,uBAAuB,GAAG,MAAOZ,UAAU,IAAK;IACpDjB,UAAU,CAAC+B,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE9B,IAAI,EAAE;IAAK,CAAC,CAAC,CAAC;IAE7C,IAAI;MACF;MACA,MAAMK,eAAe,GAAGpC,iBAAiB,CAACkE,kBAAkB,CAACC,QAAQ,CAACpB,UAAU,CAAC,CAAC;MAElF,IAAIX,eAAe,EAAE;QACnB;QACA,MAAMgC,YAAY,GAAGpE,iBAAiB,CAACqE,cAAc,CAAC,UAAU,EAAEF,QAAQ,CAACpB,UAAU,CAAC,CAAC;QACvFvB,aAAa,CAAC4C,YAAY,CAAC;QAC3BjC,mBAAmB,CAAC;UAClBC,eAAe,EAAE,IAAI;UACrBC,kBAAkB,EAAE,KAAK;UACzBC,uBAAuB,EAAE,KAAK;UAC9BC,OAAO,EAAE;QACX,CAAC,CAAC;MACJ,CAAC,MAAM;QACL;QACA,MAAMyB,iBAAiB,CAACjB,UAAU,CAAC;QACnC;QACA,MAAME,WAAW,GAAGjD,iBAAiB,CAACkD,oBAAoB,CAAC,CAAC;QAC5D1B,aAAa,CAACyB,WAAW,CAAC;QAC1Bd,mBAAmB,CAAC;UAClBC,eAAe,EAAE,KAAK;UACtBC,kBAAkB,EAAE,KAAK;UACzBC,uBAAuB,EAAE,IAAI;UAC7BC,OAAO,EAAE;QACX,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAOkB,KAAK,EAAE;MACdJ,OAAO,CAACI,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;MAC1D7B,SAAS,CAACiC,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE9B,IAAI,EAAE;MAAmC,CAAC,CAAC,CAAC;IAC5E,CAAC,SAAS;MACRD,UAAU,CAAC+B,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE9B,IAAI,EAAE;MAAM,CAAC,CAAC,CAAC;IAChD;EACF,CAAC;;EAED;EACA,MAAM6B,0BAA0B,GAAG,MAAOZ,aAAa,IAAK;IAC1DlB,UAAU,CAAC+B,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE9B,IAAI,EAAE;IAAK,CAAC,CAAC,CAAC;IAE7C,IAAI;MACF;MACA,MAAMM,kBAAkB,GAAGrC,iBAAiB,CAACsE,qBAAqB,CAACH,QAAQ,CAACnB,aAAa,CAAC,CAAC;MAE3F,IAAIX,kBAAkB,EAAE;QACtB;QACA,MAAMkC,eAAe,GAAGvE,iBAAiB,CAACqE,cAAc,CAAC,aAAa,EAAEF,QAAQ,CAACnB,aAAa,CAAC,CAAC;QAChGxB,aAAa,CAAC+C,eAAe,CAAC;QAC9BpC,mBAAmB,CAAC;UAClBC,eAAe,EAAE,KAAK;UACtBC,kBAAkB,EAAE,IAAI;UACxBC,uBAAuB,EAAE,IAAI;UAC7BC,OAAO,EAAE;QACX,CAAC,CAAC;MACJ,CAAC,MAAM;QACL;QACA,MAAMU,WAAW,GAAGjD,iBAAiB,CAACkD,oBAAoB,CAAC,CAAC;QAC5D1B,aAAa,CAACyB,WAAW,CAAC;QAC1Bd,mBAAmB,CAAC;UAClBC,eAAe,EAAE,KAAK;UACtBC,kBAAkB,EAAE,KAAK;UACzBC,uBAAuB,EAAE,IAAI;UAC7BC,OAAO,EAAE;QACX,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAOkB,KAAK,EAAE;MACdJ,OAAO,CAACI,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;MAC7D7B,SAAS,CAACiC,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE9B,IAAI,EAAE;MAAmC,CAAC,CAAC,CAAC;IAC5E,CAAC,SAAS;MACRD,UAAU,CAAC+B,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE9B,IAAI,EAAE;MAAM,CAAC,CAAC,CAAC;IAChD;EACF,CAAC;;EAED;EACA,MAAMyC,oBAAoB,GAAIC,CAAC,IAAK;IAClC,MAAMC,KAAK,GAAGD,CAAC,CAACE,MAAM,CAACD,KAAK;IAC5BxD,mBAAmB,CAACwD,KAAK,CAAC;;IAE1B;IACAhD,WAAW,CAACmC,IAAI,KAAK;MACnB,GAAGA,IAAI;MACPhB,UAAU,EAAE6B,KAAK,GAAGP,QAAQ,CAACO,KAAK,CAAC,GAAG,IAAI;MAC1C3B,UAAU,EAAE,IAAI;MAChBC,aAAa,EAAE;IACjB,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAM4B,oBAAoB,GAAIH,CAAC,IAAK;IAClC,MAAMC,KAAK,GAAGD,CAAC,CAACE,MAAM,CAACD,KAAK;IAC5BtD,mBAAmB,CAACsD,KAAK,CAAC;;IAE1B;IACAhD,WAAW,CAACmC,IAAI,KAAK;MACnB,GAAGA,IAAI;MACPd,UAAU,EAAE2B,KAAK,GAAGP,QAAQ,CAACO,KAAK,CAAC,GAAG,IAAI;MAC1C1B,aAAa,EAAE;IACjB,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAM6B,uBAAuB,GAAIJ,CAAC,IAAK;IACrC,MAAMC,KAAK,GAAGD,CAAC,CAACE,MAAM,CAACD,KAAK;IAC5BpD,sBAAsB,CAACoD,KAAK,CAAC;;IAE7B;IACAhD,WAAW,CAACmC,IAAI,KAAK;MACnB,GAAGA,IAAI;MACPb,aAAa,EAAE0B,KAAK,GAAGP,QAAQ,CAACO,KAAK,CAAC,GAAG;IAC3C,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMI,iBAAiB,GAAGA,CAACC,QAAQ,EAAEL,KAAK,KAAK;IAC7ChD,WAAW,CAACmC,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACkB,QAAQ,GAAGL;IACd,CAAC,CAAC,CAAC;;IAEH;IACA,IAAI/C,MAAM,CAACoD,QAAQ,CAAC,EAAE;MACpBnD,SAAS,CAACiC,IAAI,KAAK;QACjB,GAAGA,IAAI;QACP,CAACkB,QAAQ,GAAG;MACd,CAAC,CAAC,CAAC;IACL;EACF,CAAC;EAED,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,SAAS,GAAG,CAAC,CAAC;;IAEpB;IACA,IAAI,CAAChE,gBAAgB,EAAE;MACrBgE,SAAS,CAACC,QAAQ,GAAG,sBAAsB;IAC7C;IACA,IAAI,CAAC/D,gBAAgB,EAAE;MACrB8D,SAAS,CAACE,QAAQ,GAAG,sBAAsB;IAC7C;;IAEA;IACA,IAAI,CAAC5D,UAAU,EAAE;MACf0D,SAAS,CAAClD,IAAI,GAAG,kDAAkD;MACnE,OAAO;QAAEqD,OAAO,EAAE,KAAK;QAAEzD,MAAM,EAAEsD;MAAU,CAAC;IAC9C;;IAIA;IACA1D,UAAU,CAAC4B,MAAM,CAACkC,OAAO,CAACC,KAAK,IAAI;MACjC,MAAMZ,KAAK,GAAGjD,QAAQ,CAAC6D,KAAK,CAACC,GAAG,CAAC;;MAEjC;MACA,IAAID,KAAK,CAACE,QAAQ,KAAK,CAACd,KAAK,IAAK,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACe,IAAI,CAAC,CAAC,KAAK,EAAG,CAAC,EAAE;QACpFR,SAAS,CAACK,KAAK,CAACC,GAAG,CAAC,GAAG,GAAGD,KAAK,CAACI,KAAK,cAAc;QACnD;MACF;;MAEA;MACA,IAAIJ,KAAK,CAACK,WAAW,IAAIC,eAAe,CAACN,KAAK,CAAC,EAAE;QAC/C,IAAIA,KAAK,CAACE,QAAQ,KAAK,CAACd,KAAK,IAAK,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACe,IAAI,CAAC,CAAC,KAAK,EAAG,CAAC,EAAE;UACpFR,SAAS,CAACK,KAAK,CAACC,GAAG,CAAC,GAAG,GAAGD,KAAK,CAACI,KAAK,cAAc;UACnD;QACF;MACF;;MAEA;MACA,IAAIhB,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACe,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;QAAA,IAAAI,iBAAA,EAAAC,kBAAA,EAAAC,kBAAA;QAC7D,QAAQT,KAAK,CAACU,IAAI;UAChB,KAAK,OAAO;YACV,MAAMC,UAAU,GAAG,4BAA4B;YAC/C,IAAI,CAACA,UAAU,CAACC,IAAI,CAACxB,KAAK,CAAC,EAAE;cAC3BO,SAAS,CAACK,KAAK,CAACC,GAAG,CAAC,GAAG,oCAAoC;YAC7D;YACA;UACF,KAAK,KAAK;YACR,MAAMY,UAAU,GAAG,qCAAqC;YACxD,IAAI,CAACA,UAAU,CAACD,IAAI,CAACxB,KAAK,CAAC,EAAE;cAC3BO,SAAS,CAACK,KAAK,CAACC,GAAG,CAAC,GAAG,oCAAoC;YAC7D;YACA;UACF,KAAK,KAAK;YACR,IAAI;cACF,IAAIa,GAAG,CAAC1B,KAAK,CAAC;YAChB,CAAC,CAAC,MAAM;cACNO,SAAS,CAACK,KAAK,CAACC,GAAG,CAAC,GAAG,0BAA0B;YACnD;YACA;UACF,KAAK,QAAQ;YACX,IAAID,KAAK,CAACe,UAAU,EAAE;cACpB,MAAMC,QAAQ,GAAGC,UAAU,CAAC7B,KAAK,CAAC;cAClC,IAAIY,KAAK,CAACe,UAAU,CAACG,GAAG,KAAKC,SAAS,IAAIH,QAAQ,GAAGhB,KAAK,CAACe,UAAU,CAACG,GAAG,EAAE;gBACzEvB,SAAS,CAACK,KAAK,CAACC,GAAG,CAAC,GAAG,0BAA0BD,KAAK,CAACe,UAAU,CAACG,GAAG,EAAE;cACzE;cACA,IAAIlB,KAAK,CAACe,UAAU,CAACK,GAAG,KAAKD,SAAS,IAAIH,QAAQ,GAAGhB,KAAK,CAACe,UAAU,CAACK,GAAG,EAAE;gBACzEzB,SAAS,CAACK,KAAK,CAACC,GAAG,CAAC,GAAG,yBAAyBD,KAAK,CAACe,UAAU,CAACK,GAAG,EAAE;cACxE;YACF;YACA;QACJ;;QAEA;QACA,KAAAb,iBAAA,GAAIP,KAAK,CAACe,UAAU,cAAAR,iBAAA,eAAhBA,iBAAA,CAAkBc,OAAO,EAAE;UAC7B,MAAMC,KAAK,GAAG,IAAIC,MAAM,CAACvB,KAAK,CAACe,UAAU,CAACM,OAAO,CAAC;UAClD,IAAI,CAACC,KAAK,CAACV,IAAI,CAACxB,KAAK,CAAC,EAAE;YACtBO,SAAS,CAACK,KAAK,CAACC,GAAG,CAAC,GAAG,GAAGD,KAAK,CAACI,KAAK,oBAAoB;UAC3D;QACF;;QAEA;QACA,IAAI,CAAAI,kBAAA,GAAAR,KAAK,CAACe,UAAU,cAAAP,kBAAA,eAAhBA,kBAAA,CAAkBgB,SAAS,IAAIpC,KAAK,CAACqC,MAAM,GAAGzB,KAAK,CAACe,UAAU,CAACS,SAAS,EAAE;UAC5E7B,SAAS,CAACK,KAAK,CAACC,GAAG,CAAC,GAAG,GAAGD,KAAK,CAACI,KAAK,qBAAqBJ,KAAK,CAACe,UAAU,CAACS,SAAS,aAAa;QACnG;QACA,IAAI,CAAAf,kBAAA,GAAAT,KAAK,CAACe,UAAU,cAAAN,kBAAA,eAAhBA,kBAAA,CAAkBiB,SAAS,IAAItC,KAAK,CAACqC,MAAM,GAAGzB,KAAK,CAACe,UAAU,CAACW,SAAS,EAAE;UAC5E/B,SAAS,CAACK,KAAK,CAACC,GAAG,CAAC,GAAG,GAAGD,KAAK,CAACI,KAAK,oBAAoBJ,KAAK,CAACe,UAAU,CAACW,SAAS,aAAa;QAClG;MACF;IACF,CAAC,CAAC;;IAEF;IACA,IAAIvF,QAAQ,CAACwF,SAAS,IAAIxF,QAAQ,CAACyF,cAAc,IAAIzF,QAAQ,CAAC0F,WAAW,EAAE;MACzE,MAAMC,SAAS,GAAG,IAAIC,IAAI,CAAC5F,QAAQ,CAAC0F,WAAW,CAAC;MAChD,MAAMG,YAAY,GAAG,IAAID,IAAI,CAAC5F,QAAQ,CAACyF,cAAc,CAAC;MACtD,IAAII,YAAY,IAAIF,SAAS,EAAE;QAC7BnC,SAAS,CAACiC,cAAc,GAAG,wCAAwC;MACrE;IACF;IAEA,OAAO;MACL9B,OAAO,EAAEmC,MAAM,CAACC,IAAI,CAACvC,SAAS,CAAC,CAAC8B,MAAM,KAAK,CAAC;MAC5CpF,MAAM,EAAEsD;IACV,CAAC;EACH,CAAC;EAED,MAAMwC,YAAY,GAAG,MAAOhD,CAAC,IAAK;IAChCA,CAAC,CAACiD,cAAc,CAAC,CAAC;IAElB,MAAMrB,UAAU,GAAGrB,YAAY,CAAC,CAAC;IACjC,IAAI,CAACqB,UAAU,CAACjB,OAAO,EAAE;MACvB/B,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE+C,UAAU,CAAC1E,MAAM,CAAC;MACpDC,SAAS,CAACyE,UAAU,CAAC1E,MAAM,CAAC;MAC5B;IACF;IAEAM,aAAa,CAAC,IAAI,CAAC;IACnB,IAAI;MACF;MACA,MAAM0F,UAAU,GAAG;QACjB;QACA9E,UAAU,EAAEsB,QAAQ,CAAClD,gBAAgB,CAAC;QACtC8B,UAAU,EAAEoB,QAAQ,CAAChD,gBAAgB,CAAC;QACtC6B,aAAa,EAAE3B,mBAAmB,GAAG8C,QAAQ,CAAC9C,mBAAmB,CAAC,GAAG,IAAI;QAEzE;QACAuG,IAAI,EAAEnG,QAAQ,CAACmG,IAAI,IAAI,EAAE;QACzBC,YAAY,EAAEpG,QAAQ,CAACoG,YAAY,IAAI,EAAE;QACzCC,MAAM,EAAErG,QAAQ,CAACqG,MAAM,GAAG3D,QAAQ,CAAC1C,QAAQ,CAACqG,MAAM,CAAC,GAAG,IAAI;QAE1D;QACAC,MAAM,EAAEtG,QAAQ,CAACsG,MAAM,GAAG5D,QAAQ,CAAC1C,QAAQ,CAACsG,MAAM,CAAC,GAAG,IAAI;QAE1D;QACAC,gBAAgB,EAAEC,KAAK,CAACC,OAAO,CAACzG,QAAQ,CAACuG,gBAAgB,CAAC,GACtDvG,QAAQ,CAACuG,gBAAgB,GACzBvG,QAAQ,CAACuG,gBAAgB,GAAGvG,QAAQ,CAACuG,gBAAgB,CAACG,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,CAAC,IAAIA,CAAC,CAAC5C,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE;QAC5F6C,cAAc,EAAE7G,QAAQ,CAAC6G,cAAc,IAAI,EAAE;QAC7CC,iBAAiB,EAAEN,KAAK,CAACC,OAAO,CAACzG,QAAQ,CAAC8G,iBAAiB,CAAC,GACxD9G,QAAQ,CAAC8G,iBAAiB,GAC1B9G,QAAQ,CAAC8G,iBAAiB,GAAG9G,QAAQ,CAAC8G,iBAAiB,CAACJ,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,CAAC,IAAIA,CAAC,CAAC5C,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE;QAC9F+C,OAAO,EAAE/G,QAAQ,CAAC+G,OAAO,IAAI,EAAE;QAE/B;QACArB,WAAW,EAAE1F,QAAQ,CAAC0F,WAAW,IAAI,IAAI;QACzCF,SAAS,EAAExF,QAAQ,CAACwF,SAAS,IAAI,KAAK;QACtCC,cAAc,EAAEzF,QAAQ,CAACyF,cAAc,IAAI,IAAI;QAE/C;QACAuB,YAAY,EAAEhH,QAAQ,CAACgH,YAAY,IAAI,EAAE;QACzCC,aAAa,EAAEjH,QAAQ,CAACiH,aAAa,IAAI,EAAE;QAC3CC,QAAQ,EAAElH,QAAQ,CAACkH,QAAQ,IAAI,EAAE;QACjCC,OAAO,EAAEnH,QAAQ,CAACmH,OAAO,IAAI,EAAE;QAC/BC,WAAW,EAAEpH,QAAQ,CAACoH,WAAW,IAAI,EAAE;QAEvC;QACAC,YAAY,EAAErH,QAAQ,CAACqH,YAAY,IAAI,KAAK;QAC5CC,aAAa,EAAEtH,QAAQ,CAACsH,aAAa,IAAI,EAAE;QAC3CC,iBAAiB,EAAEvH,QAAQ,CAACuH,iBAAiB,IAAI,EAAE;QACnDC,eAAe,EAAExH,QAAQ,CAACwH,eAAe,IAAI,EAAE;QAE/C;QACAC,YAAY,EAAEzH,QAAQ,CAACyH,YAAY,IAAI,KAAK;QAC5CC,WAAW,EAAE1H,QAAQ,CAAC0H,WAAW,IAAI,EAAE;QACvCC,WAAW,EAAE3H,QAAQ,CAAC2H,WAAW,IAAI,KAAK;QAC1CC,UAAU,EAAE5H,QAAQ,CAAC4H,UAAU,IAAI,EAAE;QAErC;QACAC,gBAAgB,EAAE7H,QAAQ,CAAC6H,gBAAgB,GAAG/C,UAAU,CAAC9E,QAAQ,CAAC6H,gBAAgB,CAAC,GAAG,IAAI;QAC1FC,sBAAsB,EAAE9H,QAAQ,CAAC8H,sBAAsB,IAAI,EAAE;QAC7DC,eAAe,EAAEvB,KAAK,CAACC,OAAO,CAACzG,QAAQ,CAAC+H,eAAe,CAAC,GACpD/H,QAAQ,CAAC+H,eAAe,CAACpB,GAAG,CAACqB,EAAE,IAAItF,QAAQ,CAACsF,EAAE,CAAC,CAAC,GAChD,EAAE;QACNC,UAAU,EAAEjI,QAAQ,CAACiI,UAAU,GAAGvF,QAAQ,CAAC1C,QAAQ,CAACiI,UAAU,CAAC,GAAG,IAAI;QACtEC,MAAM,EAAElI,QAAQ,CAACkI,MAAM,IAAI,EAAE;QAC7BC,OAAO,EAAEnI,QAAQ,CAACmI,OAAO,IAAI,EAAE;QAE/B;QACAC,QAAQ,EAAEpI,QAAQ,CAACoI,QAAQ,IAAI,EAAE;QACjCC,eAAe,EAAErI,QAAQ,CAACqI,eAAe,GAAG3F,QAAQ,CAAC1C,QAAQ,CAACqI,eAAe,CAAC,GAAG,IAAI;QACrFC,gBAAgB,EAAEtI,QAAQ,CAACsI,gBAAgB,GAAG5F,QAAQ,CAAC1C,QAAQ,CAACsI,gBAAgB,CAAC,GAAG,IAAI;QACxFC,qBAAqB,EAAEvI,QAAQ,CAACuI,qBAAqB,GAAG7F,QAAQ,CAAC1C,QAAQ,CAACuI,qBAAqB,CAAC,GAAG,IAAI;QAEvG;QACAC,oBAAoB,EAAExI,QAAQ,CAACwI,oBAAoB,IAAI,EAAE;QACzDC,qBAAqB,EAAEzI,QAAQ,CAACyI,qBAAqB,IAAI,EAAE;QAC3DC,WAAW,EAAE1I,QAAQ,CAAC0I,WAAW,IAAI,EAAE;QAEvC;QACAC,gBAAgB,EAAE3I,QAAQ,CAAC2I,gBAAgB,IAAI,EAAE;QACjDC,oBAAoB,EAAE5I,QAAQ,CAAC4I,oBAAoB,IAAI,EAAE;QACzDC,cAAc,EAAE7I,QAAQ,CAAC6I,cAAc,IAAI,EAAE;QAC7CC,UAAU,EAAE9I,QAAQ,CAAC8I,UAAU,IAAI;MACrC,CAAC;;MAED;MACAlH,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEqE,UAAU,CAAC;MAClDtE,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAErC,gBAAgB,CAAC;MACnDoC,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEnC,gBAAgB,CAAC;MACnDkC,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEjC,mBAAmB,CAAC;;MAEzD;MACA,MAAMmJ,mBAAmB,GAAG;QAC1B3H,UAAU,EAAE8E,UAAU,CAAC9E,UAAU;QACjCE,UAAU,EAAE4E,UAAU,CAAC5E,UAAU;QACjC6E,IAAI,EAAED,UAAU,CAACC,IAAI;QACrBC,YAAY,EAAEF,UAAU,CAACE,YAAY;QACrCC,MAAM,EAAEH,UAAU,CAACG;MACrB,CAAC;MACDzE,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEkH,mBAAmB,CAAC;;MAE1D;MACA,MAAMC,aAAa,GAAGlD,MAAM,CAACmD,OAAO,CAACF,mBAAmB,CAAC,CACtDG,MAAM,CAAC,CAAC,GAAGjG,KAAK,CAAC,KAAK,CAACA,KAAK,IAAIA,KAAK,KAAK,EAAE,IAAIA,KAAK,KAAK,IAAI,CAAC,CAC/D0D,GAAG,CAAC,CAAC,CAAC7C,GAAG,CAAC,KAAKA,GAAG,CAAC;MAEtB,IAAIkF,aAAa,CAAC1D,MAAM,GAAG,CAAC,EAAE;QAC5B1D,OAAO,CAACI,KAAK,CAAC,0BAA0B,EAAEgH,aAAa,CAAC;QACxD7I,SAAS,CAAC;UAAEgJ,OAAO,EAAE,2BAA2B,GAAGH,aAAa,CAACI,IAAI,CAAC,IAAI;QAAE,CAAC,CAAC;QAC9E;MACF;MAEA,IAAIC,MAAM;MACV,IAAIrK,IAAI,KAAK,QAAQ,EAAE;QACrBqK,MAAM,GAAG,MAAM7K,UAAU,CAAC8K,YAAY,CAACpD,UAAU,CAAC;MACpD,CAAC,MAAM;QACLmD,MAAM,GAAG,MAAM7K,UAAU,CAAC+K,YAAY,CAACxK,WAAW,CAACyK,EAAE,EAAEtD,UAAU,CAAC;MACpE;MAEArH,QAAQ,CAACwK,MAAM,CAAC;IAClB,CAAC,CAAC,OAAOrH,KAAK,EAAE;MAAA,IAAAyH,WAAA;MACd7H,OAAO,CAACI,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9CJ,OAAO,CAACI,KAAK,CAAC,aAAa,EAAEA,KAAK,CAACK,IAAI,CAAC;MACxCT,OAAO,CAACI,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAE1C,KAAAyH,WAAA,GAAIzH,KAAK,CAACK,IAAI,cAAAoH,WAAA,eAAVA,WAAA,CAAYvJ,MAAM,EAAE;QACtB;QACA,MAAMwJ,aAAa,GAAG,CAAC,CAAC;QACxB5D,MAAM,CAACC,IAAI,CAAC/D,KAAK,CAACK,IAAI,CAACnC,MAAM,CAAC,CAAC0D,OAAO,CAACE,GAAG,IAAI;UAC5C,MAAM6F,aAAa,GAAG3H,KAAK,CAACK,IAAI,CAACnC,MAAM,CAAC4D,GAAG,CAAC;UAC5C4F,aAAa,CAAC5F,GAAG,CAAC8F,WAAW,CAAC,CAAC,CAAC,GAAGpD,KAAK,CAACC,OAAO,CAACkD,aAAa,CAAC,GAC3DA,aAAa,CAACP,IAAI,CAAC,IAAI,CAAC,GACxBO,aAAa;QACnB,CAAC,CAAC;QACF/H,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE6H,aAAa,CAAC;QACxDvJ,SAAS,CAACuJ,aAAa,CAAC;MAC1B,CAAC,MAAM,IAAI1H,KAAK,CAAC6H,iBAAiB,IAAI7H,KAAK,CAAC6H,iBAAiB,CAAC,CAAC,EAAE;QAC/D1J,SAAS,CAAC6B,KAAK,CAAC8H,mBAAmB,CAAC,CAAC,CAAC;MACxC,CAAC,MAAM;QAAA,IAAAC,YAAA,EAAAC,YAAA;QACL,MAAMC,YAAY,GAAG,EAAAF,YAAA,GAAA/H,KAAK,CAACK,IAAI,cAAA0H,YAAA,uBAAVA,YAAA,CAAYjJ,OAAO,OAAAkJ,YAAA,GACrBhI,KAAK,CAACK,IAAI,cAAA2H,YAAA,uBAAVA,YAAA,CAAYE,KAAK,KACjBlI,KAAK,CAAClB,OAAO,IACb,QAAQkB,KAAK,CAACmI,MAAM,IAAI,SAAS,mCAAmC;QACvFvI,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEoI,YAAY,CAAC;QACnD9J,SAAS,CAAC;UAAEgJ,OAAO,EAAEc;QAAa,CAAC,CAAC;MACtC;IACF,CAAC,SAAS;MACRzJ,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;EAED,MAAM2D,eAAe,GAAIN,KAAK,IAAK;IACjC,IAAI,CAACA,KAAK,CAACK,WAAW,EAAE,OAAO,IAAI;IAEnC,MAAMkG,cAAc,GAAGpK,QAAQ,CAAC6D,KAAK,CAACK,WAAW,CAACL,KAAK,CAAC;IACxD,MAAMwG,aAAa,GAAGxG,KAAK,CAACK,WAAW,CAACjB,KAAK;;IAE7C;IACA,IAAI,OAAOoH,aAAa,KAAK,SAAS,IAAIA,aAAa,KAAK,MAAM,IAAIA,aAAa,KAAK,OAAO,EAAE;MAC/F,OAAOD,cAAc,MAAMC,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,MAAM,CAAC;IAChF;IAEA,OAAOD,cAAc,KAAKC,aAAa;EACzC,CAAC;EAED,MAAMC,qBAAqB,GAAGA,CAAA,KAAM;IAClC,IAAI,CAACxK,UAAU,IAAI,CAACA,UAAU,CAAC4B,MAAM,EAAE,OAAO,CAAC,CAAC;;IAEhD;IACA,MAAM6I,SAAS,GAAGzK,UAAU,CAAC4B,MAAM,CAACiF,GAAG,CAAC9C,KAAK,IAAIA,KAAK,CAACC,GAAG,CAAC;IAC3D,MAAM0G,aAAa,GAAGD,SAAS,CAACrB,MAAM,CAAC,CAACpF,GAAG,EAAE2G,KAAK,KAAKF,SAAS,CAACG,OAAO,CAAC5G,GAAG,CAAC,KAAK2G,KAAK,CAAC;IACxF,IAAID,aAAa,CAAClF,MAAM,GAAG,CAAC,EAAE;MAC5B1D,OAAO,CAAC+I,IAAI,CAAC,6BAA6B,EAAEH,aAAa,CAAC;MAC1D5I,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE/B,UAAU,CAAC4B,MAAM,CAAC;IAC/C;IAEA,MAAMkJ,QAAQ,GAAG,CAAC,CAAC;IACnB9K,UAAU,CAAC4B,MAAM,CAACkC,OAAO,CAAEC,KAAK,IAAK;MACnC,MAAMgH,UAAU,GAAGhH,KAAK,CAACiH,OAAO,IAAI,SAAS;MAC7C,IAAI,CAACF,QAAQ,CAACC,UAAU,CAAC,EAAE;QACzBD,QAAQ,CAACC,UAAU,CAAC,GAAG;UACrBX,KAAK,EAAEa,eAAe,CAACF,UAAU,CAAC;UAClCnJ,MAAM,EAAE;QACV,CAAC;MACH;MACAkJ,QAAQ,CAACC,UAAU,CAAC,CAACnJ,MAAM,CAACsJ,IAAI,CAACnH,KAAK,CAAC;IACzC,CAAC,CAAC;IAEF,OAAO+G,QAAQ;EACjB,CAAC;EAED,MAAMG,eAAe,GAAIF,UAAU,IAAK;IACtC,MAAMI,MAAM,GAAG;MACbC,YAAY,EAAE,sBAAsB;MACpCC,WAAW,EAAE,qBAAqB;MAClCC,YAAY,EAAE,sBAAsB;MACpCC,YAAY,EAAE,sBAAsB;MACpCC,aAAa,EAAE,uBAAuB;MACtCC,eAAe,EAAE,kBAAkB;MACnCC,WAAW,EAAE,qBAAqB;MAClCC,gBAAgB,EAAE,mBAAmB;MACrCC,aAAa,EAAE,uBAAuB;MACtCvC,OAAO,EAAE;IACX,CAAC;IACD,OAAO8B,MAAM,CAACJ,UAAU,CAAC,IAAIA,UAAU;EACzC,CAAC;EAED,MAAMD,QAAQ,GAAG9K,UAAU,GAAGwK,qBAAqB,CAAC,CAAC,GAAG,CAAC,CAAC;EAI1D,oBACE3L,OAAA;IAAKgN,SAAS,EAAC,qBAAqB;IAAAC,QAAA,gBAClCjN,OAAA;MAAKgN,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BjN,OAAA;QAAAiN,QAAA,EAAK5M,IAAI,KAAK,QAAQ,GAAG,mBAAmB,GAAG;MAAa;QAAA6M,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,EACjElM,UAAU,iBACTnB,OAAA;QAAKgN,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBjN,OAAA;UAAMgN,SAAS,EAAC,WAAW;UAAAC,QAAA,EAAE9L,UAAU,CAACqG;QAAI;UAAA0F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,EACnDlM,UAAU,CAACmM,WAAW,iBACrBtN,OAAA;UAAMgN,SAAS,EAAC,kBAAkB;UAAAC,QAAA,EAAE9L,UAAU,CAACmM;QAAW;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAClE;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAEL9L,MAAM,CAACiJ,OAAO,iBACbxK,OAAA;MAAKgN,SAAS,EAAC,mBAAmB;MAAAC,QAAA,EAAE1L,MAAM,CAACiJ;IAAO;MAAA0C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CACzD,eAEDrN,OAAA;MAAME,QAAQ,EAAEmH,YAAa;MAAC2F,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAEnDjN,OAAA;QAAKgN,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BjN,OAAA;UAAAiN,QAAA,EAAI;QAA6B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAGtCrN,OAAA;UAAKgN,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBjN,OAAA;YAAOgN,SAAS,EAAC,YAAY;YAAAC,QAAA,GAAC,WACnB,eAAAjN,OAAA;cAAMgN,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC,eACRrN,OAAA;YACEsE,KAAK,EAAEzD,gBAAiB;YACxB0M,QAAQ,EAAEnJ,oBAAqB;YAC/BoJ,QAAQ,EAAE/L,OAAO,CAAClB,SAAU;YAC5ByM,SAAS,EAAE,eAAezL,MAAM,CAACuD,QAAQ,GAAG,OAAO,GAAG,EAAE,EAAG;YAC3DM,QAAQ;YAAA6H,QAAA,gBAERjN,OAAA;cAAQsE,KAAK,EAAC,EAAE;cAAA2I,QAAA,EACbxL,OAAO,CAAClB,SAAS,GAAG,sBAAsB,GAAG;YAAiB;cAAA2M,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzD,CAAC,EACR9M,SAAS,CAACyH,GAAG,CAAClD,QAAQ,iBACrB9E,OAAA;cAA0BsE,KAAK,EAAEQ,QAAQ,CAAC+F,EAAG;cAAAoC,QAAA,EAC1CnI,QAAQ,CAAC0C;YAAI,GADH1C,QAAQ,CAAC+F,EAAE;cAAAqC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEhB,CACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,EACR9L,MAAM,CAACuD,QAAQ,iBACd9E,OAAA;YAAKgN,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAE1L,MAAM,CAACuD;UAAQ;YAAAoI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACtD,EACA9L,MAAM,CAAChB,SAAS,iBACfP,OAAA;YAAKgN,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAE1L,MAAM,CAAChB;UAAS;YAAA2M,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACvD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGNrN,OAAA;UAAKgN,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBjN,OAAA;YAAOgN,SAAS,EAAC,YAAY;YAAAC,QAAA,GAAC,WACnB,eAAAjN,OAAA;cAAMgN,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC,eACRrN,OAAA;YACEsE,KAAK,EAAEvD,gBAAiB;YACxBwM,QAAQ,EAAE/I,oBAAqB;YAC/BgJ,QAAQ,EAAE,CAAC3M,gBAAgB,IAAIY,OAAO,CAAChB,UAAW;YAClDuM,SAAS,EAAE,eAAezL,MAAM,CAACwD,QAAQ,GAAG,OAAO,GAAG,EAAE,EAAG;YAC3DK,QAAQ;YAAA6H,QAAA,gBAERjN,OAAA;cAAQsE,KAAK,EAAC,EAAE;cAAA2I,QAAA,EACb,CAACpM,gBAAgB,GACd,uBAAuB,GACvBY,OAAO,CAAChB,UAAU,GAChB,uBAAuB,GACvB;YAAiB;cAAAyM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEjB,CAAC,EACR5M,UAAU,CAACuH,GAAG,CAACjD,QAAQ,iBACtB/E,OAAA;cAA0BsE,KAAK,EAAES,QAAQ,CAAC8F,EAAG;cAAAoC,QAAA,EAC1ClI,QAAQ,CAACyC;YAAI,GADHzC,QAAQ,CAAC8F,EAAE;cAAAqC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEhB,CACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,EACR9L,MAAM,CAACwD,QAAQ,iBACd/E,OAAA;YAAKgN,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAE1L,MAAM,CAACwD;UAAQ;YAAAmI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACtD,EACA9L,MAAM,CAACd,UAAU,iBAChBT,OAAA;YAAKgN,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAE1L,MAAM,CAACd;UAAU;YAAAyM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACxD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,EAGLvL,gBAAgB,CAACI,uBAAuB,iBACvClC,OAAA;UAAKgN,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBjN,OAAA;YAAOgN,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAE9B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRrN,OAAA;YACEsE,KAAK,EAAErD,mBAAoB;YAC3BsM,QAAQ,EAAE9I,uBAAwB;YAClC+I,QAAQ,EAAE,CAACzM,gBAAgB,IAAIU,OAAO,CAACd,aAAc;YACrDqM,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAEvBjN,OAAA;cAAQsE,KAAK,EAAC,EAAE;cAAA2I,QAAA,EACb,CAAClM,gBAAgB,GACd,uBAAuB,GACvBU,OAAO,CAACd,aAAa,GACnB,0BAA0B,GAC1B;YAA+B;cAAAuM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAE/B,CAAC,EACR1M,aAAa,CAACqH,GAAG,CAACyF,WAAW,iBAC5BzN,OAAA;cAA6BsE,KAAK,EAAEmJ,WAAW,CAAC5C,EAAG;cAAAoC,QAAA,EAChDQ,WAAW,CAACjG;YAAI,GADNiG,WAAW,CAAC5C,EAAE;cAAAqC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEnB,CACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,EACR9L,MAAM,CAACZ,aAAa,iBACnBX,OAAA;YAAKgN,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAE1L,MAAM,CAACZ;UAAa;YAAAuM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAC3D;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN,EAGA5L,OAAO,CAACE,IAAI,iBACX3B,OAAA;UAAKgN,SAAS,EAAC,iBAAiB;UAAAC,QAAA,eAC9BjN,OAAA;YAAAiN,QAAA,EAAM;UAA6B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CACN,EAEAvL,gBAAgB,CAACK,OAAO,IAAI,CAACV,OAAO,CAACE,IAAI,iBACxC3B,OAAA;UAAKgN,SAAS,EAAE,kBACdlL,gBAAgB,CAACE,eAAe,IAAIF,gBAAgB,CAACG,kBAAkB,GACnE,SAAS,GACTH,gBAAgB,CAACK,OAAO,CAACuL,QAAQ,CAAC,SAAS,CAAC,GAC1C,OAAO,GACP,MAAM,EACX;UAAAT,QAAA,EACAnL,gBAAgB,CAACK;QAAO;UAAA+K,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CACN,EAEA9L,MAAM,CAACI,IAAI,iBACV3B,OAAA;UAAKgN,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAE1L,MAAM,CAACI;QAAI;UAAAuL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAClD;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,EAGLlM,UAAU,IAAIgG,MAAM,CAACmD,OAAO,CAAC2B,QAAQ,CAAC,CAACjE,GAAG,CAAC,CAAC,CAACkE,UAAU,EAAEC,OAAO,CAAC,kBAChEnM,OAAA;QAAsBgN,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC5CjN,OAAA;UAAAiN,QAAA,EAAKd,OAAO,CAACZ;QAAK;UAAA2B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACxBrN,OAAA;UAAKgN,SAAS,EAAC,aAAa;UAAAC,QAAA,EACzBd,OAAO,CAACpJ,MAAM,CACZwH,MAAM,CAACrF,KAAK,IAAIM,eAAe,CAACN,KAAK,CAAC,CAAC,CACvC8C,GAAG,CAAC,CAAC9C,KAAK,EAAEyI,UAAU,kBACrB3N,OAAA,CAACF,SAAS;YAERoF,KAAK,EAAEA,KAAM;YACbZ,KAAK,EAAEjD,QAAQ,CAAC6D,KAAK,CAACC,GAAG,CAAE;YAC3BoI,QAAQ,EAAGjJ,KAAK,IAAKI,iBAAiB,CAACQ,KAAK,CAACC,GAAG,EAAEb,KAAK,CAAE;YACzDjB,KAAK,EAAE9B,MAAM,CAAC2D,KAAK,CAACC,GAAG;UAAE,GAJpB,GAAG+G,UAAU,IAAIhH,KAAK,CAACC,GAAG,IAAIwI,UAAU,EAAE;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAKhD,CACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA,GAdEnB,UAAU;QAAAgB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAef,CACN,CAAC,eAKFrN,OAAA;QAAKgN,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BjN,OAAA;UACE4F,IAAI,EAAC,QAAQ;UACbgI,OAAO,EAAEzN,QAAS;UAClB6M,SAAS,EAAC,iBAAiB;UAC3BQ,QAAQ,EAAE5L,UAAW;UAAAqL,QAAA,EACtB;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTrN,OAAA;UACE4F,IAAI,EAAC,QAAQ;UACboH,SAAS,EAAC,iBAAiB;UAC3BQ,QAAQ,EAAE5L,UAAU,IAAI,CAACf,gBAAgB,IAAI,CAACE,gBAAiB;UAAAkM,QAAA,EAE9DrL,UAAU,GACNvB,IAAI,KAAK,QAAQ,GAAG,aAAa,GAAG,aAAa,GACjDA,IAAI,KAAK,QAAQ,GAAG,eAAe,GAAG;QAAgB;UAAA6M,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAErD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAAC/M,EAAA,CA9xBIL,iBAAiB;AAAA4N,EAAA,GAAjB5N,iBAAiB;AAgyBvB,eAAeA,iBAAiB;AAAC,IAAA4N,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}