{"format": 1, "restore": {"C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\backend\\CrmApi.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\backend\\CrmApi.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\backend\\CrmApi.csproj", "projectName": "CrmApi", "projectPath": "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\backend\\CrmApi.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\backend\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"BCrypt.Net-Next": {"target": "Package", "version": "[4.0.3, )"}, "CsvHelper": {"target": "Package", "version": "[30.0.1, )"}, "EPPlus": {"target": "Package", "version": "[7.0.0, )"}, "Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.AspNetCore.Cors": {"target": "Package", "version": "[2.2.0, )"}, "Microsoft.AspNetCore.OpenApi": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.EntityFrameworkCore.Design": {"target": "Package", "version": "[8.0.0, )"}, "Pomelo.EntityFrameworkCore.MySql": {"target": "Package", "version": "[8.0.0, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[6.4.0, )"}, "System.IdentityModel.Tokens.Jwt": {"target": "Package", "version": "[7.0.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.412/PortableRuntimeIdentifierGraph.json"}}}}}