using Microsoft.AspNetCore.Mvc;
using CrmApi.Models.Admin;
using CrmApi.Services.Admin;

namespace CrmApi.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class AuthController : ControllerBase
    {
        private readonly IAdminService _adminService;

        public AuthController(IAdminService adminService)
        {
            _adminService = adminService;
        }

        [HttpPost("login")]
        public async Task<ActionResult<LoginResponse>> Login([FromBody] LoginRequest request)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var response = await _adminService.LoginAsync(request);

            if (response == null)
            {
                return Unauthorized(new { message = "Invalid username or password" });
            }

            return Ok(response);
        }

        [HttpPost("validate")]
        public IActionResult ValidateToken()
        {
            // This endpoint will be protected by JWT middleware
            // If the request reaches here, the token is valid
            return Ok(new { message = "Token is valid", isValid = true });
        }
    }
}