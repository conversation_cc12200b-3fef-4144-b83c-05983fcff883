# CRM Database Migration Guide

## Overview

This guide provides comprehensive instructions for setting up the CRM database using Entity Framework migrations. The system includes complete database schema creation and initial data seeding.

## 🗄️ Database Schema

### Tables Created

1. **States** - All 36 Indian states and union territories
2. **Divisions** - Business divisions (Real Estate, Construction, Finance, etc.)
3. **Categories** - Categories within each division
4. **SubCategories** - Subcategories for detailed classification
5. **Persons** - Comprehensive person data with all fields
6. **Admins** - System administrators (created via seeding script)

### Key Features

- ✅ **Complete Hierarchical Structure**: Division → Category → SubCategory → Person
- ✅ **Comprehensive Person Model**: 40+ fields covering all business requirements
- ✅ **Proper Relationships**: Foreign keys with appropriate constraints
- ✅ **Performance Optimized**: Strategic indexes for fast queries
- ✅ **Data Integrity**: Unique constraints and validation rules
- ✅ **Soft Delete Support**: Logical deletion for persons
- ✅ **Audit Fields**: Created/updated timestamps throughout

## 🚀 Quick Start

### Option 1: Automated Migration (Recommended)

**For PowerShell users:**
```powershell
cd backend
.\Scripts\RunAllMigrations.ps1
```

**For Command Prompt users:**
```cmd
cd backend
Scripts\RunAllMigrations.bat
```

### Option 2: Manual Entity Framework Commands

```bash
cd backend

# Install EF tools if not already installed
dotnet tool install --global dotnet-ef

# Build the project
dotnet build

# Apply migrations
dotnet ef database update
```

### Option 3: Manual SQL Scripts (If EF fails)

```sql
-- Run these scripts in order:
1. Scripts\CreateDatabase.sql
2. Scripts\SeedData.sql
```

## 📋 Prerequisites

### Required Software
- **.NET 6.0 SDK** or later
- **SQL Server** (LocalDB, Express, or Full)
- **Entity Framework Core Tools**

### Installation Commands
```bash
# Install .NET SDK (if not installed)
# Download from: https://dotnet.microsoft.com/download

# Install EF Core tools
dotnet tool install --global dotnet-ef

# Verify installation
dotnet ef --version
```

## ⚙️ Configuration

### Connection String Setup

Update `appsettings.json` with your database connection:

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=CrmDatabase;Trusted_Connection=true;MultipleActiveResultSets=true"
  }
}
```

### Common Connection Strings

**SQL Server LocalDB:**
```
Server=(localdb)\\mssqllocaldb;Database=CrmDatabase;Trusted_Connection=true;MultipleActiveResultSets=true
```

**SQL Server Express:**
```
Server=.\\SQLEXPRESS;Database=CrmDatabase;Trusted_Connection=true;MultipleActiveResultSets=true
```

**SQL Server with Authentication:**
```
Server=localhost;Database=CrmDatabase;User Id=sa;Password=YourPassword;MultipleActiveResultSets=true
```

## 📊 Migration Details

### Migration Files

1. **20241201000001_InitialCreate.cs**
   - Creates all table structures
   - Sets up foreign key relationships
   - Creates performance indexes
   - Establishes unique constraints

2. **20241201000002_SeedInitialData.cs**
   - Inserts all Indian states and union territories
   - Adds sample business divisions
   - Creates sample categories and subcategories
   - Sets up initial data for testing

### Database Schema Details

#### States Table
```sql
- id (Primary Key)
- name (Unique, Required)
- code (Unique, Required) 
- capital
- region (North, South, East, West, Northeast, Central, Islands)
- is_active
- created_at, updated_at
```

#### Divisions Table
```sql
- id (Primary Key)
- name (Unique, Required)
- description
- is_active
- created_at, updated_at
```

#### Categories Table
```sql
- id (Primary Key)
- name (Required)
- description
- division_id (Foreign Key)
- is_active
- created_at, updated_at
- Unique constraint: (name, division_id)
```

#### SubCategories Table
```sql
- id (Primary Key)
- name (Required)
- description
- category_id (Foreign Key)
- is_active
- created_at, updated_at
- Unique constraint: (name, category_id)
```

#### Persons Table (40+ Fields)
```sql
-- Core Fields
- id, division_id, category_id, subcategory_id
- name, mobile_number, nature, gender

-- Contact Information
- alternate_numbers, primary_email_id, alternate_email_ids, website

-- Personal Information
- date_of_birth, is_married, date_of_marriage

-- Location Information
- working_state, domestic_state, district, address, working_area

-- Business Information
- transaction_value, rera_registration_number, working_profiles
- star_rating, source, remarks

-- Company Information
- firm_name, number_of_offices, number_of_branches, total_employee_strength

-- And many more fields...
```

## 🔧 Troubleshooting

### Common Issues and Solutions

#### 1. Build Errors
```bash
# Error: Missing using statements
# Solution: Check all model files have proper using statements

# Error: Syntax errors in migrations
# Solution: Review migration files for proper C# syntax
```

#### 2. Connection Issues
```bash
# Error: Cannot connect to database
# Solutions:
- Verify SQL Server is running
- Check connection string in appsettings.json
- Ensure database permissions are correct
- Try using SQL Server Management Studio to test connection
```

#### 3. Migration Conflicts
```bash
# Error: Migration already exists
# Solution: Remove existing migrations and recreate
rm -rf Migrations
dotnet ef migrations add InitialCreate
dotnet ef database update
```

#### 4. Entity Framework Issues
```bash
# Error: EF tools not found
dotnet tool install --global dotnet-ef

# Error: EF version mismatch
dotnet tool update --global dotnet-ef
```

### Reset Database (Clean Start)
```bash
# Drop existing database
dotnet ef database drop --force

# Remove all migrations
rm -rf Migrations

# Create new migration
dotnet ef migrations add InitialCreate

# Apply migration
dotnet ef database update
```

## 📈 Performance Considerations

### Indexes Created
- **Primary Keys**: All tables have clustered primary key indexes
- **Foreign Keys**: All foreign key columns are indexed
- **Unique Constraints**: Name fields and business keys
- **Search Fields**: Mobile number, email, name fields
- **Composite Indexes**: Common query patterns

### Query Optimization
- **Filtered Indexes**: Unique constraints exclude deleted records
- **Covering Indexes**: Include frequently accessed columns
- **Partition Strategy**: Ready for future partitioning by division

## 🔒 Security Features

### Data Protection
- **Soft Deletes**: Persons are marked as deleted, not physically removed
- **Audit Trail**: All tables include created_at and updated_at timestamps
- **Referential Integrity**: Foreign key constraints prevent orphaned records
- **Unique Constraints**: Prevent duplicate mobile numbers within division/category

### Access Control
- **Admin System**: Built-in admin user management
- **Role-Based**: Ready for role-based access control implementation
- **API Security**: Designed to work with JWT authentication

## 📝 Sample Data Included

### States (36 Records)
- All 28 Indian states
- All 8 union territories
- Includes capitals and regional classification

### Divisions (10 Records)
- Real Estate, Construction, Finance
- Technology, Healthcare, Education
- Retail, Manufacturing, Agriculture, Government

### Categories (15 Records)
- 5 categories under Real Estate
- 5 categories under Construction  
- 5 categories under Finance

### SubCategories (10 Records)
- 5 subcategories under Residential
- 5 subcategories under Commercial

## 🚀 Next Steps After Migration

1. **Start the API Server**
   ```bash
   dotnet run
   ```

2. **Test the Endpoints**
   - Visit `https://localhost:5001/swagger`
   - Test CRUD operations for all entities

3. **Create Sample Persons**
   - Use the API to create test person records
   - Test the import functionality with Excel/CSV files

4. **Frontend Integration**
   - Start the React frontend
   - Test the complete workflow

## 📞 Support

### Getting Help
- Check the console output for detailed error messages
- Review the Entity Framework documentation
- Examine SQL Server logs for database-specific issues

### Common Commands Reference
```bash
# Check EF version
dotnet ef --version

# List migrations
dotnet ef migrations list

# Create migration
dotnet ef migrations add MigrationName

# Update database
dotnet ef database update

# Drop database
dotnet ef database drop

# Generate SQL script
dotnet ef migrations script
```

## 🎯 Success Indicators

After successful migration, you should see:
- ✅ All 5 tables created in the database
- ✅ 36 states inserted
- ✅ 10 divisions inserted
- ✅ 15 categories inserted
- ✅ 10 subcategories inserted
- ✅ All indexes and constraints in place
- ✅ API server starts without errors
- ✅ Swagger UI accessible and functional

The database is now ready for full CRM operations!
