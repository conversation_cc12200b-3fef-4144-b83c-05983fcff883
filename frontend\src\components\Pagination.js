import React from 'react';
import './Pagination.css';

const Pagination = ({
    currentPage,
    totalItems,
    itemsPerPage,
    onPageChange,
    showInfo = true
}) => {
    const totalPages = Math.ceil(totalItems / itemsPerPage);
    const startItem = (currentPage - 1) * itemsPerPage + 1;
    const endItem = Math.min(currentPage * itemsPerPage, totalItems);

    if (totalPages <= 1) return null;

    const getPageNumbers = () => {
        const pages = [];
        const maxVisiblePages = 3; // Reduced for single row

        if (totalPages <= maxVisiblePages) {
            for (let i = 1; i <= totalPages; i++) {
                pages.push(i);
            }
        } else {
            // Simplified logic for single row
            if (currentPage === 1) {
                pages.push(1, 2, '...');
            } else if (currentPage === totalPages) {
                pages.push('...', totalPages - 1, totalPages);
            } else {
                pages.push('...', currentPage, '...');
            }
        }

        return pages;
    };

    return (
        <div className="pagination-wrapper-single">
            <div className="pagination-container">
                {showInfo && (
                    <div className="pagination-info">
                        <span className="entries-text">
                            {startItem}-{endItem} of {totalItems}
                        </span>
                    </div>
                )}

                <div className="pagination-controls">
                    <button
                        className={`pagination-btn pagination-prev ${currentPage === 1 ? 'disabled' : ''}`}
                        onClick={() => onPageChange(currentPage - 1)}
                        disabled={currentPage === 1}
                        title="Previous page"
                    >
                        ‹
                    </button>

                    <div className="pagination-pages">
                        {getPageNumbers().map((page, index) => (
                            <button
                                key={index}
                                className={`pagination-btn pagination-page ${page === currentPage ? 'active' : ''
                                    } ${page === '...' ? 'dots' : ''}`}
                                onClick={() => page !== '...' && onPageChange(page)}
                                disabled={page === '...'}
                                title={page === '...' ? '' : `Go to page ${page}`}
                            >
                                {page}
                            </button>
                        ))}
                    </div>

                    <button
                        className={`pagination-btn pagination-next ${currentPage === totalPages ? 'disabled' : ''}`}
                        onClick={() => onPageChange(currentPage + 1)}
                        disabled={currentPage === totalPages}
                        title="Next page"
                    >
                        ›
                    </button>
                </div>
            </div>
        </div>
    );
};

export default Pagination;