.person-list {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.list-header {
  padding: 1.5rem;
  border-bottom: 1px solid #e1e5e9;
  background-color: #f8f9fa;
}

.header-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.header-title h2 {
  margin: 0;
  color: #495057;
}

.header-actions {
  display: flex;
  gap: 0.5rem;
}

.search-section {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.search-filters {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.filter-group {
  display: flex;
  flex-direction: column;
}

.search-input {
  padding: 0.75rem;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 1rem;
}

.search-input:focus {
  outline: none;
  border-color: #80bdff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.hierarchy-filter {
  margin: 1rem 0;
}

.search-actions {
  display: flex;
  gap: 0.5rem;
  justify-content: flex-end;
}

.results-summary {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e1e5e9;
  font-size: 0.875rem;
  color: #6c757d;
}

.page-size-selector {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.page-size-selector select {
  padding: 0.25rem 0.5rem;
  border: 1px solid #ced4da;
  border-radius: 4px;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
}

.loading-spinner {
  width: 3rem;
  height: 3rem;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.person-table-container {
  overflow-x: auto;
}

.person-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.875rem;
}

.person-table th {
  background-color: #f8f9fa;
  padding: 1rem 0.75rem;
  text-align: left;
  font-weight: 600;
  color: #495057;
  border-bottom: 2px solid #e1e5e9;
  white-space: nowrap;
}

.person-table td {
  padding: 1rem 0.75rem;
  border-bottom: 1px solid #e1e5e9;
  vertical-align: top;
}

.person-table tbody tr:hover {
  background-color: #f8f9fa;
}

.person-name strong {
  color: #495057;
  font-weight: 600;
}

.firm-name {
  font-size: 0.75rem;
  color: #6c757d;
  margin-top: 0.25rem;
}

.contact-info,
.email-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.alternate {
  font-size: 0.75rem;
  color: #6c757d;
  font-style: italic;
}

.subcategory {
  font-size: 0.75rem;
  color: #6c757d;
  margin-top: 0.25rem;
}

.nature-badge {
  padding: 0.25rem 0.5rem;
  border-radius: 3px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.nature-badge.nature-1 { /* Business */
  background-color: #e7f3ff;
  color: #0056b3;
}

.nature-badge.nature-2 { /* Corporate */
  background-color: #f8d7da;
  color: #721c24;
}

.nature-badge.nature-3 { /* Agriculture */
  background-color: #d4edda;
  color: #155724;
}

.nature-badge.nature-4 { /* Individual */
  background-color: #fff3cd;
  color: #856404;
}

.action-buttons {
  display: flex;
  gap: 0.25rem;
}

.btn-action {
  background: none;
  border: none;
  padding: 0.5rem;
  cursor: pointer;
  border-radius: 4px;
  font-size: 1rem;
  transition: background-color 0.2s ease;
}

.btn-action:hover {
  background-color: #e9ecef;
}

.btn-action.edit:hover {
  background-color: #fff3cd;
}

.btn-action.view:hover {
  background-color: #e7f3ff;
}

.no-data {
  text-align: center;
  padding: 3rem;
  color: #6c757d;
  font-style: italic;
}

.link-btn {
  background: none;
  border: none;
  color: #007bff;
  cursor: pointer;
  text-decoration: underline;
}

.link-btn:hover {
  color: #0056b3;
}

.pagination-container {
  display: flex;
  justify-content: center;
  padding: 1.5rem;
  border-top: 1px solid #e1e5e9;
}

.pagination {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.pagination-btn {
  padding: 0.5rem 0.75rem;
  border: 1px solid #ced4da;
  background-color: white;
  color: #495057;
  cursor: pointer;
  border-radius: 4px;
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.pagination-btn:hover:not(:disabled) {
  background-color: #e9ecef;
  border-color: #adb5bd;
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-btn.active {
  background-color: #007bff;
  border-color: #007bff;
  color: white;
}

.pagination-ellipsis {
  padding: 0.5rem 0.25rem;
  color: #6c757d;
}

.alert {
  padding: 1rem 1.25rem;
  margin: 1.5rem;
  border: 1px solid transparent;
  border-radius: 4px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.alert-error {
  color: #721c24;
  background-color: #f8d7da;
  border-color: #f5c6cb;
}

.retry-btn {
  background-color: #dc3545;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.875rem;
}

.retry-btn:hover {
  background-color: #c82333;
}

.btn {
  padding: 0.5rem 1rem;
  border: 1px solid transparent;
  border-radius: 4px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  text-decoration: none;
  display: inline-block;
  transition: all 0.2s ease;
}

.btn-primary {
  background-color: #007bff;
  border-color: #007bff;
  color: white;
}

.btn-primary:hover {
  background-color: #0056b3;
  border-color: #0056b3;
}

.btn-secondary {
  background-color: #6c757d;
  border-color: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background-color: #545b62;
  border-color: #545b62;
}

.btn-outline {
  background-color: transparent;
  border-color: #6c757d;
  color: #6c757d;
}

.btn-outline:hover {
  background-color: #6c757d;
  color: white;
}

/* Responsive design */
@media (max-width: 768px) {
  .list-header {
    padding: 1rem;
  }
  
  .header-title {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  
  .header-actions {
    width: 100%;
    justify-content: stretch;
  }
  
  .header-actions .btn {
    flex: 1;
  }
  
  .search-filters {
    grid-template-columns: 1fr;
  }
  
  .search-actions {
    justify-content: stretch;
  }
  
  .search-actions .btn {
    flex: 1;
  }
  
  .results-summary {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
  
  .person-table {
    font-size: 0.75rem;
  }
  
  .person-table th,
  .person-table td {
    padding: 0.5rem 0.25rem;
  }
  
  .pagination {
    flex-wrap: wrap;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .person-table-container {
    font-size: 0.7rem;
  }
  
  .person-table th:nth-child(n+6),
  .person-table td:nth-child(n+6) {
    display: none;
  }
  
  .action-buttons {
    flex-direction: column;
  }
}
