import React, { useState, useRef } from 'react';
import * as XLSX from 'xlsx';
import apiService from '../../services/apiService';
import './FileUpload.css';

const FileUpload = ({ onFileUpload, importSettings, onSettingsChange, onBack, error }) => {
  const [dragActive, setDragActive] = useState(false);
  const [selectedFile, setSelectedFile] = useState(null);
  const [fileInfo, setFileInfo] = useState(null);
  const [parsing, setParsing] = useState(false);
  const [parseError, setParseError] = useState(null);
  const fileInputRef = useRef(null);

  const handleDrag = (e) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  const handleDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFileSelect(e.dataTransfer.files[0]);
    }
  };

  const handleFileInputChange = (e) => {
    if (e.target.files && e.target.files[0]) {
      handleFileSelect(e.target.files[0]);
    }
  };

  const handleFileSelect = (file) => {
    // Validate file type
    const allowedTypes = [
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
      'application/vnd.ms-excel', // .xls
      'text/csv' // .csv
    ];

    const allowedExtensions = ['.xlsx', '.xls', '.csv'];
    const fileExtension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'));

    if (!allowedTypes.includes(file.type) && !allowedExtensions.includes(fileExtension)) {
      setParseError('Please select a valid Excel (.xlsx, .xls) or CSV file');
      return;
    }

    // Validate file size (10MB limit)
    if (file.size > 10 * 1024 * 1024) {
      setParseError('File size must be less than 10MB');
      return;
    }

    setSelectedFile(file);
    setFileInfo({
      name: file.name,
      size: formatFileSize(file.size),
      type: file.type || 'Unknown',
      lastModified: new Date(file.lastModified).toLocaleDateString()
    });
    setParseError(null);
  };

  const parseFile = async () => {
    if (!selectedFile) return;

    setParsing(true);
    setParseError(null);

    try {
      const headers = await extractHeaders(selectedFile);
      onFileUpload(selectedFile, headers);
    } catch (err) {
      console.error('File parsing error:', err);
      setParseError(err.message || 'Failed to parse file');
    } finally {
      setParsing(false);
    }
  };

  const extractHeaders = (file) => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      
      reader.onload = (e) => {
        try {
          const data = e.target.result;
          let headers = [];

          if (file.name.toLowerCase().endsWith('.csv')) {
            // Parse CSV
            const lines = data.split('\n');
            if (lines.length > 0) {
              headers = lines[0].split(',').map(header => header.trim().replace(/"/g, ''));
            }
          } else {
            // Parse Excel with better error handling
            console.log('Parsing Excel file:', file.name, 'Size:', file.size, 'Type:', file.type);

            try {
              // Check if the data looks like HTML
              if (typeof data === 'string' && (data.includes('<html>') || data.includes('<table>'))) {
                throw new Error('HTML file detected. Please upload a proper Excel (.xlsx, .xls) file.');
              }

              const workbook = XLSX.read(data, { type: 'binary' });
              console.log('Workbook loaded successfully');

              if (!workbook.SheetNames || workbook.SheetNames.length === 0) {
                throw new Error('No sheets found in the Excel file. The file might be corrupted.');
              }

              console.log('Available sheets:', workbook.SheetNames);

              const firstSheetName = workbook.SheetNames[0];
              const worksheet = workbook.Sheets[firstSheetName];

              if (!worksheet) {
                throw new Error(`Could not read the sheet "${firstSheetName}".`);
              }

              const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
              console.log('Extracted', jsonData.length, 'rows from Excel');

              if (jsonData.length > 0) {
                headers = jsonData[0].map(header => String(header || '').trim());
                console.log('Headers found:', headers);
              } else {
                throw new Error('The Excel file appears to be empty.');
              }
            } catch (xlsxError) {
              console.error('Excel parsing error:', xlsxError);

              if (xlsxError.message && xlsxError.message.toLowerCase().includes('table')) {
                throw new Error('Invalid file format. The file contains HTML content instead of Excel data. Please upload a proper .xlsx or .xls file.');
              }

              if (xlsxError.message && xlsxError.message.includes('ZIP')) {
                throw new Error('The Excel file is corrupted or not a valid .xlsx file. Please try re-saving the file in Excel.');
              }

              throw new Error(`Excel parsing failed: ${xlsxError.message}`);
            }
          }

          if (headers.length === 0) {
            reject(new Error('No headers found in file'));
            return;
          }

          resolve(headers.filter(header => header !== ''));
        } catch (err) {
          reject(new Error('Failed to parse file: ' + err.message));
        }
      };

      reader.onerror = () => {
        reject(new Error('Failed to read file'));
      };

      if (file.name.toLowerCase().endsWith('.csv')) {
        reader.readAsText(file);
      } else {
        reader.readAsBinaryString(file);
      }
    });
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const downloadTemplate = async () => {
    try {
      const response = await fetch(`${apiService.baseURL}/import-export/persons/template?format=Excel&includeSampleData=true`);
      
      if (!response.ok) {
        throw new Error('Failed to download template');
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'person_import_template.xlsx';
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (err) {
      console.error('Template download error:', err);
      setParseError('Failed to download template');
    }
  };

  return (
    <div className="file-upload">
      <div className="upload-section">
        <h3>Upload File</h3>
        
        {/* Template Download */}
        <div className="template-section">
          <p>Don't have a file? Download our template to get started:</p>
          <button onClick={downloadTemplate} className="btn btn-outline">
            📥 Download Template
          </button>
        </div>

        {/* File Drop Zone */}
        <div
          className={`drop-zone ${dragActive ? 'active' : ''} ${selectedFile ? 'has-file' : ''}`}
          onDragEnter={handleDrag}
          onDragLeave={handleDrag}
          onDragOver={handleDrag}
          onDrop={handleDrop}
          onClick={() => fileInputRef.current?.click()}
        >
          <input
            ref={fileInputRef}
            type="file"
            accept=".xlsx,.xls,.csv"
            onChange={handleFileInputChange}
            style={{ display: 'none' }}
          />
          
          {selectedFile ? (
            <div className="file-selected">
              <div className="file-icon">📄</div>
              <div className="file-details">
                <div className="file-name">{fileInfo.name}</div>
                <div className="file-meta">
                  <span>{fileInfo.size}</span>
                  <span>•</span>
                  <span>{fileInfo.lastModified}</span>
                </div>
              </div>
              <button 
                onClick={(e) => {
                  e.stopPropagation();
                  setSelectedFile(null);
                  setFileInfo(null);
                }}
                className="remove-file"
              >
                ✕
              </button>
            </div>
          ) : (
            <div className="drop-content">
              <div className="drop-icon">📁</div>
              <div className="drop-text">
                <p><strong>Click to upload</strong> or drag and drop</p>
                <p>Excel (.xlsx, .xls) or CSV files only</p>
                <p>Maximum file size: 10MB</p>
              </div>
            </div>
          )}
        </div>

        {parseError && (
          <div className="error-message">
            <span className="error-icon">⚠️</span>
            {parseError}
          </div>
        )}
      </div>

      {/* Import Settings */}
      <div className="settings-section">
        <h3>Import Settings</h3>
        
        <div className="setting-group">
          <label>Import Mode:</label>
          <select
            value={importSettings.importMode}
            onChange={(e) => onSettingsChange('importMode', e.target.value)}
          >
            <option value="SkipDuplicates">Skip Duplicates</option>
            <option value="UpdateExisting">Update Existing</option>
            <option value="FailOnDuplicates">Fail on Duplicates</option>
          </select>
          <div className="setting-help">
            {importSettings.importMode === 'SkipDuplicates' && 
              'Skip records that already exist (based on mobile number + division + category)'}
            {importSettings.importMode === 'UpdateExisting' && 
              'Update existing records with new data'}
            {importSettings.importMode === 'FailOnDuplicates' && 
              'Stop import if duplicates are found'}
          </div>
        </div>

        <div className="setting-group">
          <label>
            <input
              type="checkbox"
              checked={importSettings.validateOnly}
              onChange={(e) => onSettingsChange('validateOnly', e.target.checked)}
            />
            Validate Only (don't import data)
          </label>
          <div className="setting-help">
            Check this to validate the file without actually importing the data
          </div>
        </div>

        <div className="setting-group">
          <label>Batch Size:</label>
          <select
            value={importSettings.batchSize}
            onChange={(e) => onSettingsChange('batchSize', parseInt(e.target.value))}
          >
            <option value={50}>50 records per batch</option>
            <option value={100}>100 records per batch</option>
            <option value={200}>200 records per batch</option>
            <option value={500}>500 records per batch</option>
          </select>
          <div className="setting-help">
            Smaller batches are slower but more reliable for large files
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="upload-actions">
        {onBack && (
          <button
            onClick={onBack}
            disabled={parsing}
            className="btn btn-secondary"
          >
            ← Back to Division & Category
          </button>
        )}
        <button
          onClick={parseFile}
          disabled={!selectedFile || parsing}
          className="btn btn-primary"
        >
          {parsing ? 'Processing...' : 'Continue to Field Mapping'}
        </button>
      </div>
    </div>
  );
};

export default FileUpload;
