import React, { useState } from 'react';
import { getDefaultPersonData } from '../../constants/personConstants';
import FormField from './FormField';
import './FormPreview.css';

const FormPreview = ({ fields, formName, onClose }) => {
  const [previewData, setPreviewData] = useState(getDefaultPersonData());

  const handleFieldChange = (fieldKey, value) => {
    setPreviewData(prev => ({
      ...prev,
      [fieldKey]: value
    }));
  };

  const shouldShowField = (field) => {
    if (!field.conditional) return true;
    
    const conditionValue = previewData[field.conditional.field];
    const expectedValue = field.conditional.value;
    
    // Handle boolean conditions
    if (typeof expectedValue === 'boolean' || expectedValue === 'true' || expectedValue === 'false') {
      return conditionValue === (expectedValue === true || expectedValue === 'true');
    }
    
    return conditionValue === expectedValue;
  };

  const groupFieldsBySections = () => {
    const sections = {};
    fields.forEach(field => {
      const sectionKey = field.section || 'general';
      if (!sections[sectionKey]) {
        sections[sectionKey] = {
          title: getSectionTitle(sectionKey),
          fields: []
        };
      }
      sections[sectionKey].fields.push(field);
    });
    return sections;
  };

  const getSectionTitle = (sectionKey) => {
    const titles = {
      personalInfo: 'Personal Information',
      contactInfo: 'Contact Information',
      locationInfo: 'Location Information',
      businessInfo: 'Business Information',
      associateInfo: 'Associate Information',
      digitalPresence: 'Digital Presence',
      companyInfo: 'Company Information',
      authorizedPerson: 'Authorized Person',
      marketingInfo: 'Marketing Information',
      general: 'General Information'
    };
    return titles[sectionKey] || sectionKey;
  };

  const sections = groupFieldsBySections();
  const visibleFields = fields.filter(field => shouldShowField(field));
  const totalFields = fields.length;
  const requiredFields = fields.filter(field => field.required).length;

  return (
    <div className="form-preview-overlay">
      <div className="form-preview-modal">
        <div className="preview-header">
          <div className="preview-title">
            <h2>Form Preview</h2>
            <div className="preview-info">
              <span className="form-name">{formName}</span>
              <div className="field-stats">
                <span className="stat">Total Fields: {totalFields}</span>
                <span className="stat">Required: {requiredFields}</span>
                <span className="stat">Visible: {visibleFields.length}</span>
              </div>
            </div>
          </div>
          <button onClick={onClose} className="close-button">×</button>
        </div>

        <div className="preview-content">
          <div className="preview-notice">
            <p>
              <strong>Preview Mode:</strong> This is how the form will appear to users. 
              You can interact with fields to test conditional logic and validation.
            </p>
          </div>

          <div className="preview-form">
            {Object.entries(sections).map(([sectionKey, section]) => {
              const sectionFields = section.fields.filter(field => shouldShowField(field));
              
              if (sectionFields.length === 0) return null;

              return (
                <div key={sectionKey} className="preview-section">
                  <h3>{section.title}</h3>
                  <div className="preview-fields">
                    {sectionFields.map(field => (
                      <FormField
                        key={field.key}
                        field={field}
                        value={previewData[field.key]}
                        onChange={(value) => handleFieldChange(field.key, value)}
                      />
                    ))}
                  </div>
                </div>
              );
            })}
          </div>

          {/* Field Summary */}
          <div className="field-summary">
            <h4>Field Summary</h4>
            <div className="summary-grid">
              <div className="summary-item">
                <span className="summary-label">Total Fields:</span>
                <span className="summary-value">{totalFields}</span>
              </div>
              <div className="summary-item">
                <span className="summary-label">Required Fields:</span>
                <span className="summary-value">{requiredFields}</span>
              </div>
              <div className="summary-item">
                <span className="summary-label">Optional Fields:</span>
                <span className="summary-value">{totalFields - requiredFields}</span>
              </div>
              <div className="summary-item">
                <span className="summary-label">Currently Visible:</span>
                <span className="summary-value">{visibleFields.length}</span>
              </div>
              <div className="summary-item">
                <span className="summary-label">Conditional Fields:</span>
                <span className="summary-value">
                  {fields.filter(field => field.conditional).length}
                </span>
              </div>
              <div className="summary-item">
                <span className="summary-label">Sections:</span>
                <span className="summary-value">{Object.keys(sections).length}</span>
              </div>
            </div>
          </div>

          {/* Field List */}
          <div className="field-list">
            <h4>All Fields</h4>
            <div className="field-items">
              {fields.map(field => (
                <div 
                  key={field.key} 
                  className={`field-item ${shouldShowField(field) ? 'visible' : 'hidden'}`}
                >
                  <div className="field-name">{field.label}</div>
                  <div className="field-meta">
                    <span className="field-type">{field.type}</span>
                    {field.required && <span className="required-badge">Required</span>}
                    {field.conditional && <span className="conditional-badge">Conditional</span>}
                    {!shouldShowField(field) && <span className="hidden-badge">Hidden</span>}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        <div className="preview-footer">
          <button onClick={onClose} className="btn btn-primary">
            Close Preview
          </button>
        </div>
      </div>
    </div>
  );
};

export default FormPreview;
