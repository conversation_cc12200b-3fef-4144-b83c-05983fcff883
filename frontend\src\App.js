import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { AuthProvider } from './context/AuthContext';
import ProtectedRoute from './components/ProtectedRoute';
import Navbar from './components/Navbar';
import Dashboard from './components/Dashboard';
import DivisionSetup from './components/DivisionSetup';
import CategoryManagement from './components/CategoryManagement';
import PersonManagement from './components/PersonManagement';
import PersonsView from './components/PersonsView';
import ImportPersons from './components/import/ImportPersons';
import FormBuilder from './components/forms/FormBuilder';


function App() {
  return (
    <AuthProvider>
      <Router>
        <div className="App">
          <ProtectedRoute>
            <Navbar />
            <div className="main-content">
              <Routes>
                <Route path="/" element={<Dashboard />} />
                <Route path="/dashboard" element={<Dashboard />} />
                <Route path="/divisions" element={<DivisionSetup />} />
                <Route path="/categories" element={<CategoryManagement />} />
                <Route path="/persons-view" element={<PersonsView />} />
                <Route path="/persons" element={<PersonManagement />} />
                <Route path="/import" element={<ImportPersons />} />
                <Route path="/form-builder" element={<FormBuilder />} />
              </Routes>
            </div>
          </ProtectedRoute>
          <ToastContainer
            position="top-right"
            autoClose={3000}
            hideProgressBar={false}
            newestOnTop={false}
            closeOnClick
            rtl={false}
            pauseOnFocusLoss
            draggable
            pauseOnHover
            theme="light"
          />
        </div>
      </Router>
    </AuthProvider>
  );
}

export default App;