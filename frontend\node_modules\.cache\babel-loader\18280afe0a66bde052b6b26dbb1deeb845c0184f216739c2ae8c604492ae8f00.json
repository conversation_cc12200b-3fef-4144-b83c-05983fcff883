{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Advanced\\\\Advanced CRM filed all use\\\\frontend\\\\src\\\\context\\\\AuthContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useEffect } from 'react';\nimport axios from 'axios';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthContext = /*#__PURE__*/createContext();\nexport const useAuth = () => {\n  _s();\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n_s(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const AuthProvider = ({\n  children\n}) => {\n  _s2();\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [token, setToken] = useState(localStorage.getItem('token'));\n  useEffect(() => {\n    if (token) {\n      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n      validateToken();\n    } else {\n      setLoading(false);\n    }\n  }, [token]);\n  const validateToken = async () => {\n    try {\n      await axios.post('http://localhost:5000/api/auth/validate');\n      const userData = JSON.parse(localStorage.getItem('user') || '{}');\n      setUser(userData);\n    } catch (error) {\n      logout();\n    } finally {\n      setLoading(false);\n    }\n  };\n  const login = async (username, password) => {\n    try {\n      const response = await axios.post('http://localhost:5000/api/auth/login', {\n        username,\n        password\n      });\n      const {\n        token,\n        ...userData\n      } = response.data;\n      localStorage.setItem('token', token);\n      localStorage.setItem('user', JSON.stringify(userData));\n      setToken(token);\n      setUser(userData);\n      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n      return {\n        success: true\n      };\n    } catch (error) {\n      var _error$response, _error$response$data;\n      return {\n        success: false,\n        message: ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || 'Login failed'\n      };\n    }\n  };\n  const logout = () => {\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n    setToken(null);\n    setUser(null);\n    delete axios.defaults.headers.common['Authorization'];\n  };\n  const value = {\n    user,\n    login,\n    logout,\n    loading,\n    isAuthenticated: !!user\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 83,\n    columnNumber: 5\n  }, this);\n};\n_s2(AuthProvider, \"/pbUqy0QsBvMqKPYubk3+KKKH8I=\");\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "axios", "jsxDEV", "_jsxDEV", "AuthContext", "useAuth", "_s", "context", "Error", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s2", "user", "setUser", "loading", "setLoading", "token", "setToken", "localStorage", "getItem", "defaults", "headers", "common", "validateToken", "post", "userData", "JSON", "parse", "error", "logout", "login", "username", "password", "response", "data", "setItem", "stringify", "success", "_error$response", "_error$response$data", "message", "removeItem", "value", "isAuthenticated", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/Advanced/Advanced CRM filed all use/frontend/src/context/AuthContext.js"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect } from 'react';\r\nimport axios from 'axios';\r\n\r\nconst AuthContext = createContext();\r\n\r\nexport const useAuth = () => {\r\n  const context = useContext(AuthContext);\r\n  if (!context) {\r\n    throw new Error('useAuth must be used within an AuthProvider');\r\n  }\r\n  return context;\r\n};\r\n\r\nexport const AuthProvider = ({ children }) => {\r\n  const [user, setUser] = useState(null);\r\n  const [loading, setLoading] = useState(true);\r\n  const [token, setToken] = useState(localStorage.getItem('token'));\r\n\r\n  useEffect(() => {\r\n    if (token) {\r\n      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;\r\n      validateToken();\r\n    } else {\r\n      setLoading(false);\r\n    }\r\n  }, [token]);\r\n\r\n  const validateToken = async () => {\r\n    try {\r\n      await axios.post('http://localhost:5000/api/auth/validate');\r\n      const userData = JSON.parse(localStorage.getItem('user') || '{}');\r\n      setUser(userData);\r\n    } catch (error) {\r\n      logout();\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const login = async (username, password) => {\r\n    try {\r\n      const response = await axios.post('http://localhost:5000/api/auth/login', {\r\n        username,\r\n        password\r\n      });\r\n\r\n      const { token, ...userData } = response.data;\r\n      \r\n      localStorage.setItem('token', token);\r\n      localStorage.setItem('user', JSON.stringify(userData));\r\n      \r\n      setToken(token);\r\n      setUser(userData);\r\n      \r\n      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;\r\n      \r\n      return { success: true };\r\n    } catch (error) {\r\n      return { \r\n        success: false, \r\n        message: error.response?.data?.message || 'Login failed' \r\n      };\r\n    }\r\n  };\r\n\r\n  const logout = () => {\r\n    localStorage.removeItem('token');\r\n    localStorage.removeItem('user');\r\n    setToken(null);\r\n    setUser(null);\r\n    delete axios.defaults.headers.common['Authorization'];\r\n  };\r\n\r\n  const value = {\r\n    user,\r\n    login,\r\n    logout,\r\n    loading,\r\n    isAuthenticated: !!user\r\n  };\r\n\r\n  return (\r\n    <AuthContext.Provider value={value}>\r\n      {children}\r\n    </AuthContext.Provider>\r\n  );\r\n};"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC7E,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,WAAW,gBAAGP,aAAa,CAAC,CAAC;AAEnC,OAAO,MAAMQ,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAMC,OAAO,GAAGT,UAAU,CAACM,WAAW,CAAC;EACvC,IAAI,CAACG,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,6CAA6C,CAAC;EAChE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,EAAA,CANWD,OAAO;AAQpB,OAAO,MAAMI,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EAC5C,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACe,OAAO,EAAEC,UAAU,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACiB,KAAK,EAAEC,QAAQ,CAAC,GAAGlB,QAAQ,CAACmB,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;EAEjEnB,SAAS,CAAC,MAAM;IACd,IAAIgB,KAAK,EAAE;MACTf,KAAK,CAACmB,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC,GAAG,UAAUN,KAAK,EAAE;MAClEO,aAAa,CAAC,CAAC;IACjB,CAAC,MAAM;MACLR,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,CAACC,KAAK,CAAC,CAAC;EAEX,MAAMO,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF,MAAMtB,KAAK,CAACuB,IAAI,CAAC,yCAAyC,CAAC;MAC3D,MAAMC,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACT,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC;MACjEN,OAAO,CAACY,QAAQ,CAAC;IACnB,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdC,MAAM,CAAC,CAAC;IACV,CAAC,SAAS;MACRd,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMe,KAAK,GAAG,MAAAA,CAAOC,QAAQ,EAAEC,QAAQ,KAAK;IAC1C,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMhC,KAAK,CAACuB,IAAI,CAAC,sCAAsC,EAAE;QACxEO,QAAQ;QACRC;MACF,CAAC,CAAC;MAEF,MAAM;QAAEhB,KAAK;QAAE,GAAGS;MAAS,CAAC,GAAGQ,QAAQ,CAACC,IAAI;MAE5ChB,YAAY,CAACiB,OAAO,CAAC,OAAO,EAAEnB,KAAK,CAAC;MACpCE,YAAY,CAACiB,OAAO,CAAC,MAAM,EAAET,IAAI,CAACU,SAAS,CAACX,QAAQ,CAAC,CAAC;MAEtDR,QAAQ,CAACD,KAAK,CAAC;MACfH,OAAO,CAACY,QAAQ,CAAC;MAEjBxB,KAAK,CAACmB,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC,GAAG,UAAUN,KAAK,EAAE;MAElE,OAAO;QAAEqB,OAAO,EAAE;MAAK,CAAC;IAC1B,CAAC,CAAC,OAAOT,KAAK,EAAE;MAAA,IAAAU,eAAA,EAAAC,oBAAA;MACd,OAAO;QACLF,OAAO,EAAE,KAAK;QACdG,OAAO,EAAE,EAAAF,eAAA,GAAAV,KAAK,CAACK,QAAQ,cAAAK,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBJ,IAAI,cAAAK,oBAAA,uBAApBA,oBAAA,CAAsBC,OAAO,KAAI;MAC5C,CAAC;IACH;EACF,CAAC;EAED,MAAMX,MAAM,GAAGA,CAAA,KAAM;IACnBX,YAAY,CAACuB,UAAU,CAAC,OAAO,CAAC;IAChCvB,YAAY,CAACuB,UAAU,CAAC,MAAM,CAAC;IAC/BxB,QAAQ,CAAC,IAAI,CAAC;IACdJ,OAAO,CAAC,IAAI,CAAC;IACb,OAAOZ,KAAK,CAACmB,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC;EACvD,CAAC;EAED,MAAMoB,KAAK,GAAG;IACZ9B,IAAI;IACJkB,KAAK;IACLD,MAAM;IACNf,OAAO;IACP6B,eAAe,EAAE,CAAC,CAAC/B;EACrB,CAAC;EAED,oBACET,OAAA,CAACC,WAAW,CAACwC,QAAQ;IAACF,KAAK,EAAEA,KAAM;IAAAhC,QAAA,EAChCA;EAAQ;IAAAmC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;AAACrC,GAAA,CAzEWF,YAAY;AAAAwC,EAAA,GAAZxC,YAAY;AAAA,IAAAwC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}