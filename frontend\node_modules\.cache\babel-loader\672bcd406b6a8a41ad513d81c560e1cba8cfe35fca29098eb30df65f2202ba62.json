{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Advanced\\\\Advanced CRM filed all use\\\\frontend\\\\src\\\\components\\\\forms\\\\DynamicPersonForm.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { PersonNatureLabels, GenderLabels, WorkingProfileLabels, getDefaultPersonData } from '../../constants/personConstants';\nimport formConfigService from '../../services/formConfigService';\nimport apiService from '../../services/apiService';\nimport FormField from './FormField';\nimport './DynamicPersonForm.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DynamicPersonForm = ({\n  onSubmit,\n  onCancel,\n  initialData = null,\n  mode = 'create'\n}) => {\n  _s();\n  // Hierarchy state\n  const [divisions, setDivisions] = useState([]);\n  const [categories, setCategories] = useState([]);\n  const [subCategories, setSubCategories] = useState([]);\n\n  // Selection state\n  const [selectedDivision, setSelectedDivision] = useState('');\n  const [selectedCategory, setSelectedCategory] = useState('');\n  const [selectedSubCategory, setSelectedSubCategory] = useState('');\n\n  // Form state\n  const [formConfig, setFormConfig] = useState(null);\n  const [formData, setFormData] = useState(getDefaultPersonData());\n  const [errors, setErrors] = useState({});\n\n  // Loading states\n  const [loading, setLoading] = useState({\n    divisions: false,\n    categories: false,\n    subCategories: false,\n    form: false\n  });\n  const [submitting, setSubmitting] = useState(false);\n\n  // Form availability state\n  const [formAvailability, setFormAvailability] = useState({\n    categoryHasForm: false,\n    subCategoryHasForm: false,\n    showSubCategoryDropdown: false,\n    message: ''\n  });\n\n  // Load divisions on component mount\n  useEffect(() => {\n    loadDivisions();\n\n    // Load initial data if provided\n    if (initialData) {\n      var _initialData$division, _initialData$category, _initialData$subCateg;\n      setFormData(initialData);\n      setSelectedDivision(((_initialData$division = initialData.divisionId) === null || _initialData$division === void 0 ? void 0 : _initialData$division.toString()) || '');\n      setSelectedCategory(((_initialData$category = initialData.categoryId) === null || _initialData$category === void 0 ? void 0 : _initialData$category.toString()) || '');\n      setSelectedSubCategory(((_initialData$subCateg = initialData.subCategoryId) === null || _initialData$subCateg === void 0 ? void 0 : _initialData$subCateg.toString()) || '');\n    } else {\n      // Load default form for new person creation\n      const defaultForm = formConfigService.getDefaultFormConfig();\n      setFormConfig(defaultForm);\n    }\n  }, [initialData]);\n\n  // Load categories when division changes\n  useEffect(() => {\n    if (selectedDivision) {\n      loadCategories(selectedDivision);\n      // Reset category and subcategory when division changes\n      setSelectedCategory('');\n      setSelectedSubCategory('');\n      setSubCategories([]);\n      setFormConfig(null);\n      setFormAvailability({\n        categoryHasForm: false,\n        subCategoryHasForm: false,\n        showSubCategoryDropdown: false,\n        message: ''\n      });\n    } else {\n      setCategories([]);\n      setSubCategories([]);\n      setSelectedCategory('');\n      setSelectedSubCategory('');\n      setFormConfig(null);\n    }\n  }, [selectedDivision]);\n\n  // Handle category selection and form loading\n  useEffect(() => {\n    if (selectedCategory) {\n      handleCategorySelection(selectedCategory);\n      // Reset subcategory when category changes\n      setSelectedSubCategory('');\n    } else {\n      setSubCategories([]);\n      setSelectedSubCategory('');\n      setFormConfig(null);\n      setFormAvailability({\n        categoryHasForm: false,\n        subCategoryHasForm: false,\n        showSubCategoryDropdown: false,\n        message: ''\n      });\n    }\n  }, [selectedCategory]);\n\n  // Handle subcategory selection and form loading\n  useEffect(() => {\n    if (selectedSubCategory) {\n      handleSubCategorySelection(selectedSubCategory);\n    }\n  }, [selectedSubCategory]);\n\n  // Load divisions from API\n  const loadDivisions = async () => {\n    setLoading(prev => ({\n      ...prev,\n      divisions: true\n    }));\n    try {\n      const response = await apiService.getDivisions();\n      setDivisions(response.data || []);\n    } catch (error) {\n      console.error('Error loading divisions:', error);\n      setErrors(prev => ({\n        ...prev,\n        divisions: 'Failed to load divisions'\n      }));\n    } finally {\n      setLoading(prev => ({\n        ...prev,\n        divisions: false\n      }));\n    }\n  };\n\n  // Load categories by division\n  const loadCategories = async divisionId => {\n    setLoading(prev => ({\n      ...prev,\n      categories: true\n    }));\n    try {\n      const response = await apiService.getCategoriesByDivision(divisionId);\n      setCategories(response.data || []);\n    } catch (error) {\n      console.error('Error loading categories:', error);\n      setErrors(prev => ({\n        ...prev,\n        categories: 'Failed to load categories'\n      }));\n      setCategories([]);\n    } finally {\n      setLoading(prev => ({\n        ...prev,\n        categories: false\n      }));\n    }\n  };\n\n  // Load subcategories by category\n  const loadSubCategories = async categoryId => {\n    setLoading(prev => ({\n      ...prev,\n      subCategories: true\n    }));\n    try {\n      const response = await apiService.getSubCategoriesByCategory(categoryId);\n      setSubCategories(response.data || []);\n    } catch (error) {\n      console.error('Error loading subcategories:', error);\n      setErrors(prev => ({\n        ...prev,\n        subCategories: 'Failed to load subcategories'\n      }));\n      setSubCategories([]);\n    } finally {\n      setLoading(prev => ({\n        ...prev,\n        subCategories: false\n      }));\n    }\n  };\n\n  // Handle category selection and form loading logic\n  const handleCategorySelection = async categoryId => {\n    setLoading(prev => ({\n      ...prev,\n      form: true\n    }));\n    try {\n      // Check if category has a form\n      const categoryHasForm = formConfigService.hasFormForCategory(parseInt(categoryId));\n      if (categoryHasForm) {\n        // Load category form and hide subcategory dropdown\n        const categoryForm = formConfigService.loadFormConfig('category', parseInt(categoryId));\n        setFormConfig(categoryForm);\n        setFormAvailability({\n          categoryHasForm: true,\n          subCategoryHasForm: false,\n          showSubCategoryDropdown: false,\n          message: 'Using category-specific form'\n        });\n      } else {\n        // No category form, show subcategory dropdown and load subcategories\n        await loadSubCategories(categoryId);\n        // Load default form as fallback\n        const defaultForm = formConfigService.getDefaultFormConfig();\n        setFormConfig(defaultForm);\n        setFormAvailability({\n          categoryHasForm: false,\n          subCategoryHasForm: false,\n          showSubCategoryDropdown: true,\n          message: 'Using default form. You can select a subcategory if available.'\n        });\n      }\n    } catch (error) {\n      console.error('Error handling category selection:', error);\n      setErrors(prev => ({\n        ...prev,\n        form: 'Error loading form configuration'\n      }));\n    } finally {\n      setLoading(prev => ({\n        ...prev,\n        form: false\n      }));\n    }\n  };\n\n  // Handle subcategory selection and form loading logic\n  const handleSubCategorySelection = async subCategoryId => {\n    setLoading(prev => ({\n      ...prev,\n      form: true\n    }));\n    try {\n      // Check if subcategory has a form\n      const subCategoryHasForm = formConfigService.hasFormForSubCategory(parseInt(subCategoryId));\n      if (subCategoryHasForm) {\n        // Load subcategory form\n        const subCategoryForm = formConfigService.loadFormConfig('subcategory', parseInt(subCategoryId));\n        setFormConfig(subCategoryForm);\n        setFormAvailability({\n          categoryHasForm: false,\n          subCategoryHasForm: true,\n          showSubCategoryDropdown: true,\n          message: 'Using subcategory-specific form'\n        });\n      } else {\n        // No subcategory form available, use default form\n        const defaultForm = formConfigService.getDefaultFormConfig();\n        setFormConfig(defaultForm);\n        setFormAvailability({\n          categoryHasForm: false,\n          subCategoryHasForm: false,\n          showSubCategoryDropdown: true,\n          message: 'No specific form found for this subcategory. Using default form.'\n        });\n      }\n    } catch (error) {\n      console.error('Error handling subcategory selection:', error);\n      setErrors(prev => ({\n        ...prev,\n        form: 'Error loading form configuration'\n      }));\n    } finally {\n      setLoading(prev => ({\n        ...prev,\n        form: false\n      }));\n    }\n  };\n\n  // Handle dropdown changes\n  const handleDivisionChange = e => {\n    const value = e.target.value;\n    setSelectedDivision(value);\n\n    // Update form data\n    setFormData(prev => ({\n      ...prev,\n      divisionId: value ? parseInt(value) : null,\n      categoryId: null,\n      subCategoryId: null\n    }));\n  };\n  const handleCategoryChange = e => {\n    const value = e.target.value;\n    setSelectedCategory(value);\n\n    // Update form data\n    setFormData(prev => ({\n      ...prev,\n      categoryId: value ? parseInt(value) : null,\n      subCategoryId: null\n    }));\n  };\n  const handleSubCategoryChange = e => {\n    const value = e.target.value;\n    setSelectedSubCategory(value);\n\n    // Update form data\n    setFormData(prev => ({\n      ...prev,\n      subCategoryId: value ? parseInt(value) : null\n    }));\n  };\n  const handleFieldChange = (fieldKey, value) => {\n    setFormData(prev => ({\n      ...prev,\n      [fieldKey]: value\n    }));\n\n    // Clear field error when user starts typing\n    if (errors[fieldKey]) {\n      setErrors(prev => ({\n        ...prev,\n        [fieldKey]: null\n      }));\n    }\n  };\n  const validateForm = () => {\n    const newErrors = {};\n\n    // Validate hierarchy selection\n    if (!selectedDivision) {\n      newErrors.division = 'Division is required';\n    }\n    if (!selectedCategory) {\n      newErrors.category = 'Category is required';\n    }\n\n    // Check if form is available (should always have default form as fallback)\n    if (!formConfig) {\n      newErrors.form = 'Form configuration not loaded. Please try again.';\n      return {\n        isValid: false,\n        errors: newErrors\n      };\n    }\n    console.log('Validating form with config:', formConfig);\n    console.log('Current form data:', formData);\n\n    // Validate form fields\n    formConfig.fields.forEach(field => {\n      const value = formData[field.key];\n\n      // Required field validation\n      if (field.required && (!value || typeof value === 'string' && value.trim() === '')) {\n        newErrors[field.key] = `${field.label} is required`;\n        return;\n      }\n\n      // Conditional field validation\n      if (field.conditional && shouldShowField(field)) {\n        if (field.required && (!value || typeof value === 'string' && value.trim() === '')) {\n          newErrors[field.key] = `${field.label} is required`;\n          return;\n        }\n      }\n\n      // Type-specific validation\n      if (value && typeof value === 'string' && value.trim() !== '') {\n        var _field$validation, _field$validation2, _field$validation3;\n        switch (field.type) {\n          case 'email':\n            const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n            if (!emailRegex.test(value)) {\n              newErrors[field.key] = 'Please enter a valid email address';\n            }\n            break;\n          case 'tel':\n            const phoneRegex = /^(\\+91[\\-\\s]?)?[0]?(91)?[789]\\d{9}$/;\n            if (!phoneRegex.test(value)) {\n              newErrors[field.key] = 'Please enter a valid mobile number';\n            }\n            break;\n          case 'url':\n            try {\n              new URL(value);\n            } catch {\n              newErrors[field.key] = 'Please enter a valid URL';\n            }\n            break;\n          case 'number':\n            if (field.validation) {\n              const numValue = parseFloat(value);\n              if (field.validation.min !== undefined && numValue < field.validation.min) {\n                newErrors[field.key] = `Value must be at least ${field.validation.min}`;\n              }\n              if (field.validation.max !== undefined && numValue > field.validation.max) {\n                newErrors[field.key] = `Value must be at most ${field.validation.max}`;\n              }\n            }\n            break;\n        }\n\n        // Pattern validation\n        if ((_field$validation = field.validation) !== null && _field$validation !== void 0 && _field$validation.pattern) {\n          const regex = new RegExp(field.validation.pattern);\n          if (!regex.test(value)) {\n            newErrors[field.key] = `${field.label} format is invalid`;\n          }\n        }\n\n        // Length validation\n        if ((_field$validation2 = field.validation) !== null && _field$validation2 !== void 0 && _field$validation2.minLength && value.length < field.validation.minLength) {\n          newErrors[field.key] = `${field.label} must be at least ${field.validation.minLength} characters`;\n        }\n        if ((_field$validation3 = field.validation) !== null && _field$validation3 !== void 0 && _field$validation3.maxLength && value.length > field.validation.maxLength) {\n          newErrors[field.key] = `${field.label} must be at most ${field.validation.maxLength} characters`;\n        }\n      }\n    });\n\n    // Business logic validation\n    if (formData.isMarried && formData.dateOfMarriage && formData.dateOfBirth) {\n      const birthDate = new Date(formData.dateOfBirth);\n      const marriageDate = new Date(formData.dateOfMarriage);\n      if (marriageDate <= birthDate) {\n        newErrors.dateOfMarriage = 'Marriage date must be after birth date';\n      }\n    }\n    return {\n      isValid: Object.keys(newErrors).length === 0,\n      errors: newErrors\n    };\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    const validation = validateForm();\n    if (!validation.isValid) {\n      console.log('Validation failed:', validation.errors);\n      setErrors(validation.errors);\n      return;\n    }\n    setSubmitting(true);\n    try {\n      // Prepare data for API\n      const submitData = {\n        ...formData,\n        // Ensure hierarchy IDs are included\n        divisionId: parseInt(selectedDivision),\n        categoryId: parseInt(selectedCategory),\n        subCategoryId: selectedSubCategory ? parseInt(selectedSubCategory) : null,\n        // Ensure required fields are not null/undefined\n        name: formData.name || '',\n        mobileNumber: formData.mobileNumber || '',\n        nature: formData.nature || null,\n        // Convert arrays to proper format\n        alternateNumbers: Array.isArray(formData.alternateNumbers) ? formData.alternateNumbers : formData.alternateNumbers ? formData.alternateNumbers.split(',').map(s => s.trim()) : [],\n        alternateEmailIds: Array.isArray(formData.alternateEmailIds) ? formData.alternateEmailIds : formData.alternateEmailIds ? formData.alternateEmailIds.split(',').map(s => s.trim()) : [],\n        workingProfiles: Array.isArray(formData.workingProfiles) ? formData.workingProfiles : [],\n        // Ensure other fields have default values\n        primaryEmailId: formData.primaryEmailId || '',\n        website: formData.website || '',\n        workingState: formData.workingState || '',\n        domesticState: formData.domesticState || '',\n        district: formData.district || '',\n        address: formData.address || '',\n        workingArea: formData.workingArea || '',\n        reraRegistrationNumber: formData.reraRegistrationNumber || '',\n        source: formData.source || '',\n        remarks: formData.remarks || '',\n        firmName: formData.firmName || '',\n        authorizedPersonName: formData.authorizedPersonName || '',\n        authorizedPersonEmail: formData.authorizedPersonEmail || '',\n        designation: formData.designation || '',\n        marketingContact: formData.marketingContact || '',\n        marketingDesignation: formData.marketingDesignation || '',\n        placeOfPosting: formData.placeOfPosting || '',\n        department: formData.department || '',\n        associateName: formData.associateName || '',\n        associateRelation: formData.associateRelation || '',\n        associateMobile: formData.associateMobile || ''\n      };\n\n      // Debug logging\n      console.log('Submitting person data:', submitData);\n      console.log('Selected Division:', selectedDivision);\n      console.log('Selected Category:', selectedCategory);\n      console.log('Selected SubCategory:', selectedSubCategory);\n\n      // Validate required fields before submission\n      const requiredFieldsCheck = {\n        divisionId: submitData.divisionId,\n        categoryId: submitData.categoryId,\n        name: submitData.name,\n        mobileNumber: submitData.mobileNumber,\n        nature: submitData.nature\n      };\n      console.log('Required fields check:', requiredFieldsCheck);\n\n      // Check for missing required fields\n      const missingFields = Object.entries(requiredFieldsCheck).filter(([, value]) => !value || value === '').map(([key]) => key);\n      if (missingFields.length > 0) {\n        console.error('Missing required fields:', missingFields);\n        alert('Missing required fields: ' + missingFields.join(', '));\n        return;\n      }\n      let result;\n      if (mode === 'create') {\n        result = await apiService.createPerson(submitData);\n      } else {\n        result = await apiService.updatePerson(initialData.id, submitData);\n      }\n      onSubmit(result);\n    } catch (error) {\n      var _error$data;\n      console.error('Error submitting form:', error);\n      console.error('Error data:', error.data);\n      console.error('Full error object:', error);\n      if ((_error$data = error.data) !== null && _error$data !== void 0 && _error$data.errors) {\n        // Handle validation errors from backend\n        const backendErrors = {};\n        Object.keys(error.data.errors).forEach(key => {\n          const errorMessages = error.data.errors[key];\n          backendErrors[key.toLowerCase()] = Array.isArray(errorMessages) ? errorMessages.join(', ') : errorMessages;\n        });\n        console.log('Backend validation errors:', backendErrors);\n        setErrors(backendErrors);\n      } else if (error.isValidationError && error.isValidationError()) {\n        setErrors(error.getValidationErrors());\n      } else {\n        var _error$data2, _error$data3;\n        const errorMessage = ((_error$data2 = error.data) === null || _error$data2 === void 0 ? void 0 : _error$data2.message) || ((_error$data3 = error.data) === null || _error$data3 === void 0 ? void 0 : _error$data3.title) || error.message || `HTTP ${error.status || 'Unknown'} - An error occurred while saving`;\n        console.log('General error message:', errorMessage);\n        setErrors({\n          general: errorMessage\n        });\n      }\n    } finally {\n      setSubmitting(false);\n    }\n  };\n  const shouldShowField = field => {\n    if (!field.conditional) return true;\n    const conditionValue = formData[field.conditional.field];\n    const expectedValue = field.conditional.value;\n\n    // Handle boolean conditions\n    if (typeof expectedValue === 'boolean' || expectedValue === 'true' || expectedValue === 'false') {\n      return conditionValue === (expectedValue === true || expectedValue === 'true');\n    }\n    return conditionValue === expectedValue;\n  };\n  const groupFieldsBySections = () => {\n    if (!formConfig || !formConfig.fields) return {};\n\n    // Debug: Check for duplicate fields\n    const fieldKeys = formConfig.fields.map(field => field.key);\n    const duplicateKeys = fieldKeys.filter((key, index) => fieldKeys.indexOf(key) !== index);\n    if (duplicateKeys.length > 0) {\n      console.warn('Duplicate field keys found:', duplicateKeys);\n      console.log('All fields:', formConfig.fields);\n    }\n    const sections = {};\n    formConfig.fields.forEach(field => {\n      const sectionKey = field.section || 'general';\n      if (!sections[sectionKey]) {\n        sections[sectionKey] = {\n          title: getSectionTitle(sectionKey),\n          fields: []\n        };\n      }\n      sections[sectionKey].fields.push(field);\n    });\n    return sections;\n  };\n  const getSectionTitle = sectionKey => {\n    const titles = {\n      personalInfo: 'Personal Information',\n      contactInfo: 'Contact Information',\n      locationInfo: 'Location Information',\n      businessInfo: 'Business Information',\n      associateInfo: 'Associate Information',\n      digitalPresence: 'Digital Presence',\n      companyInfo: 'Company Information',\n      authorizedPerson: 'Authorized Person',\n      marketingInfo: 'Marketing Information',\n      general: 'General Information'\n    };\n    return titles[sectionKey] || sectionKey;\n  };\n  const sections = formConfig ? groupFieldsBySections() : {};\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"dynamic-person-form\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"form-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: mode === 'create' ? 'Create New Person' : 'Edit Person'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 572,\n        columnNumber: 9\n      }, this), formConfig && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-info\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"form-name\",\n          children: formConfig.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 575,\n          columnNumber: 13\n        }, this), formConfig.description && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"form-description\",\n          children: formConfig.description\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 577,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 574,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 571,\n      columnNumber: 7\n    }, this), errors.general && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"alert alert-error\",\n      children: errors.general\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 584,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmit,\n      className: \"person-form\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Division & Category Selection\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 590,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"form-label\",\n            children: [\"Division \", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"required\",\n              children: \"*\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 595,\n              columnNumber: 24\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 594,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: selectedDivision,\n            onChange: handleDivisionChange,\n            disabled: loading.divisions,\n            className: `form-select ${errors.division ? 'error' : ''}`,\n            required: true,\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"\",\n              children: loading.divisions ? 'Loading divisions...' : 'Select Division'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 604,\n              columnNumber: 15\n            }, this), divisions.map(division => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: division.id,\n              children: division.name\n            }, division.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 608,\n              columnNumber: 17\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 597,\n            columnNumber: 13\n          }, this), errors.division && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"error-message\",\n            children: errors.division\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 614,\n            columnNumber: 15\n          }, this), errors.divisions && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"error-message\",\n            children: errors.divisions\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 617,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 593,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"form-label\",\n            children: [\"Category \", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"required\",\n              children: \"*\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 624,\n              columnNumber: 24\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 623,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: selectedCategory,\n            onChange: handleCategoryChange,\n            disabled: !selectedDivision || loading.categories,\n            className: `form-select ${errors.category ? 'error' : ''}`,\n            required: true,\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"\",\n              children: !selectedDivision ? 'Select Division first' : loading.categories ? 'Loading categories...' : 'Select Category'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 633,\n              columnNumber: 15\n            }, this), categories.map(category => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: category.id,\n              children: category.name\n            }, category.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 642,\n              columnNumber: 17\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 626,\n            columnNumber: 13\n          }, this), errors.category && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"error-message\",\n            children: errors.category\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 648,\n            columnNumber: 15\n          }, this), errors.categories && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"error-message\",\n            children: errors.categories\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 651,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 622,\n          columnNumber: 11\n        }, this), formAvailability.showSubCategoryDropdown && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"form-label\",\n            children: \"Subcategory (Optional)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 658,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: selectedSubCategory,\n            onChange: handleSubCategoryChange,\n            disabled: !selectedCategory || loading.subCategories,\n            className: \"form-select\",\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"\",\n              children: !selectedCategory ? 'Select Category first' : loading.subCategories ? 'Loading subcategories...' : 'Select Subcategory (Optional)'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 667,\n              columnNumber: 17\n            }, this), subCategories.map(subCategory => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: subCategory.id,\n              children: subCategory.name\n            }, subCategory.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 676,\n              columnNumber: 19\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 661,\n            columnNumber: 15\n          }, this), errors.subCategories && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"error-message\",\n            children: errors.subCategories\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 682,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 657,\n          columnNumber: 13\n        }, this), loading.form && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading-message\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Loading form configuration...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 690,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 689,\n          columnNumber: 13\n        }, this), formAvailability.message && !loading.form && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `status-message ${formAvailability.categoryHasForm || formAvailability.subCategoryHasForm ? 'success' : formAvailability.message.includes('No form') ? 'error' : 'info'}`,\n          children: formAvailability.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 695,\n          columnNumber: 13\n        }, this), errors.form && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"error-message\",\n          children: errors.form\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 707,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 589,\n        columnNumber: 9\n      }, this), formConfig && Object.entries(sections).map(([sectionKey, section]) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: section.title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 714,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-fields\",\n          children: section.fields.filter(field => shouldShowField(field)).map((field, fieldIndex) => /*#__PURE__*/_jsxDEV(FormField, {\n            field: field,\n            value: formData[field.key],\n            onChange: value => handleFieldChange(field.key, value),\n            error: errors[field.key]\n          }, `${sectionKey}-${field.key}-${fieldIndex}`, false, {\n            fileName: _jsxFileName,\n            lineNumber: 719,\n            columnNumber: 19\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 715,\n          columnNumber: 13\n        }, this)]\n      }, sectionKey, true, {\n        fileName: _jsxFileName,\n        lineNumber: 713,\n        columnNumber: 11\n      }, this)), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-actions\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          onClick: onCancel,\n          className: \"btn btn-outline\",\n          disabled: submitting,\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 735,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          className: \"btn btn-primary\",\n          disabled: submitting || !selectedDivision || !selectedCategory,\n          children: submitting ? mode === 'create' ? 'Creating...' : 'Updating...' : mode === 'create' ? 'Create Person' : 'Update Person'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 743,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 734,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 587,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 570,\n    columnNumber: 5\n  }, this);\n};\n_s(DynamicPersonForm, \"HQTZ+LW5pRnY6eFU2EO5G2IvcY0=\");\n_c = DynamicPersonForm;\nexport default DynamicPersonForm;\nvar _c;\n$RefreshReg$(_c, \"DynamicPersonForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "PersonNatureLabels", "<PERSON><PERSON><PERSON><PERSON>", "WorkingProfileLabels", "getDefaultPersonData", "formConfigService", "apiService", "FormField", "jsxDEV", "_jsxDEV", "DynamicPersonForm", "onSubmit", "onCancel", "initialData", "mode", "_s", "divisions", "setDivisions", "categories", "setCategories", "subCategories", "setSubCategories", "selectedDivision", "setSelectedDivision", "selectedCate<PERSON><PERSON>", "setSelectedCategory", "selectedSubCategory", "setSelectedSubCategory", "formConfig", "setFormConfig", "formData", "setFormData", "errors", "setErrors", "loading", "setLoading", "form", "submitting", "setSubmitting", "formAvailability", "setFormAvailability", "categoryHasForm", "subCategoryHasForm", "showSubCategoryDropdown", "message", "loadDivisions", "_initialData$division", "_initialData$category", "_initialData$subCateg", "divisionId", "toString", "categoryId", "subCategoryId", "defaultForm", "getDefaultFormConfig", "loadCategories", "handleCategorySelection", "handleSubCategorySelection", "prev", "response", "getDivisions", "data", "error", "console", "getCategoriesByDivision", "loadSubCategories", "getSubCategoriesByCategory", "hasFormForCategory", "parseInt", "categoryForm", "loadFormConfig", "hasFormForSubCategory", "subCategoryForm", "handleDivisionChange", "e", "value", "target", "handleCategoryChange", "handleSubCategoryChange", "handleFieldChange", "<PERSON><PERSON><PERSON>", "validateForm", "newErrors", "division", "category", "<PERSON><PERSON><PERSON><PERSON>", "log", "fields", "for<PERSON>ach", "field", "key", "required", "trim", "label", "conditional", "shouldShowField", "_field$validation", "_field$validation2", "_field$validation3", "type", "emailRegex", "test", "phoneRegex", "URL", "validation", "numValue", "parseFloat", "min", "undefined", "max", "pattern", "regex", "RegExp", "<PERSON><PERSON><PERSON><PERSON>", "length", "max<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "dateOfMarriage", "dateOfBirth", "birthDate", "Date", "marriageDate", "Object", "keys", "handleSubmit", "preventDefault", "submitData", "name", "mobileNumber", "nature", "alternateNumbers", "Array", "isArray", "split", "map", "s", "alternateEmailIds", "workingProfiles", "primaryEmailId", "website", "workingState", "domesticState", "district", "address", "workingArea", "reraRegistrationNumber", "source", "remarks", "firmName", "authorizedPersonName", "authorizedPersonEmail", "designation", "marketingContact", "marketingDesignation", "placeOfPosting", "department", "associate<PERSON><PERSON>", "associateRelation", "associate<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "missingFields", "entries", "filter", "alert", "join", "result", "create<PERSON>erson", "update<PERSON><PERSON>", "id", "_error$data", "backendErrors", "errorMessages", "toLowerCase", "isValidationError", "getValidationErrors", "_error$data2", "_error$data3", "errorMessage", "title", "status", "general", "conditionValue", "expectedValue", "groupFieldsBySections", "fieldKeys", "duplicateKeys", "index", "indexOf", "warn", "sections", "sectionKey", "section", "getSectionTitle", "push", "titles", "personalInfo", "contactInfo", "locationInfo", "businessInfo", "associateInfo", "digitalPresence", "companyInfo", "<PERSON><PERSON><PERSON>", "marketingInfo", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "description", "onChange", "disabled", "subCategory", "includes", "fieldIndex", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/Advanced/Advanced CRM filed all use/frontend/src/components/forms/DynamicPersonForm.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { PersonNatureLabels, GenderLabels, WorkingProfileLabels, getDefaultPersonData } from '../../constants/personConstants';\nimport formConfigService from '../../services/formConfigService';\nimport apiService from '../../services/apiService';\nimport FormField from './FormField';\nimport './DynamicPersonForm.css';\n\nconst DynamicPersonForm = ({ onSubmit, onCancel, initialData = null, mode = 'create' }) => {\n  // Hierarchy state\n  const [divisions, setDivisions] = useState([]);\n  const [categories, setCategories] = useState([]);\n  const [subCategories, setSubCategories] = useState([]);\n\n  // Selection state\n  const [selectedDivision, setSelectedDivision] = useState('');\n  const [selectedCategory, setSelectedCategory] = useState('');\n  const [selectedSubCategory, setSelectedSubCategory] = useState('');\n\n  // Form state\n  const [formConfig, setFormConfig] = useState(null);\n  const [formData, setFormData] = useState(getDefaultPersonData());\n  const [errors, setErrors] = useState({});\n\n  // Loading states\n  const [loading, setLoading] = useState({\n    divisions: false,\n    categories: false,\n    subCategories: false,\n    form: false\n  });\n  const [submitting, setSubmitting] = useState(false);\n\n  // Form availability state\n  const [formAvailability, setFormAvailability] = useState({\n    categoryHasForm: false,\n    subCategoryHasForm: false,\n    showSubCategoryDropdown: false,\n    message: ''\n  });\n\n  // Load divisions on component mount\n  useEffect(() => {\n    loadDivisions();\n\n    // Load initial data if provided\n    if (initialData) {\n      setFormData(initialData);\n      setSelectedDivision(initialData.divisionId?.toString() || '');\n      setSelectedCategory(initialData.categoryId?.toString() || '');\n      setSelectedSubCategory(initialData.subCategoryId?.toString() || '');\n    } else {\n      // Load default form for new person creation\n      const defaultForm = formConfigService.getDefaultFormConfig();\n      setFormConfig(defaultForm);\n    }\n  }, [initialData]);\n\n  // Load categories when division changes\n  useEffect(() => {\n    if (selectedDivision) {\n      loadCategories(selectedDivision);\n      // Reset category and subcategory when division changes\n      setSelectedCategory('');\n      setSelectedSubCategory('');\n      setSubCategories([]);\n      setFormConfig(null);\n      setFormAvailability({\n        categoryHasForm: false,\n        subCategoryHasForm: false,\n        showSubCategoryDropdown: false,\n        message: ''\n      });\n    } else {\n      setCategories([]);\n      setSubCategories([]);\n      setSelectedCategory('');\n      setSelectedSubCategory('');\n      setFormConfig(null);\n    }\n  }, [selectedDivision]);\n\n  // Handle category selection and form loading\n  useEffect(() => {\n    if (selectedCategory) {\n      handleCategorySelection(selectedCategory);\n      // Reset subcategory when category changes\n      setSelectedSubCategory('');\n    } else {\n      setSubCategories([]);\n      setSelectedSubCategory('');\n      setFormConfig(null);\n      setFormAvailability({\n        categoryHasForm: false,\n        subCategoryHasForm: false,\n        showSubCategoryDropdown: false,\n        message: ''\n      });\n    }\n  }, [selectedCategory]);\n\n  // Handle subcategory selection and form loading\n  useEffect(() => {\n    if (selectedSubCategory) {\n      handleSubCategorySelection(selectedSubCategory);\n    }\n  }, [selectedSubCategory]);\n\n  // Load divisions from API\n  const loadDivisions = async () => {\n    setLoading(prev => ({ ...prev, divisions: true }));\n    try {\n      const response = await apiService.getDivisions();\n      setDivisions(response.data || []);\n    } catch (error) {\n      console.error('Error loading divisions:', error);\n      setErrors(prev => ({ ...prev, divisions: 'Failed to load divisions' }));\n    } finally {\n      setLoading(prev => ({ ...prev, divisions: false }));\n    }\n  };\n\n  // Load categories by division\n  const loadCategories = async (divisionId) => {\n    setLoading(prev => ({ ...prev, categories: true }));\n    try {\n      const response = await apiService.getCategoriesByDivision(divisionId);\n      setCategories(response.data || []);\n    } catch (error) {\n      console.error('Error loading categories:', error);\n      setErrors(prev => ({ ...prev, categories: 'Failed to load categories' }));\n      setCategories([]);\n    } finally {\n      setLoading(prev => ({ ...prev, categories: false }));\n    }\n  };\n\n  // Load subcategories by category\n  const loadSubCategories = async (categoryId) => {\n    setLoading(prev => ({ ...prev, subCategories: true }));\n    try {\n      const response = await apiService.getSubCategoriesByCategory(categoryId);\n      setSubCategories(response.data || []);\n    } catch (error) {\n      console.error('Error loading subcategories:', error);\n      setErrors(prev => ({ ...prev, subCategories: 'Failed to load subcategories' }));\n      setSubCategories([]);\n    } finally {\n      setLoading(prev => ({ ...prev, subCategories: false }));\n    }\n  };\n\n  // Handle category selection and form loading logic\n  const handleCategorySelection = async (categoryId) => {\n    setLoading(prev => ({ ...prev, form: true }));\n\n    try {\n      // Check if category has a form\n      const categoryHasForm = formConfigService.hasFormForCategory(parseInt(categoryId));\n\n      if (categoryHasForm) {\n        // Load category form and hide subcategory dropdown\n        const categoryForm = formConfigService.loadFormConfig('category', parseInt(categoryId));\n        setFormConfig(categoryForm);\n        setFormAvailability({\n          categoryHasForm: true,\n          subCategoryHasForm: false,\n          showSubCategoryDropdown: false,\n          message: 'Using category-specific form'\n        });\n      } else {\n        // No category form, show subcategory dropdown and load subcategories\n        await loadSubCategories(categoryId);\n        // Load default form as fallback\n        const defaultForm = formConfigService.getDefaultFormConfig();\n        setFormConfig(defaultForm);\n        setFormAvailability({\n          categoryHasForm: false,\n          subCategoryHasForm: false,\n          showSubCategoryDropdown: true,\n          message: 'Using default form. You can select a subcategory if available.'\n        });\n      }\n    } catch (error) {\n      console.error('Error handling category selection:', error);\n      setErrors(prev => ({ ...prev, form: 'Error loading form configuration' }));\n    } finally {\n      setLoading(prev => ({ ...prev, form: false }));\n    }\n  };\n\n  // Handle subcategory selection and form loading logic\n  const handleSubCategorySelection = async (subCategoryId) => {\n    setLoading(prev => ({ ...prev, form: true }));\n\n    try {\n      // Check if subcategory has a form\n      const subCategoryHasForm = formConfigService.hasFormForSubCategory(parseInt(subCategoryId));\n\n      if (subCategoryHasForm) {\n        // Load subcategory form\n        const subCategoryForm = formConfigService.loadFormConfig('subcategory', parseInt(subCategoryId));\n        setFormConfig(subCategoryForm);\n        setFormAvailability({\n          categoryHasForm: false,\n          subCategoryHasForm: true,\n          showSubCategoryDropdown: true,\n          message: 'Using subcategory-specific form'\n        });\n      } else {\n        // No subcategory form available, use default form\n        const defaultForm = formConfigService.getDefaultFormConfig();\n        setFormConfig(defaultForm);\n        setFormAvailability({\n          categoryHasForm: false,\n          subCategoryHasForm: false,\n          showSubCategoryDropdown: true,\n          message: 'No specific form found for this subcategory. Using default form.'\n        });\n      }\n    } catch (error) {\n      console.error('Error handling subcategory selection:', error);\n      setErrors(prev => ({ ...prev, form: 'Error loading form configuration' }));\n    } finally {\n      setLoading(prev => ({ ...prev, form: false }));\n    }\n  };\n\n  // Handle dropdown changes\n  const handleDivisionChange = (e) => {\n    const value = e.target.value;\n    setSelectedDivision(value);\n\n    // Update form data\n    setFormData(prev => ({\n      ...prev,\n      divisionId: value ? parseInt(value) : null,\n      categoryId: null,\n      subCategoryId: null\n    }));\n  };\n\n  const handleCategoryChange = (e) => {\n    const value = e.target.value;\n    setSelectedCategory(value);\n\n    // Update form data\n    setFormData(prev => ({\n      ...prev,\n      categoryId: value ? parseInt(value) : null,\n      subCategoryId: null\n    }));\n  };\n\n  const handleSubCategoryChange = (e) => {\n    const value = e.target.value;\n    setSelectedSubCategory(value);\n\n    // Update form data\n    setFormData(prev => ({\n      ...prev,\n      subCategoryId: value ? parseInt(value) : null\n    }));\n  };\n\n  const handleFieldChange = (fieldKey, value) => {\n    setFormData(prev => ({\n      ...prev,\n      [fieldKey]: value\n    }));\n\n    // Clear field error when user starts typing\n    if (errors[fieldKey]) {\n      setErrors(prev => ({\n        ...prev,\n        [fieldKey]: null\n      }));\n    }\n  };\n\n  const validateForm = () => {\n    const newErrors = {};\n\n    // Validate hierarchy selection\n    if (!selectedDivision) {\n      newErrors.division = 'Division is required';\n    }\n    if (!selectedCategory) {\n      newErrors.category = 'Category is required';\n    }\n\n    // Check if form is available (should always have default form as fallback)\n    if (!formConfig) {\n      newErrors.form = 'Form configuration not loaded. Please try again.';\n      return { isValid: false, errors: newErrors };\n    }\n\n    console.log('Validating form with config:', formConfig);\n    console.log('Current form data:', formData);\n\n    // Validate form fields\n    formConfig.fields.forEach(field => {\n      const value = formData[field.key];\n      \n      // Required field validation\n      if (field.required && (!value || (typeof value === 'string' && value.trim() === ''))) {\n        newErrors[field.key] = `${field.label} is required`;\n        return;\n      }\n\n      // Conditional field validation\n      if (field.conditional && shouldShowField(field)) {\n        if (field.required && (!value || (typeof value === 'string' && value.trim() === ''))) {\n          newErrors[field.key] = `${field.label} is required`;\n          return;\n        }\n      }\n\n      // Type-specific validation\n      if (value && typeof value === 'string' && value.trim() !== '') {\n        switch (field.type) {\n          case 'email':\n            const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n            if (!emailRegex.test(value)) {\n              newErrors[field.key] = 'Please enter a valid email address';\n            }\n            break;\n          case 'tel':\n            const phoneRegex = /^(\\+91[\\-\\s]?)?[0]?(91)?[789]\\d{9}$/;\n            if (!phoneRegex.test(value)) {\n              newErrors[field.key] = 'Please enter a valid mobile number';\n            }\n            break;\n          case 'url':\n            try {\n              new URL(value);\n            } catch {\n              newErrors[field.key] = 'Please enter a valid URL';\n            }\n            break;\n          case 'number':\n            if (field.validation) {\n              const numValue = parseFloat(value);\n              if (field.validation.min !== undefined && numValue < field.validation.min) {\n                newErrors[field.key] = `Value must be at least ${field.validation.min}`;\n              }\n              if (field.validation.max !== undefined && numValue > field.validation.max) {\n                newErrors[field.key] = `Value must be at most ${field.validation.max}`;\n              }\n            }\n            break;\n        }\n\n        // Pattern validation\n        if (field.validation?.pattern) {\n          const regex = new RegExp(field.validation.pattern);\n          if (!regex.test(value)) {\n            newErrors[field.key] = `${field.label} format is invalid`;\n          }\n        }\n\n        // Length validation\n        if (field.validation?.minLength && value.length < field.validation.minLength) {\n          newErrors[field.key] = `${field.label} must be at least ${field.validation.minLength} characters`;\n        }\n        if (field.validation?.maxLength && value.length > field.validation.maxLength) {\n          newErrors[field.key] = `${field.label} must be at most ${field.validation.maxLength} characters`;\n        }\n      }\n    });\n\n    // Business logic validation\n    if (formData.isMarried && formData.dateOfMarriage && formData.dateOfBirth) {\n      const birthDate = new Date(formData.dateOfBirth);\n      const marriageDate = new Date(formData.dateOfMarriage);\n      if (marriageDate <= birthDate) {\n        newErrors.dateOfMarriage = 'Marriage date must be after birth date';\n      }\n    }\n\n    return {\n      isValid: Object.keys(newErrors).length === 0,\n      errors: newErrors\n    };\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    \n    const validation = validateForm();\n    if (!validation.isValid) {\n      console.log('Validation failed:', validation.errors);\n      setErrors(validation.errors);\n      return;\n    }\n\n    setSubmitting(true);\n    try {\n      // Prepare data for API\n      const submitData = {\n        ...formData,\n        // Ensure hierarchy IDs are included\n        divisionId: parseInt(selectedDivision),\n        categoryId: parseInt(selectedCategory),\n        subCategoryId: selectedSubCategory ? parseInt(selectedSubCategory) : null,\n        // Ensure required fields are not null/undefined\n        name: formData.name || '',\n        mobileNumber: formData.mobileNumber || '',\n        nature: formData.nature || null,\n        // Convert arrays to proper format\n        alternateNumbers: Array.isArray(formData.alternateNumbers)\n          ? formData.alternateNumbers\n          : formData.alternateNumbers ? formData.alternateNumbers.split(',').map(s => s.trim()) : [],\n        alternateEmailIds: Array.isArray(formData.alternateEmailIds)\n          ? formData.alternateEmailIds\n          : formData.alternateEmailIds ? formData.alternateEmailIds.split(',').map(s => s.trim()) : [],\n        workingProfiles: Array.isArray(formData.workingProfiles)\n          ? formData.workingProfiles\n          : [],\n        // Ensure other fields have default values\n        primaryEmailId: formData.primaryEmailId || '',\n        website: formData.website || '',\n        workingState: formData.workingState || '',\n        domesticState: formData.domesticState || '',\n        district: formData.district || '',\n        address: formData.address || '',\n        workingArea: formData.workingArea || '',\n        reraRegistrationNumber: formData.reraRegistrationNumber || '',\n        source: formData.source || '',\n        remarks: formData.remarks || '',\n        firmName: formData.firmName || '',\n        authorizedPersonName: formData.authorizedPersonName || '',\n        authorizedPersonEmail: formData.authorizedPersonEmail || '',\n        designation: formData.designation || '',\n        marketingContact: formData.marketingContact || '',\n        marketingDesignation: formData.marketingDesignation || '',\n        placeOfPosting: formData.placeOfPosting || '',\n        department: formData.department || '',\n        associateName: formData.associateName || '',\n        associateRelation: formData.associateRelation || '',\n        associateMobile: formData.associateMobile || ''\n      };\n\n      // Debug logging\n      console.log('Submitting person data:', submitData);\n      console.log('Selected Division:', selectedDivision);\n      console.log('Selected Category:', selectedCategory);\n      console.log('Selected SubCategory:', selectedSubCategory);\n\n      // Validate required fields before submission\n      const requiredFieldsCheck = {\n        divisionId: submitData.divisionId,\n        categoryId: submitData.categoryId,\n        name: submitData.name,\n        mobileNumber: submitData.mobileNumber,\n        nature: submitData.nature\n      };\n      console.log('Required fields check:', requiredFieldsCheck);\n\n      // Check for missing required fields\n      const missingFields = Object.entries(requiredFieldsCheck)\n        .filter(([, value]) => !value || value === '')\n        .map(([key]) => key);\n\n      if (missingFields.length > 0) {\n        console.error('Missing required fields:', missingFields);\n        alert('Missing required fields: ' + missingFields.join(', '));\n        return;\n      }\n\n      let result;\n      if (mode === 'create') {\n        result = await apiService.createPerson(submitData);\n      } else {\n        result = await apiService.updatePerson(initialData.id, submitData);\n      }\n\n      onSubmit(result);\n    } catch (error) {\n      console.error('Error submitting form:', error);\n      console.error('Error data:', error.data);\n      console.error('Full error object:', error);\n\n      if (error.data?.errors) {\n        // Handle validation errors from backend\n        const backendErrors = {};\n        Object.keys(error.data.errors).forEach(key => {\n          const errorMessages = error.data.errors[key];\n          backendErrors[key.toLowerCase()] = Array.isArray(errorMessages)\n            ? errorMessages.join(', ')\n            : errorMessages;\n        });\n        console.log('Backend validation errors:', backendErrors);\n        setErrors(backendErrors);\n      } else if (error.isValidationError && error.isValidationError()) {\n        setErrors(error.getValidationErrors());\n      } else {\n        const errorMessage = error.data?.message ||\n                           error.data?.title ||\n                           error.message ||\n                           `HTTP ${error.status || 'Unknown'} - An error occurred while saving`;\n        console.log('General error message:', errorMessage);\n        setErrors({ general: errorMessage });\n      }\n    } finally {\n      setSubmitting(false);\n    }\n  };\n\n  const shouldShowField = (field) => {\n    if (!field.conditional) return true;\n    \n    const conditionValue = formData[field.conditional.field];\n    const expectedValue = field.conditional.value;\n    \n    // Handle boolean conditions\n    if (typeof expectedValue === 'boolean' || expectedValue === 'true' || expectedValue === 'false') {\n      return conditionValue === (expectedValue === true || expectedValue === 'true');\n    }\n    \n    return conditionValue === expectedValue;\n  };\n\n  const groupFieldsBySections = () => {\n    if (!formConfig || !formConfig.fields) return {};\n\n    // Debug: Check for duplicate fields\n    const fieldKeys = formConfig.fields.map(field => field.key);\n    const duplicateKeys = fieldKeys.filter((key, index) => fieldKeys.indexOf(key) !== index);\n    if (duplicateKeys.length > 0) {\n      console.warn('Duplicate field keys found:', duplicateKeys);\n      console.log('All fields:', formConfig.fields);\n    }\n\n    const sections = {};\n    formConfig.fields.forEach((field) => {\n      const sectionKey = field.section || 'general';\n      if (!sections[sectionKey]) {\n        sections[sectionKey] = {\n          title: getSectionTitle(sectionKey),\n          fields: []\n        };\n      }\n      sections[sectionKey].fields.push(field);\n    });\n\n    return sections;\n  };\n\n  const getSectionTitle = (sectionKey) => {\n    const titles = {\n      personalInfo: 'Personal Information',\n      contactInfo: 'Contact Information',\n      locationInfo: 'Location Information',\n      businessInfo: 'Business Information',\n      associateInfo: 'Associate Information',\n      digitalPresence: 'Digital Presence',\n      companyInfo: 'Company Information',\n      authorizedPerson: 'Authorized Person',\n      marketingInfo: 'Marketing Information',\n      general: 'General Information'\n    };\n    return titles[sectionKey] || sectionKey;\n  };\n\n  const sections = formConfig ? groupFieldsBySections() : {};\n\n\n\n  return (\n    <div className=\"dynamic-person-form\">\n      <div className=\"form-header\">\n        <h2>{mode === 'create' ? 'Create New Person' : 'Edit Person'}</h2>\n        {formConfig && (\n          <div className=\"form-info\">\n            <span className=\"form-name\">{formConfig.name}</span>\n            {formConfig.description && (\n              <span className=\"form-description\">{formConfig.description}</span>\n            )}\n          </div>\n        )}\n      </div>\n\n      {errors.general && (\n        <div className=\"alert alert-error\">{errors.general}</div>\n      )}\n\n      <form onSubmit={handleSubmit} className=\"person-form\">\n        {/* Hierarchical Dropdowns */}\n        <div className=\"form-section\">\n          <h3>Division & Category Selection</h3>\n\n          {/* Division Dropdown */}\n          <div className=\"form-group\">\n            <label className=\"form-label\">\n              Division <span className=\"required\">*</span>\n            </label>\n            <select\n              value={selectedDivision}\n              onChange={handleDivisionChange}\n              disabled={loading.divisions}\n              className={`form-select ${errors.division ? 'error' : ''}`}\n              required\n            >\n              <option value=\"\">\n                {loading.divisions ? 'Loading divisions...' : 'Select Division'}\n              </option>\n              {divisions.map(division => (\n                <option key={division.id} value={division.id}>\n                  {division.name}\n                </option>\n              ))}\n            </select>\n            {errors.division && (\n              <div className=\"error-message\">{errors.division}</div>\n            )}\n            {errors.divisions && (\n              <div className=\"error-message\">{errors.divisions}</div>\n            )}\n          </div>\n\n          {/* Category Dropdown */}\n          <div className=\"form-group\">\n            <label className=\"form-label\">\n              Category <span className=\"required\">*</span>\n            </label>\n            <select\n              value={selectedCategory}\n              onChange={handleCategoryChange}\n              disabled={!selectedDivision || loading.categories}\n              className={`form-select ${errors.category ? 'error' : ''}`}\n              required\n            >\n              <option value=\"\">\n                {!selectedDivision\n                  ? 'Select Division first'\n                  : loading.categories\n                    ? 'Loading categories...'\n                    : 'Select Category'\n                }\n              </option>\n              {categories.map(category => (\n                <option key={category.id} value={category.id}>\n                  {category.name}\n                </option>\n              ))}\n            </select>\n            {errors.category && (\n              <div className=\"error-message\">{errors.category}</div>\n            )}\n            {errors.categories && (\n              <div className=\"error-message\">{errors.categories}</div>\n            )}\n          </div>\n\n          {/* Subcategory Dropdown - Only show if category doesn't have a form */}\n          {formAvailability.showSubCategoryDropdown && (\n            <div className=\"form-group\">\n              <label className=\"form-label\">\n                Subcategory (Optional)\n              </label>\n              <select\n                value={selectedSubCategory}\n                onChange={handleSubCategoryChange}\n                disabled={!selectedCategory || loading.subCategories}\n                className=\"form-select\"\n              >\n                <option value=\"\">\n                  {!selectedCategory\n                    ? 'Select Category first'\n                    : loading.subCategories\n                      ? 'Loading subcategories...'\n                      : 'Select Subcategory (Optional)'\n                  }\n                </option>\n                {subCategories.map(subCategory => (\n                  <option key={subCategory.id} value={subCategory.id}>\n                    {subCategory.name}\n                  </option>\n                ))}\n              </select>\n              {errors.subCategories && (\n                <div className=\"error-message\">{errors.subCategories}</div>\n              )}\n            </div>\n          )}\n\n          {/* Form Status Messages */}\n          {loading.form && (\n            <div className=\"loading-message\">\n              <span>Loading form configuration...</span>\n            </div>\n          )}\n\n          {formAvailability.message && !loading.form && (\n            <div className={`status-message ${\n              formAvailability.categoryHasForm || formAvailability.subCategoryHasForm\n                ? 'success'\n                : formAvailability.message.includes('No form')\n                  ? 'error'\n                  : 'info'\n            }`}>\n              {formAvailability.message}\n            </div>\n          )}\n\n          {errors.form && (\n            <div className=\"error-message\">{errors.form}</div>\n          )}\n        </div>\n\n        {/* Dynamic Form Fields - Only show when form is available */}\n        {formConfig && Object.entries(sections).map(([sectionKey, section]) => (\n          <div key={sectionKey} className=\"form-section\">\n            <h3>{section.title}</h3>\n            <div className=\"form-fields\">\n              {section.fields\n                .filter(field => shouldShowField(field))\n                .map((field, fieldIndex) => (\n                  <FormField\n                    key={`${sectionKey}-${field.key}-${fieldIndex}`}\n                    field={field}\n                    value={formData[field.key]}\n                    onChange={(value) => handleFieldChange(field.key, value)}\n                    error={errors[field.key]}\n                  />\n                ))}\n            </div>\n          </div>\n        ))}\n\n\n\n        {/* Form Actions */}\n        <div className=\"form-actions\">\n          <button\n            type=\"button\"\n            onClick={onCancel}\n            className=\"btn btn-outline\"\n            disabled={submitting}\n          >\n            Cancel\n          </button>\n          <button\n            type=\"submit\"\n            className=\"btn btn-primary\"\n            disabled={submitting || !selectedDivision || !selectedCategory}\n          >\n            {submitting\n              ? (mode === 'create' ? 'Creating...' : 'Updating...')\n              : (mode === 'create' ? 'Create Person' : 'Update Person')\n            }\n          </button>\n        </div>\n      </form>\n    </div>\n  );\n};\n\nexport default DynamicPersonForm;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,kBAAkB,EAAEC,YAAY,EAAEC,oBAAoB,EAAEC,oBAAoB,QAAQ,iCAAiC;AAC9H,OAAOC,iBAAiB,MAAM,kCAAkC;AAChE,OAAOC,UAAU,MAAM,2BAA2B;AAClD,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAO,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjC,MAAMC,iBAAiB,GAAGA,CAAC;EAAEC,QAAQ;EAAEC,QAAQ;EAAEC,WAAW,GAAG,IAAI;EAAEC,IAAI,GAAG;AAAS,CAAC,KAAK;EAAAC,EAAA;EACzF;EACA,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACmB,UAAU,EAAEC,aAAa,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACqB,aAAa,EAAEC,gBAAgB,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;;EAEtD;EACA,MAAM,CAACuB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACyB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAAC2B,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;;EAElE;EACA,MAAM,CAAC6B,UAAU,EAAEC,aAAa,CAAC,GAAG9B,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAAC+B,QAAQ,EAAEC,WAAW,CAAC,GAAGhC,QAAQ,CAACK,oBAAoB,CAAC,CAAC,CAAC;EAChE,MAAM,CAAC4B,MAAM,EAAEC,SAAS,CAAC,GAAGlC,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAExC;EACA,MAAM,CAACmC,OAAO,EAAEC,UAAU,CAAC,GAAGpC,QAAQ,CAAC;IACrCiB,SAAS,EAAE,KAAK;IAChBE,UAAU,EAAE,KAAK;IACjBE,aAAa,EAAE,KAAK;IACpBgB,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGvC,QAAQ,CAAC,KAAK,CAAC;;EAEnD;EACA,MAAM,CAACwC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGzC,QAAQ,CAAC;IACvD0C,eAAe,EAAE,KAAK;IACtBC,kBAAkB,EAAE,KAAK;IACzBC,uBAAuB,EAAE,KAAK;IAC9BC,OAAO,EAAE;EACX,CAAC,CAAC;;EAEF;EACA5C,SAAS,CAAC,MAAM;IACd6C,aAAa,CAAC,CAAC;;IAEf;IACA,IAAIhC,WAAW,EAAE;MAAA,IAAAiC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA;MACfjB,WAAW,CAAClB,WAAW,CAAC;MACxBU,mBAAmB,CAAC,EAAAuB,qBAAA,GAAAjC,WAAW,CAACoC,UAAU,cAAAH,qBAAA,uBAAtBA,qBAAA,CAAwBI,QAAQ,CAAC,CAAC,KAAI,EAAE,CAAC;MAC7DzB,mBAAmB,CAAC,EAAAsB,qBAAA,GAAAlC,WAAW,CAACsC,UAAU,cAAAJ,qBAAA,uBAAtBA,qBAAA,CAAwBG,QAAQ,CAAC,CAAC,KAAI,EAAE,CAAC;MAC7DvB,sBAAsB,CAAC,EAAAqB,qBAAA,GAAAnC,WAAW,CAACuC,aAAa,cAAAJ,qBAAA,uBAAzBA,qBAAA,CAA2BE,QAAQ,CAAC,CAAC,KAAI,EAAE,CAAC;IACrE,CAAC,MAAM;MACL;MACA,MAAMG,WAAW,GAAGhD,iBAAiB,CAACiD,oBAAoB,CAAC,CAAC;MAC5DzB,aAAa,CAACwB,WAAW,CAAC;IAC5B;EACF,CAAC,EAAE,CAACxC,WAAW,CAAC,CAAC;;EAEjB;EACAb,SAAS,CAAC,MAAM;IACd,IAAIsB,gBAAgB,EAAE;MACpBiC,cAAc,CAACjC,gBAAgB,CAAC;MAChC;MACAG,mBAAmB,CAAC,EAAE,CAAC;MACvBE,sBAAsB,CAAC,EAAE,CAAC;MAC1BN,gBAAgB,CAAC,EAAE,CAAC;MACpBQ,aAAa,CAAC,IAAI,CAAC;MACnBW,mBAAmB,CAAC;QAClBC,eAAe,EAAE,KAAK;QACtBC,kBAAkB,EAAE,KAAK;QACzBC,uBAAuB,EAAE,KAAK;QAC9BC,OAAO,EAAE;MACX,CAAC,CAAC;IACJ,CAAC,MAAM;MACLzB,aAAa,CAAC,EAAE,CAAC;MACjBE,gBAAgB,CAAC,EAAE,CAAC;MACpBI,mBAAmB,CAAC,EAAE,CAAC;MACvBE,sBAAsB,CAAC,EAAE,CAAC;MAC1BE,aAAa,CAAC,IAAI,CAAC;IACrB;EACF,CAAC,EAAE,CAACP,gBAAgB,CAAC,CAAC;;EAEtB;EACAtB,SAAS,CAAC,MAAM;IACd,IAAIwB,gBAAgB,EAAE;MACpBgC,uBAAuB,CAAChC,gBAAgB,CAAC;MACzC;MACAG,sBAAsB,CAAC,EAAE,CAAC;IAC5B,CAAC,MAAM;MACLN,gBAAgB,CAAC,EAAE,CAAC;MACpBM,sBAAsB,CAAC,EAAE,CAAC;MAC1BE,aAAa,CAAC,IAAI,CAAC;MACnBW,mBAAmB,CAAC;QAClBC,eAAe,EAAE,KAAK;QACtBC,kBAAkB,EAAE,KAAK;QACzBC,uBAAuB,EAAE,KAAK;QAC9BC,OAAO,EAAE;MACX,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACpB,gBAAgB,CAAC,CAAC;;EAEtB;EACAxB,SAAS,CAAC,MAAM;IACd,IAAI0B,mBAAmB,EAAE;MACvB+B,0BAA0B,CAAC/B,mBAAmB,CAAC;IACjD;EACF,CAAC,EAAE,CAACA,mBAAmB,CAAC,CAAC;;EAEzB;EACA,MAAMmB,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChCV,UAAU,CAACuB,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE1C,SAAS,EAAE;IAAK,CAAC,CAAC,CAAC;IAClD,IAAI;MACF,MAAM2C,QAAQ,GAAG,MAAMrD,UAAU,CAACsD,YAAY,CAAC,CAAC;MAChD3C,YAAY,CAAC0C,QAAQ,CAACE,IAAI,IAAI,EAAE,CAAC;IACnC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChD7B,SAAS,CAACyB,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE1C,SAAS,EAAE;MAA2B,CAAC,CAAC,CAAC;IACzE,CAAC,SAAS;MACRmB,UAAU,CAACuB,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE1C,SAAS,EAAE;MAAM,CAAC,CAAC,CAAC;IACrD;EACF,CAAC;;EAED;EACA,MAAMuC,cAAc,GAAG,MAAON,UAAU,IAAK;IAC3Cd,UAAU,CAACuB,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAExC,UAAU,EAAE;IAAK,CAAC,CAAC,CAAC;IACnD,IAAI;MACF,MAAMyC,QAAQ,GAAG,MAAMrD,UAAU,CAAC0D,uBAAuB,CAACf,UAAU,CAAC;MACrE9B,aAAa,CAACwC,QAAQ,CAACE,IAAI,IAAI,EAAE,CAAC;IACpC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD7B,SAAS,CAACyB,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAExC,UAAU,EAAE;MAA4B,CAAC,CAAC,CAAC;MACzEC,aAAa,CAAC,EAAE,CAAC;IACnB,CAAC,SAAS;MACRgB,UAAU,CAACuB,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAExC,UAAU,EAAE;MAAM,CAAC,CAAC,CAAC;IACtD;EACF,CAAC;;EAED;EACA,MAAM+C,iBAAiB,GAAG,MAAOd,UAAU,IAAK;IAC9ChB,UAAU,CAACuB,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAEtC,aAAa,EAAE;IAAK,CAAC,CAAC,CAAC;IACtD,IAAI;MACF,MAAMuC,QAAQ,GAAG,MAAMrD,UAAU,CAAC4D,0BAA0B,CAACf,UAAU,CAAC;MACxE9B,gBAAgB,CAACsC,QAAQ,CAACE,IAAI,IAAI,EAAE,CAAC;IACvC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD7B,SAAS,CAACyB,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEtC,aAAa,EAAE;MAA+B,CAAC,CAAC,CAAC;MAC/EC,gBAAgB,CAAC,EAAE,CAAC;IACtB,CAAC,SAAS;MACRc,UAAU,CAACuB,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEtC,aAAa,EAAE;MAAM,CAAC,CAAC,CAAC;IACzD;EACF,CAAC;;EAED;EACA,MAAMoC,uBAAuB,GAAG,MAAOL,UAAU,IAAK;IACpDhB,UAAU,CAACuB,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAEtB,IAAI,EAAE;IAAK,CAAC,CAAC,CAAC;IAE7C,IAAI;MACF;MACA,MAAMK,eAAe,GAAGpC,iBAAiB,CAAC8D,kBAAkB,CAACC,QAAQ,CAACjB,UAAU,CAAC,CAAC;MAElF,IAAIV,eAAe,EAAE;QACnB;QACA,MAAM4B,YAAY,GAAGhE,iBAAiB,CAACiE,cAAc,CAAC,UAAU,EAAEF,QAAQ,CAACjB,UAAU,CAAC,CAAC;QACvFtB,aAAa,CAACwC,YAAY,CAAC;QAC3B7B,mBAAmB,CAAC;UAClBC,eAAe,EAAE,IAAI;UACrBC,kBAAkB,EAAE,KAAK;UACzBC,uBAAuB,EAAE,KAAK;UAC9BC,OAAO,EAAE;QACX,CAAC,CAAC;MACJ,CAAC,MAAM;QACL;QACA,MAAMqB,iBAAiB,CAACd,UAAU,CAAC;QACnC;QACA,MAAME,WAAW,GAAGhD,iBAAiB,CAACiD,oBAAoB,CAAC,CAAC;QAC5DzB,aAAa,CAACwB,WAAW,CAAC;QAC1Bb,mBAAmB,CAAC;UAClBC,eAAe,EAAE,KAAK;UACtBC,kBAAkB,EAAE,KAAK;UACzBC,uBAAuB,EAAE,IAAI;UAC7BC,OAAO,EAAE;QACX,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAOkB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;MAC1D7B,SAAS,CAACyB,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEtB,IAAI,EAAE;MAAmC,CAAC,CAAC,CAAC;IAC5E,CAAC,SAAS;MACRD,UAAU,CAACuB,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEtB,IAAI,EAAE;MAAM,CAAC,CAAC,CAAC;IAChD;EACF,CAAC;;EAED;EACA,MAAMqB,0BAA0B,GAAG,MAAOL,aAAa,IAAK;IAC1DjB,UAAU,CAACuB,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAEtB,IAAI,EAAE;IAAK,CAAC,CAAC,CAAC;IAE7C,IAAI;MACF;MACA,MAAMM,kBAAkB,GAAGrC,iBAAiB,CAACkE,qBAAqB,CAACH,QAAQ,CAAChB,aAAa,CAAC,CAAC;MAE3F,IAAIV,kBAAkB,EAAE;QACtB;QACA,MAAM8B,eAAe,GAAGnE,iBAAiB,CAACiE,cAAc,CAAC,aAAa,EAAEF,QAAQ,CAAChB,aAAa,CAAC,CAAC;QAChGvB,aAAa,CAAC2C,eAAe,CAAC;QAC9BhC,mBAAmB,CAAC;UAClBC,eAAe,EAAE,KAAK;UACtBC,kBAAkB,EAAE,IAAI;UACxBC,uBAAuB,EAAE,IAAI;UAC7BC,OAAO,EAAE;QACX,CAAC,CAAC;MACJ,CAAC,MAAM;QACL;QACA,MAAMS,WAAW,GAAGhD,iBAAiB,CAACiD,oBAAoB,CAAC,CAAC;QAC5DzB,aAAa,CAACwB,WAAW,CAAC;QAC1Bb,mBAAmB,CAAC;UAClBC,eAAe,EAAE,KAAK;UACtBC,kBAAkB,EAAE,KAAK;UACzBC,uBAAuB,EAAE,IAAI;UAC7BC,OAAO,EAAE;QACX,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAOkB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;MAC7D7B,SAAS,CAACyB,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEtB,IAAI,EAAE;MAAmC,CAAC,CAAC,CAAC;IAC5E,CAAC,SAAS;MACRD,UAAU,CAACuB,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEtB,IAAI,EAAE;MAAM,CAAC,CAAC,CAAC;IAChD;EACF,CAAC;;EAED;EACA,MAAMqC,oBAAoB,GAAIC,CAAC,IAAK;IAClC,MAAMC,KAAK,GAAGD,CAAC,CAACE,MAAM,CAACD,KAAK;IAC5BpD,mBAAmB,CAACoD,KAAK,CAAC;;IAE1B;IACA5C,WAAW,CAAC2B,IAAI,KAAK;MACnB,GAAGA,IAAI;MACPT,UAAU,EAAE0B,KAAK,GAAGP,QAAQ,CAACO,KAAK,CAAC,GAAG,IAAI;MAC1CxB,UAAU,EAAE,IAAI;MAChBC,aAAa,EAAE;IACjB,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMyB,oBAAoB,GAAIH,CAAC,IAAK;IAClC,MAAMC,KAAK,GAAGD,CAAC,CAACE,MAAM,CAACD,KAAK;IAC5BlD,mBAAmB,CAACkD,KAAK,CAAC;;IAE1B;IACA5C,WAAW,CAAC2B,IAAI,KAAK;MACnB,GAAGA,IAAI;MACPP,UAAU,EAAEwB,KAAK,GAAGP,QAAQ,CAACO,KAAK,CAAC,GAAG,IAAI;MAC1CvB,aAAa,EAAE;IACjB,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAM0B,uBAAuB,GAAIJ,CAAC,IAAK;IACrC,MAAMC,KAAK,GAAGD,CAAC,CAACE,MAAM,CAACD,KAAK;IAC5BhD,sBAAsB,CAACgD,KAAK,CAAC;;IAE7B;IACA5C,WAAW,CAAC2B,IAAI,KAAK;MACnB,GAAGA,IAAI;MACPN,aAAa,EAAEuB,KAAK,GAAGP,QAAQ,CAACO,KAAK,CAAC,GAAG;IAC3C,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMI,iBAAiB,GAAGA,CAACC,QAAQ,EAAEL,KAAK,KAAK;IAC7C5C,WAAW,CAAC2B,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACsB,QAAQ,GAAGL;IACd,CAAC,CAAC,CAAC;;IAEH;IACA,IAAI3C,MAAM,CAACgD,QAAQ,CAAC,EAAE;MACpB/C,SAAS,CAACyB,IAAI,KAAK;QACjB,GAAGA,IAAI;QACP,CAACsB,QAAQ,GAAG;MACd,CAAC,CAAC,CAAC;IACL;EACF,CAAC;EAED,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,SAAS,GAAG,CAAC,CAAC;;IAEpB;IACA,IAAI,CAAC5D,gBAAgB,EAAE;MACrB4D,SAAS,CAACC,QAAQ,GAAG,sBAAsB;IAC7C;IACA,IAAI,CAAC3D,gBAAgB,EAAE;MACrB0D,SAAS,CAACE,QAAQ,GAAG,sBAAsB;IAC7C;;IAEA;IACA,IAAI,CAACxD,UAAU,EAAE;MACfsD,SAAS,CAAC9C,IAAI,GAAG,kDAAkD;MACnE,OAAO;QAAEiD,OAAO,EAAE,KAAK;QAAErD,MAAM,EAAEkD;MAAU,CAAC;IAC9C;IAEAnB,OAAO,CAACuB,GAAG,CAAC,8BAA8B,EAAE1D,UAAU,CAAC;IACvDmC,OAAO,CAACuB,GAAG,CAAC,oBAAoB,EAAExD,QAAQ,CAAC;;IAE3C;IACAF,UAAU,CAAC2D,MAAM,CAACC,OAAO,CAACC,KAAK,IAAI;MACjC,MAAMd,KAAK,GAAG7C,QAAQ,CAAC2D,KAAK,CAACC,GAAG,CAAC;;MAEjC;MACA,IAAID,KAAK,CAACE,QAAQ,KAAK,CAAChB,KAAK,IAAK,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACiB,IAAI,CAAC,CAAC,KAAK,EAAG,CAAC,EAAE;QACpFV,SAAS,CAACO,KAAK,CAACC,GAAG,CAAC,GAAG,GAAGD,KAAK,CAACI,KAAK,cAAc;QACnD;MACF;;MAEA;MACA,IAAIJ,KAAK,CAACK,WAAW,IAAIC,eAAe,CAACN,KAAK,CAAC,EAAE;QAC/C,IAAIA,KAAK,CAACE,QAAQ,KAAK,CAAChB,KAAK,IAAK,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACiB,IAAI,CAAC,CAAC,KAAK,EAAG,CAAC,EAAE;UACpFV,SAAS,CAACO,KAAK,CAACC,GAAG,CAAC,GAAG,GAAGD,KAAK,CAACI,KAAK,cAAc;UACnD;QACF;MACF;;MAEA;MACA,IAAIlB,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACiB,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;QAAA,IAAAI,iBAAA,EAAAC,kBAAA,EAAAC,kBAAA;QAC7D,QAAQT,KAAK,CAACU,IAAI;UAChB,KAAK,OAAO;YACV,MAAMC,UAAU,GAAG,4BAA4B;YAC/C,IAAI,CAACA,UAAU,CAACC,IAAI,CAAC1B,KAAK,CAAC,EAAE;cAC3BO,SAAS,CAACO,KAAK,CAACC,GAAG,CAAC,GAAG,oCAAoC;YAC7D;YACA;UACF,KAAK,KAAK;YACR,MAAMY,UAAU,GAAG,qCAAqC;YACxD,IAAI,CAACA,UAAU,CAACD,IAAI,CAAC1B,KAAK,CAAC,EAAE;cAC3BO,SAAS,CAACO,KAAK,CAACC,GAAG,CAAC,GAAG,oCAAoC;YAC7D;YACA;UACF,KAAK,KAAK;YACR,IAAI;cACF,IAAIa,GAAG,CAAC5B,KAAK,CAAC;YAChB,CAAC,CAAC,MAAM;cACNO,SAAS,CAACO,KAAK,CAACC,GAAG,CAAC,GAAG,0BAA0B;YACnD;YACA;UACF,KAAK,QAAQ;YACX,IAAID,KAAK,CAACe,UAAU,EAAE;cACpB,MAAMC,QAAQ,GAAGC,UAAU,CAAC/B,KAAK,CAAC;cAClC,IAAIc,KAAK,CAACe,UAAU,CAACG,GAAG,KAAKC,SAAS,IAAIH,QAAQ,GAAGhB,KAAK,CAACe,UAAU,CAACG,GAAG,EAAE;gBACzEzB,SAAS,CAACO,KAAK,CAACC,GAAG,CAAC,GAAG,0BAA0BD,KAAK,CAACe,UAAU,CAACG,GAAG,EAAE;cACzE;cACA,IAAIlB,KAAK,CAACe,UAAU,CAACK,GAAG,KAAKD,SAAS,IAAIH,QAAQ,GAAGhB,KAAK,CAACe,UAAU,CAACK,GAAG,EAAE;gBACzE3B,SAAS,CAACO,KAAK,CAACC,GAAG,CAAC,GAAG,yBAAyBD,KAAK,CAACe,UAAU,CAACK,GAAG,EAAE;cACxE;YACF;YACA;QACJ;;QAEA;QACA,KAAAb,iBAAA,GAAIP,KAAK,CAACe,UAAU,cAAAR,iBAAA,eAAhBA,iBAAA,CAAkBc,OAAO,EAAE;UAC7B,MAAMC,KAAK,GAAG,IAAIC,MAAM,CAACvB,KAAK,CAACe,UAAU,CAACM,OAAO,CAAC;UAClD,IAAI,CAACC,KAAK,CAACV,IAAI,CAAC1B,KAAK,CAAC,EAAE;YACtBO,SAAS,CAACO,KAAK,CAACC,GAAG,CAAC,GAAG,GAAGD,KAAK,CAACI,KAAK,oBAAoB;UAC3D;QACF;;QAEA;QACA,IAAI,CAAAI,kBAAA,GAAAR,KAAK,CAACe,UAAU,cAAAP,kBAAA,eAAhBA,kBAAA,CAAkBgB,SAAS,IAAItC,KAAK,CAACuC,MAAM,GAAGzB,KAAK,CAACe,UAAU,CAACS,SAAS,EAAE;UAC5E/B,SAAS,CAACO,KAAK,CAACC,GAAG,CAAC,GAAG,GAAGD,KAAK,CAACI,KAAK,qBAAqBJ,KAAK,CAACe,UAAU,CAACS,SAAS,aAAa;QACnG;QACA,IAAI,CAAAf,kBAAA,GAAAT,KAAK,CAACe,UAAU,cAAAN,kBAAA,eAAhBA,kBAAA,CAAkBiB,SAAS,IAAIxC,KAAK,CAACuC,MAAM,GAAGzB,KAAK,CAACe,UAAU,CAACW,SAAS,EAAE;UAC5EjC,SAAS,CAACO,KAAK,CAACC,GAAG,CAAC,GAAG,GAAGD,KAAK,CAACI,KAAK,oBAAoBJ,KAAK,CAACe,UAAU,CAACW,SAAS,aAAa;QAClG;MACF;IACF,CAAC,CAAC;;IAEF;IACA,IAAIrF,QAAQ,CAACsF,SAAS,IAAItF,QAAQ,CAACuF,cAAc,IAAIvF,QAAQ,CAACwF,WAAW,EAAE;MACzE,MAAMC,SAAS,GAAG,IAAIC,IAAI,CAAC1F,QAAQ,CAACwF,WAAW,CAAC;MAChD,MAAMG,YAAY,GAAG,IAAID,IAAI,CAAC1F,QAAQ,CAACuF,cAAc,CAAC;MACtD,IAAII,YAAY,IAAIF,SAAS,EAAE;QAC7BrC,SAAS,CAACmC,cAAc,GAAG,wCAAwC;MACrE;IACF;IAEA,OAAO;MACLhC,OAAO,EAAEqC,MAAM,CAACC,IAAI,CAACzC,SAAS,CAAC,CAACgC,MAAM,KAAK,CAAC;MAC5ClF,MAAM,EAAEkD;IACV,CAAC;EACH,CAAC;EAED,MAAM0C,YAAY,GAAG,MAAOlD,CAAC,IAAK;IAChCA,CAAC,CAACmD,cAAc,CAAC,CAAC;IAElB,MAAMrB,UAAU,GAAGvB,YAAY,CAAC,CAAC;IACjC,IAAI,CAACuB,UAAU,CAACnB,OAAO,EAAE;MACvBtB,OAAO,CAACuB,GAAG,CAAC,oBAAoB,EAAEkB,UAAU,CAACxE,MAAM,CAAC;MACpDC,SAAS,CAACuE,UAAU,CAACxE,MAAM,CAAC;MAC5B;IACF;IAEAM,aAAa,CAAC,IAAI,CAAC;IACnB,IAAI;MACF;MACA,MAAMwF,UAAU,GAAG;QACjB,GAAGhG,QAAQ;QACX;QACAmB,UAAU,EAAEmB,QAAQ,CAAC9C,gBAAgB,CAAC;QACtC6B,UAAU,EAAEiB,QAAQ,CAAC5C,gBAAgB,CAAC;QACtC4B,aAAa,EAAE1B,mBAAmB,GAAG0C,QAAQ,CAAC1C,mBAAmB,CAAC,GAAG,IAAI;QACzE;QACAqG,IAAI,EAAEjG,QAAQ,CAACiG,IAAI,IAAI,EAAE;QACzBC,YAAY,EAAElG,QAAQ,CAACkG,YAAY,IAAI,EAAE;QACzCC,MAAM,EAAEnG,QAAQ,CAACmG,MAAM,IAAI,IAAI;QAC/B;QACAC,gBAAgB,EAAEC,KAAK,CAACC,OAAO,CAACtG,QAAQ,CAACoG,gBAAgB,CAAC,GACtDpG,QAAQ,CAACoG,gBAAgB,GACzBpG,QAAQ,CAACoG,gBAAgB,GAAGpG,QAAQ,CAACoG,gBAAgB,CAACG,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,CAAC,IAAIA,CAAC,CAAC3C,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE;QAC5F4C,iBAAiB,EAAEL,KAAK,CAACC,OAAO,CAACtG,QAAQ,CAAC0G,iBAAiB,CAAC,GACxD1G,QAAQ,CAAC0G,iBAAiB,GAC1B1G,QAAQ,CAAC0G,iBAAiB,GAAG1G,QAAQ,CAAC0G,iBAAiB,CAACH,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,CAAC,IAAIA,CAAC,CAAC3C,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE;QAC9F6C,eAAe,EAAEN,KAAK,CAACC,OAAO,CAACtG,QAAQ,CAAC2G,eAAe,CAAC,GACpD3G,QAAQ,CAAC2G,eAAe,GACxB,EAAE;QACN;QACAC,cAAc,EAAE5G,QAAQ,CAAC4G,cAAc,IAAI,EAAE;QAC7CC,OAAO,EAAE7G,QAAQ,CAAC6G,OAAO,IAAI,EAAE;QAC/BC,YAAY,EAAE9G,QAAQ,CAAC8G,YAAY,IAAI,EAAE;QACzCC,aAAa,EAAE/G,QAAQ,CAAC+G,aAAa,IAAI,EAAE;QAC3CC,QAAQ,EAAEhH,QAAQ,CAACgH,QAAQ,IAAI,EAAE;QACjCC,OAAO,EAAEjH,QAAQ,CAACiH,OAAO,IAAI,EAAE;QAC/BC,WAAW,EAAElH,QAAQ,CAACkH,WAAW,IAAI,EAAE;QACvCC,sBAAsB,EAAEnH,QAAQ,CAACmH,sBAAsB,IAAI,EAAE;QAC7DC,MAAM,EAAEpH,QAAQ,CAACoH,MAAM,IAAI,EAAE;QAC7BC,OAAO,EAAErH,QAAQ,CAACqH,OAAO,IAAI,EAAE;QAC/BC,QAAQ,EAAEtH,QAAQ,CAACsH,QAAQ,IAAI,EAAE;QACjCC,oBAAoB,EAAEvH,QAAQ,CAACuH,oBAAoB,IAAI,EAAE;QACzDC,qBAAqB,EAAExH,QAAQ,CAACwH,qBAAqB,IAAI,EAAE;QAC3DC,WAAW,EAAEzH,QAAQ,CAACyH,WAAW,IAAI,EAAE;QACvCC,gBAAgB,EAAE1H,QAAQ,CAAC0H,gBAAgB,IAAI,EAAE;QACjDC,oBAAoB,EAAE3H,QAAQ,CAAC2H,oBAAoB,IAAI,EAAE;QACzDC,cAAc,EAAE5H,QAAQ,CAAC4H,cAAc,IAAI,EAAE;QAC7CC,UAAU,EAAE7H,QAAQ,CAAC6H,UAAU,IAAI,EAAE;QACrCC,aAAa,EAAE9H,QAAQ,CAAC8H,aAAa,IAAI,EAAE;QAC3CC,iBAAiB,EAAE/H,QAAQ,CAAC+H,iBAAiB,IAAI,EAAE;QACnDC,eAAe,EAAEhI,QAAQ,CAACgI,eAAe,IAAI;MAC/C,CAAC;;MAED;MACA/F,OAAO,CAACuB,GAAG,CAAC,yBAAyB,EAAEwC,UAAU,CAAC;MAClD/D,OAAO,CAACuB,GAAG,CAAC,oBAAoB,EAAEhE,gBAAgB,CAAC;MACnDyC,OAAO,CAACuB,GAAG,CAAC,oBAAoB,EAAE9D,gBAAgB,CAAC;MACnDuC,OAAO,CAACuB,GAAG,CAAC,uBAAuB,EAAE5D,mBAAmB,CAAC;;MAEzD;MACA,MAAMqI,mBAAmB,GAAG;QAC1B9G,UAAU,EAAE6E,UAAU,CAAC7E,UAAU;QACjCE,UAAU,EAAE2E,UAAU,CAAC3E,UAAU;QACjC4E,IAAI,EAAED,UAAU,CAACC,IAAI;QACrBC,YAAY,EAAEF,UAAU,CAACE,YAAY;QACrCC,MAAM,EAAEH,UAAU,CAACG;MACrB,CAAC;MACDlE,OAAO,CAACuB,GAAG,CAAC,wBAAwB,EAAEyE,mBAAmB,CAAC;;MAE1D;MACA,MAAMC,aAAa,GAAGtC,MAAM,CAACuC,OAAO,CAACF,mBAAmB,CAAC,CACtDG,MAAM,CAAC,CAAC,GAAGvF,KAAK,CAAC,KAAK,CAACA,KAAK,IAAIA,KAAK,KAAK,EAAE,CAAC,CAC7C2D,GAAG,CAAC,CAAC,CAAC5C,GAAG,CAAC,KAAKA,GAAG,CAAC;MAEtB,IAAIsE,aAAa,CAAC9C,MAAM,GAAG,CAAC,EAAE;QAC5BnD,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEkG,aAAa,CAAC;QACxDG,KAAK,CAAC,2BAA2B,GAAGH,aAAa,CAACI,IAAI,CAAC,IAAI,CAAC,CAAC;QAC7D;MACF;MAEA,IAAIC,MAAM;MACV,IAAIvJ,IAAI,KAAK,QAAQ,EAAE;QACrBuJ,MAAM,GAAG,MAAM/J,UAAU,CAACgK,YAAY,CAACxC,UAAU,CAAC;MACpD,CAAC,MAAM;QACLuC,MAAM,GAAG,MAAM/J,UAAU,CAACiK,YAAY,CAAC1J,WAAW,CAAC2J,EAAE,EAAE1C,UAAU,CAAC;MACpE;MAEAnH,QAAQ,CAAC0J,MAAM,CAAC;IAClB,CAAC,CAAC,OAAOvG,KAAK,EAAE;MAAA,IAAA2G,WAAA;MACd1G,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9CC,OAAO,CAACD,KAAK,CAAC,aAAa,EAAEA,KAAK,CAACD,IAAI,CAAC;MACxCE,OAAO,CAACD,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAE1C,KAAA2G,WAAA,GAAI3G,KAAK,CAACD,IAAI,cAAA4G,WAAA,eAAVA,WAAA,CAAYzI,MAAM,EAAE;QACtB;QACA,MAAM0I,aAAa,GAAG,CAAC,CAAC;QACxBhD,MAAM,CAACC,IAAI,CAAC7D,KAAK,CAACD,IAAI,CAAC7B,MAAM,CAAC,CAACwD,OAAO,CAACE,GAAG,IAAI;UAC5C,MAAMiF,aAAa,GAAG7G,KAAK,CAACD,IAAI,CAAC7B,MAAM,CAAC0D,GAAG,CAAC;UAC5CgF,aAAa,CAAChF,GAAG,CAACkF,WAAW,CAAC,CAAC,CAAC,GAAGzC,KAAK,CAACC,OAAO,CAACuC,aAAa,CAAC,GAC3DA,aAAa,CAACP,IAAI,CAAC,IAAI,CAAC,GACxBO,aAAa;QACnB,CAAC,CAAC;QACF5G,OAAO,CAACuB,GAAG,CAAC,4BAA4B,EAAEoF,aAAa,CAAC;QACxDzI,SAAS,CAACyI,aAAa,CAAC;MAC1B,CAAC,MAAM,IAAI5G,KAAK,CAAC+G,iBAAiB,IAAI/G,KAAK,CAAC+G,iBAAiB,CAAC,CAAC,EAAE;QAC/D5I,SAAS,CAAC6B,KAAK,CAACgH,mBAAmB,CAAC,CAAC,CAAC;MACxC,CAAC,MAAM;QAAA,IAAAC,YAAA,EAAAC,YAAA;QACL,MAAMC,YAAY,GAAG,EAAAF,YAAA,GAAAjH,KAAK,CAACD,IAAI,cAAAkH,YAAA,uBAAVA,YAAA,CAAYnI,OAAO,OAAAoI,YAAA,GACrBlH,KAAK,CAACD,IAAI,cAAAmH,YAAA,uBAAVA,YAAA,CAAYE,KAAK,KACjBpH,KAAK,CAAClB,OAAO,IACb,QAAQkB,KAAK,CAACqH,MAAM,IAAI,SAAS,mCAAmC;QACvFpH,OAAO,CAACuB,GAAG,CAAC,wBAAwB,EAAE2F,YAAY,CAAC;QACnDhJ,SAAS,CAAC;UAAEmJ,OAAO,EAAEH;QAAa,CAAC,CAAC;MACtC;IACF,CAAC,SAAS;MACR3I,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;EAED,MAAMyD,eAAe,GAAIN,KAAK,IAAK;IACjC,IAAI,CAACA,KAAK,CAACK,WAAW,EAAE,OAAO,IAAI;IAEnC,MAAMuF,cAAc,GAAGvJ,QAAQ,CAAC2D,KAAK,CAACK,WAAW,CAACL,KAAK,CAAC;IACxD,MAAM6F,aAAa,GAAG7F,KAAK,CAACK,WAAW,CAACnB,KAAK;;IAE7C;IACA,IAAI,OAAO2G,aAAa,KAAK,SAAS,IAAIA,aAAa,KAAK,MAAM,IAAIA,aAAa,KAAK,OAAO,EAAE;MAC/F,OAAOD,cAAc,MAAMC,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,MAAM,CAAC;IAChF;IAEA,OAAOD,cAAc,KAAKC,aAAa;EACzC,CAAC;EAED,MAAMC,qBAAqB,GAAGA,CAAA,KAAM;IAClC,IAAI,CAAC3J,UAAU,IAAI,CAACA,UAAU,CAAC2D,MAAM,EAAE,OAAO,CAAC,CAAC;;IAEhD;IACA,MAAMiG,SAAS,GAAG5J,UAAU,CAAC2D,MAAM,CAAC+C,GAAG,CAAC7C,KAAK,IAAIA,KAAK,CAACC,GAAG,CAAC;IAC3D,MAAM+F,aAAa,GAAGD,SAAS,CAACtB,MAAM,CAAC,CAACxE,GAAG,EAAEgG,KAAK,KAAKF,SAAS,CAACG,OAAO,CAACjG,GAAG,CAAC,KAAKgG,KAAK,CAAC;IACxF,IAAID,aAAa,CAACvE,MAAM,GAAG,CAAC,EAAE;MAC5BnD,OAAO,CAAC6H,IAAI,CAAC,6BAA6B,EAAEH,aAAa,CAAC;MAC1D1H,OAAO,CAACuB,GAAG,CAAC,aAAa,EAAE1D,UAAU,CAAC2D,MAAM,CAAC;IAC/C;IAEA,MAAMsG,QAAQ,GAAG,CAAC,CAAC;IACnBjK,UAAU,CAAC2D,MAAM,CAACC,OAAO,CAAEC,KAAK,IAAK;MACnC,MAAMqG,UAAU,GAAGrG,KAAK,CAACsG,OAAO,IAAI,SAAS;MAC7C,IAAI,CAACF,QAAQ,CAACC,UAAU,CAAC,EAAE;QACzBD,QAAQ,CAACC,UAAU,CAAC,GAAG;UACrBZ,KAAK,EAAEc,eAAe,CAACF,UAAU,CAAC;UAClCvG,MAAM,EAAE;QACV,CAAC;MACH;MACAsG,QAAQ,CAACC,UAAU,CAAC,CAACvG,MAAM,CAAC0G,IAAI,CAACxG,KAAK,CAAC;IACzC,CAAC,CAAC;IAEF,OAAOoG,QAAQ;EACjB,CAAC;EAED,MAAMG,eAAe,GAAIF,UAAU,IAAK;IACtC,MAAMI,MAAM,GAAG;MACbC,YAAY,EAAE,sBAAsB;MACpCC,WAAW,EAAE,qBAAqB;MAClCC,YAAY,EAAE,sBAAsB;MACpCC,YAAY,EAAE,sBAAsB;MACpCC,aAAa,EAAE,uBAAuB;MACtCC,eAAe,EAAE,kBAAkB;MACnCC,WAAW,EAAE,qBAAqB;MAClCC,gBAAgB,EAAE,mBAAmB;MACrCC,aAAa,EAAE,uBAAuB;MACtCvB,OAAO,EAAE;IACX,CAAC;IACD,OAAOc,MAAM,CAACJ,UAAU,CAAC,IAAIA,UAAU;EACzC,CAAC;EAED,MAAMD,QAAQ,GAAGjK,UAAU,GAAG2J,qBAAqB,CAAC,CAAC,GAAG,CAAC,CAAC;EAI1D,oBACE9K,OAAA;IAAKmM,SAAS,EAAC,qBAAqB;IAAAC,QAAA,gBAClCpM,OAAA;MAAKmM,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BpM,OAAA;QAAAoM,QAAA,EAAK/L,IAAI,KAAK,QAAQ,GAAG,mBAAmB,GAAG;MAAa;QAAAgM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,EACjErL,UAAU,iBACTnB,OAAA;QAAKmM,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBpM,OAAA;UAAMmM,SAAS,EAAC,WAAW;UAAAC,QAAA,EAAEjL,UAAU,CAACmG;QAAI;UAAA+E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,EACnDrL,UAAU,CAACsL,WAAW,iBACrBzM,OAAA;UAAMmM,SAAS,EAAC,kBAAkB;UAAAC,QAAA,EAAEjL,UAAU,CAACsL;QAAW;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAClE;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAELjL,MAAM,CAACoJ,OAAO,iBACb3K,OAAA;MAAKmM,SAAS,EAAC,mBAAmB;MAAAC,QAAA,EAAE7K,MAAM,CAACoJ;IAAO;MAAA0B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CACzD,eAEDxM,OAAA;MAAME,QAAQ,EAAEiH,YAAa;MAACgF,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAEnDpM,OAAA;QAAKmM,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BpM,OAAA;UAAAoM,QAAA,EAAI;QAA6B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAGtCxM,OAAA;UAAKmM,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBpM,OAAA;YAAOmM,SAAS,EAAC,YAAY;YAAAC,QAAA,GAAC,WACnB,eAAApM,OAAA;cAAMmM,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC,eACRxM,OAAA;YACEkE,KAAK,EAAErD,gBAAiB;YACxB6L,QAAQ,EAAE1I,oBAAqB;YAC/B2I,QAAQ,EAAElL,OAAO,CAAClB,SAAU;YAC5B4L,SAAS,EAAE,eAAe5K,MAAM,CAACmD,QAAQ,GAAG,OAAO,GAAG,EAAE,EAAG;YAC3DQ,QAAQ;YAAAkH,QAAA,gBAERpM,OAAA;cAAQkE,KAAK,EAAC,EAAE;cAAAkI,QAAA,EACb3K,OAAO,CAAClB,SAAS,GAAG,sBAAsB,GAAG;YAAiB;cAAA8L,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzD,CAAC,EACRjM,SAAS,CAACsH,GAAG,CAACnD,QAAQ,iBACrB1E,OAAA;cAA0BkE,KAAK,EAAEQ,QAAQ,CAACqF,EAAG;cAAAqC,QAAA,EAC1C1H,QAAQ,CAAC4C;YAAI,GADH5C,QAAQ,CAACqF,EAAE;cAAAsC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEhB,CACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,EACRjL,MAAM,CAACmD,QAAQ,iBACd1E,OAAA;YAAKmM,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAE7K,MAAM,CAACmD;UAAQ;YAAA2H,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACtD,EACAjL,MAAM,CAAChB,SAAS,iBACfP,OAAA;YAAKmM,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAE7K,MAAM,CAAChB;UAAS;YAAA8L,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACvD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGNxM,OAAA;UAAKmM,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBpM,OAAA;YAAOmM,SAAS,EAAC,YAAY;YAAAC,QAAA,GAAC,WACnB,eAAApM,OAAA;cAAMmM,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC,eACRxM,OAAA;YACEkE,KAAK,EAAEnD,gBAAiB;YACxB2L,QAAQ,EAAEtI,oBAAqB;YAC/BuI,QAAQ,EAAE,CAAC9L,gBAAgB,IAAIY,OAAO,CAAChB,UAAW;YAClD0L,SAAS,EAAE,eAAe5K,MAAM,CAACoD,QAAQ,GAAG,OAAO,GAAG,EAAE,EAAG;YAC3DO,QAAQ;YAAAkH,QAAA,gBAERpM,OAAA;cAAQkE,KAAK,EAAC,EAAE;cAAAkI,QAAA,EACb,CAACvL,gBAAgB,GACd,uBAAuB,GACvBY,OAAO,CAAChB,UAAU,GAChB,uBAAuB,GACvB;YAAiB;cAAA4L,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEjB,CAAC,EACR/L,UAAU,CAACoH,GAAG,CAAClD,QAAQ,iBACtB3E,OAAA;cAA0BkE,KAAK,EAAES,QAAQ,CAACoF,EAAG;cAAAqC,QAAA,EAC1CzH,QAAQ,CAAC2C;YAAI,GADH3C,QAAQ,CAACoF,EAAE;cAAAsC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEhB,CACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,EACRjL,MAAM,CAACoD,QAAQ,iBACd3E,OAAA;YAAKmM,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAE7K,MAAM,CAACoD;UAAQ;YAAA0H,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACtD,EACAjL,MAAM,CAACd,UAAU,iBAChBT,OAAA;YAAKmM,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAE7K,MAAM,CAACd;UAAU;YAAA4L,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACxD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,EAGL1K,gBAAgB,CAACI,uBAAuB,iBACvClC,OAAA;UAAKmM,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBpM,OAAA;YAAOmM,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAE9B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRxM,OAAA;YACEkE,KAAK,EAAEjD,mBAAoB;YAC3ByL,QAAQ,EAAErI,uBAAwB;YAClCsI,QAAQ,EAAE,CAAC5L,gBAAgB,IAAIU,OAAO,CAACd,aAAc;YACrDwL,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAEvBpM,OAAA;cAAQkE,KAAK,EAAC,EAAE;cAAAkI,QAAA,EACb,CAACrL,gBAAgB,GACd,uBAAuB,GACvBU,OAAO,CAACd,aAAa,GACnB,0BAA0B,GAC1B;YAA+B;cAAA0L,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAE/B,CAAC,EACR7L,aAAa,CAACkH,GAAG,CAAC+E,WAAW,iBAC5B5M,OAAA;cAA6BkE,KAAK,EAAE0I,WAAW,CAAC7C,EAAG;cAAAqC,QAAA,EAChDQ,WAAW,CAACtF;YAAI,GADNsF,WAAW,CAAC7C,EAAE;cAAAsC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEnB,CACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,EACRjL,MAAM,CAACZ,aAAa,iBACnBX,OAAA;YAAKmM,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAE7K,MAAM,CAACZ;UAAa;YAAA0L,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAC3D;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN,EAGA/K,OAAO,CAACE,IAAI,iBACX3B,OAAA;UAAKmM,SAAS,EAAC,iBAAiB;UAAAC,QAAA,eAC9BpM,OAAA;YAAAoM,QAAA,EAAM;UAA6B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CACN,EAEA1K,gBAAgB,CAACK,OAAO,IAAI,CAACV,OAAO,CAACE,IAAI,iBACxC3B,OAAA;UAAKmM,SAAS,EAAE,kBACdrK,gBAAgB,CAACE,eAAe,IAAIF,gBAAgB,CAACG,kBAAkB,GACnE,SAAS,GACTH,gBAAgB,CAACK,OAAO,CAAC0K,QAAQ,CAAC,SAAS,CAAC,GAC1C,OAAO,GACP,MAAM,EACX;UAAAT,QAAA,EACAtK,gBAAgB,CAACK;QAAO;UAAAkK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CACN,EAEAjL,MAAM,CAACI,IAAI,iBACV3B,OAAA;UAAKmM,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAE7K,MAAM,CAACI;QAAI;UAAA0K,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAClD;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,EAGLrL,UAAU,IAAI8F,MAAM,CAACuC,OAAO,CAAC4B,QAAQ,CAAC,CAACvD,GAAG,CAAC,CAAC,CAACwD,UAAU,EAAEC,OAAO,CAAC,kBAChEtL,OAAA;QAAsBmM,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC5CpM,OAAA;UAAAoM,QAAA,EAAKd,OAAO,CAACb;QAAK;UAAA4B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACxBxM,OAAA;UAAKmM,SAAS,EAAC,aAAa;UAAAC,QAAA,EACzBd,OAAO,CAACxG,MAAM,CACZ2E,MAAM,CAACzE,KAAK,IAAIM,eAAe,CAACN,KAAK,CAAC,CAAC,CACvC6C,GAAG,CAAC,CAAC7C,KAAK,EAAE8H,UAAU,kBACrB9M,OAAA,CAACF,SAAS;YAERkF,KAAK,EAAEA,KAAM;YACbd,KAAK,EAAE7C,QAAQ,CAAC2D,KAAK,CAACC,GAAG,CAAE;YAC3ByH,QAAQ,EAAGxI,KAAK,IAAKI,iBAAiB,CAACU,KAAK,CAACC,GAAG,EAAEf,KAAK,CAAE;YACzDb,KAAK,EAAE9B,MAAM,CAACyD,KAAK,CAACC,GAAG;UAAE,GAJpB,GAAGoG,UAAU,IAAIrG,KAAK,CAACC,GAAG,IAAI6H,UAAU,EAAE;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAKhD,CACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA,GAdEnB,UAAU;QAAAgB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAef,CACN,CAAC,eAKFxM,OAAA;QAAKmM,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BpM,OAAA;UACE0F,IAAI,EAAC,QAAQ;UACbqH,OAAO,EAAE5M,QAAS;UAClBgM,SAAS,EAAC,iBAAiB;UAC3BQ,QAAQ,EAAE/K,UAAW;UAAAwK,QAAA,EACtB;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTxM,OAAA;UACE0F,IAAI,EAAC,QAAQ;UACbyG,SAAS,EAAC,iBAAiB;UAC3BQ,QAAQ,EAAE/K,UAAU,IAAI,CAACf,gBAAgB,IAAI,CAACE,gBAAiB;UAAAqL,QAAA,EAE9DxK,UAAU,GACNvB,IAAI,KAAK,QAAQ,GAAG,aAAa,GAAG,aAAa,GACjDA,IAAI,KAAK,QAAQ,GAAG,eAAe,GAAG;QAAgB;UAAAgM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAErD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAAClM,EAAA,CA7uBIL,iBAAiB;AAAA+M,EAAA,GAAjB/M,iBAAiB;AA+uBvB,eAAeA,iBAAiB;AAAC,IAAA+M,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}