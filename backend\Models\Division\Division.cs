using System.Text.Json.Serialization;

namespace CrmApi.Models.Division
{
    public class Division
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
        
        [JsonIgnore]
        public ICollection<Category.Category> Categories { get; set; } = new List<Category.Category>();
    }
}
