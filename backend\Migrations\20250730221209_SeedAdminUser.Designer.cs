﻿// <auto-generated />
using System;
using CrmApi.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace CrmApi.Migrations
{
    [DbContext(typeof(CrmDbContext))]
    [Migration("20250730221209_SeedAdminUser")]
    partial class SeedAdminUser
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.0")
                .HasAnnotation("Relational:MaxIdentifierLength", 64);

            modelBuilder.Entity("CrmApi.Models.Admin.Admin", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("created_at");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)")
                        .HasColumnName("email");

                    b.Property<string>("FirstName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)")
                        .HasColumnName("first_name");

                    b.Property<bool>("IsActive")
                        .HasColumnType("tinyint(1)")
                        .HasColumnName("is_active");

                    b.Property<DateTime?>("LastLoginAt")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("last_login_at");

                    b.Property<string>("LastName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)")
                        .HasColumnName("last_name");

                    b.Property<string>("PasswordHash")
                        .IsRequired()
                        .HasColumnType("longtext")
                        .HasColumnName("password_hash");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("updated_at");

                    b.Property<string>("Username")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)")
                        .HasColumnName("username");

                    b.HasKey("Id");

                    b.HasIndex("Email")
                        .IsUnique();

                    b.HasIndex("Username")
                        .IsUnique();

                    b.ToTable("admins", (string)null);
                });

            modelBuilder.Entity("CrmApi.Models.Category.Category", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("created_at");

                    b.Property<int>("DivisionId")
                        .HasColumnType("int")
                        .HasColumnName("division_id");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)")
                        .HasColumnName("name");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("updated_at");

                    b.HasKey("Id");

                    b.HasIndex("DivisionId", "Name")
                        .IsUnique();

                    b.ToTable("categories", (string)null);
                });

            modelBuilder.Entity("CrmApi.Models.Division.Division", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("created_at");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)")
                        .HasColumnName("name");

                    b.Property<int?>("StateId")
                        .HasColumnType("int");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("updated_at");

                    b.HasKey("Id");

                    b.HasIndex("Name")
                        .IsUnique();

                    b.HasIndex("StateId");

                    b.ToTable("divisions", (string)null);
                });

            modelBuilder.Entity("CrmApi.Models.Person.Person", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id");

                    b.Property<string>("Address")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("varchar(500)")
                        .HasColumnName("address");

                    b.Property<string>("AlternateEmailIds")
                        .IsRequired()
                        .HasColumnType("longtext")
                        .HasColumnName("alternate_email_ids");

                    b.Property<string>("AlternateNumbers")
                        .IsRequired()
                        .HasColumnType("longtext")
                        .HasColumnName("alternate_numbers");

                    b.Property<string>("AssociateMobile")
                        .IsRequired()
                        .HasMaxLength(15)
                        .HasColumnType("varchar(15)")
                        .HasColumnName("associate_mobile");

                    b.Property<string>("AssociateName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)")
                        .HasColumnName("associate_name");

                    b.Property<string>("AssociateRelation")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)")
                        .HasColumnName("associate_relation");

                    b.Property<string>("AuthorizedPersonEmail")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)")
                        .HasColumnName("authorized_person_email");

                    b.Property<string>("AuthorizedPersonName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)")
                        .HasColumnName("authorized_person_name");

                    b.Property<string>("CRMAppLink")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("varchar(500)")
                        .HasColumnName("crm_app_link");

                    b.Property<int>("CategoryId")
                        .HasColumnType("int")
                        .HasColumnName("category_id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("created_at");

                    b.Property<DateTime?>("DateOfBirth")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("date_of_birth");

                    b.Property<DateTime?>("DateOfMarriage")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("date_of_marriage");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("deleted_at");

                    b.Property<string>("Department")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)")
                        .HasColumnName("department");

                    b.Property<string>("Designation")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)")
                        .HasColumnName("designation");

                    b.Property<string>("District")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)")
                        .HasColumnName("district");

                    b.Property<int>("DivisionId")
                        .HasColumnType("int")
                        .HasColumnName("division_id");

                    b.Property<string>("DomesticState")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)")
                        .HasColumnName("domestic_state");

                    b.Property<string>("FirmName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)")
                        .HasColumnName("firm_name");

                    b.Property<int?>("Gender")
                        .HasColumnType("int")
                        .HasColumnName("gender");

                    b.Property<bool>("HasAssociate")
                        .HasColumnType("tinyint(1)")
                        .HasColumnName("has_associate");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("tinyint(1)")
                        .HasColumnName("is_deleted");

                    b.Property<bool?>("IsMarried")
                        .HasColumnType("tinyint(1)")
                        .HasColumnName("is_married");

                    b.Property<string>("MarketingContact")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)")
                        .HasColumnName("marketing_contact");

                    b.Property<string>("MarketingDesignation")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)")
                        .HasColumnName("marketing_designation");

                    b.Property<string>("MobileNumber")
                        .IsRequired()
                        .HasMaxLength(15)
                        .HasColumnType("varchar(15)")
                        .HasColumnName("mobile_number");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)")
                        .HasColumnName("name");

                    b.Property<int>("Nature")
                        .HasColumnType("int")
                        .HasColumnName("nature");

                    b.Property<int?>("NumberOfBranches")
                        .HasColumnType("int")
                        .HasColumnName("number_of_branches");

                    b.Property<int?>("NumberOfOffices")
                        .HasColumnType("int")
                        .HasColumnName("number_of_offices");

                    b.Property<string>("PlaceOfPosting")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("varchar(200)")
                        .HasColumnName("place_of_posting");

                    b.Property<string>("PrimaryEmailId")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)")
                        .HasColumnName("primary_email_id");

                    b.Property<string>("RERARegistrationNumber")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)")
                        .HasColumnName("rera_registration_number");

                    b.Property<string>("Remarks")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("varchar(1000)")
                        .HasColumnName("remarks");

                    b.Property<string>("Source")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("varchar(200)")
                        .HasColumnName("source");

                    b.Property<int?>("StarRating")
                        .HasColumnType("int")
                        .HasColumnName("star_rating");

                    b.Property<int?>("SubCategoryId")
                        .HasColumnType("int")
                        .HasColumnName("sub_category_id");

                    b.Property<int?>("TotalEmployeeStrength")
                        .HasColumnType("int")
                        .HasColumnName("total_employee_strength");

                    b.Property<decimal?>("TransactionValue")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("transaction_value");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("updated_at");

                    b.Property<bool>("UsingCRMApp")
                        .HasColumnType("tinyint(1)")
                        .HasColumnName("using_crm_app");

                    b.Property<bool>("UsingWebsite")
                        .HasColumnType("tinyint(1)")
                        .HasColumnName("using_website");

                    b.Property<string>("Website")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("varchar(500)")
                        .HasColumnName("website");

                    b.Property<string>("WebsiteLink")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("varchar(500)")
                        .HasColumnName("website_link");

                    b.Property<string>("WorkingArea")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("varchar(200)")
                        .HasColumnName("working_area");

                    b.Property<string>("WorkingProfiles")
                        .IsRequired()
                        .HasColumnType("longtext")
                        .HasColumnName("working_profiles");

                    b.Property<string>("WorkingState")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)")
                        .HasColumnName("working_state");

                    b.HasKey("Id");

                    b.HasIndex("CategoryId")
                        .HasDatabaseName("IX_Persons_CategoryId");

                    b.HasIndex("CreatedAt")
                        .HasDatabaseName("IX_Persons_CreatedAt");

                    b.HasIndex("District")
                        .HasDatabaseName("IX_Persons_District");

                    b.HasIndex("DivisionId")
                        .HasDatabaseName("IX_Persons_DivisionId");

                    b.HasIndex("IsDeleted")
                        .HasDatabaseName("IX_Persons_IsDeleted");

                    b.HasIndex("MobileNumber")
                        .HasDatabaseName("IX_Persons_MobileNumber");

                    b.HasIndex("Name")
                        .HasDatabaseName("IX_Persons_Name");

                    b.HasIndex("Nature")
                        .HasDatabaseName("IX_Persons_Nature");

                    b.HasIndex("PrimaryEmailId")
                        .HasDatabaseName("IX_Persons_PrimaryEmailId");

                    b.HasIndex("StarRating")
                        .HasDatabaseName("IX_Persons_StarRating");

                    b.HasIndex("SubCategoryId")
                        .HasDatabaseName("IX_Persons_SubCategoryId");

                    b.HasIndex("WorkingState")
                        .HasDatabaseName("IX_Persons_WorkingState");

                    b.HasIndex("DivisionId", "CategoryId")
                        .HasDatabaseName("IX_Persons_Division_Category");

                    b.HasIndex("IsDeleted", "CreatedAt")
                        .HasDatabaseName("IX_Persons_IsDeleted_CreatedAt");

                    b.HasIndex("Nature", "IsDeleted")
                        .HasDatabaseName("IX_Persons_Nature_IsDeleted");

                    b.HasIndex("DivisionId", "CategoryId", "MobileNumber")
                        .HasDatabaseName("IX_Persons_Division_Category_Mobile");

                    b.HasIndex("MobileNumber", "DivisionId", "CategoryId", "IsDeleted")
                        .IsUnique()
                        .HasDatabaseName("UX_Persons_Mobile_Division_Category_NotDeleted")
                        .HasFilter("is_deleted = 0");

                    b.ToTable("persons", (string)null);
                });

            modelBuilder.Entity("CrmApi.Models.State.State", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id");

                    b.Property<string>("Capital")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)")
                        .HasColumnName("capital");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("varchar(10)")
                        .HasColumnName("code");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("created_at");

                    b.Property<bool>("IsActive")
                        .HasColumnType("tinyint(1)")
                        .HasColumnName("is_active");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)")
                        .HasColumnName("name");

                    b.Property<string>("Region")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)")
                        .HasColumnName("region");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("updated_at");

                    b.HasKey("Id");

                    b.HasIndex("Code")
                        .IsUnique();

                    b.HasIndex("Name")
                        .IsUnique();

                    b.ToTable("states", (string)null);
                });

            modelBuilder.Entity("CrmApi.Models.SubCategory.SubCategory", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id");

                    b.Property<int>("CategoryId")
                        .HasColumnType("int")
                        .HasColumnName("category_id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("created_at");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)")
                        .HasColumnName("name");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("updated_at");

                    b.HasKey("Id");

                    b.HasIndex("CategoryId", "Name")
                        .IsUnique();

                    b.ToTable("sub_categories", (string)null);
                });

            modelBuilder.Entity("CrmApi.Models.Category.Category", b =>
                {
                    b.HasOne("CrmApi.Models.Division.Division", "Division")
                        .WithMany("Categories")
                        .HasForeignKey("DivisionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Division");
                });

            modelBuilder.Entity("CrmApi.Models.Division.Division", b =>
                {
                    b.HasOne("CrmApi.Models.State.State", null)
                        .WithMany("Divisions")
                        .HasForeignKey("StateId");
                });

            modelBuilder.Entity("CrmApi.Models.Person.Person", b =>
                {
                    b.HasOne("CrmApi.Models.Category.Category", "Category")
                        .WithMany()
                        .HasForeignKey("CategoryId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("CrmApi.Models.Division.Division", "Division")
                        .WithMany()
                        .HasForeignKey("DivisionId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("CrmApi.Models.SubCategory.SubCategory", "SubCategory")
                        .WithMany()
                        .HasForeignKey("SubCategoryId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("Category");

                    b.Navigation("Division");

                    b.Navigation("SubCategory");
                });

            modelBuilder.Entity("CrmApi.Models.SubCategory.SubCategory", b =>
                {
                    b.HasOne("CrmApi.Models.Category.Category", "Category")
                        .WithMany("SubCategories")
                        .HasForeignKey("CategoryId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Category");
                });

            modelBuilder.Entity("CrmApi.Models.Category.Category", b =>
                {
                    b.Navigation("SubCategories");
                });

            modelBuilder.Entity("CrmApi.Models.Division.Division", b =>
                {
                    b.Navigation("Categories");
                });

            modelBuilder.Entity("CrmApi.Models.State.State", b =>
                {
                    b.Navigation("Divisions");
                });
#pragma warning restore 612, 618
        }
    }
}
