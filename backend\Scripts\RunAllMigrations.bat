@echo off
cls
echo === CRM Database Migration Script ===
echo This script will create the complete database schema and insert initial data
echo.

cd /d "%~dp0\.."
echo Current directory: %CD%
echo.

echo Checking Entity Framework Core tools...
dotnet ef --version >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo Installing Entity Framework Core tools...
    dotnet tool install --global dotnet-ef
    if %ERRORLEVEL% neq 0 (
        echo Failed to install EF tools. Please install manually.
        pause
        exit /b 1
    )
    echo Entity Framework Core tools installed successfully
) else (
    echo Entity Framework Core tools found
)
echo.

echo Building the project...
dotnet build --configuration Release

if %ERRORLEVEL% neq 0 (
    echo.
    echo Build failed. Please fix compilation errors first.
    echo.
    echo Common issues:
    echo - Missing using statements
    echo - Syntax errors in migration files
    echo - Missing references
    pause
    exit /b 1
)
echo Project built successfully
echo.

echo Checking current database status...
dotnet ef migrations list >nul 2>&1
echo.

echo Do you want to drop and recreate the database? (This will delete all existing data)
set /p response="Enter 'yes' to drop and recreate, or 'no' to update existing database: "
if /i "%response%"=="yes" (
    echo Dropping existing database...
    dotnet ef database drop --force
    echo Database dropped
) else if /i "%response%"=="y" (
    echo Dropping existing database...
    dotnet ef database drop --force
    echo Database dropped
)
echo.

echo Applying migrations to database...
dotnet ef database update

if %ERRORLEVEL% equ 0 (
    echo.
    echo === MIGRATION COMPLETED SUCCESSFULLY! ===
    echo.
    echo Database Schema Created:
    echo - States table with all 36 Indian states and union territories
    echo - Divisions table with sample business divisions
    echo - Categories table with sample categories
    echo - SubCategories table with sample subcategories
    echo - Persons table with comprehensive person data structure
    echo.
    echo Features Included:
    echo - Complete hierarchical structure (Division → Category → SubCategory)
    echo - Comprehensive person data model with all fields
    echo - Proper foreign key relationships and constraints
    echo - Optimized indexes for performance
    echo - Unique constraints for data integrity
    echo - Soft delete support for persons
    echo - Audit fields (created_at, updated_at)
    echo.
    echo Sample Data Inserted:
    echo - 36 Indian states and union territories
    echo - 10 business divisions (Real Estate, Construction, Finance, etc.)
    echo - 15 categories across different divisions
    echo - 10 subcategories for detailed classification
    echo.
    echo Next Steps:
    echo 1. Start the API server: dotnet run
    echo 2. Test the endpoints using Swagger UI
    echo 3. Use the frontend to create and manage persons
    echo 4. Import bulk data using the import functionality
    echo.
    echo API Endpoints Available:
    echo • GET /api/states - List all states
    echo • GET /api/divisions - List all divisions
    echo • GET /api/categories - List categories
    echo • GET /api/subcategories - List subcategories
    echo • POST /api/persons - Create new person
    echo • GET /api/persons/search - Search persons
    echo • POST /api/import-export/persons/import - Import persons from file
    echo.
) else (
    echo.
    echo === MIGRATION FAILED ===
    echo.
    echo Common issues and solutions:
    echo.
    echo 1. Connection String Issues:
    echo    - Check appsettings.json for correct database connection
    echo    - Ensure SQL Server is running
    echo    - Verify database permissions
    echo.
    echo 2. Migration Conflicts:
    echo    - Remove Migrations folder and recreate: dotnet ef migrations add InitialCreate
    echo    - Check for duplicate migration files
    echo.
    echo 3. Model Issues:
    echo    - Verify all model classes are properly configured
    echo    - Check DbContext configuration
    echo.
    echo 4. Database Issues:
    echo    - Try dropping the database: dotnet ef database drop
    echo    - Check SQL Server logs for detailed errors
    echo.
    pause
    exit /b 1
)

echo Migration script completed successfully!
pause
