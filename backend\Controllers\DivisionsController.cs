using Microsoft.AspNetCore.Mvc;
using CrmApi.Models.Division;
using CrmApi.Services.Division;

namespace CrmApi.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class DivisionsController : ControllerBase
    {
        private readonly IDivisionService _divisionService;

        public DivisionsController(IDivisionService divisionService)
        {
            _divisionService = divisionService;
        }

        [HttpGet]
        public async Task<ActionResult<IEnumerable<DivisionResponse>>> GetDivisions()
        {
            var divisions = await _divisionService.GetAllDivisionsAsync();
            return Ok(divisions);
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<DivisionResponse>> GetDivision(int id)
        {
            var division = await _divisionService.GetDivisionWithCategoriesAsync(id);

            if (division == null)
            {
                return NotFound();
            }

            return Ok(division);
        }

        [HttpPost]
        public async Task<ActionResult<DivisionResponse>> PostDivision([FromBody] CreateDivisionRequest request)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var division = await _divisionService.CreateDivisionAsync(request);
            return CreatedAtAction("GetDivision", new { id = division.Id }, division);
        }

        [HttpPut("{id}")]
        public async Task<ActionResult<DivisionResponse>> PutDivision(int id, [FromBody] UpdateDivisionRequest request)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var division = await _divisionService.UpdateDivisionAsync(id, request);
            return Ok(division);
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteDivision(int id)
        {
            var result = await _divisionService.DeleteDivisionAsync(id);
            if (!result)
            {
                return NotFound();
            }

            return NoContent();
        }
    }
}