{"ast": null, "code": "import { invariant } from '../utils/errors.mjs';\nimport * as React from 'react';\nimport { useConstant } from '../utils/use-constant.mjs';\nimport { LayoutGroup } from './LayoutGroup/index.mjs';\nlet id = 0;\nconst AnimateSharedLayout = ({\n  children\n}) => {\n  React.useEffect(() => {\n    invariant(false, \"AnimateSharedLayout is deprecated: https://www.framer.com/docs/guide-upgrade/##shared-layout-animations\");\n  }, []);\n  return React.createElement(LayoutGroup, {\n    id: useConstant(() => `asl-${id++}`)\n  }, children);\n};\nexport { AnimateSharedLayout };", "map": {"version": 3, "names": ["invariant", "React", "useConstant", "LayoutGroup", "id", "AnimateSharedLayout", "children", "useEffect", "createElement"], "sources": ["C:/Users/<USER>/Downloads/Advanced/Advanced CRM filed all use/frontend/node_modules/framer-motion/dist/es/components/AnimateSharedLayout.mjs"], "sourcesContent": ["import { invariant } from '../utils/errors.mjs';\nimport * as React from 'react';\nimport { useConstant } from '../utils/use-constant.mjs';\nimport { LayoutGroup } from './LayoutGroup/index.mjs';\n\nlet id = 0;\nconst AnimateSharedLayout = ({ children }) => {\n    React.useEffect(() => {\n        invariant(false, \"AnimateSharedLayout is deprecated: https://www.framer.com/docs/guide-upgrade/##shared-layout-animations\");\n    }, []);\n    return (React.createElement(LayoutGroup, { id: useConstant(() => `asl-${id++}`) }, children));\n};\n\nexport { AnimateSharedLayout };\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,qBAAqB;AAC/C,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,WAAW,QAAQ,2BAA2B;AACvD,SAASC,WAAW,QAAQ,yBAAyB;AAErD,IAAIC,EAAE,GAAG,CAAC;AACV,MAAMC,mBAAmB,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAC1CL,KAAK,CAACM,SAAS,CAAC,MAAM;IAClBP,SAAS,CAAC,KAAK,EAAE,yGAAyG,CAAC;EAC/H,CAAC,EAAE,EAAE,CAAC;EACN,OAAQC,KAAK,CAACO,aAAa,CAACL,WAAW,EAAE;IAAEC,EAAE,EAAEF,WAAW,CAAC,MAAM,OAAOE,EAAE,EAAE,EAAE;EAAE,CAAC,EAAEE,QAAQ,CAAC;AAChG,CAAC;AAED,SAASD,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}