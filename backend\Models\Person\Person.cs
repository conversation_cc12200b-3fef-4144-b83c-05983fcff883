using System.Text.Json.Serialization;

namespace CrmApi.Models.Person
{
    public class Person
    {
        public int Id { get; set; }
        
        // Required Foreign Keys
        public int DivisionId { get; set; }
        public int CategoryId { get; set; }
        public int? SubCategoryId { get; set; }
        
        // Required Fields
        public string Name { get; set; } = string.Empty;
        public string MobileNumber { get; set; } = string.Empty;
        
        // Enum Fields
        public PersonNature Nature { get; set; }
        public Gender? Gender { get; set; }
        
        // Contact Information
        public string[] AlternateNumbers { get; set; } = Array.Empty<string>();
        public string? PrimaryEmailId { get; set; }
        public string[] AlternateEmailIds { get; set; } = Array.Empty<string>();
        public string? Website { get; set; }
        
        // Personal Information
        public DateTime? DateOfBirth { get; set; }
        public bool? IsMarried { get; set; }
        public DateTime? DateOfMarriage { get; set; }
        
        // Location Information
        public string WorkingState { get; set; } = string.Empty;
        public string DomesticState { get; set; } = string.Empty;
        public string District { get; set; } = string.Empty;
        public string Address { get; set; } = string.Empty;
        public string WorkingArea { get; set; } = string.Empty;
        
        // Associate Information
        public bool HasAssociate { get; set; }
        public string AssociateName { get; set; } = string.Empty;
        public string AssociateRelation { get; set; } = string.Empty;
        public string? AssociateMobile { get; set; }

        // Digital Presence
        public bool UsingWebsite { get; set; }
        public string? WebsiteLink { get; set; }
        public bool UsingCRMApp { get; set; }
        public string? CRMAppLink { get; set; }
        
        // Business Information
        public decimal? TransactionValue { get; set; }
        public string RERARegistrationNumber { get; set; } = string.Empty;
        public WorkingProfile[] WorkingProfiles { get; set; } = Array.Empty<WorkingProfile>();
        public int? StarRating { get; set; }
        public string Source { get; set; } = string.Empty;
        public string Remarks { get; set; } = string.Empty;
        
        // Company Information
        public string FirmName { get; set; } = string.Empty;
        public int? NumberOfOffices { get; set; }
        public int? NumberOfBranches { get; set; }
        public int? TotalEmployeeStrength { get; set; }
        
        // Authorized Person Information
        public string AuthorizedPersonName { get; set; } = string.Empty;
        public string? AuthorizedPersonEmail { get; set; }
        public string Designation { get; set; } = string.Empty;
        
        // Marketing Information
        public string MarketingContact { get; set; } = string.Empty;
        public string MarketingDesignation { get; set; } = string.Empty;
        public string PlaceOfPosting { get; set; } = string.Empty;
        public string Department { get; set; } = string.Empty;
        
        // Audit Fields
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
        public bool IsDeleted { get; set; } = false;
        public DateTime? DeletedAt { get; set; }
        
        // Navigation Properties
        [JsonIgnore]
        public Division.Division Division { get; set; } = null!;
        
        [JsonIgnore]
        public Category.Category Category { get; set; } = null!;
        
        [JsonIgnore]
        public SubCategory.SubCategory? SubCategory { get; set; }
    }
}
