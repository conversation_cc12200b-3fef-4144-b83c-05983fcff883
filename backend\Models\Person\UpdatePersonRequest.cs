using System.ComponentModel.DataAnnotations;

namespace CrmApi.Models.Person
{
    public class UpdatePersonRequest
    {
        // Foreign Keys
        [Range(1, int.MaxValue, ErrorMessage = "Division ID must be a positive number")]
        public int? DivisionId { get; set; }

        [Range(1, int.MaxValue, ErrorMessage = "Category ID must be a positive number")]
        public int? CategoryId { get; set; }

        [Range(1, int.MaxValue, ErrorMessage = "SubCategory ID must be a positive number")]
        public int? SubCategoryId { get; set; }

        // Basic Fields
        [StringLength(255, ErrorMessage = "Name cannot exceed 255 characters")]
        public string? Name { get; set; }

        [RegularExpression(@"^(\+91[\-\s]?)?[0]?(91)?[789]\d{9}$", ErrorMessage = "Invalid mobile number format")]
        public string? MobileNumber { get; set; }

        // Enum Fields
        [EnumDataType(typeof(PersonNature), ErrorMessage = "Invalid nature value")]
        public PersonNature? Nature { get; set; }

        [EnumDataType(typeof(Gender), ErrorMessage = "Invalid gender value")]
        public Gender? Gender { get; set; }

        // Contact Information
        public string[]? AlternateNumbers { get; set; }

        [EmailAddress(ErrorMessage = "Invalid email format")]
        [StringLength(255, ErrorMessage = "Email cannot exceed 255 characters")]
        public string? PrimaryEmailId { get; set; }

        public string[]? AlternateEmailIds { get; set; }

        [Url(ErrorMessage = "Invalid website URL format")]
        public string? Website { get; set; }

        // Personal Information
        public DateTime? DateOfBirth { get; set; }
        public bool? IsMarried { get; set; }
        public DateTime? DateOfMarriage { get; set; }

        // Location Information
        [StringLength(100, ErrorMessage = "Working state cannot exceed 100 characters")]
        public string? WorkingState { get; set; }

        [StringLength(100, ErrorMessage = "Domestic state cannot exceed 100 characters")]
        public string? DomesticState { get; set; }

        [StringLength(100, ErrorMessage = "District cannot exceed 100 characters")]
        public string? District { get; set; }

        [StringLength(500, ErrorMessage = "Address cannot exceed 500 characters")]
        public string? Address { get; set; }

        [StringLength(200, ErrorMessage = "Working area cannot exceed 200 characters")]
        public string? WorkingArea { get; set; }

        // Associate Information
        public bool? HasAssociate { get; set; }

        [StringLength(255, ErrorMessage = "Associate name cannot exceed 255 characters")]
        public string? AssociateName { get; set; }

        [StringLength(100, ErrorMessage = "Associate relation cannot exceed 100 characters")]
        public string? AssociateRelation { get; set; }

        [RegularExpression(@"^(\+91[\-\s]?)?[0]?(91)?[789]\d{9}$", ErrorMessage = "Invalid associate mobile number format")]
        public string? AssociateMobile { get; set; }

        // Digital Presence
        public bool? UsingWebsite { get; set; }

        [Url(ErrorMessage = "Invalid website link URL format")]
        public string? WebsiteLink { get; set; }

        public bool? UsingCRMApp { get; set; }

        [Url(ErrorMessage = "Invalid CRM app link URL format")]
        public string? CRMAppLink { get; set; }

        // Business Information
        [Range(0, double.MaxValue, ErrorMessage = "Transaction value must be non-negative")]
        public decimal? TransactionValue { get; set; }

        [StringLength(50, ErrorMessage = "RERA registration number cannot exceed 50 characters")]
        public string? RERARegistrationNumber { get; set; }

        public WorkingProfile[]? WorkingProfiles { get; set; }

        [Range(1, 5, ErrorMessage = "Star rating must be between 1 and 5")]
        public int? StarRating { get; set; }

        [StringLength(200, ErrorMessage = "Source cannot exceed 200 characters")]
        public string? Source { get; set; }

        [StringLength(1000, ErrorMessage = "Remarks cannot exceed 1000 characters")]
        public string? Remarks { get; set; }

        // Company Information
        [StringLength(255, ErrorMessage = "Firm name cannot exceed 255 characters")]
        public string? FirmName { get; set; }

        [Range(0, int.MaxValue, ErrorMessage = "Number of offices must be non-negative")]
        public int? NumberOfOffices { get; set; }

        [Range(0, int.MaxValue, ErrorMessage = "Number of branches must be non-negative")]
        public int? NumberOfBranches { get; set; }

        [Range(0, int.MaxValue, ErrorMessage = "Total employee strength must be non-negative")]
        public int? TotalEmployeeStrength { get; set; }

        // Authorized Person Information
        [StringLength(255, ErrorMessage = "Authorized person name cannot exceed 255 characters")]
        public string? AuthorizedPersonName { get; set; }

        [EmailAddress(ErrorMessage = "Invalid authorized person email format")]
        [StringLength(255, ErrorMessage = "Authorized person email cannot exceed 255 characters")]
        public string? AuthorizedPersonEmail { get; set; }

        [StringLength(100, ErrorMessage = "Designation cannot exceed 100 characters")]
        public string? Designation { get; set; }

        // Marketing Information
        [StringLength(255, ErrorMessage = "Marketing contact cannot exceed 255 characters")]
        public string? MarketingContact { get; set; }

        [StringLength(100, ErrorMessage = "Marketing designation cannot exceed 100 characters")]
        public string? MarketingDesignation { get; set; }

        [StringLength(200, ErrorMessage = "Place of posting cannot exceed 200 characters")]
        public string? PlaceOfPosting { get; set; }

        [StringLength(100, ErrorMessage = "Department cannot exceed 100 characters")]
        public string? Department { get; set; }
    }
}
