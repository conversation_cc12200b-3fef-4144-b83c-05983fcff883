using System.Net;
using System.Text.Json;
using CrmApi.Exceptions;
using Microsoft.EntityFrameworkCore;

namespace CrmApi.Middleware
{
    public class GlobalExceptionMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ILogger<GlobalExceptionMiddleware> _logger;
        private readonly IWebHostEnvironment _environment;

        public GlobalExceptionMiddleware(RequestDelegate next, ILogger<GlobalExceptionMiddleware> logger, IWebHostEnvironment environment)
        {
            _next = next;
            _logger = logger;
            _environment = environment;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            try
            {
                await _next(context);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "An unhandled exception occurred");
                await HandleExceptionAsync(context, ex);
            }
        }

        private async Task HandleExceptionAsync(HttpContext context, Exception exception)
        {
            context.Response.ContentType = "application/json";
            
            var response = new ErrorResponse
            {
                TraceId = context.TraceIdentifier
            };

            switch (exception)
            {
                case BaseException baseEx:
                    context.Response.StatusCode = baseEx.StatusCode;
                    response.Type = baseEx.ErrorType;
                    response.Message = baseEx.Message;
                    
                    if (baseEx is ValidationException validationEx)
                    {
                        response.Errors = validationEx.Errors;
                    }
                    break;

                case DbUpdateConcurrencyException:
                    context.Response.StatusCode = (int)HttpStatusCode.Conflict;
                    response.Type = "Concurrency";
                    response.Message = "The record was modified by another user. Please refresh and try again.";
                    break;

                case DbUpdateException dbEx when dbEx.InnerException?.Message.Contains("UNIQUE constraint") == true:
                    context.Response.StatusCode = (int)HttpStatusCode.Conflict;
                    response.Type = "Duplicate";
                    response.Message = "A record with the same value already exists.";
                    break;

                case DbUpdateException dbEx when dbEx.InnerException?.Message.Contains("FOREIGN KEY constraint") == true:
                    context.Response.StatusCode = (int)HttpStatusCode.BadRequest;
                    response.Type = "ForeignKey";
                    response.Message = "Cannot perform this operation due to related data constraints.";
                    break;

                case ArgumentNullException argEx:
                    context.Response.StatusCode = (int)HttpStatusCode.BadRequest;
                    response.Type = "ArgumentNull";
                    response.Message = $"Required parameter is missing: {argEx.ParamName}";
                    break;

                case ArgumentException argEx:
                    context.Response.StatusCode = (int)HttpStatusCode.BadRequest;
                    response.Type = "InvalidArgument";
                    response.Message = argEx.Message;
                    break;

                case UnauthorizedAccessException:
                    context.Response.StatusCode = (int)HttpStatusCode.Unauthorized;
                    response.Type = "Unauthorized";
                    response.Message = "Access denied. Please check your credentials.";
                    break;

                case TimeoutException:
                    context.Response.StatusCode = (int)HttpStatusCode.RequestTimeout;
                    response.Type = "Timeout";
                    response.Message = "The request timed out. Please try again.";
                    break;

                default:
                    context.Response.StatusCode = (int)HttpStatusCode.InternalServerError;
                    response.Type = "InternalError";
                    response.Message = "An internal server error occurred.";
                    
                    // Only include details in development
                    if (_environment.IsDevelopment())
                    {
                        response.Details = exception.ToString();
                    }
                    break;
            }

            var jsonResponse = JsonSerializer.Serialize(response, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            });

            await context.Response.WriteAsync(jsonResponse);
        }
    }
}
