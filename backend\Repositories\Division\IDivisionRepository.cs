using CrmApi.Models.Division;

namespace CrmApi.Repositories.Division
{
    public interface IDivisionRepository
    {
        Task<IEnumerable<Models.Division.Division>> GetAllAsync();
        Task<Models.Division.Division?> GetByIdAsync(int id);
        Task<Models.Division.Division?> GetByIdWithCategoriesAsync(int id);
        Task<Models.Division.Division> CreateAsync(Models.Division.Division division);
        Task<Models.Division.Division> UpdateAsync(Models.Division.Division division);
        Task<bool> DeleteAsync(int id);
        Task<bool> ExistsAsync(int id);
        Task<bool> NameExistsAsync(string name, int? excludeId = null);
    }
}
