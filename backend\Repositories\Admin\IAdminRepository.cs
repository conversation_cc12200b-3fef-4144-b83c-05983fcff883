using CrmApi.Models.Admin;

namespace CrmApi.Repositories.Admin
{
    public interface IAdminRepository
    {
        Task<IEnumerable<Models.Admin.Admin>> GetAllAsync();
        Task<Models.Admin.Admin?> GetByIdAsync(int id);
        Task<Models.Admin.Admin?> GetByUsernameAsync(string username);
        Task<Models.Admin.Admin?> GetByEmailAsync(string email);
        Task<Models.Admin.Admin> CreateAsync(Models.Admin.Admin admin);
        Task<Models.Admin.Admin> UpdateAsync(Models.Admin.Admin admin);
        Task<bool> DeleteAsync(int id);
        Task<bool> ExistsAsync(int id);
        Task<bool> UsernameExistsAsync(string username, int? excludeId = null);
        Task<bool> EmailExistsAsync(string email, int? excludeId = null);
    }
}
