using CrmApi.Models.SubCategory;

namespace CrmApi.Services.SubCategory
{
    public interface ISubCategoryService
    {
        Task<IEnumerable<SubCategoryResponse>> GetAllSubCategoriesAsync();
        Task<SubCategoryResponse?> GetSubCategoryByIdAsync(int id);
        Task<IEnumerable<SubCategoryResponse>> GetSubCategoriesByCategoryAsync(int categoryId);
        Task<SubCategoryResponse> CreateSubCategoryAsync(CreateSubCategoryRequest request);
        Task<SubCategoryResponse> UpdateSubCategoryAsync(int id, UpdateSubCategoryRequest request);
        Task<bool> DeleteSubCategoryAsync(int id);
    }
}
