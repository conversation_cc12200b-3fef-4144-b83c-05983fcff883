{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Advanced\\\\Advanced CRM filed all use\\\\frontend\\\\src\\\\components\\\\forms\\\\DynamicPersonForm.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { PersonNatureLabels, GenderLabels, WorkingProfileLabels, getDefaultPersonData } from '../../constants/personConstants';\nimport formConfigService from '../../services/formConfigService';\nimport apiService from '../../services/apiService';\nimport HierarchicalSelector from './HierarchicalSelector';\nimport FormField from './FormField';\nimport './DynamicPersonForm.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DynamicPersonForm = ({\n  onSubmit,\n  onCancel,\n  initialData = null,\n  mode = 'create',\n  selectedForm = null\n}) => {\n  _s();\n  const [selectedHierarchy, setSelectedHierarchy] = useState({});\n  const [formConfig, setFormConfig] = useState(null);\n  const [formData, setFormData] = useState(getDefaultPersonData());\n  const [errors, setErrors] = useState({});\n  const [loading, setLoading] = useState(false);\n  const [submitting, setSubmitting] = useState(false);\n  useEffect(() => {\n    if (initialData) {\n      setFormData(initialData);\n      setSelectedHierarchy({\n        divisionId: initialData.divisionId,\n        categoryId: initialData.categoryId,\n        subCategoryId: initialData.subCategoryId\n      });\n    }\n  }, [initialData]);\n\n  // Load form configuration when selectedForm changes or on initial mount\n  useEffect(() => {\n    if (selectedForm) {\n      setFormConfig(selectedForm);\n    } else {\n      loadFormConfiguration();\n    }\n  }, [selectedForm]);\n\n  // Only load configuration based on hierarchy if no selectedForm is provided\n  useEffect(() => {\n    if (!selectedForm && selectedHierarchy.categoryId) {\n      loadFormConfiguration();\n    }\n  }, [selectedHierarchy.categoryId, selectedHierarchy.divisionId, selectedHierarchy.subCategoryId]);\n  const loadFormConfiguration = useCallback(() => {\n    // Don't load if we already have a selectedForm\n    if (selectedForm) {\n      return;\n    }\n    setLoading(true);\n    try {\n      let config;\n\n      // Try to get specific configuration if hierarchy is selected\n      if (selectedHierarchy.categoryId || selectedHierarchy.divisionId) {\n        config = formConfigService.getFormConfigForSelection(selectedHierarchy.divisionId, selectedHierarchy.categoryId, selectedHierarchy.subCategoryId);\n      }\n\n      // If no specific config found or no hierarchy selected, use default\n      if (!config) {\n        config = formConfigService.getDefaultFormConfig();\n      }\n      setFormConfig(config);\n    } catch (error) {\n      console.error('Error loading form configuration:', error);\n      // Always fallback to default configuration\n      const defaultConfig = formConfigService.getDefaultFormConfig();\n      setFormConfig(defaultConfig);\n    } finally {\n      setLoading(false);\n    }\n  }, [selectedForm, selectedHierarchy]);\n  const handleHierarchyChange = useCallback(selection => {\n    setSelectedHierarchy(selection);\n    setFormData(prev => ({\n      ...prev,\n      divisionId: selection.divisionId,\n      categoryId: selection.categoryId,\n      subCategoryId: selection.subCategoryId\n    }));\n  }, []);\n  const handleFieldChange = (fieldKey, value) => {\n    setFormData(prev => ({\n      ...prev,\n      [fieldKey]: value\n    }));\n\n    // Clear field error when user starts typing\n    if (errors[fieldKey]) {\n      setErrors(prev => ({\n        ...prev,\n        [fieldKey]: null\n      }));\n    }\n  };\n  const validateForm = () => {\n    const newErrors = {};\n    if (!formConfig) {\n      newErrors.general = 'Form configuration not loaded';\n      return {\n        isValid: false,\n        errors: newErrors\n      };\n    }\n\n    // Validate hierarchy selection\n    if (!selectedHierarchy.divisionId) {\n      newErrors.divisionId = 'Division is required';\n    }\n    // Category is optional, but if subcategory is selected, category must be selected\n    if (selectedHierarchy.subCategoryId && !selectedHierarchy.categoryId) {\n      newErrors.categoryId = 'Category is required when subcategory is selected';\n    }\n\n    // Validate form fields\n    formConfig.fields.forEach(field => {\n      const value = formData[field.key];\n\n      // Required field validation\n      if (field.required && (!value || typeof value === 'string' && value.trim() === '')) {\n        newErrors[field.key] = `${field.label} is required`;\n        return;\n      }\n\n      // Conditional field validation\n      if (field.conditional && shouldShowField(field)) {\n        if (field.required && (!value || typeof value === 'string' && value.trim() === '')) {\n          newErrors[field.key] = `${field.label} is required`;\n          return;\n        }\n      }\n\n      // Type-specific validation\n      if (value && typeof value === 'string' && value.trim() !== '') {\n        var _field$validation, _field$validation2, _field$validation3;\n        switch (field.type) {\n          case 'email':\n            const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n            if (!emailRegex.test(value)) {\n              newErrors[field.key] = 'Please enter a valid email address';\n            }\n            break;\n          case 'tel':\n            const phoneRegex = /^(\\+91[\\-\\s]?)?[0]?(91)?[789]\\d{9}$/;\n            if (!phoneRegex.test(value)) {\n              newErrors[field.key] = 'Please enter a valid mobile number';\n            }\n            break;\n          case 'url':\n            try {\n              new URL(value);\n            } catch {\n              newErrors[field.key] = 'Please enter a valid URL';\n            }\n            break;\n          case 'number':\n            if (field.validation) {\n              const numValue = parseFloat(value);\n              if (field.validation.min !== undefined && numValue < field.validation.min) {\n                newErrors[field.key] = `Value must be at least ${field.validation.min}`;\n              }\n              if (field.validation.max !== undefined && numValue > field.validation.max) {\n                newErrors[field.key] = `Value must be at most ${field.validation.max}`;\n              }\n            }\n            break;\n        }\n\n        // Pattern validation\n        if ((_field$validation = field.validation) !== null && _field$validation !== void 0 && _field$validation.pattern) {\n          const regex = new RegExp(field.validation.pattern);\n          if (!regex.test(value)) {\n            newErrors[field.key] = `${field.label} format is invalid`;\n          }\n        }\n\n        // Length validation\n        if ((_field$validation2 = field.validation) !== null && _field$validation2 !== void 0 && _field$validation2.minLength && value.length < field.validation.minLength) {\n          newErrors[field.key] = `${field.label} must be at least ${field.validation.minLength} characters`;\n        }\n        if ((_field$validation3 = field.validation) !== null && _field$validation3 !== void 0 && _field$validation3.maxLength && value.length > field.validation.maxLength) {\n          newErrors[field.key] = `${field.label} must be at most ${field.validation.maxLength} characters`;\n        }\n      }\n    });\n\n    // Business logic validation\n    if (formData.isMarried && formData.dateOfMarriage && formData.dateOfBirth) {\n      const birthDate = new Date(formData.dateOfBirth);\n      const marriageDate = new Date(formData.dateOfMarriage);\n      if (marriageDate <= birthDate) {\n        newErrors.dateOfMarriage = 'Marriage date must be after birth date';\n      }\n    }\n    return {\n      isValid: Object.keys(newErrors).length === 0,\n      errors: newErrors\n    };\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    const validation = validateForm();\n    if (!validation.isValid) {\n      setErrors(validation.errors);\n      return;\n    }\n    setSubmitting(true);\n    try {\n      // Prepare data for API\n      const submitData = {\n        ...formData,\n        // Convert arrays to proper format\n        alternateNumbers: Array.isArray(formData.alternateNumbers) ? formData.alternateNumbers : formData.alternateNumbers ? formData.alternateNumbers.split(',').map(s => s.trim()) : [],\n        alternateEmailIds: Array.isArray(formData.alternateEmailIds) ? formData.alternateEmailIds : formData.alternateEmailIds ? formData.alternateEmailIds.split(',').map(s => s.trim()) : [],\n        workingProfiles: Array.isArray(formData.workingProfiles) ? formData.workingProfiles : []\n      };\n      let result;\n      if (mode === 'create') {\n        result = await apiService.createPerson(submitData);\n      } else {\n        result = await apiService.updatePerson(initialData.id, submitData);\n      }\n      onSubmit(result);\n    } catch (error) {\n      console.error('Error submitting form:', error);\n      if (error.isValidationError && error.isValidationError()) {\n        setErrors(error.getValidationErrors());\n      } else {\n        setErrors({\n          general: error.message || 'An error occurred while saving'\n        });\n      }\n    } finally {\n      setSubmitting(false);\n    }\n  };\n  const shouldShowField = field => {\n    if (!field.conditional) return true;\n    const conditionValue = formData[field.conditional.field];\n    const expectedValue = field.conditional.value;\n\n    // Handle boolean conditions\n    if (typeof expectedValue === 'boolean' || expectedValue === 'true' || expectedValue === 'false') {\n      return conditionValue === (expectedValue === true || expectedValue === 'true');\n    }\n    return conditionValue === expectedValue;\n  };\n  const groupFieldsBySections = () => {\n    if (!formConfig || !formConfig.fields) return {};\n    const sections = {};\n    formConfig.fields.forEach(field => {\n      const sectionKey = field.section || 'general';\n      if (!sections[sectionKey]) {\n        sections[sectionKey] = {\n          title: getSectionTitle(sectionKey),\n          fields: []\n        };\n      }\n      sections[sectionKey].fields.push(field);\n    });\n    return sections;\n  };\n  const getSectionTitle = sectionKey => {\n    const titles = {\n      personalInfo: 'Personal Information',\n      contactInfo: 'Contact Information',\n      locationInfo: 'Location Information',\n      businessInfo: 'Business Information',\n      associateInfo: 'Associate Information',\n      digitalPresence: 'Digital Presence',\n      companyInfo: 'Company Information',\n      authorizedPerson: 'Authorized Person',\n      marketingInfo: 'Marketing Information',\n      general: 'General Information'\n    };\n    return titles[sectionKey] || sectionKey;\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"dynamic-form-loading\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-spinner\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 305,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Loading form configuration...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 306,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 304,\n      columnNumber: 7\n    }, this);\n  }\n  if (!formConfig) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"dynamic-form-error\",\n      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Unable to load form configuration. Please try again.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 314,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: onCancel,\n        className: \"btn btn-outline\",\n        children: \"Go Back\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 315,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 313,\n      columnNumber: 7\n    }, this);\n  }\n  const sections = groupFieldsBySections();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"dynamic-person-form\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"form-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: mode === 'create' ? 'Create New Person' : 'Edit Person'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 327,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-info\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"form-name\",\n          children: formConfig.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 329,\n          columnNumber: 11\n        }, this), formConfig.description && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"form-description\",\n          children: formConfig.description\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 331,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 328,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 326,\n      columnNumber: 7\n    }, this), errors.general && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"alert alert-error\",\n      children: errors.general\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 337,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmit,\n      className: \"person-form\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Division & Category Selection\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 343,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(HierarchicalSelector, {\n          onSelectionChange: handleHierarchyChange,\n          initialSelection: selectedHierarchy,\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 344,\n          columnNumber: 11\n        }, this), (errors.divisionId || errors.categoryId) && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"error-message\",\n          children: errors.divisionId || errors.categoryId\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 350,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 342,\n        columnNumber: 9\n      }, this), Object.entries(sections).map(([sectionKey, section]) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: section.title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 359,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-fields\",\n          children: section.fields.filter(field => shouldShowField(field)).map(field => /*#__PURE__*/_jsxDEV(FormField, {\n            field: field,\n            value: formData[field.key],\n            onChange: value => handleFieldChange(field.key, value),\n            error: errors[field.key]\n          }, field.key, false, {\n            fileName: _jsxFileName,\n            lineNumber: 364,\n            columnNumber: 19\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 360,\n          columnNumber: 13\n        }, this)]\n      }, sectionKey, true, {\n        fileName: _jsxFileName,\n        lineNumber: 358,\n        columnNumber: 11\n      }, this)), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-actions\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          onClick: onCancel,\n          className: \"btn btn-outline\",\n          disabled: submitting,\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 378,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          className: \"btn btn-primary\",\n          disabled: submitting || !selectedHierarchy.divisionId,\n          children: submitting ? mode === 'create' ? 'Creating...' : 'Updating...' : mode === 'create' ? 'Create Person' : 'Update Person'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 386,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 377,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 340,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 325,\n    columnNumber: 5\n  }, this);\n};\n_s(DynamicPersonForm, \"Z0a6G5BeHmJurE9kWCS0ymDjnek=\");\n_c = DynamicPersonForm;\nexport default DynamicPersonForm;\nvar _c;\n$RefreshReg$(_c, \"DynamicPersonForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "PersonNatureLabels", "<PERSON><PERSON><PERSON><PERSON>", "WorkingProfileLabels", "getDefaultPersonData", "formConfigService", "apiService", "HierarchicalSelector", "FormField", "jsxDEV", "_jsxDEV", "DynamicPersonForm", "onSubmit", "onCancel", "initialData", "mode", "selectedForm", "_s", "selectedHierarchy", "setSelectedHierarchy", "formConfig", "setFormConfig", "formData", "setFormData", "errors", "setErrors", "loading", "setLoading", "submitting", "setSubmitting", "divisionId", "categoryId", "subCategoryId", "loadFormConfiguration", "config", "getFormConfigForSelection", "getDefaultFormConfig", "error", "console", "defaultConfig", "handleHierarchyChange", "selection", "prev", "handleFieldChange", "<PERSON><PERSON><PERSON>", "value", "validateForm", "newErrors", "general", "<PERSON><PERSON><PERSON><PERSON>", "fields", "for<PERSON>ach", "field", "key", "required", "trim", "label", "conditional", "shouldShowField", "_field$validation", "_field$validation2", "_field$validation3", "type", "emailRegex", "test", "phoneRegex", "URL", "validation", "numValue", "parseFloat", "min", "undefined", "max", "pattern", "regex", "RegExp", "<PERSON><PERSON><PERSON><PERSON>", "length", "max<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "dateOfMarriage", "dateOfBirth", "birthDate", "Date", "marriageDate", "Object", "keys", "handleSubmit", "e", "preventDefault", "submitData", "alternateNumbers", "Array", "isArray", "split", "map", "s", "alternateEmailIds", "workingProfiles", "result", "create<PERSON>erson", "update<PERSON><PERSON>", "id", "isValidationError", "getValidationErrors", "message", "conditionValue", "expectedValue", "groupFieldsBySections", "sections", "sectionKey", "section", "title", "getSectionTitle", "push", "titles", "personalInfo", "contactInfo", "locationInfo", "businessInfo", "associateInfo", "digitalPresence", "companyInfo", "<PERSON><PERSON><PERSON>", "marketingInfo", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "name", "description", "onSelectionChange", "initialSelection", "entries", "filter", "onChange", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/Advanced/Advanced CRM filed all use/frontend/src/components/forms/DynamicPersonForm.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\nimport { PersonNatureLabels, GenderLabels, WorkingProfileLabels, getDefaultPersonData } from '../../constants/personConstants';\nimport formConfigService from '../../services/formConfigService';\nimport apiService from '../../services/apiService';\nimport HierarchicalSelector from './HierarchicalSelector';\nimport <PERSON><PERSON>ield from './FormField';\nimport './DynamicPersonForm.css';\n\nconst DynamicPersonForm = ({ onSubmit, onCancel, initialData = null, mode = 'create', selectedForm = null }) => {\n  const [selectedHierarchy, setSelectedHierarchy] = useState({});\n  const [formConfig, setFormConfig] = useState(null);\n  const [formData, setFormData] = useState(getDefaultPersonData());\n  const [errors, setErrors] = useState({});\n  const [loading, setLoading] = useState(false);\n  const [submitting, setSubmitting] = useState(false);\n\n  useEffect(() => {\n    if (initialData) {\n      setFormData(initialData);\n      setSelectedHierarchy({\n        divisionId: initialData.divisionId,\n        categoryId: initialData.categoryId,\n        subCategoryId: initialData.subCategoryId\n      });\n    }\n  }, [initialData]);\n\n  // Load form configuration when selectedForm changes or on initial mount\n  useEffect(() => {\n    if (selectedForm) {\n      setFormConfig(selectedForm);\n    } else {\n      loadFormConfiguration();\n    }\n  }, [selectedForm]);\n\n  // Only load configuration based on hierarchy if no selectedForm is provided\n  useEffect(() => {\n    if (!selectedForm && selectedHierarchy.categoryId) {\n      loadFormConfiguration();\n    }\n  }, [selectedHierarchy.categoryId, selectedHierarchy.divisionId, selectedHierarchy.subCategoryId]);\n\n  const loadFormConfiguration = useCallback(() => {\n    // Don't load if we already have a selectedForm\n    if (selectedForm) {\n      return;\n    }\n\n    setLoading(true);\n    try {\n      let config;\n\n      // Try to get specific configuration if hierarchy is selected\n      if (selectedHierarchy.categoryId || selectedHierarchy.divisionId) {\n        config = formConfigService.getFormConfigForSelection(\n          selectedHierarchy.divisionId,\n          selectedHierarchy.categoryId,\n          selectedHierarchy.subCategoryId\n        );\n      }\n\n      // If no specific config found or no hierarchy selected, use default\n      if (!config) {\n        config = formConfigService.getDefaultFormConfig();\n      }\n\n      setFormConfig(config);\n    } catch (error) {\n      console.error('Error loading form configuration:', error);\n      // Always fallback to default configuration\n      const defaultConfig = formConfigService.getDefaultFormConfig();\n      setFormConfig(defaultConfig);\n    } finally {\n      setLoading(false);\n    }\n  }, [selectedForm, selectedHierarchy]);\n\n  const handleHierarchyChange = useCallback((selection) => {\n    setSelectedHierarchy(selection);\n    setFormData(prev => ({\n      ...prev,\n      divisionId: selection.divisionId,\n      categoryId: selection.categoryId,\n      subCategoryId: selection.subCategoryId\n    }));\n  }, []);\n\n  const handleFieldChange = (fieldKey, value) => {\n    setFormData(prev => ({\n      ...prev,\n      [fieldKey]: value\n    }));\n\n    // Clear field error when user starts typing\n    if (errors[fieldKey]) {\n      setErrors(prev => ({\n        ...prev,\n        [fieldKey]: null\n      }));\n    }\n  };\n\n  const validateForm = () => {\n    const newErrors = {};\n\n    if (!formConfig) {\n      newErrors.general = 'Form configuration not loaded';\n      return { isValid: false, errors: newErrors };\n    }\n\n    // Validate hierarchy selection\n    if (!selectedHierarchy.divisionId) {\n      newErrors.divisionId = 'Division is required';\n    }\n    // Category is optional, but if subcategory is selected, category must be selected\n    if (selectedHierarchy.subCategoryId && !selectedHierarchy.categoryId) {\n      newErrors.categoryId = 'Category is required when subcategory is selected';\n    }\n\n    // Validate form fields\n    formConfig.fields.forEach(field => {\n      const value = formData[field.key];\n      \n      // Required field validation\n      if (field.required && (!value || (typeof value === 'string' && value.trim() === ''))) {\n        newErrors[field.key] = `${field.label} is required`;\n        return;\n      }\n\n      // Conditional field validation\n      if (field.conditional && shouldShowField(field)) {\n        if (field.required && (!value || (typeof value === 'string' && value.trim() === ''))) {\n          newErrors[field.key] = `${field.label} is required`;\n          return;\n        }\n      }\n\n      // Type-specific validation\n      if (value && typeof value === 'string' && value.trim() !== '') {\n        switch (field.type) {\n          case 'email':\n            const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n            if (!emailRegex.test(value)) {\n              newErrors[field.key] = 'Please enter a valid email address';\n            }\n            break;\n          case 'tel':\n            const phoneRegex = /^(\\+91[\\-\\s]?)?[0]?(91)?[789]\\d{9}$/;\n            if (!phoneRegex.test(value)) {\n              newErrors[field.key] = 'Please enter a valid mobile number';\n            }\n            break;\n          case 'url':\n            try {\n              new URL(value);\n            } catch {\n              newErrors[field.key] = 'Please enter a valid URL';\n            }\n            break;\n          case 'number':\n            if (field.validation) {\n              const numValue = parseFloat(value);\n              if (field.validation.min !== undefined && numValue < field.validation.min) {\n                newErrors[field.key] = `Value must be at least ${field.validation.min}`;\n              }\n              if (field.validation.max !== undefined && numValue > field.validation.max) {\n                newErrors[field.key] = `Value must be at most ${field.validation.max}`;\n              }\n            }\n            break;\n        }\n\n        // Pattern validation\n        if (field.validation?.pattern) {\n          const regex = new RegExp(field.validation.pattern);\n          if (!regex.test(value)) {\n            newErrors[field.key] = `${field.label} format is invalid`;\n          }\n        }\n\n        // Length validation\n        if (field.validation?.minLength && value.length < field.validation.minLength) {\n          newErrors[field.key] = `${field.label} must be at least ${field.validation.minLength} characters`;\n        }\n        if (field.validation?.maxLength && value.length > field.validation.maxLength) {\n          newErrors[field.key] = `${field.label} must be at most ${field.validation.maxLength} characters`;\n        }\n      }\n    });\n\n    // Business logic validation\n    if (formData.isMarried && formData.dateOfMarriage && formData.dateOfBirth) {\n      const birthDate = new Date(formData.dateOfBirth);\n      const marriageDate = new Date(formData.dateOfMarriage);\n      if (marriageDate <= birthDate) {\n        newErrors.dateOfMarriage = 'Marriage date must be after birth date';\n      }\n    }\n\n    return {\n      isValid: Object.keys(newErrors).length === 0,\n      errors: newErrors\n    };\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    \n    const validation = validateForm();\n    if (!validation.isValid) {\n      setErrors(validation.errors);\n      return;\n    }\n\n    setSubmitting(true);\n    try {\n      // Prepare data for API\n      const submitData = {\n        ...formData,\n        // Convert arrays to proper format\n        alternateNumbers: Array.isArray(formData.alternateNumbers) \n          ? formData.alternateNumbers \n          : formData.alternateNumbers ? formData.alternateNumbers.split(',').map(s => s.trim()) : [],\n        alternateEmailIds: Array.isArray(formData.alternateEmailIds) \n          ? formData.alternateEmailIds \n          : formData.alternateEmailIds ? formData.alternateEmailIds.split(',').map(s => s.trim()) : [],\n        workingProfiles: Array.isArray(formData.workingProfiles) \n          ? formData.workingProfiles \n          : []\n      };\n\n      let result;\n      if (mode === 'create') {\n        result = await apiService.createPerson(submitData);\n      } else {\n        result = await apiService.updatePerson(initialData.id, submitData);\n      }\n\n      onSubmit(result);\n    } catch (error) {\n      console.error('Error submitting form:', error);\n      \n      if (error.isValidationError && error.isValidationError()) {\n        setErrors(error.getValidationErrors());\n      } else {\n        setErrors({ general: error.message || 'An error occurred while saving' });\n      }\n    } finally {\n      setSubmitting(false);\n    }\n  };\n\n  const shouldShowField = (field) => {\n    if (!field.conditional) return true;\n    \n    const conditionValue = formData[field.conditional.field];\n    const expectedValue = field.conditional.value;\n    \n    // Handle boolean conditions\n    if (typeof expectedValue === 'boolean' || expectedValue === 'true' || expectedValue === 'false') {\n      return conditionValue === (expectedValue === true || expectedValue === 'true');\n    }\n    \n    return conditionValue === expectedValue;\n  };\n\n  const groupFieldsBySections = () => {\n    if (!formConfig || !formConfig.fields) return {};\n    \n    const sections = {};\n    formConfig.fields.forEach(field => {\n      const sectionKey = field.section || 'general';\n      if (!sections[sectionKey]) {\n        sections[sectionKey] = {\n          title: getSectionTitle(sectionKey),\n          fields: []\n        };\n      }\n      sections[sectionKey].fields.push(field);\n    });\n    \n    return sections;\n  };\n\n  const getSectionTitle = (sectionKey) => {\n    const titles = {\n      personalInfo: 'Personal Information',\n      contactInfo: 'Contact Information',\n      locationInfo: 'Location Information',\n      businessInfo: 'Business Information',\n      associateInfo: 'Associate Information',\n      digitalPresence: 'Digital Presence',\n      companyInfo: 'Company Information',\n      authorizedPerson: 'Authorized Person',\n      marketingInfo: 'Marketing Information',\n      general: 'General Information'\n    };\n    return titles[sectionKey] || sectionKey;\n  };\n\n  if (loading) {\n    return (\n      <div className=\"dynamic-form-loading\">\n        <div className=\"loading-spinner\"></div>\n        <p>Loading form configuration...</p>\n      </div>\n    );\n  }\n\n  if (!formConfig) {\n    return (\n      <div className=\"dynamic-form-error\">\n        <p>Unable to load form configuration. Please try again.</p>\n        <button onClick={onCancel} className=\"btn btn-outline\">Go Back</button>\n      </div>\n    );\n  }\n\n  const sections = groupFieldsBySections();\n\n\n\n  return (\n    <div className=\"dynamic-person-form\">\n      <div className=\"form-header\">\n        <h2>{mode === 'create' ? 'Create New Person' : 'Edit Person'}</h2>\n        <div className=\"form-info\">\n          <span className=\"form-name\">{formConfig.name}</span>\n          {formConfig.description && (\n            <span className=\"form-description\">{formConfig.description}</span>\n          )}\n        </div>\n      </div>\n\n      {errors.general && (\n        <div className=\"alert alert-error\">{errors.general}</div>\n      )}\n\n      <form onSubmit={handleSubmit} className=\"person-form\">\n        {/* Hierarchy Selection */}\n        <div className=\"form-section\">\n          <h3>Division & Category Selection</h3>\n          <HierarchicalSelector\n            onSelectionChange={handleHierarchyChange}\n            initialSelection={selectedHierarchy}\n            required={true}\n          />\n          {(errors.divisionId || errors.categoryId) && (\n            <div className=\"error-message\">\n              {errors.divisionId || errors.categoryId}\n            </div>\n          )}\n        </div>\n\n        {/* Dynamic Form Fields */}\n        {Object.entries(sections).map(([sectionKey, section]) => (\n          <div key={sectionKey} className=\"form-section\">\n            <h3>{section.title}</h3>\n            <div className=\"form-fields\">\n              {section.fields\n                .filter(field => shouldShowField(field))\n                .map(field => (\n                  <FormField\n                    key={field.key}\n                    field={field}\n                    value={formData[field.key]}\n                    onChange={(value) => handleFieldChange(field.key, value)}\n                    error={errors[field.key]}\n                  />\n                ))}\n            </div>\n          </div>\n        ))}\n\n        {/* Form Actions */}\n        <div className=\"form-actions\">\n          <button\n            type=\"button\"\n            onClick={onCancel}\n            className=\"btn btn-outline\"\n            disabled={submitting}\n          >\n            Cancel\n          </button>\n          <button\n            type=\"submit\"\n            className=\"btn btn-primary\"\n            disabled={submitting || !selectedHierarchy.divisionId}\n          >\n            {submitting \n              ? (mode === 'create' ? 'Creating...' : 'Updating...') \n              : (mode === 'create' ? 'Create Person' : 'Update Person')\n            }\n          </button>\n        </div>\n      </form>\n    </div>\n  );\n};\n\nexport default DynamicPersonForm;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SAASC,kBAAkB,EAAEC,YAAY,EAAEC,oBAAoB,EAAEC,oBAAoB,QAAQ,iCAAiC;AAC9H,OAAOC,iBAAiB,MAAM,kCAAkC;AAChE,OAAOC,UAAU,MAAM,2BAA2B;AAClD,OAAOC,oBAAoB,MAAM,wBAAwB;AACzD,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAO,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjC,MAAMC,iBAAiB,GAAGA,CAAC;EAAEC,QAAQ;EAAEC,QAAQ;EAAEC,WAAW,GAAG,IAAI;EAAEC,IAAI,GAAG,QAAQ;EAAEC,YAAY,GAAG;AAAK,CAAC,KAAK;EAAAC,EAAA;EAC9G,MAAM,CAACC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGrB,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC9D,MAAM,CAACsB,UAAU,EAAEC,aAAa,CAAC,GAAGvB,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACwB,QAAQ,EAAEC,WAAW,CAAC,GAAGzB,QAAQ,CAACM,oBAAoB,CAAC,CAAC,CAAC;EAChE,MAAM,CAACoB,MAAM,EAAEC,SAAS,CAAC,GAAG3B,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxC,MAAM,CAAC4B,OAAO,EAAEC,UAAU,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC8B,UAAU,EAAEC,aAAa,CAAC,GAAG/B,QAAQ,CAAC,KAAK,CAAC;EAEnDC,SAAS,CAAC,MAAM;IACd,IAAIe,WAAW,EAAE;MACfS,WAAW,CAACT,WAAW,CAAC;MACxBK,oBAAoB,CAAC;QACnBW,UAAU,EAAEhB,WAAW,CAACgB,UAAU;QAClCC,UAAU,EAAEjB,WAAW,CAACiB,UAAU;QAClCC,aAAa,EAAElB,WAAW,CAACkB;MAC7B,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAAClB,WAAW,CAAC,CAAC;;EAEjB;EACAf,SAAS,CAAC,MAAM;IACd,IAAIiB,YAAY,EAAE;MAChBK,aAAa,CAACL,YAAY,CAAC;IAC7B,CAAC,MAAM;MACLiB,qBAAqB,CAAC,CAAC;IACzB;EACF,CAAC,EAAE,CAACjB,YAAY,CAAC,CAAC;;EAElB;EACAjB,SAAS,CAAC,MAAM;IACd,IAAI,CAACiB,YAAY,IAAIE,iBAAiB,CAACa,UAAU,EAAE;MACjDE,qBAAqB,CAAC,CAAC;IACzB;EACF,CAAC,EAAE,CAACf,iBAAiB,CAACa,UAAU,EAAEb,iBAAiB,CAACY,UAAU,EAAEZ,iBAAiB,CAACc,aAAa,CAAC,CAAC;EAEjG,MAAMC,qBAAqB,GAAGjC,WAAW,CAAC,MAAM;IAC9C;IACA,IAAIgB,YAAY,EAAE;MAChB;IACF;IAEAW,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,IAAIO,MAAM;;MAEV;MACA,IAAIhB,iBAAiB,CAACa,UAAU,IAAIb,iBAAiB,CAACY,UAAU,EAAE;QAChEI,MAAM,GAAG7B,iBAAiB,CAAC8B,yBAAyB,CAClDjB,iBAAiB,CAACY,UAAU,EAC5BZ,iBAAiB,CAACa,UAAU,EAC5Bb,iBAAiB,CAACc,aACpB,CAAC;MACH;;MAEA;MACA,IAAI,CAACE,MAAM,EAAE;QACXA,MAAM,GAAG7B,iBAAiB,CAAC+B,oBAAoB,CAAC,CAAC;MACnD;MAEAf,aAAa,CAACa,MAAM,CAAC;IACvB,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MACzD;MACA,MAAME,aAAa,GAAGlC,iBAAiB,CAAC+B,oBAAoB,CAAC,CAAC;MAC9Df,aAAa,CAACkB,aAAa,CAAC;IAC9B,CAAC,SAAS;MACRZ,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,CAACX,YAAY,EAAEE,iBAAiB,CAAC,CAAC;EAErC,MAAMsB,qBAAqB,GAAGxC,WAAW,CAAEyC,SAAS,IAAK;IACvDtB,oBAAoB,CAACsB,SAAS,CAAC;IAC/BlB,WAAW,CAACmB,IAAI,KAAK;MACnB,GAAGA,IAAI;MACPZ,UAAU,EAAEW,SAAS,CAACX,UAAU;MAChCC,UAAU,EAAEU,SAAS,CAACV,UAAU;MAChCC,aAAa,EAAES,SAAS,CAACT;IAC3B,CAAC,CAAC,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMW,iBAAiB,GAAGA,CAACC,QAAQ,EAAEC,KAAK,KAAK;IAC7CtB,WAAW,CAACmB,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACE,QAAQ,GAAGC;IACd,CAAC,CAAC,CAAC;;IAEH;IACA,IAAIrB,MAAM,CAACoB,QAAQ,CAAC,EAAE;MACpBnB,SAAS,CAACiB,IAAI,KAAK;QACjB,GAAGA,IAAI;QACP,CAACE,QAAQ,GAAG;MACd,CAAC,CAAC,CAAC;IACL;EACF,CAAC;EAED,MAAME,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,SAAS,GAAG,CAAC,CAAC;IAEpB,IAAI,CAAC3B,UAAU,EAAE;MACf2B,SAAS,CAACC,OAAO,GAAG,+BAA+B;MACnD,OAAO;QAAEC,OAAO,EAAE,KAAK;QAAEzB,MAAM,EAAEuB;MAAU,CAAC;IAC9C;;IAEA;IACA,IAAI,CAAC7B,iBAAiB,CAACY,UAAU,EAAE;MACjCiB,SAAS,CAACjB,UAAU,GAAG,sBAAsB;IAC/C;IACA;IACA,IAAIZ,iBAAiB,CAACc,aAAa,IAAI,CAACd,iBAAiB,CAACa,UAAU,EAAE;MACpEgB,SAAS,CAAChB,UAAU,GAAG,mDAAmD;IAC5E;;IAEA;IACAX,UAAU,CAAC8B,MAAM,CAACC,OAAO,CAACC,KAAK,IAAI;MACjC,MAAMP,KAAK,GAAGvB,QAAQ,CAAC8B,KAAK,CAACC,GAAG,CAAC;;MAEjC;MACA,IAAID,KAAK,CAACE,QAAQ,KAAK,CAACT,KAAK,IAAK,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACU,IAAI,CAAC,CAAC,KAAK,EAAG,CAAC,EAAE;QACpFR,SAAS,CAACK,KAAK,CAACC,GAAG,CAAC,GAAG,GAAGD,KAAK,CAACI,KAAK,cAAc;QACnD;MACF;;MAEA;MACA,IAAIJ,KAAK,CAACK,WAAW,IAAIC,eAAe,CAACN,KAAK,CAAC,EAAE;QAC/C,IAAIA,KAAK,CAACE,QAAQ,KAAK,CAACT,KAAK,IAAK,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACU,IAAI,CAAC,CAAC,KAAK,EAAG,CAAC,EAAE;UACpFR,SAAS,CAACK,KAAK,CAACC,GAAG,CAAC,GAAG,GAAGD,KAAK,CAACI,KAAK,cAAc;UACnD;QACF;MACF;;MAEA;MACA,IAAIX,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACU,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;QAAA,IAAAI,iBAAA,EAAAC,kBAAA,EAAAC,kBAAA;QAC7D,QAAQT,KAAK,CAACU,IAAI;UAChB,KAAK,OAAO;YACV,MAAMC,UAAU,GAAG,4BAA4B;YAC/C,IAAI,CAACA,UAAU,CAACC,IAAI,CAACnB,KAAK,CAAC,EAAE;cAC3BE,SAAS,CAACK,KAAK,CAACC,GAAG,CAAC,GAAG,oCAAoC;YAC7D;YACA;UACF,KAAK,KAAK;YACR,MAAMY,UAAU,GAAG,qCAAqC;YACxD,IAAI,CAACA,UAAU,CAACD,IAAI,CAACnB,KAAK,CAAC,EAAE;cAC3BE,SAAS,CAACK,KAAK,CAACC,GAAG,CAAC,GAAG,oCAAoC;YAC7D;YACA;UACF,KAAK,KAAK;YACR,IAAI;cACF,IAAIa,GAAG,CAACrB,KAAK,CAAC;YAChB,CAAC,CAAC,MAAM;cACNE,SAAS,CAACK,KAAK,CAACC,GAAG,CAAC,GAAG,0BAA0B;YACnD;YACA;UACF,KAAK,QAAQ;YACX,IAAID,KAAK,CAACe,UAAU,EAAE;cACpB,MAAMC,QAAQ,GAAGC,UAAU,CAACxB,KAAK,CAAC;cAClC,IAAIO,KAAK,CAACe,UAAU,CAACG,GAAG,KAAKC,SAAS,IAAIH,QAAQ,GAAGhB,KAAK,CAACe,UAAU,CAACG,GAAG,EAAE;gBACzEvB,SAAS,CAACK,KAAK,CAACC,GAAG,CAAC,GAAG,0BAA0BD,KAAK,CAACe,UAAU,CAACG,GAAG,EAAE;cACzE;cACA,IAAIlB,KAAK,CAACe,UAAU,CAACK,GAAG,KAAKD,SAAS,IAAIH,QAAQ,GAAGhB,KAAK,CAACe,UAAU,CAACK,GAAG,EAAE;gBACzEzB,SAAS,CAACK,KAAK,CAACC,GAAG,CAAC,GAAG,yBAAyBD,KAAK,CAACe,UAAU,CAACK,GAAG,EAAE;cACxE;YACF;YACA;QACJ;;QAEA;QACA,KAAAb,iBAAA,GAAIP,KAAK,CAACe,UAAU,cAAAR,iBAAA,eAAhBA,iBAAA,CAAkBc,OAAO,EAAE;UAC7B,MAAMC,KAAK,GAAG,IAAIC,MAAM,CAACvB,KAAK,CAACe,UAAU,CAACM,OAAO,CAAC;UAClD,IAAI,CAACC,KAAK,CAACV,IAAI,CAACnB,KAAK,CAAC,EAAE;YACtBE,SAAS,CAACK,KAAK,CAACC,GAAG,CAAC,GAAG,GAAGD,KAAK,CAACI,KAAK,oBAAoB;UAC3D;QACF;;QAEA;QACA,IAAI,CAAAI,kBAAA,GAAAR,KAAK,CAACe,UAAU,cAAAP,kBAAA,eAAhBA,kBAAA,CAAkBgB,SAAS,IAAI/B,KAAK,CAACgC,MAAM,GAAGzB,KAAK,CAACe,UAAU,CAACS,SAAS,EAAE;UAC5E7B,SAAS,CAACK,KAAK,CAACC,GAAG,CAAC,GAAG,GAAGD,KAAK,CAACI,KAAK,qBAAqBJ,KAAK,CAACe,UAAU,CAACS,SAAS,aAAa;QACnG;QACA,IAAI,CAAAf,kBAAA,GAAAT,KAAK,CAACe,UAAU,cAAAN,kBAAA,eAAhBA,kBAAA,CAAkBiB,SAAS,IAAIjC,KAAK,CAACgC,MAAM,GAAGzB,KAAK,CAACe,UAAU,CAACW,SAAS,EAAE;UAC5E/B,SAAS,CAACK,KAAK,CAACC,GAAG,CAAC,GAAG,GAAGD,KAAK,CAACI,KAAK,oBAAoBJ,KAAK,CAACe,UAAU,CAACW,SAAS,aAAa;QAClG;MACF;IACF,CAAC,CAAC;;IAEF;IACA,IAAIxD,QAAQ,CAACyD,SAAS,IAAIzD,QAAQ,CAAC0D,cAAc,IAAI1D,QAAQ,CAAC2D,WAAW,EAAE;MACzE,MAAMC,SAAS,GAAG,IAAIC,IAAI,CAAC7D,QAAQ,CAAC2D,WAAW,CAAC;MAChD,MAAMG,YAAY,GAAG,IAAID,IAAI,CAAC7D,QAAQ,CAAC0D,cAAc,CAAC;MACtD,IAAII,YAAY,IAAIF,SAAS,EAAE;QAC7BnC,SAAS,CAACiC,cAAc,GAAG,wCAAwC;MACrE;IACF;IAEA,OAAO;MACL/B,OAAO,EAAEoC,MAAM,CAACC,IAAI,CAACvC,SAAS,CAAC,CAAC8B,MAAM,KAAK,CAAC;MAC5CrD,MAAM,EAAEuB;IACV,CAAC;EACH,CAAC;EAED,MAAMwC,YAAY,GAAG,MAAOC,CAAC,IAAK;IAChCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAElB,MAAMtB,UAAU,GAAGrB,YAAY,CAAC,CAAC;IACjC,IAAI,CAACqB,UAAU,CAAClB,OAAO,EAAE;MACvBxB,SAAS,CAAC0C,UAAU,CAAC3C,MAAM,CAAC;MAC5B;IACF;IAEAK,aAAa,CAAC,IAAI,CAAC;IACnB,IAAI;MACF;MACA,MAAM6D,UAAU,GAAG;QACjB,GAAGpE,QAAQ;QACX;QACAqE,gBAAgB,EAAEC,KAAK,CAACC,OAAO,CAACvE,QAAQ,CAACqE,gBAAgB,CAAC,GACtDrE,QAAQ,CAACqE,gBAAgB,GACzBrE,QAAQ,CAACqE,gBAAgB,GAAGrE,QAAQ,CAACqE,gBAAgB,CAACG,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACzC,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE;QAC5F0C,iBAAiB,EAAEL,KAAK,CAACC,OAAO,CAACvE,QAAQ,CAAC2E,iBAAiB,CAAC,GACxD3E,QAAQ,CAAC2E,iBAAiB,GAC1B3E,QAAQ,CAAC2E,iBAAiB,GAAG3E,QAAQ,CAAC2E,iBAAiB,CAACH,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACzC,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE;QAC9F2C,eAAe,EAAEN,KAAK,CAACC,OAAO,CAACvE,QAAQ,CAAC4E,eAAe,CAAC,GACpD5E,QAAQ,CAAC4E,eAAe,GACxB;MACN,CAAC;MAED,IAAIC,MAAM;MACV,IAAIpF,IAAI,KAAK,QAAQ,EAAE;QACrBoF,MAAM,GAAG,MAAM7F,UAAU,CAAC8F,YAAY,CAACV,UAAU,CAAC;MACpD,CAAC,MAAM;QACLS,MAAM,GAAG,MAAM7F,UAAU,CAAC+F,YAAY,CAACvF,WAAW,CAACwF,EAAE,EAAEZ,UAAU,CAAC;MACpE;MAEA9E,QAAQ,CAACuF,MAAM,CAAC;IAClB,CAAC,CAAC,OAAO9D,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAE9C,IAAIA,KAAK,CAACkE,iBAAiB,IAAIlE,KAAK,CAACkE,iBAAiB,CAAC,CAAC,EAAE;QACxD9E,SAAS,CAACY,KAAK,CAACmE,mBAAmB,CAAC,CAAC,CAAC;MACxC,CAAC,MAAM;QACL/E,SAAS,CAAC;UAAEuB,OAAO,EAAEX,KAAK,CAACoE,OAAO,IAAI;QAAiC,CAAC,CAAC;MAC3E;IACF,CAAC,SAAS;MACR5E,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;EAED,MAAM6B,eAAe,GAAIN,KAAK,IAAK;IACjC,IAAI,CAACA,KAAK,CAACK,WAAW,EAAE,OAAO,IAAI;IAEnC,MAAMiD,cAAc,GAAGpF,QAAQ,CAAC8B,KAAK,CAACK,WAAW,CAACL,KAAK,CAAC;IACxD,MAAMuD,aAAa,GAAGvD,KAAK,CAACK,WAAW,CAACZ,KAAK;;IAE7C;IACA,IAAI,OAAO8D,aAAa,KAAK,SAAS,IAAIA,aAAa,KAAK,MAAM,IAAIA,aAAa,KAAK,OAAO,EAAE;MAC/F,OAAOD,cAAc,MAAMC,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,MAAM,CAAC;IAChF;IAEA,OAAOD,cAAc,KAAKC,aAAa;EACzC,CAAC;EAED,MAAMC,qBAAqB,GAAGA,CAAA,KAAM;IAClC,IAAI,CAACxF,UAAU,IAAI,CAACA,UAAU,CAAC8B,MAAM,EAAE,OAAO,CAAC,CAAC;IAEhD,MAAM2D,QAAQ,GAAG,CAAC,CAAC;IACnBzF,UAAU,CAAC8B,MAAM,CAACC,OAAO,CAACC,KAAK,IAAI;MACjC,MAAM0D,UAAU,GAAG1D,KAAK,CAAC2D,OAAO,IAAI,SAAS;MAC7C,IAAI,CAACF,QAAQ,CAACC,UAAU,CAAC,EAAE;QACzBD,QAAQ,CAACC,UAAU,CAAC,GAAG;UACrBE,KAAK,EAAEC,eAAe,CAACH,UAAU,CAAC;UAClC5D,MAAM,EAAE;QACV,CAAC;MACH;MACA2D,QAAQ,CAACC,UAAU,CAAC,CAAC5D,MAAM,CAACgE,IAAI,CAAC9D,KAAK,CAAC;IACzC,CAAC,CAAC;IAEF,OAAOyD,QAAQ;EACjB,CAAC;EAED,MAAMI,eAAe,GAAIH,UAAU,IAAK;IACtC,MAAMK,MAAM,GAAG;MACbC,YAAY,EAAE,sBAAsB;MACpCC,WAAW,EAAE,qBAAqB;MAClCC,YAAY,EAAE,sBAAsB;MACpCC,YAAY,EAAE,sBAAsB;MACpCC,aAAa,EAAE,uBAAuB;MACtCC,eAAe,EAAE,kBAAkB;MACnCC,WAAW,EAAE,qBAAqB;MAClCC,gBAAgB,EAAE,mBAAmB;MACrCC,aAAa,EAAE,uBAAuB;MACtC5E,OAAO,EAAE;IACX,CAAC;IACD,OAAOmE,MAAM,CAACL,UAAU,CAAC,IAAIA,UAAU;EACzC,CAAC;EAED,IAAIpF,OAAO,EAAE;IACX,oBACEhB,OAAA;MAAKmH,SAAS,EAAC,sBAAsB;MAAAC,QAAA,gBACnCpH,OAAA;QAAKmH,SAAS,EAAC;MAAiB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACvCxH,OAAA;QAAAoH,QAAA,EAAG;MAA6B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjC,CAAC;EAEV;EAEA,IAAI,CAAC9G,UAAU,EAAE;IACf,oBACEV,OAAA;MAAKmH,SAAS,EAAC,oBAAoB;MAAAC,QAAA,gBACjCpH,OAAA;QAAAoH,QAAA,EAAG;MAAoD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAC3DxH,OAAA;QAAQyH,OAAO,EAAEtH,QAAS;QAACgH,SAAS,EAAC,iBAAiB;QAAAC,QAAA,EAAC;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpE,CAAC;EAEV;EAEA,MAAMrB,QAAQ,GAAGD,qBAAqB,CAAC,CAAC;EAIxC,oBACElG,OAAA;IAAKmH,SAAS,EAAC,qBAAqB;IAAAC,QAAA,gBAClCpH,OAAA;MAAKmH,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BpH,OAAA;QAAAoH,QAAA,EAAK/G,IAAI,KAAK,QAAQ,GAAG,mBAAmB,GAAG;MAAa;QAAAgH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAClExH,OAAA;QAAKmH,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBpH,OAAA;UAAMmH,SAAS,EAAC,WAAW;UAAAC,QAAA,EAAE1G,UAAU,CAACgH;QAAI;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,EACnD9G,UAAU,CAACiH,WAAW,iBACrB3H,OAAA;UAAMmH,SAAS,EAAC,kBAAkB;UAAAC,QAAA,EAAE1G,UAAU,CAACiH;QAAW;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAClE;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAEL1G,MAAM,CAACwB,OAAO,iBACbtC,OAAA;MAAKmH,SAAS,EAAC,mBAAmB;MAAAC,QAAA,EAAEtG,MAAM,CAACwB;IAAO;MAAA+E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CACzD,eAEDxH,OAAA;MAAME,QAAQ,EAAE2E,YAAa;MAACsC,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAEnDpH,OAAA;QAAKmH,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BpH,OAAA;UAAAoH,QAAA,EAAI;QAA6B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtCxH,OAAA,CAACH,oBAAoB;UACnB+H,iBAAiB,EAAE9F,qBAAsB;UACzC+F,gBAAgB,EAAErH,iBAAkB;UACpCoC,QAAQ,EAAE;QAAK;UAAAyE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CAAC,EACD,CAAC1G,MAAM,CAACM,UAAU,IAAIN,MAAM,CAACO,UAAU,kBACtCrB,OAAA;UAAKmH,SAAS,EAAC,eAAe;UAAAC,QAAA,EAC3BtG,MAAM,CAACM,UAAU,IAAIN,MAAM,CAACO;QAAU;UAAAgG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,EAGL7C,MAAM,CAACmD,OAAO,CAAC3B,QAAQ,CAAC,CAACd,GAAG,CAAC,CAAC,CAACe,UAAU,EAAEC,OAAO,CAAC,kBAClDrG,OAAA;QAAsBmH,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC5CpH,OAAA;UAAAoH,QAAA,EAAKf,OAAO,CAACC;QAAK;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACxBxH,OAAA;UAAKmH,SAAS,EAAC,aAAa;UAAAC,QAAA,EACzBf,OAAO,CAAC7D,MAAM,CACZuF,MAAM,CAACrF,KAAK,IAAIM,eAAe,CAACN,KAAK,CAAC,CAAC,CACvC2C,GAAG,CAAC3C,KAAK,iBACR1C,OAAA,CAACF,SAAS;YAER4C,KAAK,EAAEA,KAAM;YACbP,KAAK,EAAEvB,QAAQ,CAAC8B,KAAK,CAACC,GAAG,CAAE;YAC3BqF,QAAQ,EAAG7F,KAAK,IAAKF,iBAAiB,CAACS,KAAK,CAACC,GAAG,EAAER,KAAK,CAAE;YACzDR,KAAK,EAAEb,MAAM,CAAC4B,KAAK,CAACC,GAAG;UAAE,GAJpBD,KAAK,CAACC,GAAG;YAAA0E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAKf,CACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA,GAdEpB,UAAU;QAAAiB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAef,CACN,CAAC,eAGFxH,OAAA;QAAKmH,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BpH,OAAA;UACEoD,IAAI,EAAC,QAAQ;UACbqE,OAAO,EAAEtH,QAAS;UAClBgH,SAAS,EAAC,iBAAiB;UAC3Bc,QAAQ,EAAE/G,UAAW;UAAAkG,QAAA,EACtB;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTxH,OAAA;UACEoD,IAAI,EAAC,QAAQ;UACb+D,SAAS,EAAC,iBAAiB;UAC3Bc,QAAQ,EAAE/G,UAAU,IAAI,CAACV,iBAAiB,CAACY,UAAW;UAAAgG,QAAA,EAErDlG,UAAU,GACNb,IAAI,KAAK,QAAQ,GAAG,aAAa,GAAG,aAAa,GACjDA,IAAI,KAAK,QAAQ,GAAG,eAAe,GAAG;QAAgB;UAAAgH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAErD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACjH,EAAA,CAvYIN,iBAAiB;AAAAiI,EAAA,GAAjBjI,iBAAiB;AAyYvB,eAAeA,iBAAiB;AAAC,IAAAiI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}