.hierarchical-selector {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding: 1rem;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  background-color: #f8f9fa;
  position: relative;
  width: 100%;
}

.selector-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.selector-label {
  font-weight: 600;
  color: #495057;
  font-size: 0.875rem;
}

.selector-label .required {
  color: #dc3545;
  margin-left: 0.25rem;
}

.selector-input {
  padding: 0.75rem;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 1rem;
  background-color: white;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  width: 100%;
}

.selector-input:focus {
  outline: none;
  border-color: #80bdff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.selector-input:disabled {
  background-color: #e9ecef;
  opacity: 1;
  cursor: not-allowed;
}

.selector-input.error {
  border-color: #dc3545;
}

.selector-input.error:focus {
  border-color: #dc3545;
  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.error-message {
  color: #dc3545;
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

.selection-summary {
  margin-top: 1rem;
  padding: 1rem;
  background-color: #e7f3ff;
  border: 1px solid #b3d7ff;
  border-radius: 4px;
}

.selection-summary h4 {
  margin: 0 0 0.5rem 0;
  color: #0056b3;
  font-size: 0.875rem;
  font-weight: 600;
}

.selection-path {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.selection-item {
  background-color: #007bff;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.875rem;
  font-weight: 500;
}

.separator {
  color: #6c757d;
  font-weight: bold;
}

/* Responsive design */
@media (min-width: 768px) {
  .hierarchical-selector {
    flex-direction: row;
    align-items: end;
  }
  
  .selector-group {
    flex: 1;
    min-width: 200px;
  }
  
  .selection-summary {
    flex-basis: 100%;
    margin-top: 1rem;
  }
}

/* Loading state */
.selector-input:disabled {
  background-image: linear-gradient(45deg, transparent 25%, rgba(255,255,255,.5) 25%, rgba(255,255,255,.5) 75%, transparent 75%, transparent),
                    linear-gradient(45deg, transparent 25%, rgba(255,255,255,.5) 25%, rgba(255,255,255,.5) 75%, transparent 75%, transparent);
  background-size: 20px 20px;
  background-position: 0 0, 10px 10px;
  animation: loading 1s linear infinite;
}

@keyframes loading {
  0% {
    background-position: 0 0, 10px 10px;
  }
  100% {
    background-position: 20px 20px, 30px 30px;
  }
}
