using CrmApi.Models.State;

namespace CrmApi.Repositories.State
{
    public interface IStateRepository
    {
        Task<IEnumerable<Models.State.State>> GetAllAsync();
        Task<Models.State.State?> GetByIdAsync(int id);
        Task<Models.State.State> CreateAsync(Models.State.State state);
        Task<Models.State.State> UpdateAsync(Models.State.State state);
        Task<bool> DeleteAsync(int id);
        Task<bool> ExistsAsync(int id);
        Task<bool> NameExistsAsync(string name, int? excludeId = null);
    }
}
