{"ast": null, "code": "import { PersonFieldDefinitions, getAllPersonFields } from '../constants/personConstants';\nclass FormConfigService {\n  constructor() {\n    this.storagePrefix = 'crm_form_config_';\n    this.defaultFormKey = 'default_person_form';\n  }\n\n  // Generate storage key for form configuration\n  getStorageKey(type, id) {\n    return `${this.storagePrefix}${type}_${id}`;\n  }\n\n  // Save form configuration\n  saveFormConfig(type, id, config) {\n    try {\n      const key = this.getStorageKey(type, id);\n      const formConfig = {\n        id: `${type}_${id}`,\n        type,\n        // 'division', 'category', or 'subcategory'\n        associatedId: id,\n        name: config.name || `${type} ${id} Form`,\n        description: config.description || '',\n        fields: config.fields || [],\n        sections: config.sections || [],\n        settings: config.settings || {},\n        hierarchy: config.hierarchy || {},\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString(),\n        version: 1,\n        ...config\n      };\n      localStorage.setItem(key, JSON.stringify(formConfig));\n      return formConfig;\n    } catch (error) {\n      console.error('Error saving form configuration:', error);\n      throw new Error('Failed to save form configuration');\n    }\n  }\n\n  // Load form configuration\n  loadFormConfig(type, id) {\n    try {\n      const key = this.getStorageKey(type, id);\n      const stored = localStorage.getItem(key);\n      if (stored) {\n        return JSON.parse(stored);\n      }\n      return null;\n    } catch (error) {\n      console.error('Error loading form configuration:', error);\n      return null;\n    }\n  }\n\n  // Delete form configuration\n  deleteFormConfig(type, id) {\n    try {\n      const key = this.getStorageKey(type, id);\n      localStorage.removeItem(key);\n      return true;\n    } catch (error) {\n      console.error('Error deleting form configuration:', error);\n      return false;\n    }\n  }\n\n  // Get all form configurations\n  getAllFormConfigs() {\n    try {\n      const configs = [];\n      for (let i = 0; i < localStorage.length; i++) {\n        const key = localStorage.key(i);\n        if (key && key.startsWith(this.storagePrefix)) {\n          const config = JSON.parse(localStorage.getItem(key));\n          configs.push(config);\n        }\n      }\n      return configs;\n    } catch (error) {\n      console.error('Error loading all form configurations:', error);\n      return [];\n    }\n  }\n\n  // Get form configuration for category or subcategory\n  getFormConfigForSelection(divisionId, categoryId, subCategoryId = null) {\n    // First try to get subcategory-specific form if subcategory is selected\n    if (subCategoryId) {\n      const subCategoryForm = this.loadFormConfig('subcategory', subCategoryId);\n      if (subCategoryForm) {\n        return subCategoryForm;\n      }\n    }\n\n    // Then try category-specific form\n    const categoryForm = this.loadFormConfig('category', categoryId);\n    if (categoryForm) {\n      return categoryForm;\n    }\n\n    // Return default form if no custom form found\n    return this.getDefaultFormConfig();\n  }\n\n  // Get default form configuration with all fields\n  getDefaultFormConfig() {\n    return {\n      id: 'default',\n      type: 'default',\n      name: 'Default Person Form',\n      description: 'Default form with all person fields',\n      fields: getAllPersonFields(),\n      sections: Object.keys(PersonFieldDefinitions),\n      settings: {\n        showSections: true,\n        allowConditionalFields: true,\n        validateOnChange: true\n      },\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString(),\n      version: 1\n    };\n  }\n\n  // Create form configuration from field selection\n  createFormConfigFromFields(selectedFields, settings = {}) {\n    const config = {\n      fields: selectedFields,\n      sections: this.groupFieldsBySections(selectedFields),\n      settings: {\n        showSections: true,\n        allowConditionalFields: true,\n        validateOnChange: true,\n        ...settings\n      }\n    };\n    return config;\n  }\n\n  // Group fields by their sections\n  groupFieldsBySections(fields) {\n    const sections = {};\n    fields.forEach(field => {\n      const sectionKey = field.section;\n      if (!sections[sectionKey]) {\n        var _PersonFieldDefinitio;\n        sections[sectionKey] = {\n          key: sectionKey,\n          title: ((_PersonFieldDefinitio = PersonFieldDefinitions[sectionKey]) === null || _PersonFieldDefinitio === void 0 ? void 0 : _PersonFieldDefinitio.title) || sectionKey,\n          fields: []\n        };\n      }\n      sections[sectionKey].fields.push(field);\n    });\n    return Object.values(sections);\n  }\n\n  // Validate form configuration\n  validateFormConfig(config) {\n    const errors = [];\n    if (!config.name || config.name.trim() === '') {\n      errors.push('Form name is required');\n    }\n    if (!config.fields || config.fields.length === 0) {\n      errors.push('At least one field must be selected');\n    }\n\n    // Check if required fields are included\n    const requiredFields = ['name', 'mobileNumber', 'nature'];\n    const selectedFieldKeys = config.fields.map(f => f.key);\n    requiredFields.forEach(requiredField => {\n      if (!selectedFieldKeys.includes(requiredField)) {\n        errors.push(`Required field '${requiredField}' must be included`);\n      }\n    });\n    return {\n      isValid: errors.length === 0,\n      errors\n    };\n  }\n\n  // Export form configuration\n  exportFormConfig(type, id) {\n    const config = this.loadFormConfig(type, id);\n    if (!config) {\n      throw new Error('Form configuration not found');\n    }\n    const exportData = {\n      ...config,\n      exportedAt: new Date().toISOString(),\n      exportVersion: '1.0'\n    };\n    return JSON.stringify(exportData, null, 2);\n  }\n\n  // Import form configuration\n  importFormConfig(configJson, type, id) {\n    try {\n      const config = JSON.parse(configJson);\n\n      // Validate imported configuration\n      const validation = this.validateFormConfig(config);\n      if (!validation.isValid) {\n        throw new Error(`Invalid configuration: ${validation.errors.join(', ')}`);\n      }\n\n      // Update metadata for import\n      config.type = type;\n      config.associatedId = id;\n      config.importedAt = new Date().toISOString();\n      config.updatedAt = new Date().toISOString();\n      return this.saveFormConfig(type, id, config);\n    } catch (error) {\n      console.error('Error importing form configuration:', error);\n      throw new Error('Failed to import form configuration');\n    }\n  }\n\n  // Clone form configuration\n  cloneFormConfig(sourceType, sourceId, targetType, targetId, newName) {\n    const sourceConfig = this.loadFormConfig(sourceType, sourceId);\n    if (!sourceConfig) {\n      throw new Error('Source form configuration not found');\n    }\n    const clonedConfig = {\n      ...sourceConfig,\n      name: newName || `${sourceConfig.name} (Copy)`,\n      type: targetType,\n      associatedId: targetId,\n      clonedFrom: `${sourceType}_${sourceId}`,\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString()\n    };\n    return this.saveFormConfig(targetType, targetId, clonedConfig);\n  }\n\n  // Get form statistics\n  getFormStatistics() {\n    const allConfigs = this.getAllFormConfigs();\n    return {\n      totalForms: allConfigs.length,\n      categoryForms: allConfigs.filter(c => c.type === 'category').length,\n      subCategoryForms: allConfigs.filter(c => c.type === 'subcategory').length,\n      averageFieldCount: allConfigs.length > 0 ? Math.round(allConfigs.reduce((sum, c) => {\n        var _c$fields;\n        return sum + (((_c$fields = c.fields) === null || _c$fields === void 0 ? void 0 : _c$fields.length) || 0);\n      }, 0) / allConfigs.length) : 0,\n      mostUsedFields: this.getMostUsedFields(allConfigs),\n      recentlyModified: allConfigs.sort((a, b) => new Date(b.updatedAt) - new Date(a.updatedAt)).slice(0, 5)\n    };\n  }\n\n  // Get most used fields across all forms\n  getMostUsedFields(configs) {\n    const fieldUsage = {};\n    configs.forEach(config => {\n      if (config.fields) {\n        config.fields.forEach(field => {\n          fieldUsage[field.key] = (fieldUsage[field.key] || 0) + 1;\n        });\n      }\n    });\n    return Object.entries(fieldUsage).sort(([, a], [, b]) => b - a).slice(0, 10).map(([key, count]) => ({\n      field: key,\n      usage: count\n    }));\n  }\n\n  // Get all saved form configurations\n  getAllFormConfigs() {\n    try {\n      const forms = [];\n      for (let i = 0; i < localStorage.length; i++) {\n        const key = localStorage.key(i);\n        if (key && key.startsWith(this.storagePrefix)) {\n          const stored = localStorage.getItem(key);\n          if (stored) {\n            var _config$fields;\n            const config = JSON.parse(stored);\n            forms.push({\n              key,\n              ...config,\n              summary: {\n                fieldCount: ((_config$fields = config.fields) === null || _config$fields === void 0 ? void 0 : _config$fields.length) || 0,\n                type: config.type,\n                associatedId: config.associatedId,\n                createdAt: config.createdAt,\n                updatedAt: config.updatedAt\n              }\n            });\n          }\n        }\n      }\n\n      // Sort by updated date (most recent first)\n      return forms.sort((a, b) => new Date(b.updatedAt) - new Date(a.updatedAt));\n    } catch (error) {\n      console.error('Error getting form configurations:', error);\n      return [];\n    }\n  }\n\n  // Delete a specific form configuration\n  deleteFormConfig(type, id) {\n    try {\n      const key = this.getStorageKey(type, id);\n      localStorage.removeItem(key);\n      return true;\n    } catch (error) {\n      console.error('Error deleting form configuration:', error);\n      return false;\n    }\n  }\n\n  // Clear all form configurations (for development/testing)\n  clearAllFormConfigs() {\n    try {\n      const keysToRemove = [];\n      for (let i = 0; i < localStorage.length; i++) {\n        const key = localStorage.key(i);\n        if (key && key.startsWith(this.storagePrefix)) {\n          keysToRemove.push(key);\n        }\n      }\n      keysToRemove.forEach(key => localStorage.removeItem(key));\n      return true;\n    } catch (error) {\n      console.error('Error clearing form configurations:', error);\n      return false;\n    }\n  }\n}\nexport default new FormConfigService();", "map": {"version": 3, "names": ["PersonFieldDefinitions", "<PERSON><PERSON><PERSON><PERSON><PERSON>F<PERSON>s", "FormConfigService", "constructor", "storagePrefix", "defaultFormKey", "getStorageKey", "type", "id", "saveFormConfig", "config", "key", "formConfig", "associatedId", "name", "description", "fields", "sections", "settings", "hierarchy", "createdAt", "Date", "toISOString", "updatedAt", "version", "localStorage", "setItem", "JSON", "stringify", "error", "console", "Error", "loadFormConfig", "stored", "getItem", "parse", "deleteFormConfig", "removeItem", "getAllFormConfigs", "configs", "i", "length", "startsWith", "push", "getFormConfigForSelection", "divisionId", "categoryId", "subCategoryId", "subCategoryForm", "categoryForm", "getDefaultFormConfig", "Object", "keys", "showSections", "allowConditionalFields", "validateOnChange", "createFormConfigFromFields", "<PERSON><PERSON><PERSON>s", "groupFieldsBySections", "for<PERSON>ach", "field", "sectionKey", "section", "_PersonFieldDefinitio", "title", "values", "validateFormConfig", "errors", "trim", "requiredFields", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "map", "f", "requiredField", "includes", "<PERSON><PERSON><PERSON><PERSON>", "exportFormConfig", "exportData", "exportedAt", "exportVersion", "importFormConfig", "config<PERSON><PERSON>", "validation", "join", "importedAt", "cloneFormConfig", "sourceType", "sourceId", "targetType", "targetId", "newName", "sourceConfig", "clonedConfig", "clonedFrom", "getFormStatistics", "allConfigs", "totalForms", "categoryForms", "filter", "c", "subCategoryForms", "averageFieldCount", "Math", "round", "reduce", "sum", "_c$fields", "mostUsedFields", "getMostUsedFields", "recentlyModified", "sort", "a", "b", "slice", "fieldUsage", "entries", "count", "usage", "forms", "_config$fields", "summary", "fieldCount", "clearAllFormConfigs", "keysToRemove"], "sources": ["C:/Users/<USER>/Downloads/Advanced/Advanced CRM filed all use/frontend/src/services/formConfigService.js"], "sourcesContent": ["import { PersonFieldDefinitions, getAllPersonFields } from '../constants/personConstants';\n\nclass FormConfigService {\n  constructor() {\n    this.storagePrefix = 'crm_form_config_';\n    this.defaultFormKey = 'default_person_form';\n  }\n\n  // Generate storage key for form configuration\n  getStorageKey(type, id) {\n    return `${this.storagePrefix}${type}_${id}`;\n  }\n\n  // Save form configuration\n  saveFormConfig(type, id, config) {\n    try {\n      const key = this.getStorageKey(type, id);\n      const formConfig = {\n        id: `${type}_${id}`,\n        type, // 'division', 'category', or 'subcategory'\n        associatedId: id,\n        name: config.name || `${type} ${id} Form`,\n        description: config.description || '',\n        fields: config.fields || [],\n        sections: config.sections || [],\n        settings: config.settings || {},\n        hierarchy: config.hierarchy || {},\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString(),\n        version: 1,\n        ...config\n      };\n\n      localStorage.setItem(key, JSON.stringify(formConfig));\n      return formConfig;\n    } catch (error) {\n      console.error('Error saving form configuration:', error);\n      throw new Error('Failed to save form configuration');\n    }\n  }\n\n  // Load form configuration\n  loadFormConfig(type, id) {\n    try {\n      const key = this.getStorageKey(type, id);\n      const stored = localStorage.getItem(key);\n      \n      if (stored) {\n        return JSON.parse(stored);\n      }\n      \n      return null;\n    } catch (error) {\n      console.error('Error loading form configuration:', error);\n      return null;\n    }\n  }\n\n  // Delete form configuration\n  deleteFormConfig(type, id) {\n    try {\n      const key = this.getStorageKey(type, id);\n      localStorage.removeItem(key);\n      return true;\n    } catch (error) {\n      console.error('Error deleting form configuration:', error);\n      return false;\n    }\n  }\n\n  // Get all form configurations\n  getAllFormConfigs() {\n    try {\n      const configs = [];\n      for (let i = 0; i < localStorage.length; i++) {\n        const key = localStorage.key(i);\n        if (key && key.startsWith(this.storagePrefix)) {\n          const config = JSON.parse(localStorage.getItem(key));\n          configs.push(config);\n        }\n      }\n      return configs;\n    } catch (error) {\n      console.error('Error loading all form configurations:', error);\n      return [];\n    }\n  }\n\n  // Get form configuration for category or subcategory\n  getFormConfigForSelection(divisionId, categoryId, subCategoryId = null) {\n    // First try to get subcategory-specific form if subcategory is selected\n    if (subCategoryId) {\n      const subCategoryForm = this.loadFormConfig('subcategory', subCategoryId);\n      if (subCategoryForm) {\n        return subCategoryForm;\n      }\n    }\n\n    // Then try category-specific form\n    const categoryForm = this.loadFormConfig('category', categoryId);\n    if (categoryForm) {\n      return categoryForm;\n    }\n\n    // Return default form if no custom form found\n    return this.getDefaultFormConfig();\n  }\n\n  // Get default form configuration with all fields\n  getDefaultFormConfig() {\n    return {\n      id: 'default',\n      type: 'default',\n      name: 'Default Person Form',\n      description: 'Default form with all person fields',\n      fields: getAllPersonFields(),\n      sections: Object.keys(PersonFieldDefinitions),\n      settings: {\n        showSections: true,\n        allowConditionalFields: true,\n        validateOnChange: true\n      },\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString(),\n      version: 1\n    };\n  }\n\n  // Create form configuration from field selection\n  createFormConfigFromFields(selectedFields, settings = {}) {\n    const config = {\n      fields: selectedFields,\n      sections: this.groupFieldsBySections(selectedFields),\n      settings: {\n        showSections: true,\n        allowConditionalFields: true,\n        validateOnChange: true,\n        ...settings\n      }\n    };\n\n    return config;\n  }\n\n  // Group fields by their sections\n  groupFieldsBySections(fields) {\n    const sections = {};\n    \n    fields.forEach(field => {\n      const sectionKey = field.section;\n      if (!sections[sectionKey]) {\n        sections[sectionKey] = {\n          key: sectionKey,\n          title: PersonFieldDefinitions[sectionKey]?.title || sectionKey,\n          fields: []\n        };\n      }\n      sections[sectionKey].fields.push(field);\n    });\n\n    return Object.values(sections);\n  }\n\n  // Validate form configuration\n  validateFormConfig(config) {\n    const errors = [];\n\n    if (!config.name || config.name.trim() === '') {\n      errors.push('Form name is required');\n    }\n\n    if (!config.fields || config.fields.length === 0) {\n      errors.push('At least one field must be selected');\n    }\n\n    // Check if required fields are included\n    const requiredFields = ['name', 'mobileNumber', 'nature'];\n    const selectedFieldKeys = config.fields.map(f => f.key);\n    \n    requiredFields.forEach(requiredField => {\n      if (!selectedFieldKeys.includes(requiredField)) {\n        errors.push(`Required field '${requiredField}' must be included`);\n      }\n    });\n\n    return {\n      isValid: errors.length === 0,\n      errors\n    };\n  }\n\n  // Export form configuration\n  exportFormConfig(type, id) {\n    const config = this.loadFormConfig(type, id);\n    if (!config) {\n      throw new Error('Form configuration not found');\n    }\n\n    const exportData = {\n      ...config,\n      exportedAt: new Date().toISOString(),\n      exportVersion: '1.0'\n    };\n\n    return JSON.stringify(exportData, null, 2);\n  }\n\n  // Import form configuration\n  importFormConfig(configJson, type, id) {\n    try {\n      const config = JSON.parse(configJson);\n      \n      // Validate imported configuration\n      const validation = this.validateFormConfig(config);\n      if (!validation.isValid) {\n        throw new Error(`Invalid configuration: ${validation.errors.join(', ')}`);\n      }\n\n      // Update metadata for import\n      config.type = type;\n      config.associatedId = id;\n      config.importedAt = new Date().toISOString();\n      config.updatedAt = new Date().toISOString();\n\n      return this.saveFormConfig(type, id, config);\n    } catch (error) {\n      console.error('Error importing form configuration:', error);\n      throw new Error('Failed to import form configuration');\n    }\n  }\n\n  // Clone form configuration\n  cloneFormConfig(sourceType, sourceId, targetType, targetId, newName) {\n    const sourceConfig = this.loadFormConfig(sourceType, sourceId);\n    if (!sourceConfig) {\n      throw new Error('Source form configuration not found');\n    }\n\n    const clonedConfig = {\n      ...sourceConfig,\n      name: newName || `${sourceConfig.name} (Copy)`,\n      type: targetType,\n      associatedId: targetId,\n      clonedFrom: `${sourceType}_${sourceId}`,\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString()\n    };\n\n    return this.saveFormConfig(targetType, targetId, clonedConfig);\n  }\n\n  // Get form statistics\n  getFormStatistics() {\n    const allConfigs = this.getAllFormConfigs();\n    \n    return {\n      totalForms: allConfigs.length,\n      categoryForms: allConfigs.filter(c => c.type === 'category').length,\n      subCategoryForms: allConfigs.filter(c => c.type === 'subcategory').length,\n      averageFieldCount: allConfigs.length > 0 \n        ? Math.round(allConfigs.reduce((sum, c) => sum + (c.fields?.length || 0), 0) / allConfigs.length)\n        : 0,\n      mostUsedFields: this.getMostUsedFields(allConfigs),\n      recentlyModified: allConfigs\n        .sort((a, b) => new Date(b.updatedAt) - new Date(a.updatedAt))\n        .slice(0, 5)\n    };\n  }\n\n  // Get most used fields across all forms\n  getMostUsedFields(configs) {\n    const fieldUsage = {};\n    \n    configs.forEach(config => {\n      if (config.fields) {\n        config.fields.forEach(field => {\n          fieldUsage[field.key] = (fieldUsage[field.key] || 0) + 1;\n        });\n      }\n    });\n\n    return Object.entries(fieldUsage)\n      .sort(([,a], [,b]) => b - a)\n      .slice(0, 10)\n      .map(([key, count]) => ({ field: key, usage: count }));\n  }\n\n  // Get all saved form configurations\n  getAllFormConfigs() {\n    try {\n      const forms = [];\n      for (let i = 0; i < localStorage.length; i++) {\n        const key = localStorage.key(i);\n        if (key && key.startsWith(this.storagePrefix)) {\n          const stored = localStorage.getItem(key);\n          if (stored) {\n            const config = JSON.parse(stored);\n            forms.push({\n              key,\n              ...config,\n              summary: {\n                fieldCount: config.fields?.length || 0,\n                type: config.type,\n                associatedId: config.associatedId,\n                createdAt: config.createdAt,\n                updatedAt: config.updatedAt\n              }\n            });\n          }\n        }\n      }\n\n      // Sort by updated date (most recent first)\n      return forms.sort((a, b) => new Date(b.updatedAt) - new Date(a.updatedAt));\n    } catch (error) {\n      console.error('Error getting form configurations:', error);\n      return [];\n    }\n  }\n\n  // Delete a specific form configuration\n  deleteFormConfig(type, id) {\n    try {\n      const key = this.getStorageKey(type, id);\n      localStorage.removeItem(key);\n      return true;\n    } catch (error) {\n      console.error('Error deleting form configuration:', error);\n      return false;\n    }\n  }\n\n  // Clear all form configurations (for development/testing)\n  clearAllFormConfigs() {\n    try {\n      const keysToRemove = [];\n      for (let i = 0; i < localStorage.length; i++) {\n        const key = localStorage.key(i);\n        if (key && key.startsWith(this.storagePrefix)) {\n          keysToRemove.push(key);\n        }\n      }\n\n      keysToRemove.forEach(key => localStorage.removeItem(key));\n      return true;\n    } catch (error) {\n      console.error('Error clearing form configurations:', error);\n      return false;\n    }\n  }\n}\n\nexport default new FormConfigService();\n"], "mappings": "AAAA,SAASA,sBAAsB,EAAEC,kBAAkB,QAAQ,8BAA8B;AAEzF,MAAMC,iBAAiB,CAAC;EACtBC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,aAAa,GAAG,kBAAkB;IACvC,IAAI,CAACC,cAAc,GAAG,qBAAqB;EAC7C;;EAEA;EACAC,aAAaA,CAACC,IAAI,EAAEC,EAAE,EAAE;IACtB,OAAO,GAAG,IAAI,CAACJ,aAAa,GAAGG,IAAI,IAAIC,EAAE,EAAE;EAC7C;;EAEA;EACAC,cAAcA,CAACF,IAAI,EAAEC,EAAE,EAAEE,MAAM,EAAE;IAC/B,IAAI;MACF,MAAMC,GAAG,GAAG,IAAI,CAACL,aAAa,CAACC,IAAI,EAAEC,EAAE,CAAC;MACxC,MAAMI,UAAU,GAAG;QACjBJ,EAAE,EAAE,GAAGD,IAAI,IAAIC,EAAE,EAAE;QACnBD,IAAI;QAAE;QACNM,YAAY,EAAEL,EAAE;QAChBM,IAAI,EAAEJ,MAAM,CAACI,IAAI,IAAI,GAAGP,IAAI,IAAIC,EAAE,OAAO;QACzCO,WAAW,EAAEL,MAAM,CAACK,WAAW,IAAI,EAAE;QACrCC,MAAM,EAAEN,MAAM,CAACM,MAAM,IAAI,EAAE;QAC3BC,QAAQ,EAAEP,MAAM,CAACO,QAAQ,IAAI,EAAE;QAC/BC,QAAQ,EAAER,MAAM,CAACQ,QAAQ,IAAI,CAAC,CAAC;QAC/BC,SAAS,EAAET,MAAM,CAACS,SAAS,IAAI,CAAC,CAAC;QACjCC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;QACnCC,SAAS,EAAE,IAAIF,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;QACnCE,OAAO,EAAE,CAAC;QACV,GAAGd;MACL,CAAC;MAEDe,YAAY,CAACC,OAAO,CAACf,GAAG,EAAEgB,IAAI,CAACC,SAAS,CAAChB,UAAU,CAAC,CAAC;MACrD,OAAOA,UAAU;IACnB,CAAC,CAAC,OAAOiB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxD,MAAM,IAAIE,KAAK,CAAC,mCAAmC,CAAC;IACtD;EACF;;EAEA;EACAC,cAAcA,CAACzB,IAAI,EAAEC,EAAE,EAAE;IACvB,IAAI;MACF,MAAMG,GAAG,GAAG,IAAI,CAACL,aAAa,CAACC,IAAI,EAAEC,EAAE,CAAC;MACxC,MAAMyB,MAAM,GAAGR,YAAY,CAACS,OAAO,CAACvB,GAAG,CAAC;MAExC,IAAIsB,MAAM,EAAE;QACV,OAAON,IAAI,CAACQ,KAAK,CAACF,MAAM,CAAC;MAC3B;MAEA,OAAO,IAAI;IACb,CAAC,CAAC,OAAOJ,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MACzD,OAAO,IAAI;IACb;EACF;;EAEA;EACAO,gBAAgBA,CAAC7B,IAAI,EAAEC,EAAE,EAAE;IACzB,IAAI;MACF,MAAMG,GAAG,GAAG,IAAI,CAACL,aAAa,CAACC,IAAI,EAAEC,EAAE,CAAC;MACxCiB,YAAY,CAACY,UAAU,CAAC1B,GAAG,CAAC;MAC5B,OAAO,IAAI;IACb,CAAC,CAAC,OAAOkB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;MAC1D,OAAO,KAAK;IACd;EACF;;EAEA;EACAS,iBAAiBA,CAAA,EAAG;IAClB,IAAI;MACF,MAAMC,OAAO,GAAG,EAAE;MAClB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGf,YAAY,CAACgB,MAAM,EAAED,CAAC,EAAE,EAAE;QAC5C,MAAM7B,GAAG,GAAGc,YAAY,CAACd,GAAG,CAAC6B,CAAC,CAAC;QAC/B,IAAI7B,GAAG,IAAIA,GAAG,CAAC+B,UAAU,CAAC,IAAI,CAACtC,aAAa,CAAC,EAAE;UAC7C,MAAMM,MAAM,GAAGiB,IAAI,CAACQ,KAAK,CAACV,YAAY,CAACS,OAAO,CAACvB,GAAG,CAAC,CAAC;UACpD4B,OAAO,CAACI,IAAI,CAACjC,MAAM,CAAC;QACtB;MACF;MACA,OAAO6B,OAAO;IAChB,CAAC,CAAC,OAAOV,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;MAC9D,OAAO,EAAE;IACX;EACF;;EAEA;EACAe,yBAAyBA,CAACC,UAAU,EAAEC,UAAU,EAAEC,aAAa,GAAG,IAAI,EAAE;IACtE;IACA,IAAIA,aAAa,EAAE;MACjB,MAAMC,eAAe,GAAG,IAAI,CAAChB,cAAc,CAAC,aAAa,EAAEe,aAAa,CAAC;MACzE,IAAIC,eAAe,EAAE;QACnB,OAAOA,eAAe;MACxB;IACF;;IAEA;IACA,MAAMC,YAAY,GAAG,IAAI,CAACjB,cAAc,CAAC,UAAU,EAAEc,UAAU,CAAC;IAChE,IAAIG,YAAY,EAAE;MAChB,OAAOA,YAAY;IACrB;;IAEA;IACA,OAAO,IAAI,CAACC,oBAAoB,CAAC,CAAC;EACpC;;EAEA;EACAA,oBAAoBA,CAAA,EAAG;IACrB,OAAO;MACL1C,EAAE,EAAE,SAAS;MACbD,IAAI,EAAE,SAAS;MACfO,IAAI,EAAE,qBAAqB;MAC3BC,WAAW,EAAE,qCAAqC;MAClDC,MAAM,EAAEf,kBAAkB,CAAC,CAAC;MAC5BgB,QAAQ,EAAEkC,MAAM,CAACC,IAAI,CAACpD,sBAAsB,CAAC;MAC7CkB,QAAQ,EAAE;QACRmC,YAAY,EAAE,IAAI;QAClBC,sBAAsB,EAAE,IAAI;QAC5BC,gBAAgB,EAAE;MACpB,CAAC;MACDnC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MACnCC,SAAS,EAAE,IAAIF,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MACnCE,OAAO,EAAE;IACX,CAAC;EACH;;EAEA;EACAgC,0BAA0BA,CAACC,cAAc,EAAEvC,QAAQ,GAAG,CAAC,CAAC,EAAE;IACxD,MAAMR,MAAM,GAAG;MACbM,MAAM,EAAEyC,cAAc;MACtBxC,QAAQ,EAAE,IAAI,CAACyC,qBAAqB,CAACD,cAAc,CAAC;MACpDvC,QAAQ,EAAE;QACRmC,YAAY,EAAE,IAAI;QAClBC,sBAAsB,EAAE,IAAI;QAC5BC,gBAAgB,EAAE,IAAI;QACtB,GAAGrC;MACL;IACF,CAAC;IAED,OAAOR,MAAM;EACf;;EAEA;EACAgD,qBAAqBA,CAAC1C,MAAM,EAAE;IAC5B,MAAMC,QAAQ,GAAG,CAAC,CAAC;IAEnBD,MAAM,CAAC2C,OAAO,CAACC,KAAK,IAAI;MACtB,MAAMC,UAAU,GAAGD,KAAK,CAACE,OAAO;MAChC,IAAI,CAAC7C,QAAQ,CAAC4C,UAAU,CAAC,EAAE;QAAA,IAAAE,qBAAA;QACzB9C,QAAQ,CAAC4C,UAAU,CAAC,GAAG;UACrBlD,GAAG,EAAEkD,UAAU;UACfG,KAAK,EAAE,EAAAD,qBAAA,GAAA/D,sBAAsB,CAAC6D,UAAU,CAAC,cAAAE,qBAAA,uBAAlCA,qBAAA,CAAoCC,KAAK,KAAIH,UAAU;UAC9D7C,MAAM,EAAE;QACV,CAAC;MACH;MACAC,QAAQ,CAAC4C,UAAU,CAAC,CAAC7C,MAAM,CAAC2B,IAAI,CAACiB,KAAK,CAAC;IACzC,CAAC,CAAC;IAEF,OAAOT,MAAM,CAACc,MAAM,CAAChD,QAAQ,CAAC;EAChC;;EAEA;EACAiD,kBAAkBA,CAACxD,MAAM,EAAE;IACzB,MAAMyD,MAAM,GAAG,EAAE;IAEjB,IAAI,CAACzD,MAAM,CAACI,IAAI,IAAIJ,MAAM,CAACI,IAAI,CAACsD,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MAC7CD,MAAM,CAACxB,IAAI,CAAC,uBAAuB,CAAC;IACtC;IAEA,IAAI,CAACjC,MAAM,CAACM,MAAM,IAAIN,MAAM,CAACM,MAAM,CAACyB,MAAM,KAAK,CAAC,EAAE;MAChD0B,MAAM,CAACxB,IAAI,CAAC,qCAAqC,CAAC;IACpD;;IAEA;IACA,MAAM0B,cAAc,GAAG,CAAC,MAAM,EAAE,cAAc,EAAE,QAAQ,CAAC;IACzD,MAAMC,iBAAiB,GAAG5D,MAAM,CAACM,MAAM,CAACuD,GAAG,CAACC,CAAC,IAAIA,CAAC,CAAC7D,GAAG,CAAC;IAEvD0D,cAAc,CAACV,OAAO,CAACc,aAAa,IAAI;MACtC,IAAI,CAACH,iBAAiB,CAACI,QAAQ,CAACD,aAAa,CAAC,EAAE;QAC9CN,MAAM,CAACxB,IAAI,CAAC,mBAAmB8B,aAAa,oBAAoB,CAAC;MACnE;IACF,CAAC,CAAC;IAEF,OAAO;MACLE,OAAO,EAAER,MAAM,CAAC1B,MAAM,KAAK,CAAC;MAC5B0B;IACF,CAAC;EACH;;EAEA;EACAS,gBAAgBA,CAACrE,IAAI,EAAEC,EAAE,EAAE;IACzB,MAAME,MAAM,GAAG,IAAI,CAACsB,cAAc,CAACzB,IAAI,EAAEC,EAAE,CAAC;IAC5C,IAAI,CAACE,MAAM,EAAE;MACX,MAAM,IAAIqB,KAAK,CAAC,8BAA8B,CAAC;IACjD;IAEA,MAAM8C,UAAU,GAAG;MACjB,GAAGnE,MAAM;MACToE,UAAU,EAAE,IAAIzD,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MACpCyD,aAAa,EAAE;IACjB,CAAC;IAED,OAAOpD,IAAI,CAACC,SAAS,CAACiD,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC;EAC5C;;EAEA;EACAG,gBAAgBA,CAACC,UAAU,EAAE1E,IAAI,EAAEC,EAAE,EAAE;IACrC,IAAI;MACF,MAAME,MAAM,GAAGiB,IAAI,CAACQ,KAAK,CAAC8C,UAAU,CAAC;;MAErC;MACA,MAAMC,UAAU,GAAG,IAAI,CAAChB,kBAAkB,CAACxD,MAAM,CAAC;MAClD,IAAI,CAACwE,UAAU,CAACP,OAAO,EAAE;QACvB,MAAM,IAAI5C,KAAK,CAAC,0BAA0BmD,UAAU,CAACf,MAAM,CAACgB,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;MAC3E;;MAEA;MACAzE,MAAM,CAACH,IAAI,GAAGA,IAAI;MAClBG,MAAM,CAACG,YAAY,GAAGL,EAAE;MACxBE,MAAM,CAAC0E,UAAU,GAAG,IAAI/D,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MAC5CZ,MAAM,CAACa,SAAS,GAAG,IAAIF,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MAE3C,OAAO,IAAI,CAACb,cAAc,CAACF,IAAI,EAAEC,EAAE,EAAEE,MAAM,CAAC;IAC9C,CAAC,CAAC,OAAOmB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC3D,MAAM,IAAIE,KAAK,CAAC,qCAAqC,CAAC;IACxD;EACF;;EAEA;EACAsD,eAAeA,CAACC,UAAU,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,OAAO,EAAE;IACnE,MAAMC,YAAY,GAAG,IAAI,CAAC3D,cAAc,CAACsD,UAAU,EAAEC,QAAQ,CAAC;IAC9D,IAAI,CAACI,YAAY,EAAE;MACjB,MAAM,IAAI5D,KAAK,CAAC,qCAAqC,CAAC;IACxD;IAEA,MAAM6D,YAAY,GAAG;MACnB,GAAGD,YAAY;MACf7E,IAAI,EAAE4E,OAAO,IAAI,GAAGC,YAAY,CAAC7E,IAAI,SAAS;MAC9CP,IAAI,EAAEiF,UAAU;MAChB3E,YAAY,EAAE4E,QAAQ;MACtBI,UAAU,EAAE,GAAGP,UAAU,IAAIC,QAAQ,EAAE;MACvCnE,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MACnCC,SAAS,EAAE,IAAIF,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;IACpC,CAAC;IAED,OAAO,IAAI,CAACb,cAAc,CAAC+E,UAAU,EAAEC,QAAQ,EAAEG,YAAY,CAAC;EAChE;;EAEA;EACAE,iBAAiBA,CAAA,EAAG;IAClB,MAAMC,UAAU,GAAG,IAAI,CAACzD,iBAAiB,CAAC,CAAC;IAE3C,OAAO;MACL0D,UAAU,EAAED,UAAU,CAACtD,MAAM;MAC7BwD,aAAa,EAAEF,UAAU,CAACG,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC5F,IAAI,KAAK,UAAU,CAAC,CAACkC,MAAM;MACnE2D,gBAAgB,EAAEL,UAAU,CAACG,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC5F,IAAI,KAAK,aAAa,CAAC,CAACkC,MAAM;MACzE4D,iBAAiB,EAAEN,UAAU,CAACtD,MAAM,GAAG,CAAC,GACpC6D,IAAI,CAACC,KAAK,CAACR,UAAU,CAACS,MAAM,CAAC,CAACC,GAAG,EAAEN,CAAC;QAAA,IAAAO,SAAA;QAAA,OAAKD,GAAG,IAAI,EAAAC,SAAA,GAAAP,CAAC,CAACnF,MAAM,cAAA0F,SAAA,uBAARA,SAAA,CAAUjE,MAAM,KAAI,CAAC,CAAC;MAAA,GAAE,CAAC,CAAC,GAAGsD,UAAU,CAACtD,MAAM,CAAC,GAC/F,CAAC;MACLkE,cAAc,EAAE,IAAI,CAACC,iBAAiB,CAACb,UAAU,CAAC;MAClDc,gBAAgB,EAAEd,UAAU,CACzBe,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAI3F,IAAI,CAAC2F,CAAC,CAACzF,SAAS,CAAC,GAAG,IAAIF,IAAI,CAAC0F,CAAC,CAACxF,SAAS,CAAC,CAAC,CAC7D0F,KAAK,CAAC,CAAC,EAAE,CAAC;IACf,CAAC;EACH;;EAEA;EACAL,iBAAiBA,CAACrE,OAAO,EAAE;IACzB,MAAM2E,UAAU,GAAG,CAAC,CAAC;IAErB3E,OAAO,CAACoB,OAAO,CAACjD,MAAM,IAAI;MACxB,IAAIA,MAAM,CAACM,MAAM,EAAE;QACjBN,MAAM,CAACM,MAAM,CAAC2C,OAAO,CAACC,KAAK,IAAI;UAC7BsD,UAAU,CAACtD,KAAK,CAACjD,GAAG,CAAC,GAAG,CAACuG,UAAU,CAACtD,KAAK,CAACjD,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC;QAC1D,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;IAEF,OAAOwC,MAAM,CAACgE,OAAO,CAACD,UAAU,CAAC,CAC9BJ,IAAI,CAAC,CAAC,GAAEC,CAAC,CAAC,EAAE,GAAEC,CAAC,CAAC,KAAKA,CAAC,GAAGD,CAAC,CAAC,CAC3BE,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CACZ1C,GAAG,CAAC,CAAC,CAAC5D,GAAG,EAAEyG,KAAK,CAAC,MAAM;MAAExD,KAAK,EAAEjD,GAAG;MAAE0G,KAAK,EAAED;IAAM,CAAC,CAAC,CAAC;EAC1D;;EAEA;EACA9E,iBAAiBA,CAAA,EAAG;IAClB,IAAI;MACF,MAAMgF,KAAK,GAAG,EAAE;MAChB,KAAK,IAAI9E,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGf,YAAY,CAACgB,MAAM,EAAED,CAAC,EAAE,EAAE;QAC5C,MAAM7B,GAAG,GAAGc,YAAY,CAACd,GAAG,CAAC6B,CAAC,CAAC;QAC/B,IAAI7B,GAAG,IAAIA,GAAG,CAAC+B,UAAU,CAAC,IAAI,CAACtC,aAAa,CAAC,EAAE;UAC7C,MAAM6B,MAAM,GAAGR,YAAY,CAACS,OAAO,CAACvB,GAAG,CAAC;UACxC,IAAIsB,MAAM,EAAE;YAAA,IAAAsF,cAAA;YACV,MAAM7G,MAAM,GAAGiB,IAAI,CAACQ,KAAK,CAACF,MAAM,CAAC;YACjCqF,KAAK,CAAC3E,IAAI,CAAC;cACThC,GAAG;cACH,GAAGD,MAAM;cACT8G,OAAO,EAAE;gBACPC,UAAU,EAAE,EAAAF,cAAA,GAAA7G,MAAM,CAACM,MAAM,cAAAuG,cAAA,uBAAbA,cAAA,CAAe9E,MAAM,KAAI,CAAC;gBACtClC,IAAI,EAAEG,MAAM,CAACH,IAAI;gBACjBM,YAAY,EAAEH,MAAM,CAACG,YAAY;gBACjCO,SAAS,EAAEV,MAAM,CAACU,SAAS;gBAC3BG,SAAS,EAAEb,MAAM,CAACa;cACpB;YACF,CAAC,CAAC;UACJ;QACF;MACF;;MAEA;MACA,OAAO+F,KAAK,CAACR,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAI3F,IAAI,CAAC2F,CAAC,CAACzF,SAAS,CAAC,GAAG,IAAIF,IAAI,CAAC0F,CAAC,CAACxF,SAAS,CAAC,CAAC;IAC5E,CAAC,CAAC,OAAOM,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;MAC1D,OAAO,EAAE;IACX;EACF;;EAEA;EACAO,gBAAgBA,CAAC7B,IAAI,EAAEC,EAAE,EAAE;IACzB,IAAI;MACF,MAAMG,GAAG,GAAG,IAAI,CAACL,aAAa,CAACC,IAAI,EAAEC,EAAE,CAAC;MACxCiB,YAAY,CAACY,UAAU,CAAC1B,GAAG,CAAC;MAC5B,OAAO,IAAI;IACb,CAAC,CAAC,OAAOkB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;MAC1D,OAAO,KAAK;IACd;EACF;;EAEA;EACA6F,mBAAmBA,CAAA,EAAG;IACpB,IAAI;MACF,MAAMC,YAAY,GAAG,EAAE;MACvB,KAAK,IAAInF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGf,YAAY,CAACgB,MAAM,EAAED,CAAC,EAAE,EAAE;QAC5C,MAAM7B,GAAG,GAAGc,YAAY,CAACd,GAAG,CAAC6B,CAAC,CAAC;QAC/B,IAAI7B,GAAG,IAAIA,GAAG,CAAC+B,UAAU,CAAC,IAAI,CAACtC,aAAa,CAAC,EAAE;UAC7CuH,YAAY,CAAChF,IAAI,CAAChC,GAAG,CAAC;QACxB;MACF;MAEAgH,YAAY,CAAChE,OAAO,CAAChD,GAAG,IAAIc,YAAY,CAACY,UAAU,CAAC1B,GAAG,CAAC,CAAC;MACzD,OAAO,IAAI;IACb,CAAC,CAAC,OAAOkB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC3D,OAAO,KAAK;IACd;EACF;AACF;AAEA,eAAe,IAAI3B,iBAAiB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}