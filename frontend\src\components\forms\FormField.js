import React from 'react';
import './FormField.css';

const FormField = ({ field, value, onChange, error }) => {
  const handleChange = (e) => {
    const { type, checked, value: inputValue } = e.target;
    
    if (type === 'checkbox') {
      onChange(checked);
    } else if (type === 'number') {
      onChange(inputValue ? parseFloat(inputValue) : null);
    } else {
      onChange(inputValue);
    }
  };

  const handleArrayChange = (e) => {
    const inputValue = e.target.value;
    const arrayValue = inputValue.split(',').map(item => item.trim()).filter(item => item);
    onChange(arrayValue);
  };

  const handleMultiSelectChange = (e) => {
    const selectedOptions = Array.from(e.target.selectedOptions, option => 
      isNaN(option.value) ? option.value : parseInt(option.value)
    );
    onChange(selectedOptions);
  };

  const renderField = () => {
    const commonProps = {
      id: field.key,
      name: field.key,
      placeholder: field.placeholder || '',
      className: `form-input ${error ? 'error' : ''}`,
      'aria-describedby': error ? `${field.key}-error` : undefined
    };

    switch (field.type) {
      case 'text':
      case 'email':
      case 'tel':
      case 'url':
        return (
          <input
            {...commonProps}
            type={field.type}
            value={value || ''}
            onChange={handleChange}
            maxLength={field.validation?.maxLength}
            minLength={field.validation?.minLength}
            pattern={field.validation?.pattern}
            required={field.required}
          />
        );

      case 'textarea':
        return (
          <textarea
            {...commonProps}
            value={value || ''}
            onChange={handleChange}
            rows={field.rows || 3}
            maxLength={field.validation?.maxLength}
            minLength={field.validation?.minLength}
            required={field.required}
          />
        );

      case 'number':
        return (
          <input
            {...commonProps}
            type="number"
            value={value || ''}
            onChange={handleChange}
            min={field.validation?.min}
            max={field.validation?.max}
            step={field.validation?.step || 'any'}
            required={field.required}
          />
        );

      case 'date':
        return (
          <input
            {...commonProps}
            type="date"
            value={value || ''}
            onChange={handleChange}
            required={field.required}
          />
        );

      case 'checkbox':
        return (
          <div className="checkbox-wrapper">
            <input
              {...commonProps}
              type="checkbox"
              checked={value || false}
              onChange={handleChange}
              className="form-checkbox"
            />
            <label htmlFor={field.key} className="checkbox-label">
              {field.label}
            </label>
          </div>
        );

      case 'select':
        return (
          <select
            {...commonProps}
            value={value || ''}
            onChange={handleChange}
            required={field.required}
          >
            <option value="">Select {field.label}</option>
            {field.options?.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        );

      case 'multiselect':
        return (
          <select
            {...commonProps}
            multiple
            value={Array.isArray(value) ? value : []}
            onChange={handleMultiSelectChange}
            size={Math.min(field.options?.length || 5, 5)}
          >
            {field.options?.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        );

      case 'array':
        return (
          <input
            {...commonProps}
            type="text"
            value={Array.isArray(value) ? value.join(', ') : value || ''}
            onChange={handleArrayChange}
            placeholder={field.placeholder || 'Enter values separated by commas'}
          />
        );

      default:
        return (
          <input
            {...commonProps}
            type="text"
            value={value || ''}
            onChange={handleChange}
            required={field.required}
          />
        );
    }
  };

  // Don't render label for checkbox as it's handled inside the field
  const shouldRenderLabel = field.type !== 'checkbox';

  return (
    <div className={`form-field ${field.type === 'checkbox' ? 'checkbox-field' : ''}`}>
      {shouldRenderLabel && (
        <label htmlFor={field.key} className="form-label">
          {field.label}
          {field.required && <span className="required-indicator">*</span>}
        </label>
      )}
      
      {renderField()}
      
      {field.helpText && (
        <div className="help-text">{field.helpText}</div>
      )}
      
      {error && (
        <div id={`${field.key}-error`} className="error-message" role="alert">
          {error}
        </div>
      )}
    </div>
  );
};

export default FormField;
