-- CRM Database Creation Script
-- This script creates the complete database schema manually
-- Use this if Entity Framework migrations are not working

USE master;
GO

-- Create database if it doesn't exist
IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = 'CrmDatabase')
BEGIN
    CREATE DATABASE CrmDatabase;
    PRINT 'Database CrmDatabase created successfully';
END
ELSE
BEGIN
    PRINT 'Database CrmDatabase already exists';
END
GO

USE CrmDatabase;
GO

-- Create States table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='states' AND xtype='U')
BEGIN
    CREATE TABLE states (
        id int IDENTITY(1,1) PRIMARY KEY,
        name nvarchar(100) NOT NULL,
        code nvarchar(10) NOT NULL,
        capital nvarchar(100) NULL,
        region nvarchar(50) NULL,
        is_active bit NOT NULL DEFAULT 1,
        created_at datetime2 NOT NULL DEFAULT GETUTCDATE(),
        updated_at datetime2 NOT NULL DEFAULT GETUTCDATE(),
        
        CONSTRAINT UQ_states_name UNIQUE (name),
        CONSTRAINT UQ_states_code UNIQUE (code)
    );
    
    CREATE INDEX IX_states_name ON states(name);
    CREATE INDEX IX_states_code ON states(code);
    
    PRINT 'States table created successfully';
END
GO

-- Create Divisions table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='divisions' AND xtype='U')
BEGIN
    CREATE TABLE divisions (
        id int IDENTITY(1,1) PRIMARY KEY,
        name nvarchar(255) NOT NULL,
        description nvarchar(500) NULL,
        is_active bit NOT NULL DEFAULT 1,
        created_at datetime2 NOT NULL DEFAULT GETUTCDATE(),
        updated_at datetime2 NOT NULL DEFAULT GETUTCDATE(),
        
        CONSTRAINT UQ_divisions_name UNIQUE (name)
    );
    
    CREATE INDEX IX_divisions_name ON divisions(name);
    
    PRINT 'Divisions table created successfully';
END
GO

-- Create Categories table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='categories' AND xtype='U')
BEGIN
    CREATE TABLE categories (
        id int IDENTITY(1,1) PRIMARY KEY,
        name nvarchar(255) NOT NULL,
        description nvarchar(500) NULL,
        division_id int NOT NULL,
        is_active bit NOT NULL DEFAULT 1,
        created_at datetime2 NOT NULL DEFAULT GETUTCDATE(),
        updated_at datetime2 NOT NULL DEFAULT GETUTCDATE(),
        
        CONSTRAINT FK_categories_divisions FOREIGN KEY (division_id) REFERENCES divisions(id),
        CONSTRAINT UQ_categories_name_division UNIQUE (name, division_id)
    );
    
    CREATE INDEX IX_categories_division_id ON categories(division_id);
    CREATE INDEX IX_categories_name_division_id ON categories(name, division_id);
    
    PRINT 'Categories table created successfully';
END
GO

-- Create SubCategories table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='subcategories' AND xtype='U')
BEGIN
    CREATE TABLE subcategories (
        id int IDENTITY(1,1) PRIMARY KEY,
        name nvarchar(255) NOT NULL,
        description nvarchar(500) NULL,
        category_id int NOT NULL,
        is_active bit NOT NULL DEFAULT 1,
        created_at datetime2 NOT NULL DEFAULT GETUTCDATE(),
        updated_at datetime2 NOT NULL DEFAULT GETUTCDATE(),
        
        CONSTRAINT FK_subcategories_categories FOREIGN KEY (category_id) REFERENCES categories(id),
        CONSTRAINT UQ_subcategories_name_category UNIQUE (name, category_id)
    );
    
    CREATE INDEX IX_subcategories_category_id ON subcategories(category_id);
    CREATE INDEX IX_subcategories_name_category_id ON subcategories(name, category_id);
    
    PRINT 'SubCategories table created successfully';
END
GO

-- Create Persons table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='persons' AND xtype='U')
BEGIN
    CREATE TABLE persons (
        id int IDENTITY(1,1) PRIMARY KEY,
        
        -- Foreign Keys
        division_id int NOT NULL,
        category_id int NOT NULL,
        subcategory_id int NULL,
        
        -- Required Fields
        name nvarchar(255) NOT NULL,
        mobile_number nvarchar(15) NOT NULL,
        nature int NOT NULL,
        
        -- Optional Fields
        gender int NULL,
        
        -- Contact Information
        alternate_numbers nvarchar(max) NULL,
        primary_email_id nvarchar(255) NULL,
        alternate_email_ids nvarchar(max) NULL,
        website nvarchar(500) NULL,
        
        -- Personal Information
        date_of_birth datetime2 NULL,
        is_married bit NULL,
        date_of_marriage datetime2 NULL,
        
        -- Location Information
        working_state nvarchar(100) NULL,
        domestic_state nvarchar(100) NULL,
        district nvarchar(100) NULL,
        address nvarchar(1000) NULL,
        working_area nvarchar(255) NULL,
        
        -- Associate Information
        has_associate bit NOT NULL DEFAULT 0,
        associate_name nvarchar(255) NULL,
        associate_relation nvarchar(100) NULL,
        associate_mobile nvarchar(15) NULL,
        
        -- Digital Presence
        using_website bit NOT NULL DEFAULT 0,
        website_link nvarchar(500) NULL,
        using_crm_app bit NOT NULL DEFAULT 0,
        crm_app_link nvarchar(500) NULL,
        
        -- Business Information
        transaction_value decimal(18,2) NULL,
        rera_registration_number nvarchar(100) NULL,
        working_profiles nvarchar(max) NULL,
        star_rating int NULL,
        source nvarchar(255) NULL,
        remarks nvarchar(max) NULL,
        
        -- Company Information
        firm_name nvarchar(255) NULL,
        number_of_offices int NULL,
        number_of_branches int NULL,
        total_employee_strength int NULL,
        
        -- Authorized Person Information
        authorized_person_name nvarchar(255) NULL,
        authorized_person_email nvarchar(255) NULL,
        designation nvarchar(100) NULL,
        
        -- Marketing Information
        marketing_contact nvarchar(255) NULL,
        marketing_designation nvarchar(100) NULL,
        place_of_posting nvarchar(255) NULL,
        department nvarchar(100) NULL,
        
        -- Audit Fields
        created_at datetime2 NOT NULL DEFAULT GETUTCDATE(),
        updated_at datetime2 NOT NULL DEFAULT GETUTCDATE(),
        is_deleted bit NOT NULL DEFAULT 0,
        deleted_at datetime2 NULL,
        
        -- Foreign Key Constraints
        CONSTRAINT FK_persons_divisions FOREIGN KEY (division_id) REFERENCES divisions(id),
        CONSTRAINT FK_persons_categories FOREIGN KEY (category_id) REFERENCES categories(id),
        CONSTRAINT FK_persons_subcategories FOREIGN KEY (subcategory_id) REFERENCES subcategories(id)
    );
    
    -- Create indexes for performance
    CREATE INDEX IX_persons_division_id ON persons(division_id);
    CREATE INDEX IX_persons_category_id ON persons(category_id);
    CREATE INDEX IX_persons_subcategory_id ON persons(subcategory_id);
    CREATE INDEX IX_persons_mobile_number ON persons(mobile_number);
    CREATE INDEX IX_persons_name ON persons(name);
    CREATE INDEX IX_persons_primary_email_id ON persons(primary_email_id);
    CREATE INDEX IX_persons_is_deleted ON persons(is_deleted);
    CREATE INDEX IX_persons_created_at ON persons(created_at);
    
    -- Unique constraint for mobile number within division and category (excluding deleted records)
    CREATE UNIQUE INDEX IX_persons_mobile_division_category_unique 
    ON persons(mobile_number, division_id, category_id) 
    WHERE is_deleted = 0;
    
    PRINT 'Persons table created successfully';
END
GO

PRINT 'Database schema creation completed successfully!';
PRINT 'Next step: Run the data seeding script to insert initial data.';
GO
