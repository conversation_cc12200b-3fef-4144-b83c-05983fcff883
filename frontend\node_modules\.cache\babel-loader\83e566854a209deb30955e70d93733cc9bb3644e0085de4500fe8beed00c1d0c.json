{"ast": null, "code": "import { lowercaseSVGElements } from '../../svg/lowercase-elements.mjs';\nfunction isSVGComponent(Component) {\n  if (\n  /**\n   * If it's not a string, it's a custom React component. Currently we only support\n   * HTML custom React components.\n   */\n  typeof Component !== \"string\" ||\n  /**\n   * If it contains a dash, the element is a custom HTML webcomponent.\n   */\n  Component.includes(\"-\")) {\n    return false;\n  } else if (\n  /**\n   * If it's in our list of lowercase SVG tags, it's an SVG component\n   */\n  lowercaseSVGElements.indexOf(Component) > -1 ||\n  /**\n   * If it contains a capital letter, it's an SVG component\n   */\n  /[A-Z]/.test(Component)) {\n    return true;\n  }\n  return false;\n}\nexport { isSVGComponent };", "map": {"version": 3, "names": ["lowercaseSVGElements", "isSVGComponent", "Component", "includes", "indexOf", "test"], "sources": ["C:/Users/<USER>/Downloads/Advanced/Advanced CRM filed all use/frontend/node_modules/framer-motion/dist/es/render/dom/utils/is-svg-component.mjs"], "sourcesContent": ["import { lowercaseSVGElements } from '../../svg/lowercase-elements.mjs';\n\nfunction isSVGComponent(Component) {\n    if (\n    /**\n     * If it's not a string, it's a custom React component. Currently we only support\n     * HTML custom React components.\n     */\n    typeof Component !== \"string\" ||\n        /**\n         * If it contains a dash, the element is a custom HTML webcomponent.\n         */\n        Component.includes(\"-\")) {\n        return false;\n    }\n    else if (\n    /**\n     * If it's in our list of lowercase SVG tags, it's an SVG component\n     */\n    lowercaseSVGElements.indexOf(Component) > -1 ||\n        /**\n         * If it contains a capital letter, it's an SVG component\n         */\n        /[A-Z]/.test(Component)) {\n        return true;\n    }\n    return false;\n}\n\nexport { isSVGComponent };\n"], "mappings": "AAAA,SAASA,oBAAoB,QAAQ,kCAAkC;AAEvE,SAASC,cAAcA,CAACC,SAAS,EAAE;EAC/B;EACA;AACJ;AACA;AACA;EACI,OAAOA,SAAS,KAAK,QAAQ;EACzB;AACR;AACA;EACQA,SAAS,CAACC,QAAQ,CAAC,GAAG,CAAC,EAAE;IACzB,OAAO,KAAK;EAChB,CAAC,MACI;EACL;AACJ;AACA;EACIH,oBAAoB,CAACI,OAAO,CAACF,SAAS,CAAC,GAAG,CAAC,CAAC;EACxC;AACR;AACA;EACQ,OAAO,CAACG,IAAI,CAACH,SAAS,CAAC,EAAE;IACzB,OAAO,IAAI;EACf;EACA,OAAO,KAAK;AAChB;AAEA,SAASD,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}