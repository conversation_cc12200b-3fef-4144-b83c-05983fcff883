* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  --primary-color: #667eea;
  --primary-dark: #5a67d8;
  --secondary-color: #764ba2;
  --success-color: #48bb78;
  --danger-color: #f56565;
  --warning-color: #ed8936;
  --info-color: #4299e1;
  --dark-color: #2d3748;
  --light-color: #f7fafc;
  --gray-100: #f7fafc;
  --gray-200: #edf2f7;
  --gray-300: #e2e8f0;
  --gray-400: #cbd5e0;
  --gray-500: #a0aec0;
  --gray-600: #718096;
  --gray-700: #4a5568;
  --gray-800: #2d3748;
  --gray-900: #1a202c;
  --white: #ffffff;
  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: var(--gray-100);
  min-height: 100vh;
}

/* Login Styles */
.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.login-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  z-index: -2;
}

.login-background::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  animation: float 20s ease-in-out infinite;
}

.login-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.1);
  z-index: -1;
}

.login-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 40px;
  width: 100%;
  max-width: 400px;
  box-shadow: var(--shadow-xl);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.login-header {
  text-align: center;
  margin-bottom: 30px;
}

.login-logo {
  margin-bottom: 20px;
}

.logo-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  color: white;
  box-shadow: var(--shadow-lg);
}

.login-header h1 {
  font-size: 2rem;
  font-weight: 700;
  color: var(--gray-800);
  margin-bottom: 8px;
}

.login-header p {
  color: var(--gray-600);
  font-size: 0.9rem;
}

.login-form {
  margin-bottom: 20px;
}

.form-group {
  margin-bottom: 20px;
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.input-icon {
  position: absolute;
  left: 15px;
  color: var(--gray-500);
  z-index: 1;
}

.form-input {
  width: 100%;
  padding: 15px 15px 15px 45px;
  border: 2px solid var(--gray-200);
  border-radius: 12px;
  font-size: 16px;
  transition: all 0.3s ease;
  background: var(--white);
}

.form-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.password-toggle {
  position: absolute;
  right: 15px;
  background: none;
  border: none;
  color: var(--gray-500);
  cursor: pointer;
  padding: 5px;
  border-radius: 4px;
  transition: color 0.2s;
}

.password-toggle:hover {
  color: var(--primary-color);
}

.login-btn {
  width: 100%;
  padding: 15px;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 50px;
}

.login-btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.login-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.login-footer {
  text-align: center;
  color: var(--gray-600);
  font-size: 0.8rem;
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 1s ease-in-out infinite;
}

/* Admin Sidebar Styles */
.admin-sidebar {
  position: fixed;
  left: 0;
  top: 0;
  width: 280px;
  height: 100vh;
  background: linear-gradient(180deg, #a7f3d0 0%, #86efac 100%);
  color: var(--gray-800);
  z-index: 1000;
  box-shadow: var(--shadow-xl);
  overflow-y: auto;
}

.sidebar-header {
  padding: 30px 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.admin-profile {
  display: flex;
  align-items: center;
  gap: 15px;
}

.profile-avatar {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--shadow-md);
}

.profile-info h3 {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 4px;
}

.profile-info p {
  font-size: 0.85rem;
  color: var(--gray-600);
}

.sidebar-nav {
  padding: 20px 0;
}

.sidebar-link {
  display: flex;
  align-items: center;
  gap: 15px;
  color: var(--gray-700);
  text-decoration: none;
  padding: 15px 20px;
  margin: 0 10px;
  border-radius: 12px;
  transition: all 0.3s ease;
  font-weight: 500;
}

.sidebar-link:hover {
  background: rgba(255, 255, 255, 0.3);
  color: var(--gray-800);
  text-decoration: none;
  transform: translateX(5px);
}

.sidebar-link.active {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: white;
  box-shadow: var(--shadow-md);
}

.sidebar-icon {
  font-size: 18px;
}

.sidebar-footer {
  position: absolute;
  bottom: 20px;
  left: 0;
  right: 0;
  padding: 0 20px;
}

.logout-btn {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 15px;
  background: rgba(245, 101, 101, 0.1);
  color: #f56565;
  border: 1px solid rgba(245, 101, 101, 0.2);
  padding: 12px 20px;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
}

.logout-btn:hover {
  background: rgba(245, 101, 101, 0.2);
  transform: translateY(-2px);
}

/* Main content area */
.main-content {
  margin-left: 280px;
  padding: 30px;
  min-height: 100vh;
  background: var(--gray-100);
}

/* Card Styles */
.card {
  background: white;
  border-radius: 16px;
  box-shadow: var(--shadow-sm);
  padding: 30px;
  margin-bottom: 30px;
  border: 1px solid var(--gray-200);
  transition: all 0.3s ease;
}

.card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.card h1, .card h2, .card h3 {
  color: var(--gray-800);
  margin-bottom: 20px;
}

.card h1 {
  font-size: 2rem;
  font-weight: 700;
}

.card h3 {
  font-size: 1.25rem;
  font-weight: 600;
}

/* Form Styles */
.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: var(--gray-700);
  font-size: 0.9rem;
}

.form-control {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid var(--gray-200);
  border-radius: 12px;
  font-size: 14px;
  transition: all 0.3s ease;
  background: white;
}

.form-control:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Button Styles */
.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 12px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  transition: all 0.3s ease;
  min-height: 44px;
}

.btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.btn-primary {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: white;
}

.btn-success {
  background: var(--success-color);
  color: white;
}

.btn-danger {
  background: var(--danger-color);
  color: white;
}

.btn-sm {
  padding: 8px 16px;
  font-size: 12px;
  min-height: 36px;
}

/* Table Styles */
.table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 20px;
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.table th,
.table td {
  padding: 16px;
  text-align: left;
  border-bottom: 1px solid var(--gray-200);
}

.table th {
  background: var(--gray-50);
  font-weight: 600;
  color: var(--gray-700);
  font-size: 0.9rem;
}

.table tbody tr:hover {
  background: var(--gray-50);
}

/* Star Rating */
.star-rating {
  display: flex;
  gap: 4px;
}

.star {
  cursor: pointer;
  font-size: 20px;
  color: var(--gray-300);
  transition: all 0.2s ease;
}

.star.active {
  color: #fbbf24;
}

.star:hover {
  color: #fbbf24;
  transform: scale(1.1);
}

/* Multi Input */
.multi-input {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.multi-input input {
  flex: 1;
}

.add-btn, .remove-btn {
  width: 36px;
  height: 36px;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.add-btn {
  background: var(--success-color);
  color: white;
}

.remove-btn {
  background: var(--danger-color);
  color: white;
}

.add-btn:hover, .remove-btn:hover {
  transform: scale(1.1);
}

/* Layout */
.row {
  display: flex;
  gap: 30px;
  flex-wrap: wrap;
}

.col {
  flex: 1;
  min-width: 300px;
}

/* Loading */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background: var(--gray-100);
}

.loading-spinner-large {
  width: 40px;
  height: 40px;
  border: 4px solid var(--gray-300);
  border-radius: 50%;
  border-top-color: var(--primary-color);
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 20px;
}

/* Animations */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

/* Responsive */
@media (max-width: 768px) {
  .admin-sidebar {
    width: 100%;
    height: auto;
    position: relative;
  }
  
  .main-content {
    margin-left: 0;
    padding: 20px;
  }
  
  .row {
    flex-direction: column;
    gap: 20px;
  }
  
  .col {
    min-width: auto;
  }
}/* Da
shboard Styles */
.dashboard {
  padding: 0;
  background: var(--gray-100);
  min-height: 100vh;
}

.dashboard-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  color: var(--gray-600);
}

.dashboard-header {
  background: white;
  padding: 30px;
  margin-bottom: 30px;
  border-radius: 16px;
  box-shadow: var(--shadow-sm);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 20px;
}

.dashboard-title h1 {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--gray-800);
  margin-bottom: 8px;
}

.dashboard-title p {
  color: var(--gray-600);
  font-size: 1.1rem;
}

.dashboard-controls {
  display: flex;
  gap: 15px;
  align-items: center;
}

.dashboard-select {
  min-width: 150px;
}

.dashboard-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  white-space: nowrap;
}

.spinning {
  animation: spin 1s linear infinite;
}

/* Metrics Grid */
.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 25px;
  margin-bottom: 30px;
}

.metric-card {
  background: white;
  padding: 25px;
  border-radius: 16px;
  box-shadow: var(--shadow-sm);
  display: flex;
  align-items: center;
  gap: 20px;
  transition: all 0.3s ease;
  border: 1px solid var(--gray-200);
}

.metric-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.metric-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow: var(--shadow-sm);
}

.metric-content h3 {
  font-size: 2rem;
  font-weight: 700;
  color: var(--gray-800);
  margin-bottom: 4px;
}

.metric-content p {
  color: var(--gray-600);
  font-size: 0.9rem;
  font-weight: 500;
}

/* Charts Grid */
.charts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 25px;
  margin-bottom: 30px;
}

.chart-card {
  background: white;
  border-radius: 16px;
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--gray-200);
  overflow: hidden;
  transition: all 0.3s ease;
}

.chart-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.chart-header {
  padding: 20px 25px;
  border-bottom: 1px solid var(--gray-200);
  background: var(--gray-50);
}

.chart-header h3 {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--gray-800);
  display: flex;
  align-items: center;
  gap: 10px;
}

.chart-container {
  padding: 25px;
  height: 300px;
  position: relative;
}

/* Tables Grid */
.tables-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
  gap: 25px;
  margin-bottom: 30px;
}

.table-card {
  background: white;
  border-radius: 16px;
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--gray-200);
  overflow: hidden;
  transition: all 0.3s ease;
}

.table-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.table-header {
  padding: 20px 25px;
  border-bottom: 1px solid var(--gray-200);
  background: var(--gray-50);
}

.table-header h3 {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--gray-800);
  display: flex;
  align-items: center;
  gap: 10px;
}

.table-container {
  max-height: 400px;
  overflow-y: auto;
}

.dashboard-table {
  width: 100%;
  border-collapse: collapse;
}

.dashboard-table th {
  background: var(--gray-50);
  padding: 15px 20px;
  text-align: left;
  font-weight: 600;
  color: var(--gray-700);
  font-size: 0.9rem;
  border-bottom: 1px solid var(--gray-200);
  position: sticky;
  top: 0;
  z-index: 10;
}

.dashboard-table td {
  padding: 15px 20px;
  border-bottom: 1px solid var(--gray-100);
  color: var(--gray-700);
}

.dashboard-table tbody tr:hover {
  background: var(--gray-50);
}

.rating-display {
  color: #fbbf24;
  font-size: 1.1rem;
  letter-spacing: 1px;
}

.percentage-bar {
  position: relative;
  background: var(--gray-200);
  height: 20px;
  border-radius: 10px;
  overflow: hidden;
  display: flex;
  align-items: center;
  min-width: 100px;
}

.percentage-fill {
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
  height: 100%;
  border-radius: 10px;
  transition: width 0.3s ease;
}

.percentage-bar span {
  position: absolute;
  right: 8px;
  font-size: 0.8rem;
  font-weight: 600;
  color: var(--gray-700);
  z-index: 1;
}

/* Top Performers Section */
.performers-section {
  background: white;
  border-radius: 16px;
  padding: 30px;
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--gray-200);
}

.performers-section h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--gray-800);
  margin-bottom: 25px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.performers-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.performer-card {
  background: var(--gray-50);
  border-radius: 12px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 15px;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.performer-card:hover {
  background: white;
  border-color: var(--primary-color);
  box-shadow: var(--shadow-sm);
  transform: translateY(-2px);
}

.performer-rank {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 1.1rem;
}

.performer-info h4 {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--gray-800);
  margin-bottom: 4px;
}

.performer-info p {
  color: var(--gray-600);
  font-size: 0.9rem;
  margin-bottom: 8px;
}

.performer-rating {
  color: #fbbf24;
  font-size: 1.2rem;
  letter-spacing: 2px;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .charts-grid {
    grid-template-columns: 1fr;
  }
  
  .tables-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .dashboard-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .dashboard-controls {
    width: 100%;
    justify-content: flex-start;
    flex-wrap: wrap;
  }
  
  .metrics-grid {
    grid-template-columns: 1fr;
  }
  
  .charts-grid {
    grid-template-columns: 1fr;
  }
  
  .chart-container {
    height: 250px;
  }
  
  .tables-grid {
    grid-template-columns: 1fr;
  }
  
  .performers-grid {
    grid-template-columns: 1fr;
  }
  
  .dashboard-title h1 {
    font-size: 2rem;
  }
}

@media (max-width: 480px) {
  .dashboard {
    padding: 15px;
  }
  
  .dashboard-header {
    padding: 20px;
  }
  
  .metric-card {
    padding: 20px;
  }
  
  .chart-container {
    padding: 15px;
    height: 200px;
  }
  
  .dashboard-table th,
  .dashboard-table td {
    padding: 10px 15px;
  }
}

/* Animation for loading states */
.chart-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: var(--gray-500);
}

/* Custom scrollbar for table containers */
.table-container::-webkit-scrollbar {
  width: 6px;
}

.table-container::-webkit-scrollbar-track {
  background: var(--gray-100);
  border-radius: 3px;
}

.table-container::-webkit-scrollbar-thumb {
  background: var(--gray-400);
  border-radius: 3px;
}

.table-container::-webkit-scrollbar-thumb:hover {
  background: var(--gray-500);
}/* Birth
day Section Styles */
.birthday-section {
  background: white;
  border-radius: 16px;
  padding: 30px;
  margin-bottom: 30px;
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--gray-200);
  transition: all 0.3s ease;
}

.birthday-section:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.birthday-header {
  margin-bottom: 25px;
}

.birthday-header h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--gray-800);
  display: flex;
  align-items: center;
  gap: 10px;
}

.birthday-content {
  min-height: 120px;
}

.birthday-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.birthday-card {
  background: linear-gradient(135deg, #fef3c7, #fde68a);
  border-radius: 12px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 15px;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  position: relative;
  overflow: hidden;
}

.birthday-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(255,255,255,0.1) 25%, transparent 25%, transparent 75%, rgba(255,255,255,0.1) 75%);
  background-size: 20px 20px;
  pointer-events: none;
}

.birthday-card:hover {
  transform: translateY(-3px);
  border-color: #f59e0b;
  box-shadow: 0 8px 25px rgba(245, 158, 11, 0.2);
}

.birthday-icon {
  font-size: 2.5rem;
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

.birthday-info {
  flex: 1;
}

.birthday-info h4 {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--gray-800);
  margin-bottom: 4px;
}

.birthday-info p {
  color: var(--gray-600);
  font-size: 0.9rem;
  margin-bottom: 6px;
}

.birthday-age {
  background: rgba(245, 158, 11, 0.2);
  color: #92400e;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
}

.birthday-rating {
  color: #f59e0b;
  font-size: 1.2rem;
  letter-spacing: 2px;
}

.no-birthdays {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
  color: var(--gray-500);
}

.no-birthdays-icon {
  font-size: 3rem;
  margin-bottom: 15px;
  opacity: 0.7;
}

.no-birthdays p {
  font-size: 1.1rem;
  font-weight: 500;
  margin-bottom: 8px;
  color: var(--gray-600);
}

.no-birthdays span {
  font-size: 0.9rem;
  color: var(--gray-500);
}

/* Responsive Design for Birthday Section */
@media (max-width: 768px) {
  .birthday-list {
    grid-template-columns: 1fr;
  }
  
  .birthday-card {
    padding: 15px;
  }
  
  .birthday-icon {
    font-size: 2rem;
  }
  
  .birthday-info h4 {
    font-size: 1.1rem;
  }
}

@media (max-width: 480px) {
  .birthday-section {
    padding: 20px;
  }
  
  .birthday-header h3 {
    font-size: 1.3rem;
  }
  
  .birthday-card {
    flex-direction: column;
    text-align: center;
    gap: 10px;
  }
  
  .no-birthdays {
    padding: 30px 15px;
  }
}/*
 Birthday Pagination Styles */
.birthday-pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
  margin-top: 25px;
  padding-top: 20px;
  border-top: 1px solid var(--gray-200);
}

.pagination-btn {
  padding: 8px 16px;
  background: white;
  border: 2px solid var(--gray-300);
  border-radius: 8px;
  color: var(--gray-600);
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  min-width: 80px;
}

.pagination-btn:hover:not(:disabled) {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
  transform: translateY(-1px);
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: var(--gray-100);
  color: var(--gray-400);
}

.pagination-numbers {
  display: flex;
  gap: 5px;
}

.pagination-number {
  width: 36px;
  height: 36px;
  background: white;
  border: 2px solid var(--gray-300);
  border-radius: 8px;
  color: var(--gray-600);
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pagination-number:hover {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
  transform: translateY(-1px);
}

.pagination-number.active {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: white;
  border-color: var(--primary-color);
  box-shadow: var(--shadow-sm);
}

/* Responsive pagination */
@media (max-width: 768px) {
  .birthday-pagination {
    flex-wrap: wrap;
    gap: 8px;
  }
  
  .pagination-btn {
    padding: 6px 12px;
    font-size: 13px;
    min-width: 70px;
  }
  
  .pagination-number {
    width: 32px;
    height: 32px;
    font-size: 13px;
  }
}

@media (max-width: 480px) {
  .birthday-pagination {
    gap: 5px;
  }
  
  .pagination-numbers {
    gap: 3px;
  }
  
  .pagination-btn {
    padding: 5px 10px;
    font-size: 12px;
    min-width: 60px;
  }
  
  .pagination-number {
    width: 28px;
    height: 28px;
    font-size: 12px;
  }
}