import React, { useState, useEffect } from 'react';
import { PersonFieldDefinitions, getAllPersonFields } from '../../constants/personConstants';
import './FieldMapping.css';

const FieldMapping = ({ fileHeaders, onMappingComplete, onBack, error }) => {
  const [mapping, setMapping] = useState({});
  const [unmappedHeaders, setUnmappedHeaders] = useState([]);
  const [requiredFieldsStatus, setRequiredFieldsStatus] = useState({});
  const [autoMappingSuggestions, setAutoMappingSuggestions] = useState({});
  const [defaultValues, setDefaultValues] = useState({});

  const personFields = getAllPersonFields();
  const requiredFields = personFields.filter(field => field.required);

  useEffect(() => {
    // Auto-suggest mappings based on header names
    const suggestions = autoMapFields();
    setAutoMappingSuggestions(suggestions);
    setMapping(suggestions);
    updateUnmappedHeaders(suggestions);
    updateRequiredFieldsStatus(suggestions);
  }, [fileHeaders]);

  // Re-validate when default values change
  useEffect(() => {
    updateRequiredFieldsStatus(mapping);
  }, [defaultValues]);

  const autoMapFields = () => {
    const suggestions = {};
    
    fileHeaders.forEach(header => {
      const normalizedHeader = header.toLowerCase().trim();
      
      // Find exact matches first
      const exactMatch = personFields.find(field => 
        field.label.toLowerCase() === normalizedHeader ||
        field.key.toLowerCase() === normalizedHeader
      );
      
      if (exactMatch) {
        suggestions[header] = exactMatch.key;
        return;
      }

      // Find partial matches
      const partialMatch = personFields.find(field => {
        const fieldLabel = field.label.toLowerCase();
        const fieldKey = field.key.toLowerCase();
        
        return fieldLabel.includes(normalizedHeader) || 
               normalizedHeader.includes(fieldLabel) ||
               fieldKey.includes(normalizedHeader) ||
               normalizedHeader.includes(fieldKey);
      });

      if (partialMatch) {
        suggestions[header] = partialMatch.key;
      }
    });

    return suggestions;
  };

  const handleMappingChange = (fileHeader, personFieldKey) => {
    const newMapping = { ...mapping };
    
    if (personFieldKey === '') {
      delete newMapping[fileHeader];
    } else {
      // Remove any existing mapping to this person field
      Object.keys(newMapping).forEach(key => {
        if (newMapping[key] === personFieldKey && key !== fileHeader) {
          delete newMapping[key];
        }
      });
      
      newMapping[fileHeader] = personFieldKey;
    }
    
    setMapping(newMapping);
    updateUnmappedHeaders(newMapping);
    updateRequiredFieldsStatus(newMapping);
  };

  const updateUnmappedHeaders = (currentMapping) => {
    const mapped = Object.keys(currentMapping);
    const unmapped = fileHeaders.filter(header => !mapped.includes(header));
    setUnmappedHeaders(unmapped);
  };

  const updateRequiredFieldsStatus = (currentMapping) => {
    const mappedFields = Object.values(currentMapping);
    const status = {};

    requiredFields.forEach(field => {
      // Field is satisfied if it's mapped from Excel OR has a default value
      const isMapped = mappedFields.includes(field.key);
      const hasDefaultValue = defaultValues[field.key] && defaultValues[field.key].trim() !== '';
      status[field.key] = isMapped || hasDefaultValue;
    });

    setRequiredFieldsStatus(status);
  };

  const handleDefaultValueChange = (fieldKey, value) => {
    const newDefaultValues = { ...defaultValues, [fieldKey]: value };
    setDefaultValues(newDefaultValues);
    // Re-validate required fields status when default values change
    updateRequiredFieldsStatus(mapping);
  };

  const applyAutoMapping = () => {
    setMapping(autoMappingSuggestions);
    updateUnmappedHeaders(autoMappingSuggestions);
    updateRequiredFieldsStatus(autoMappingSuggestions);
  };

  const clearAllMappings = () => {
    setMapping({});
    updateUnmappedHeaders({});
    updateRequiredFieldsStatus({});
  };

  const handleContinue = () => {
    // Validate that all required fields are mapped or have default values
    const missingRequired = requiredFields.filter(field => !requiredFieldsStatus[field.key]);

    if (missingRequired.length > 0) {
      alert(`Please map the following required fields or provide default values: ${missingRequired.map(f => f.label).join(', ')}`);
      return;
    }

    // Include both field mapping and default values
    const completeMapping = {
      fieldMapping: mapping,
      defaultValues: defaultValues
    };

    onMappingComplete(completeMapping);
  };

  const getPersonFieldByKey = (key) => {
    return personFields.find(field => field.key === key);
  };

  const getMappedPersonFields = () => {
    return Object.values(mapping).map(key => getPersonFieldByKey(key)).filter(Boolean);
  };

  const getAvailablePersonFields = (currentHeader) => {
    const mappedFields = Object.entries(mapping)
      .filter(([header, _]) => header !== currentHeader)
      .map(([_, fieldKey]) => fieldKey);
    
    return personFields.filter(field => !mappedFields.includes(field.key));
  };

  const groupPersonFieldsBySection = (fields) => {
    const sections = {};
    fields.forEach(field => {
      const sectionKey = field.section || 'general';
      if (!sections[sectionKey]) {
        sections[sectionKey] = {
          title: PersonFieldDefinitions[sectionKey]?.title || sectionKey,
          fields: []
        };
      }
      sections[sectionKey].fields.push(field);
    });
    return sections;
  };

  const renderFieldSelect = (fileHeader) => {
    const availableFields = getAvailablePersonFields(fileHeader);
    const sections = groupPersonFieldsBySection(availableFields);
    const currentMapping = mapping[fileHeader] || '';

    return (
      <select
        value={currentMapping}
        onChange={(e) => handleMappingChange(fileHeader, e.target.value)}
        className={`field-select ${currentMapping ? 'mapped' : 'unmapped'}`}
      >
        <option value="">-- Select Field --</option>
        {Object.entries(sections).map(([sectionKey, section]) => (
          <optgroup key={sectionKey} label={section.title}>
            {section.fields.map(field => (
              <option key={field.key} value={field.key}>
                {field.label} {field.required ? '*' : ''}
              </option>
            ))}
          </optgroup>
        ))}
      </select>
    );
  };

  const allRequiredMapped = requiredFields.every(field => requiredFieldsStatus[field.key]);
  const mappingCount = Object.keys(mapping).length;

  return (
    <div className="field-mapping">
      <div className="mapping-header">
        <h3>Map File Columns to Person Fields</h3>
        <p>Match your file columns with the corresponding person fields. Required fields are marked with *</p>
        
        <div className="mapping-stats">
          <div className="stat">
            <span className="stat-value">{mappingCount}</span>
            <span className="stat-label">Mapped Fields</span>
          </div>
          <div className="stat">
            <span className="stat-value">{unmappedHeaders.length}</span>
            <span className="stat-label">Unmapped Columns</span>
          </div>
          <div className="stat">
            <span className={`stat-value ${allRequiredMapped ? 'success' : 'error'}`}>
              {Object.values(requiredFieldsStatus).filter(Boolean).length}/{requiredFields.length}
            </span>
            <span className="stat-label">Required Fields</span>
          </div>
        </div>

        <div className="mapping-actions">
          <button onClick={applyAutoMapping} className="btn btn-outline">
            🤖 Auto Map
          </button>
          <button onClick={clearAllMappings} className="btn btn-outline">
            🗑️ Clear All
          </button>
        </div>
      </div>

      {/* Required Fields Status */}
      <div className="required-fields-status">
        <h4>Required Fields Status</h4>
        <div className="required-fields-grid">
          {requiredFields.map(field => {
            const isMapped = Object.values(mapping).includes(field.key);
            const hasDefaultValue = defaultValues[field.key] && defaultValues[field.key].trim() !== '';

            return (
              <div
                key={field.key}
                className={`required-field ${requiredFieldsStatus[field.key] ? 'mapped' : 'unmapped'}`}
              >
                <span className="field-name">{field.label}</span>
                <span className="field-status">
                  {isMapped ? '📋 Mapped' : hasDefaultValue ? '⚙️ Default' : '❌ Missing'}
                </span>
              </div>
            );
          })}
        </div>
      </div>

      {/* Default Values for Unmapped Required Fields */}
      {requiredFields.some(field => !Object.values(mapping).includes(field.key)) && (
        <div className="default-values-section">
          <h4>Default Values for Missing Required Fields</h4>
          <p className="section-description">
            Provide default values for required fields that are not present in your Excel file.
            These values will be applied to all imported records.
          </p>
          <div className="default-values-grid">
            {requiredFields
              .filter(field => !Object.values(mapping).includes(field.key))
              .map(field => (
                <div key={field.key} className="default-value-input">
                  <label className="default-value-label">
                    {field.label} <span className="required-indicator">*</span>
                  </label>
                  {field.type === 'select' ? (
                    <select
                      value={defaultValues[field.key] || ''}
                      onChange={(e) => handleDefaultValueChange(field.key, e.target.value)}
                      className="default-value-select"
                    >
                      <option value="">Select {field.label}</option>
                      {field.options?.map(option => (
                        <option key={option.value} value={option.value}>
                          {option.label}
                        </option>
                      ))}
                    </select>
                  ) : (
                    <input
                      type={field.type || 'text'}
                      value={defaultValues[field.key] || ''}
                      onChange={(e) => handleDefaultValueChange(field.key, e.target.value)}
                      placeholder={`Enter default ${field.label.toLowerCase()}`}
                      className="default-value-input-field"
                    />
                  )}
                  {field.validation?.pattern && (
                    <small className="field-hint">
                      Format: {field.validation.pattern}
                    </small>
                  )}
                </div>
              ))}
          </div>
        </div>
      )}

      {/* Mapping Table */}
      <div className="mapping-table-container">
        <table className="mapping-table">
          <thead>
            <tr>
              <th>File Column</th>
              <th>Sample Data</th>
              <th>Maps To</th>
              <th>Field Type</th>
              <th>Required</th>
            </tr>
          </thead>
          <tbody>
            {fileHeaders.map(header => {
              const mappedField = mapping[header] ? getPersonFieldByKey(mapping[header]) : null;
              return (
                <tr key={header} className={mapping[header] ? 'mapped-row' : 'unmapped-row'}>
                  <td>
                    <div className="file-header">
                      <strong>{header}</strong>
                    </div>
                  </td>
                  <td>
                    <div className="sample-data">
                      {/* This would show sample data from the file */}
                      <em>Sample data preview</em>
                    </div>
                  </td>
                  <td>
                    {renderFieldSelect(header)}
                  </td>
                  <td>
                    <div className="field-type">
                      {mappedField ? (
                        <span className="type-badge">{mappedField.type}</span>
                      ) : (
                        <span className="no-mapping">-</span>
                      )}
                    </div>
                  </td>
                  <td>
                    <div className="required-indicator">
                      {mappedField?.required ? (
                        <span className="required-badge">Required</span>
                      ) : (
                        <span className="optional-badge">Optional</span>
                      )}
                    </div>
                  </td>
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>

      {/* Unmapped Headers Warning */}
      {unmappedHeaders.length > 0 && (
        <div className="unmapped-warning">
          <h4>⚠️ Unmapped Columns</h4>
          <p>The following columns from your file will be ignored during import:</p>
          <div className="unmapped-list">
            {unmappedHeaders.map(header => (
              <span key={header} className="unmapped-header">{header}</span>
            ))}
          </div>
        </div>
      )}

      {/* Mapping Summary */}
      <div className="mapping-summary">
        <h4>Mapping Summary</h4>
        <div className="summary-content">
          <div className="summary-section">
            <h5>Mapped Fields ({mappingCount})</h5>
            <div className="mapped-fields-list">
              {getMappedPersonFields().map(field => (
                <div key={field.key} className="mapped-field-item">
                  <span className="field-label">{field.label}</span>
                  <span className={`field-badge ${field.required ? 'required' : 'optional'}`}>
                    {field.required ? 'Required' : 'Optional'}
                  </span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <div className="mapping-navigation">
        <button onClick={onBack} className="btn btn-outline">
          ← Back to Upload
        </button>
        <button
          onClick={handleContinue}
          disabled={!allRequiredMapped}
          className="btn btn-primary"
        >
          Continue to Import →
        </button>
      </div>

      {!allRequiredMapped && (
        <div className="validation-error">
          <span className="error-icon">⚠️</span>
          Please map all required fields before continuing
        </div>
      )}
    </div>
  );
};

export default FieldMapping;
