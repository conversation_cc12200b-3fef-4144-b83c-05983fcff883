{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Advanced\\\\Advanced CRM filed all use\\\\frontend\\\\src\\\\components\\\\DivisionSetup.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport Pagination from './Pagination';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst API_BASE_URL = 'http://localhost:5000/api';\nconst DivisionSetup = () => {\n  _s();\n  const [divisions, setDivisions] = useState([]);\n  const [newDivision, setNewDivision] = useState('');\n  const [loading, setLoading] = useState(false);\n  const [currentPage, setCurrentPage] = useState(1);\n  const [itemsPerPage] = useState(10);\n  useEffect(() => {\n    fetchDivisions();\n  }, []);\n  const fetchDivisions = async () => {\n    try {\n      const response = await axios.get(`${API_BASE_URL}/divisions`);\n      setDivisions(response.data);\n    } catch (error) {\n      console.error('Error fetching divisions:', error);\n    }\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    if (!newDivision.trim()) return;\n    setLoading(true);\n    try {\n      await axios.post(`${API_BASE_URL}/divisions`, {\n        name: newDivision\n      });\n      setNewDivision('');\n      fetchDivisions();\n    } catch (error) {\n      console.error('Error creating division:', error);\n      alert('Error creating division. Please try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleDelete = async id => {\n    if (window.confirm('Are you sure you want to delete this division?')) {\n      try {\n        await axios.delete(`${API_BASE_URL}/divisions/${id}`);\n        fetchDivisions();\n      } catch (error) {\n        console.error('Error deleting division:', error);\n        alert('Error deleting division. Please try again.');\n      }\n    }\n  };\n\n  // Calculate pagination\n  const totalItems = divisions.length;\n  const startIndex = (currentPage - 1) * itemsPerPage;\n  const endIndex = startIndex + itemsPerPage;\n  const currentItems = divisions.slice(startIndex, endIndex);\n  const handlePageChange = page => {\n    setCurrentPage(page);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      children: \"Division Setup\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 70,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"Add New Division\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 73,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSubmit,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"divisionName\",\n            children: \"Division Name\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 76,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            id: \"divisionName\",\n            className: \"form-control\",\n            value: newDivision,\n            onChange: e => setNewDivision(e.target.value),\n            placeholder: \"Enter division name\",\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 77,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          className: \"btn btn-primary\",\n          disabled: loading,\n          children: loading ? 'Adding...' : 'Add Division'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 74,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 72,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"Existing Divisions\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 94,\n        columnNumber: 9\n      }, this), divisions.length === 0 ? /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"No divisions found. Add your first division above.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 96,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"table\", {\n          className: \"table\",\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"ID\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 102,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 103,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Created At\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 104,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Actions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 105,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 101,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 100,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            children: currentItems.map(division => /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                children: division.id\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 111,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: division.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 112,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: new Date(division.createdAt).toLocaleDateString()\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 113,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"btn btn-danger btn-sm\",\n                  onClick: () => handleDelete(division.id),\n                  children: \"Delete\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 115,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 114,\n                columnNumber: 21\n              }, this)]\n            }, division.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 110,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 108,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 99,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Pagination, {\n          currentPage: currentPage,\n          totalItems: totalItems,\n          itemsPerPage: itemsPerPage,\n          onPageChange: handlePageChange\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 93,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 69,\n    columnNumber: 5\n  }, this);\n};\n_s(DivisionSetup, \"gnaxJMqM7fu4+tHbvm02eym0p6g=\");\n_c = DivisionSetup;\nexport default DivisionSetup;\nvar _c;\n$RefreshReg$(_c, \"DivisionSetup\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "Pagination", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "API_BASE_URL", "DivisionSetup", "_s", "divisions", "setDivisions", "newDivision", "setNewDivision", "loading", "setLoading", "currentPage", "setCurrentPage", "itemsPerPage", "fetchDivisions", "response", "get", "data", "error", "console", "handleSubmit", "e", "preventDefault", "trim", "post", "name", "alert", "handleDelete", "id", "window", "confirm", "delete", "totalItems", "length", "startIndex", "endIndex", "currentItems", "slice", "handlePageChange", "page", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "onSubmit", "htmlFor", "type", "value", "onChange", "target", "placeholder", "required", "disabled", "map", "division", "Date", "createdAt", "toLocaleDateString", "onClick", "onPageChange", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/Advanced/Advanced CRM filed all use/frontend/src/components/DivisionSetup.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport axios from 'axios';\r\nimport Pagination from './Pagination';\r\n\r\nconst API_BASE_URL = 'http://localhost:5000/api';\r\n\r\nconst DivisionSetup = () => {\r\n  const [divisions, setDivisions] = useState([]);\r\n  const [newDivision, setNewDivision] = useState('');\r\n  const [loading, setLoading] = useState(false);\r\n  const [currentPage, setCurrentPage] = useState(1);\r\n  const [itemsPerPage] = useState(10);\r\n\r\n  useEffect(() => {\r\n    fetchDivisions();\r\n  }, []);\r\n\r\n  const fetchDivisions = async () => {\r\n    try {\r\n      const response = await axios.get(`${API_BASE_URL}/divisions`);\r\n      setDivisions(response.data);\r\n    } catch (error) {\r\n      console.error('Error fetching divisions:', error);\r\n    }\r\n  };\r\n\r\n  const handleSubmit = async (e) => {\r\n    e.preventDefault();\r\n    if (!newDivision.trim()) return;\r\n\r\n    setLoading(true);\r\n    try {\r\n      await axios.post(`${API_BASE_URL}/divisions`, {\r\n        name: newDivision\r\n      });\r\n      setNewDivision('');\r\n      fetchDivisions();\r\n    } catch (error) {\r\n      console.error('Error creating division:', error);\r\n      alert('Error creating division. Please try again.');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleDelete = async (id) => {\r\n    if (window.confirm('Are you sure you want to delete this division?')) {\r\n      try {\r\n        await axios.delete(`${API_BASE_URL}/divisions/${id}`);\r\n        fetchDivisions();\r\n      } catch (error) {\r\n        console.error('Error deleting division:', error);\r\n        alert('Error deleting division. Please try again.');\r\n      }\r\n    }\r\n  };\r\n\r\n  // Calculate pagination\r\n  const totalItems = divisions.length;\r\n  const startIndex = (currentPage - 1) * itemsPerPage;\r\n  const endIndex = startIndex + itemsPerPage;\r\n  const currentItems = divisions.slice(startIndex, endIndex);\r\n\r\n  const handlePageChange = (page) => {\r\n    setCurrentPage(page);\r\n  };\r\n\r\n  return (\r\n    <div>\r\n      <h1>Division Setup</h1>\r\n      \r\n      <div className=\"card\">\r\n        <h3>Add New Division</h3>\r\n        <form onSubmit={handleSubmit}>\r\n          <div className=\"form-group\">\r\n            <label htmlFor=\"divisionName\">Division Name</label>\r\n            <input\r\n              type=\"text\"\r\n              id=\"divisionName\"\r\n              className=\"form-control\"\r\n              value={newDivision}\r\n              onChange={(e) => setNewDivision(e.target.value)}\r\n              placeholder=\"Enter division name\"\r\n              required\r\n            />\r\n          </div>\r\n          <button type=\"submit\" className=\"btn btn-primary\" disabled={loading}>\r\n            {loading ? 'Adding...' : 'Add Division'}\r\n          </button>\r\n        </form>\r\n      </div>\r\n\r\n      <div className=\"card\">\r\n        <h3>Existing Divisions</h3>\r\n        {divisions.length === 0 ? (\r\n          <p>No divisions found. Add your first division above.</p>\r\n        ) : (\r\n          <>\r\n            <table className=\"table\">\r\n              <thead>\r\n                <tr>\r\n                  <th>ID</th>\r\n                  <th>Name</th>\r\n                  <th>Created At</th>\r\n                  <th>Actions</th>\r\n                </tr>\r\n              </thead>\r\n              <tbody>\r\n                {currentItems.map((division) => (\r\n                  <tr key={division.id}>\r\n                    <td>{division.id}</td>\r\n                    <td>{division.name}</td>\r\n                    <td>{new Date(division.createdAt).toLocaleDateString()}</td>\r\n                    <td>\r\n                      <button\r\n                        className=\"btn btn-danger btn-sm\"\r\n                        onClick={() => handleDelete(division.id)}\r\n                      >\r\n                        Delete\r\n                      </button>\r\n                    </td>\r\n                  </tr>\r\n                ))}\r\n              </tbody>\r\n            </table>\r\n            <Pagination\r\n              currentPage={currentPage}\r\n              totalItems={totalItems}\r\n              itemsPerPage={itemsPerPage}\r\n              onPageChange={handlePageChange}\r\n            />\r\n          </>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default DivisionSetup;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,UAAU,MAAM,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEtC,MAAMC,YAAY,GAAG,2BAA2B;AAEhD,MAAMC,aAAa,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1B,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGZ,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACa,WAAW,EAAEC,cAAc,CAAC,GAAGd,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACe,OAAO,EAAEC,UAAU,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACiB,WAAW,EAAEC,cAAc,CAAC,GAAGlB,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACmB,YAAY,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC;EAEnCC,SAAS,CAAC,MAAM;IACdmB,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMnB,KAAK,CAACoB,GAAG,CAAC,GAAGd,YAAY,YAAY,CAAC;MAC7DI,YAAY,CAACS,QAAQ,CAACE,IAAI,CAAC;IAC7B,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;IACnD;EACF,CAAC;EAED,MAAME,YAAY,GAAG,MAAOC,CAAC,IAAK;IAChCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAI,CAACf,WAAW,CAACgB,IAAI,CAAC,CAAC,EAAE;IAEzBb,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMd,KAAK,CAAC4B,IAAI,CAAC,GAAGtB,YAAY,YAAY,EAAE;QAC5CuB,IAAI,EAAElB;MACR,CAAC,CAAC;MACFC,cAAc,CAAC,EAAE,CAAC;MAClBM,cAAc,CAAC,CAAC;IAClB,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChDQ,KAAK,CAAC,4CAA4C,CAAC;IACrD,CAAC,SAAS;MACRhB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMiB,YAAY,GAAG,MAAOC,EAAE,IAAK;IACjC,IAAIC,MAAM,CAACC,OAAO,CAAC,gDAAgD,CAAC,EAAE;MACpE,IAAI;QACF,MAAMlC,KAAK,CAACmC,MAAM,CAAC,GAAG7B,YAAY,cAAc0B,EAAE,EAAE,CAAC;QACrDd,cAAc,CAAC,CAAC;MAClB,CAAC,CAAC,OAAOI,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAChDQ,KAAK,CAAC,4CAA4C,CAAC;MACrD;IACF;EACF,CAAC;;EAED;EACA,MAAMM,UAAU,GAAG3B,SAAS,CAAC4B,MAAM;EACnC,MAAMC,UAAU,GAAG,CAACvB,WAAW,GAAG,CAAC,IAAIE,YAAY;EACnD,MAAMsB,QAAQ,GAAGD,UAAU,GAAGrB,YAAY;EAC1C,MAAMuB,YAAY,GAAG/B,SAAS,CAACgC,KAAK,CAACH,UAAU,EAAEC,QAAQ,CAAC;EAE1D,MAAMG,gBAAgB,GAAIC,IAAI,IAAK;IACjC3B,cAAc,CAAC2B,IAAI,CAAC;EACtB,CAAC;EAED,oBACExC,OAAA;IAAAyC,QAAA,gBACEzC,OAAA;MAAAyC,QAAA,EAAI;IAAc;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAEvB7C,OAAA;MAAK8C,SAAS,EAAC,MAAM;MAAAL,QAAA,gBACnBzC,OAAA;QAAAyC,QAAA,EAAI;MAAgB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACzB7C,OAAA;QAAM+C,QAAQ,EAAE1B,YAAa;QAAAoB,QAAA,gBAC3BzC,OAAA;UAAK8C,SAAS,EAAC,YAAY;UAAAL,QAAA,gBACzBzC,OAAA;YAAOgD,OAAO,EAAC,cAAc;YAAAP,QAAA,EAAC;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACnD7C,OAAA;YACEiD,IAAI,EAAC,MAAM;YACXpB,EAAE,EAAC,cAAc;YACjBiB,SAAS,EAAC,cAAc;YACxBI,KAAK,EAAE1C,WAAY;YACnB2C,QAAQ,EAAG7B,CAAC,IAAKb,cAAc,CAACa,CAAC,CAAC8B,MAAM,CAACF,KAAK,CAAE;YAChDG,WAAW,EAAC,qBAAqB;YACjCC,QAAQ;UAAA;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACN7C,OAAA;UAAQiD,IAAI,EAAC,QAAQ;UAACH,SAAS,EAAC,iBAAiB;UAACS,QAAQ,EAAE7C,OAAQ;UAAA+B,QAAA,EACjE/B,OAAO,GAAG,WAAW,GAAG;QAAc;UAAAgC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAEN7C,OAAA;MAAK8C,SAAS,EAAC,MAAM;MAAAL,QAAA,gBACnBzC,OAAA;QAAAyC,QAAA,EAAI;MAAkB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,EAC1BvC,SAAS,CAAC4B,MAAM,KAAK,CAAC,gBACrBlC,OAAA;QAAAyC,QAAA,EAAG;MAAkD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,gBAEzD7C,OAAA,CAAAE,SAAA;QAAAuC,QAAA,gBACEzC,OAAA;UAAO8C,SAAS,EAAC,OAAO;UAAAL,QAAA,gBACtBzC,OAAA;YAAAyC,QAAA,eACEzC,OAAA;cAAAyC,QAAA,gBACEzC,OAAA;gBAAAyC,QAAA,EAAI;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACX7C,OAAA;gBAAAyC,QAAA,EAAI;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACb7C,OAAA;gBAAAyC,QAAA,EAAI;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnB7C,OAAA;gBAAAyC,QAAA,EAAI;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACR7C,OAAA;YAAAyC,QAAA,EACGJ,YAAY,CAACmB,GAAG,CAAEC,QAAQ,iBACzBzD,OAAA;cAAAyC,QAAA,gBACEzC,OAAA;gBAAAyC,QAAA,EAAKgB,QAAQ,CAAC5B;cAAE;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACtB7C,OAAA;gBAAAyC,QAAA,EAAKgB,QAAQ,CAAC/B;cAAI;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACxB7C,OAAA;gBAAAyC,QAAA,EAAK,IAAIiB,IAAI,CAACD,QAAQ,CAACE,SAAS,CAAC,CAACC,kBAAkB,CAAC;cAAC;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC5D7C,OAAA;gBAAAyC,QAAA,eACEzC,OAAA;kBACE8C,SAAS,EAAC,uBAAuB;kBACjCe,OAAO,EAAEA,CAAA,KAAMjC,YAAY,CAAC6B,QAAQ,CAAC5B,EAAE,CAAE;kBAAAY,QAAA,EAC1C;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP,CAAC;YAAA,GAXEY,QAAQ,CAAC5B,EAAE;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAYhB,CACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACR7C,OAAA,CAACF,UAAU;UACTc,WAAW,EAAEA,WAAY;UACzBqB,UAAU,EAAEA,UAAW;UACvBnB,YAAY,EAAEA,YAAa;UAC3BgD,YAAY,EAAEvB;QAAiB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC;MAAA,eACF,CACH;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACxC,EAAA,CAlIID,aAAa;AAAA2D,EAAA,GAAb3D,aAAa;AAoInB,eAAeA,aAAa;AAAC,IAAA2D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}