{"ast": null, "code": "import { isBezierDefinition } from '../../../easing/utils/is-bezier-definition.mjs';\nfunction isWaapiSupportedEasing(easing) {\n  return Boolean(!easing || typeof easing === \"string\" && supportedWaapiEasing[easing] || isBezierDefinition(easing) || Array.isArray(easing) && easing.every(isWaapiSupportedEasing));\n}\nconst cubicBezierAsString = ([a, b, c, d]) => `cubic-bezier(${a}, ${b}, ${c}, ${d})`;\nconst supportedWaapiEasing = {\n  linear: \"linear\",\n  ease: \"ease\",\n  easeIn: \"ease-in\",\n  easeOut: \"ease-out\",\n  easeInOut: \"ease-in-out\",\n  circIn: cubicBezierAsString([0, 0.65, 0.55, 1]),\n  circOut: cubicBezierAsString([0.55, 0, 1, 0.45]),\n  backIn: cubicBezierAsString([0.31, 0.01, 0.66, -0.59]),\n  backOut: cubicBezierAsString([0.33, 1.53, 0.69, 0.99])\n};\nfunction mapEasingToNativeEasing(easing) {\n  if (!easing) return undefined;\n  return isBezierDefinition(easing) ? cubicBezierAsString(easing) : Array.isArray(easing) ? easing.map(mapEasingToNativeEasing) : supportedWaapiEasing[easing];\n}\nexport { cubicBezierAsString, isWaapiSupportedEasing, mapEasingToNativeEasing, supportedWaapiEasing };", "map": {"version": 3, "names": ["isBezierDefinition", "isWaapiSupportedEasing", "easing", "Boolean", "supportedWaapiEasing", "Array", "isArray", "every", "cubicBezierAsString", "a", "b", "c", "d", "linear", "ease", "easeIn", "easeOut", "easeInOut", "circIn", "circOut", "backIn", "backOut", "mapEasingToNativeEasing", "undefined", "map"], "sources": ["C:/Users/<USER>/Downloads/Advanced/Advanced CRM filed all use/frontend/node_modules/framer-motion/dist/es/animation/animators/waapi/easing.mjs"], "sourcesContent": ["import { isBezierDefinition } from '../../../easing/utils/is-bezier-definition.mjs';\n\nfunction isWaapiSupportedEasing(easing) {\n    return Boolean(!easing ||\n        (typeof easing === \"string\" && supportedWaapiEasing[easing]) ||\n        isBezierDefinition(easing) ||\n        (Array.isArray(easing) && easing.every(isWaapiSupportedEasing)));\n}\nconst cubicBezierAsString = ([a, b, c, d]) => `cubic-bezier(${a}, ${b}, ${c}, ${d})`;\nconst supportedWaapiEasing = {\n    linear: \"linear\",\n    ease: \"ease\",\n    easeIn: \"ease-in\",\n    easeOut: \"ease-out\",\n    easeInOut: \"ease-in-out\",\n    circIn: cubicBezierAsString([0, 0.65, 0.55, 1]),\n    circOut: cubicBezierAsString([0.55, 0, 1, 0.45]),\n    backIn: cubicBezierAsString([0.31, 0.01, 0.66, -0.59]),\n    backOut: cubicBezierAsString([0.33, 1.53, 0.69, 0.99]),\n};\nfunction mapEasingToNativeEasing(easing) {\n    if (!easing)\n        return undefined;\n    return isBezierDefinition(easing)\n        ? cubicBezierAsString(easing)\n        : Array.isArray(easing)\n            ? easing.map(mapEasingToNativeEasing)\n            : supportedWaapiEasing[easing];\n}\n\nexport { cubicBezierAsString, isWaapiSupportedEasing, mapEasingToNativeEasing, supportedWaapiEasing };\n"], "mappings": "AAAA,SAASA,kBAAkB,QAAQ,gDAAgD;AAEnF,SAASC,sBAAsBA,CAACC,MAAM,EAAE;EACpC,OAAOC,OAAO,CAAC,CAACD,MAAM,IACjB,OAAOA,MAAM,KAAK,QAAQ,IAAIE,oBAAoB,CAACF,MAAM,CAAE,IAC5DF,kBAAkB,CAACE,MAAM,CAAC,IACzBG,KAAK,CAACC,OAAO,CAACJ,MAAM,CAAC,IAAIA,MAAM,CAACK,KAAK,CAACN,sBAAsB,CAAE,CAAC;AACxE;AACA,MAAMO,mBAAmB,GAAGA,CAAC,CAACC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC,KAAK,gBAAgBH,CAAC,KAAKC,CAAC,KAAKC,CAAC,KAAKC,CAAC,GAAG;AACpF,MAAMR,oBAAoB,GAAG;EACzBS,MAAM,EAAE,QAAQ;EAChBC,IAAI,EAAE,MAAM;EACZC,MAAM,EAAE,SAAS;EACjBC,OAAO,EAAE,UAAU;EACnBC,SAAS,EAAE,aAAa;EACxBC,MAAM,EAAEV,mBAAmB,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;EAC/CW,OAAO,EAAEX,mBAAmB,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;EAChDY,MAAM,EAAEZ,mBAAmB,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,IAAI,CAAC,CAAC;EACtDa,OAAO,EAAEb,mBAAmB,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;AACzD,CAAC;AACD,SAASc,uBAAuBA,CAACpB,MAAM,EAAE;EACrC,IAAI,CAACA,MAAM,EACP,OAAOqB,SAAS;EACpB,OAAOvB,kBAAkB,CAACE,MAAM,CAAC,GAC3BM,mBAAmB,CAACN,MAAM,CAAC,GAC3BG,KAAK,CAACC,OAAO,CAACJ,MAAM,CAAC,GACjBA,MAAM,CAACsB,GAAG,CAACF,uBAAuB,CAAC,GACnClB,oBAAoB,CAACF,MAAM,CAAC;AAC1C;AAEA,SAASM,mBAAmB,EAAEP,sBAAsB,EAAEqB,uBAAuB,EAAElB,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}