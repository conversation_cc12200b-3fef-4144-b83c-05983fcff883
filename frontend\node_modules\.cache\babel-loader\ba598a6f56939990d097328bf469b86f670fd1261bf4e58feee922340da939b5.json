{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Advanced\\\\Advanced CRM filed all use\\\\frontend\\\\src\\\\components\\\\PersonManagement.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport FormBuilder from './forms/FormBuilder';\nimport DynamicPersonForm from './forms/DynamicPersonForm';\nimport PersonList from './PersonList';\nimport ImportPersons from './import/ImportPersons';\nimport formConfigService from '../services/formConfigService';\nimport './PersonManagement.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PersonManagement = () => {\n  _s();\n  const [currentView, setCurrentView] = useState('list'); // 'list', 'create', 'edit', 'formBuilder', 'import'\n  const [selectedPerson, setSelectedPerson] = useState(null);\n  const [notification, setNotification] = useState(null);\n  const [showImportModal, setShowImportModal] = useState(false);\n  const showNotification = (message, type = 'success') => {\n    setNotification({\n      message,\n      type\n    });\n    setTimeout(() => setNotification(null), 5000);\n  };\n  const handleCreatePerson = () => {\n    setSelectedPerson(null);\n    setCurrentView('create');\n  };\n  const handleEditPerson = person => {\n    setSelectedPerson(person);\n    setCurrentView('edit');\n  };\n  const handlePersonSubmit = result => {\n    const action = currentView === 'create' ? 'created' : 'updated';\n    showNotification(`Person ${action} successfully!`);\n    setCurrentView('list');\n    setSelectedPerson(null);\n  };\n  const handleFormBuilderOpen = () => {\n    setCurrentView('formBuilder');\n  };\n  const handleFormBuilderSave = config => {\n    showNotification(`Form configuration \"${config.name}\" saved successfully!`);\n    setCurrentView('list');\n  };\n  const handleImportOpen = () => {\n    setShowImportModal(true);\n  };\n  const handleImportSuccess = results => {\n    showNotification(`Import completed! ${results.successfulRows} persons imported successfully.`);\n    setShowImportModal(false);\n    // Refresh the person list if we're on the list view\n    if (currentView === 'list') {\n      // The PersonList component should handle refreshing\n    }\n  };\n  const handleImportClose = () => {\n    setShowImportModal(false);\n  };\n  const handleCancel = () => {\n    setCurrentView('list');\n    setSelectedPerson(null);\n  };\n  const getFormStatistics = () => {\n    return formConfigService.getFormStatistics();\n  };\n  const renderCurrentView = () => {\n    switch (currentView) {\n      case 'create':\n        return /*#__PURE__*/_jsxDEV(DynamicPersonForm, {\n          mode: \"create\",\n          onSubmit: handlePersonSubmit,\n          onCancel: handleCancel\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 11\n        }, this);\n      case 'edit':\n        return /*#__PURE__*/_jsxDEV(DynamicPersonForm, {\n          mode: \"edit\",\n          initialData: selectedPerson,\n          onSubmit: handlePersonSubmit,\n          onCancel: handleCancel\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 11\n        }, this);\n      case 'formBuilder':\n        return /*#__PURE__*/_jsxDEV(FormBuilder, {\n          onSave: handleFormBuilderSave,\n          onCancel: handleCancel\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 95,\n          columnNumber: 11\n        }, this);\n      case 'list':\n      default:\n        return /*#__PURE__*/_jsxDEV(PersonList, {\n          onCreatePerson: handleCreatePerson,\n          onEditPerson: handleEditPerson,\n          onFormBuilder: handleFormBuilderOpen,\n          onImportPersons: handleImportOpen\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 104,\n          columnNumber: 11\n        }, this);\n    }\n  };\n  const statistics = getFormStatistics();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"person-management\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"management-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          children: \"Person Management System\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"header-stats\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"stat-label\",\n              children: \"Total Forms:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 124,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"stat-value\",\n              children: statistics.totalForms\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 125,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 123,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"stat-label\",\n              children: \"Category Forms:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 128,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"stat-value\",\n              children: statistics.categoryForms\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 129,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 127,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"stat-label\",\n              children: \"SubCategory Forms:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 132,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"stat-value\",\n              children: statistics.subCategoryForms\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 133,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 131,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 122,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 120,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-nav\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setCurrentView('list'),\n          className: `nav-btn ${currentView === 'list' ? 'active' : ''}`,\n          children: \"\\uD83D\\uDCCB Person List\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleCreatePerson,\n          className: `nav-btn ${currentView === 'create' ? 'active' : ''}`,\n          children: \"\\u2795 Create Person\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleFormBuilderOpen,\n          className: `nav-btn ${currentView === 'formBuilder' ? 'active' : ''}`,\n          children: \"\\uD83D\\uDD27 Form Builder\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 152,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleImportOpen,\n          className: \"nav-btn\",\n          children: \"\\uD83D\\uDCE5 Import Persons\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 139,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 119,\n      columnNumber: 7\n    }, this), notification && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `notification ${notification.type}`,\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        children: notification.message\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 170,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => setNotification(null),\n        className: \"notification-close\",\n        children: \"\\xD7\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 171,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 169,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"management-content\",\n      children: renderCurrentView()\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 178,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"management-footer\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"footer-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"footer-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            children: \"Quick Actions\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"quick-actions\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleCreatePerson,\n              className: \"quick-action-btn\",\n              children: \"Create New Person\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleFormBuilderOpen,\n              className: \"quick-action-btn\",\n              children: \"Design Custom Form\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 191,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleImportOpen,\n              className: \"quick-action-btn\",\n              children: \"Import Persons from File\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 194,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"footer-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            children: \"Form Statistics\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"footer-stats\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"footer-stat\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Average Fields per Form:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 204,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: statistics.averageFieldCount\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 205,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 203,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"footer-stat\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Most Used Fields:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 208,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: statistics.mostUsedFields.slice(0, 3).map(f => f.field).join(', ')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 209,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 207,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 202,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 200,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"footer-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            children: \"System Info\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"footer-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: \"Dynamic Form Builder v1.0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 217,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: \"React.js Frontend\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: \"Local Storage Configuration\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 216,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 214,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 184,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 183,\n      columnNumber: 7\n    }, this), showImportModal && /*#__PURE__*/_jsxDEV(ImportPersons, {\n      onClose: handleImportClose,\n      onSuccess: handleImportSuccess\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 227,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 117,\n    columnNumber: 5\n  }, this);\n};\n_s(PersonManagement, \"JoZRaQBbva0YxvBMychW+zke+vc=\");\n_c = PersonManagement;\nexport default PersonManagement;\nvar _c;\n$RefreshReg$(_c, \"PersonManagement\");", "map": {"version": 3, "names": ["React", "useState", "FormBuilder", "DynamicPersonForm", "PersonList", "<PERSON><PERSON>rt<PERSON><PERSON><PERSON>", "formConfigService", "jsxDEV", "_jsxDEV", "PersonManagement", "_s", "current<PERSON>iew", "set<PERSON><PERSON><PERSON>View", "<PERSON><PERSON><PERSON>", "setSelected<PERSON><PERSON>", "notification", "setNotification", "showImportModal", "setShowImportModal", "showNotification", "message", "type", "setTimeout", "handleCreate<PERSON>erson", "handleEditPerson", "person", "handlePersonSubmit", "result", "action", "handleFormBuilderOpen", "handleFormBuilderSave", "config", "name", "handleImportOpen", "handleImportSuccess", "results", "successfulRows", "handleImportClose", "handleCancel", "getFormStatistics", "renderCurrentView", "mode", "onSubmit", "onCancel", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "initialData", "onSave", "on<PERSON><PERSON><PERSON><PERSON>", "onEdit<PERSON>erson", "onFormBuilder", "onImport<PERSON>ersons", "statistics", "className", "children", "totalForms", "categoryForms", "subCategoryForms", "onClick", "averageFieldCount", "mostUsedFields", "slice", "map", "f", "field", "join", "onClose", "onSuccess", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/Advanced/Advanced CRM filed all use/frontend/src/components/PersonManagement.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport FormBuilder from './forms/FormBuilder';\nimport DynamicPersonForm from './forms/DynamicPersonForm';\nimport PersonList from './PersonList';\nimport ImportPersons from './import/ImportPersons';\nimport formConfigService from '../services/formConfigService';\nimport './PersonManagement.css';\n\nconst PersonManagement = () => {\n  const [currentView, setCurrentView] = useState('list'); // 'list', 'create', 'edit', 'formBuilder', 'import'\n  const [selectedPerson, setSelectedPerson] = useState(null);\n  const [notification, setNotification] = useState(null);\n  const [showImportModal, setShowImportModal] = useState(false);\n\n  const showNotification = (message, type = 'success') => {\n    setNotification({ message, type });\n    setTimeout(() => setNotification(null), 5000);\n  };\n\n  const handleCreatePerson = () => {\n    setSelectedPerson(null);\n    setCurrentView('create');\n  };\n\n  const handleEditPerson = (person) => {\n    setSelectedPerson(person);\n    setCurrentView('edit');\n  };\n\n  const handlePersonSubmit = (result) => {\n    const action = currentView === 'create' ? 'created' : 'updated';\n    showNotification(`Person ${action} successfully!`);\n    setCurrentView('list');\n    setSelectedPerson(null);\n  };\n\n  const handleFormBuilderOpen = () => {\n    setCurrentView('formBuilder');\n  };\n\n  const handleFormBuilderSave = (config) => {\n    showNotification(`Form configuration \"${config.name}\" saved successfully!`);\n    setCurrentView('list');\n  };\n\n  const handleImportOpen = () => {\n    setShowImportModal(true);\n  };\n\n  const handleImportSuccess = (results) => {\n    showNotification(`Import completed! ${results.successfulRows} persons imported successfully.`);\n    setShowImportModal(false);\n    // Refresh the person list if we're on the list view\n    if (currentView === 'list') {\n      // The PersonList component should handle refreshing\n    }\n  };\n\n  const handleImportClose = () => {\n    setShowImportModal(false);\n  };\n\n  const handleCancel = () => {\n    setCurrentView('list');\n    setSelectedPerson(null);\n  };\n\n  const getFormStatistics = () => {\n    return formConfigService.getFormStatistics();\n  };\n\n  const renderCurrentView = () => {\n    switch (currentView) {\n      case 'create':\n        return (\n          <DynamicPersonForm\n            mode=\"create\"\n            onSubmit={handlePersonSubmit}\n            onCancel={handleCancel}\n          />\n        );\n      \n      case 'edit':\n        return (\n          <DynamicPersonForm\n            mode=\"edit\"\n            initialData={selectedPerson}\n            onSubmit={handlePersonSubmit}\n            onCancel={handleCancel}\n          />\n        );\n      \n      case 'formBuilder':\n        return (\n          <FormBuilder\n            onSave={handleFormBuilderSave}\n            onCancel={handleCancel}\n          />\n        );\n      \n      case 'list':\n      default:\n        return (\n          <PersonList\n            onCreatePerson={handleCreatePerson}\n            onEditPerson={handleEditPerson}\n            onFormBuilder={handleFormBuilderOpen}\n            onImportPersons={handleImportOpen}\n          />\n        );\n    }\n  };\n\n  const statistics = getFormStatistics();\n\n  return (\n    <div className=\"person-management\">\n      {/* Header */}\n      <div className=\"management-header\">\n        <div className=\"header-content\">\n          <h1>Person Management System</h1>\n          <div className=\"header-stats\">\n            <div className=\"stat-item\">\n              <span className=\"stat-label\">Total Forms:</span>\n              <span className=\"stat-value\">{statistics.totalForms}</span>\n            </div>\n            <div className=\"stat-item\">\n              <span className=\"stat-label\">Category Forms:</span>\n              <span className=\"stat-value\">{statistics.categoryForms}</span>\n            </div>\n            <div className=\"stat-item\">\n              <span className=\"stat-label\">SubCategory Forms:</span>\n              <span className=\"stat-value\">{statistics.subCategoryForms}</span>\n            </div>\n          </div>\n        </div>\n        \n        {/* Navigation */}\n        <div className=\"header-nav\">\n          <button\n            onClick={() => setCurrentView('list')}\n            className={`nav-btn ${currentView === 'list' ? 'active' : ''}`}\n          >\n            📋 Person List\n          </button>\n          <button\n            onClick={handleCreatePerson}\n            className={`nav-btn ${currentView === 'create' ? 'active' : ''}`}\n          >\n            ➕ Create Person\n          </button>\n          <button\n            onClick={handleFormBuilderOpen}\n            className={`nav-btn ${currentView === 'formBuilder' ? 'active' : ''}`}\n          >\n            🔧 Form Builder\n          </button>\n          <button\n            onClick={handleImportOpen}\n            className=\"nav-btn\"\n          >\n            📥 Import Persons\n          </button>\n        </div>\n      </div>\n\n      {/* Notification */}\n      {notification && (\n        <div className={`notification ${notification.type}`}>\n          <span>{notification.message}</span>\n          <button onClick={() => setNotification(null)} className=\"notification-close\">\n            ×\n          </button>\n        </div>\n      )}\n\n      {/* Main Content */}\n      <div className=\"management-content\">\n        {renderCurrentView()}\n      </div>\n\n      {/* Footer */}\n      <div className=\"management-footer\">\n        <div className=\"footer-content\">\n          <div className=\"footer-section\">\n            <h4>Quick Actions</h4>\n            <div className=\"quick-actions\">\n              <button onClick={handleCreatePerson} className=\"quick-action-btn\">\n                Create New Person\n              </button>\n              <button onClick={handleFormBuilderOpen} className=\"quick-action-btn\">\n                Design Custom Form\n              </button>\n              <button onClick={handleImportOpen} className=\"quick-action-btn\">\n                Import Persons from File\n              </button>\n            </div>\n          </div>\n          \n          <div className=\"footer-section\">\n            <h4>Form Statistics</h4>\n            <div className=\"footer-stats\">\n              <div className=\"footer-stat\">\n                <span>Average Fields per Form:</span>\n                <span>{statistics.averageFieldCount}</span>\n              </div>\n              <div className=\"footer-stat\">\n                <span>Most Used Fields:</span>\n                <span>{statistics.mostUsedFields.slice(0, 3).map(f => f.field).join(', ')}</span>\n              </div>\n            </div>\n          </div>\n          \n          <div className=\"footer-section\">\n            <h4>System Info</h4>\n            <div className=\"footer-info\">\n              <div>Dynamic Form Builder v1.0</div>\n              <div>React.js Frontend</div>\n              <div>Local Storage Configuration</div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Import Modal */}\n      {showImportModal && (\n        <ImportPersons\n          onClose={handleImportClose}\n          onSuccess={handleImportSuccess}\n        />\n      )}\n    </div>\n  );\n};\n\nexport default PersonManagement;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,iBAAiB,MAAM,2BAA2B;AACzD,OAAOC,UAAU,MAAM,cAAc;AACrC,OAAOC,aAAa,MAAM,wBAAwB;AAClD,OAAOC,iBAAiB,MAAM,+BAA+B;AAC7D,OAAO,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhC,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC7B,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGX,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;EACxD,MAAM,CAACY,cAAc,EAAEC,iBAAiB,CAAC,GAAGb,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACc,YAAY,EAAEC,eAAe,CAAC,GAAGf,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACgB,eAAe,EAAEC,kBAAkB,CAAC,GAAGjB,QAAQ,CAAC,KAAK,CAAC;EAE7D,MAAMkB,gBAAgB,GAAGA,CAACC,OAAO,EAAEC,IAAI,GAAG,SAAS,KAAK;IACtDL,eAAe,CAAC;MAAEI,OAAO;MAAEC;IAAK,CAAC,CAAC;IAClCC,UAAU,CAAC,MAAMN,eAAe,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC;EAC/C,CAAC;EAED,MAAMO,kBAAkB,GAAGA,CAAA,KAAM;IAC/BT,iBAAiB,CAAC,IAAI,CAAC;IACvBF,cAAc,CAAC,QAAQ,CAAC;EAC1B,CAAC;EAED,MAAMY,gBAAgB,GAAIC,MAAM,IAAK;IACnCX,iBAAiB,CAACW,MAAM,CAAC;IACzBb,cAAc,CAAC,MAAM,CAAC;EACxB,CAAC;EAED,MAAMc,kBAAkB,GAAIC,MAAM,IAAK;IACrC,MAAMC,MAAM,GAAGjB,WAAW,KAAK,QAAQ,GAAG,SAAS,GAAG,SAAS;IAC/DQ,gBAAgB,CAAC,UAAUS,MAAM,gBAAgB,CAAC;IAClDhB,cAAc,CAAC,MAAM,CAAC;IACtBE,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAMe,qBAAqB,GAAGA,CAAA,KAAM;IAClCjB,cAAc,CAAC,aAAa,CAAC;EAC/B,CAAC;EAED,MAAMkB,qBAAqB,GAAIC,MAAM,IAAK;IACxCZ,gBAAgB,CAAC,uBAAuBY,MAAM,CAACC,IAAI,uBAAuB,CAAC;IAC3EpB,cAAc,CAAC,MAAM,CAAC;EACxB,CAAC;EAED,MAAMqB,gBAAgB,GAAGA,CAAA,KAAM;IAC7Bf,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,MAAMgB,mBAAmB,GAAIC,OAAO,IAAK;IACvChB,gBAAgB,CAAC,qBAAqBgB,OAAO,CAACC,cAAc,iCAAiC,CAAC;IAC9FlB,kBAAkB,CAAC,KAAK,CAAC;IACzB;IACA,IAAIP,WAAW,KAAK,MAAM,EAAE;MAC1B;IAAA;EAEJ,CAAC;EAED,MAAM0B,iBAAiB,GAAGA,CAAA,KAAM;IAC9BnB,kBAAkB,CAAC,KAAK,CAAC;EAC3B,CAAC;EAED,MAAMoB,YAAY,GAAGA,CAAA,KAAM;IACzB1B,cAAc,CAAC,MAAM,CAAC;IACtBE,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAMyB,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,OAAOjC,iBAAiB,CAACiC,iBAAiB,CAAC,CAAC;EAC9C,CAAC;EAED,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,QAAQ7B,WAAW;MACjB,KAAK,QAAQ;QACX,oBACEH,OAAA,CAACL,iBAAiB;UAChBsC,IAAI,EAAC,QAAQ;UACbC,QAAQ,EAAEhB,kBAAmB;UAC7BiB,QAAQ,EAAEL;QAAa;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC;MAGN,KAAK,MAAM;QACT,oBACEvC,OAAA,CAACL,iBAAiB;UAChBsC,IAAI,EAAC,MAAM;UACXO,WAAW,EAAEnC,cAAe;UAC5B6B,QAAQ,EAAEhB,kBAAmB;UAC7BiB,QAAQ,EAAEL;QAAa;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC;MAGN,KAAK,aAAa;QAChB,oBACEvC,OAAA,CAACN,WAAW;UACV+C,MAAM,EAAEnB,qBAAsB;UAC9Ba,QAAQ,EAAEL;QAAa;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC;MAGN,KAAK,MAAM;MACX;QACE,oBACEvC,OAAA,CAACJ,UAAU;UACT8C,cAAc,EAAE3B,kBAAmB;UACnC4B,YAAY,EAAE3B,gBAAiB;UAC/B4B,aAAa,EAAEvB,qBAAsB;UACrCwB,eAAe,EAAEpB;QAAiB;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC;IAER;EACF,CAAC;EAED,MAAMO,UAAU,GAAGf,iBAAiB,CAAC,CAAC;EAEtC,oBACE/B,OAAA;IAAK+C,SAAS,EAAC,mBAAmB;IAAAC,QAAA,gBAEhChD,OAAA;MAAK+C,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChChD,OAAA;QAAK+C,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BhD,OAAA;UAAAgD,QAAA,EAAI;QAAwB;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACjCvC,OAAA;UAAK+C,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BhD,OAAA;YAAK+C,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBhD,OAAA;cAAM+C,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAY;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAChDvC,OAAA;cAAM+C,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAEF,UAAU,CAACG;YAAU;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxD,CAAC,eACNvC,OAAA;YAAK+C,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBhD,OAAA;cAAM+C,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAe;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACnDvC,OAAA;cAAM+C,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAEF,UAAU,CAACI;YAAa;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D,CAAC,eACNvC,OAAA;YAAK+C,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBhD,OAAA;cAAM+C,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAkB;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACtDvC,OAAA;cAAM+C,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAEF,UAAU,CAACK;YAAgB;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNvC,OAAA;QAAK+C,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBhD,OAAA;UACEoD,OAAO,EAAEA,CAAA,KAAMhD,cAAc,CAAC,MAAM,CAAE;UACtC2C,SAAS,EAAE,WAAW5C,WAAW,KAAK,MAAM,GAAG,QAAQ,GAAG,EAAE,EAAG;UAAA6C,QAAA,EAChE;QAED;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTvC,OAAA;UACEoD,OAAO,EAAErC,kBAAmB;UAC5BgC,SAAS,EAAE,WAAW5C,WAAW,KAAK,QAAQ,GAAG,QAAQ,GAAG,EAAE,EAAG;UAAA6C,QAAA,EAClE;QAED;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTvC,OAAA;UACEoD,OAAO,EAAE/B,qBAAsB;UAC/B0B,SAAS,EAAE,WAAW5C,WAAW,KAAK,aAAa,GAAG,QAAQ,GAAG,EAAE,EAAG;UAAA6C,QAAA,EACvE;QAED;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTvC,OAAA;UACEoD,OAAO,EAAE3B,gBAAiB;UAC1BsB,SAAS,EAAC,SAAS;UAAAC,QAAA,EACpB;QAED;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLhC,YAAY,iBACXP,OAAA;MAAK+C,SAAS,EAAE,gBAAgBxC,YAAY,CAACM,IAAI,EAAG;MAAAmC,QAAA,gBAClDhD,OAAA;QAAAgD,QAAA,EAAOzC,YAAY,CAACK;MAAO;QAAAwB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACnCvC,OAAA;QAAQoD,OAAO,EAAEA,CAAA,KAAM5C,eAAe,CAAC,IAAI,CAAE;QAACuC,SAAS,EAAC,oBAAoB;QAAAC,QAAA,EAAC;MAE7E;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CACN,eAGDvC,OAAA;MAAK+C,SAAS,EAAC,oBAAoB;MAAAC,QAAA,EAChChB,iBAAiB,CAAC;IAAC;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CAAC,eAGNvC,OAAA;MAAK+C,SAAS,EAAC,mBAAmB;MAAAC,QAAA,eAChChD,OAAA;QAAK+C,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BhD,OAAA;UAAK+C,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BhD,OAAA;YAAAgD,QAAA,EAAI;UAAa;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACtBvC,OAAA;YAAK+C,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5BhD,OAAA;cAAQoD,OAAO,EAAErC,kBAAmB;cAACgC,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAC;YAElE;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTvC,OAAA;cAAQoD,OAAO,EAAE/B,qBAAsB;cAAC0B,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAC;YAErE;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTvC,OAAA;cAAQoD,OAAO,EAAE3B,gBAAiB;cAACsB,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAC;YAEhE;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENvC,OAAA;UAAK+C,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BhD,OAAA;YAAAgD,QAAA,EAAI;UAAe;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxBvC,OAAA;YAAK+C,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BhD,OAAA;cAAK+C,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BhD,OAAA;gBAAAgD,QAAA,EAAM;cAAwB;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACrCvC,OAAA;gBAAAgD,QAAA,EAAOF,UAAU,CAACO;cAAiB;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CAAC,eACNvC,OAAA;cAAK+C,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BhD,OAAA;gBAAAgD,QAAA,EAAM;cAAiB;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC9BvC,OAAA;gBAAAgD,QAAA,EAAOF,UAAU,CAACQ,cAAc,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACC,KAAK,CAAC,CAACC,IAAI,CAAC,IAAI;cAAC;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9E,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENvC,OAAA;UAAK+C,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BhD,OAAA;YAAAgD,QAAA,EAAI;UAAW;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpBvC,OAAA;YAAK+C,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BhD,OAAA;cAAAgD,QAAA,EAAK;YAAyB;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACpCvC,OAAA;cAAAgD,QAAA,EAAK;YAAiB;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC5BvC,OAAA;cAAAgD,QAAA,EAAK;YAA2B;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGL9B,eAAe,iBACdT,OAAA,CAACH,aAAa;MACZ+D,OAAO,EAAE/B,iBAAkB;MAC3BgC,SAAS,EAAEnC;IAAoB;MAAAU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChC,CACF;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACrC,EAAA,CAjOID,gBAAgB;AAAA6D,EAAA,GAAhB7D,gBAAgB;AAmOtB,eAAeA,gBAAgB;AAAC,IAAA6D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}