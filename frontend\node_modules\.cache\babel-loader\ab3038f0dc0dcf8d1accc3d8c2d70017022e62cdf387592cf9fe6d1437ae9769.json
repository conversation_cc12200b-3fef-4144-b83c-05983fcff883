{"ast": null, "code": "// Enums\nexport const PersonNature = {\n  Business: 1,\n  Corporate: 2,\n  Agriculture: 3,\n  Individual: 4\n};\nexport const PersonNatureLabels = {\n  [PersonNature.Business]: 'Business',\n  [PersonNature.Corporate]: 'Corporate',\n  [PersonNature.Agriculture]: 'Agriculture',\n  [PersonNature.Individual]: 'Individual'\n};\nexport const Gender = {\n  Male: 1,\n  Female: 2,\n  Other: 3\n};\nexport const GenderLabels = {\n  [Gender.Male]: 'Male',\n  [Gender.Female]: 'Female',\n  [Gender.Other]: 'Other'\n};\nexport const WorkingProfile = {\n  Business: 1,\n  Corporate: 2,\n  Agriculture: 3\n};\nexport const WorkingProfileLabels = {\n  [WorkingProfile.Business]: 'Business',\n  [WorkingProfile.Corporate]: 'Corporate',\n  [WorkingProfile.Agriculture]: 'Agriculture'\n};\n\n// Field definitions for form builder\nexport const PersonFieldDefinitions = {\n  // Personal Information Section\n  personalInfo: {\n    title: 'Personal Information',\n    fields: {\n      name: {\n        key: 'name',\n        label: 'Full Name',\n        type: 'text',\n        required: true,\n        validation: {\n          maxLength: 255\n        },\n        section: 'personalInfo'\n      },\n      gender: {\n        key: 'gender',\n        label: 'Gender',\n        type: 'select',\n        required: false,\n        options: Object.entries(GenderLabels).map(([value, label]) => ({\n          value: parseInt(value),\n          label\n        })),\n        section: 'personalInfo'\n      },\n      dateOfBirth: {\n        key: 'dateOfBirth',\n        label: 'Date of Birth',\n        type: 'date',\n        required: false,\n        section: 'personalInfo'\n      },\n      isMarried: {\n        key: 'isMarried',\n        label: 'Is Married',\n        type: 'checkbox',\n        required: false,\n        section: 'personalInfo'\n      },\n      dateOfMarriage: {\n        key: 'dateOfMarriage',\n        label: 'Date of Marriage',\n        type: 'date',\n        required: false,\n        conditional: {\n          field: 'isMarried',\n          value: true\n        },\n        section: 'personalInfo'\n      }\n    }\n  },\n  // Contact Information Section\n  contactInfo: {\n    title: 'Contact Information',\n    fields: {\n      mobileNumber: {\n        key: 'mobileNumber',\n        label: 'Mobile Number',\n        type: 'tel',\n        required: true,\n        validation: {\n          pattern: '^(\\\\+91[\\\\-\\\\s]?)?[0]?(91)?[789]\\\\d{9}$'\n        },\n        section: 'contactInfo'\n      },\n      alternateNumbers: {\n        key: 'alternateNumbers',\n        label: 'Alternate Numbers',\n        type: 'array',\n        required: false,\n        section: 'contactInfo'\n      },\n      primaryEmailId: {\n        key: 'primaryEmailId',\n        label: 'Primary Email',\n        type: 'email',\n        required: false,\n        section: 'contactInfo'\n      },\n      alternateEmailIds: {\n        key: 'alternateEmailIds',\n        label: 'Alternate Emails',\n        type: 'array',\n        required: false,\n        section: 'contactInfo'\n      },\n      website: {\n        key: 'website',\n        label: 'Website',\n        type: 'url',\n        required: false,\n        section: 'contactInfo'\n      }\n    }\n  },\n  // Location Information Section\n  locationInfo: {\n    title: 'Location Information',\n    fields: {\n      workingState: {\n        key: 'workingState',\n        label: 'Working State',\n        type: 'text',\n        required: false,\n        section: 'locationInfo'\n      },\n      domesticState: {\n        key: 'domesticState',\n        label: 'Domestic State',\n        type: 'text',\n        required: false,\n        section: 'locationInfo'\n      },\n      district: {\n        key: 'district',\n        label: 'District',\n        type: 'text',\n        required: false,\n        section: 'locationInfo'\n      },\n      address: {\n        key: 'address',\n        label: 'Address',\n        type: 'textarea',\n        required: false,\n        section: 'locationInfo'\n      },\n      workingArea: {\n        key: 'workingArea',\n        label: 'Working Area',\n        type: 'text',\n        required: false,\n        section: 'locationInfo'\n      }\n    }\n  },\n  // Business Information Section\n  businessInfo: {\n    title: 'Business Information',\n    fields: {\n      nature: {\n        key: 'nature',\n        label: 'Nature',\n        type: 'select',\n        required: true,\n        options: Object.entries(PersonNatureLabels).map(([value, label]) => ({\n          value: parseInt(value),\n          label\n        })),\n        section: 'businessInfo'\n      },\n      transactionValue: {\n        key: 'transactionValue',\n        label: 'Transaction Value',\n        type: 'number',\n        required: false,\n        section: 'businessInfo'\n      },\n      reraRegistrationNumber: {\n        key: 'reraRegistrationNumber',\n        label: 'RERA Registration Number',\n        type: 'text',\n        required: false,\n        section: 'businessInfo'\n      },\n      workingProfiles: {\n        key: 'workingProfiles',\n        label: 'Working Profiles',\n        type: 'multiselect',\n        required: false,\n        options: Object.entries(WorkingProfileLabels).map(([value, label]) => ({\n          value: parseInt(value),\n          label\n        })),\n        section: 'businessInfo'\n      },\n      starRating: {\n        key: 'starRating',\n        label: 'Star Rating',\n        type: 'number',\n        required: false,\n        validation: {\n          min: 1,\n          max: 5\n        },\n        section: 'businessInfo'\n      },\n      source: {\n        key: 'source',\n        label: 'Source',\n        type: 'text',\n        required: false,\n        section: 'businessInfo'\n      },\n      remarks: {\n        key: 'remarks',\n        label: 'Remarks',\n        type: 'textarea',\n        required: false,\n        section: 'businessInfo'\n      }\n    }\n  },\n  // Associate Information Section\n  associateInfo: {\n    title: 'Associate Information',\n    fields: {\n      hasAssociate: {\n        key: 'hasAssociate',\n        label: 'Has Associate',\n        type: 'checkbox',\n        required: false,\n        section: 'associateInfo'\n      },\n      associateName: {\n        key: 'associateName',\n        label: 'Associate Name',\n        type: 'text',\n        required: false,\n        conditional: {\n          field: 'hasAssociate',\n          value: true\n        },\n        section: 'associateInfo'\n      },\n      associateRelation: {\n        key: 'associateRelation',\n        label: 'Associate Relation',\n        type: 'text',\n        required: false,\n        conditional: {\n          field: 'hasAssociate',\n          value: true\n        },\n        section: 'associateInfo'\n      },\n      associateMobile: {\n        key: 'associateMobile',\n        label: 'Associate Mobile',\n        type: 'tel',\n        required: false,\n        conditional: {\n          field: 'hasAssociate',\n          value: true\n        },\n        section: 'associateInfo'\n      }\n    }\n  },\n  // Digital Presence Section\n  digitalPresence: {\n    title: 'Digital Presence',\n    fields: {\n      usingWebsite: {\n        key: 'usingWebsite',\n        label: 'Using Website',\n        type: 'checkbox',\n        required: false,\n        section: 'digitalPresence'\n      },\n      websiteLink: {\n        key: 'websiteLink',\n        label: 'Website Link',\n        type: 'url',\n        required: false,\n        conditional: {\n          field: 'usingWebsite',\n          value: true\n        },\n        section: 'digitalPresence'\n      },\n      usingCRMApp: {\n        key: 'usingCRMApp',\n        label: 'Using CRM App',\n        type: 'checkbox',\n        required: false,\n        section: 'digitalPresence'\n      },\n      crmAppLink: {\n        key: 'crmAppLink',\n        label: 'CRM App Link',\n        type: 'url',\n        required: false,\n        conditional: {\n          field: 'usingCRMApp',\n          value: true\n        },\n        section: 'digitalPresence'\n      }\n    }\n  },\n  // Company Information Section\n  companyInfo: {\n    title: 'Company Information',\n    fields: {\n      firmName: {\n        key: 'firmName',\n        label: 'Firm Name',\n        type: 'text',\n        required: false,\n        section: 'companyInfo'\n      },\n      numberOfOffices: {\n        key: 'numberOfOffices',\n        label: 'Number of Offices',\n        type: 'number',\n        required: false,\n        section: 'companyInfo'\n      },\n      numberOfBranches: {\n        key: 'numberOfBranches',\n        label: 'Number of Branches',\n        type: 'number',\n        required: false,\n        section: 'companyInfo'\n      },\n      totalEmployeeStrength: {\n        key: 'totalEmployeeStrength',\n        label: 'Total Employee Strength',\n        type: 'number',\n        required: false,\n        section: 'companyInfo'\n      }\n    }\n  },\n  // Authorized Person Section\n  authorizedPerson: {\n    title: 'Authorized Person',\n    fields: {\n      authorizedPersonName: {\n        key: 'authorizedPersonName',\n        label: 'Authorized Person Name',\n        type: 'text',\n        required: false,\n        section: 'authorizedPerson'\n      },\n      authorizedPersonEmail: {\n        key: 'authorizedPersonEmail',\n        label: 'Authorized Person Email',\n        type: 'email',\n        required: false,\n        section: 'authorizedPerson'\n      },\n      designation: {\n        key: 'designation',\n        label: 'Designation',\n        type: 'text',\n        required: false,\n        section: 'authorizedPerson'\n      }\n    }\n  },\n  // Marketing Information Section\n  marketingInfo: {\n    title: 'Marketing Information',\n    fields: {\n      marketingContact: {\n        key: 'marketingContact',\n        label: 'Marketing Contact',\n        type: 'text',\n        required: false,\n        section: 'marketingInfo'\n      },\n      marketingDesignation: {\n        key: 'marketingDesignation',\n        label: 'Marketing Designation',\n        type: 'text',\n        required: false,\n        section: 'marketingInfo'\n      },\n      placeOfPosting: {\n        key: 'placeOfPosting',\n        label: 'Place of Posting',\n        type: 'text',\n        required: false,\n        section: 'marketingInfo'\n      },\n      department: {\n        key: 'department',\n        label: 'Department',\n        type: 'text',\n        required: false,\n        section: 'marketingInfo'\n      }\n    }\n  }\n};\n\n// Get all fields as a flat array\nexport const getAllPersonFields = () => {\n  const allFields = [];\n  Object.values(PersonFieldDefinitions).forEach(section => {\n    Object.values(section.fields).forEach(field => {\n      allFields.push(field);\n    });\n  });\n  return allFields;\n};\n\n// Get required fields\nexport const getRequiredFields = () => {\n  return getAllPersonFields().filter(field => field.required);\n};\n\n// Default form data structure\nexport const getDefaultPersonData = () => ({\n  divisionId: null,\n  categoryId: null,\n  subCategoryId: null,\n  name: '',\n  mobileNumber: '',\n  nature: null,\n  gender: null,\n  alternateNumbers: [],\n  primaryEmailId: '',\n  alternateEmailIds: [],\n  website: '',\n  dateOfBirth: null,\n  isMarried: false,\n  dateOfMarriage: null,\n  workingState: '',\n  domesticState: '',\n  district: '',\n  address: '',\n  workingArea: '',\n  hasAssociate: false,\n  associateName: '',\n  associateRelation: '',\n  associateMobile: '',\n  usingWebsite: false,\n  websiteLink: '',\n  usingCRMApp: false,\n  crmAppLink: '',\n  transactionValue: null,\n  reraRegistrationNumber: '',\n  workingProfiles: [],\n  starRating: null,\n  source: '',\n  remarks: '',\n  firmName: '',\n  numberOfOffices: null,\n  numberOfBranches: null,\n  totalEmployeeStrength: null,\n  authorizedPersonName: '',\n  authorizedPersonEmail: '',\n  designation: '',\n  marketingContact: '',\n  marketingDesignation: '',\n  placeOfPosting: '',\n  department: ''\n});", "map": {"version": 3, "names": ["PersonNature", "Business", "Corporate", "Agriculture", "Individual", "PersonNatureLabels", "Gender", "Male", "Female", "Other", "<PERSON><PERSON><PERSON><PERSON>", "WorkingProfile", "WorkingProfileLabels", "PersonFieldDefinitions", "personalInfo", "title", "fields", "name", "key", "label", "type", "required", "validation", "max<PERSON><PERSON><PERSON>", "section", "gender", "options", "Object", "entries", "map", "value", "parseInt", "dateOfBirth", "<PERSON><PERSON><PERSON><PERSON>", "dateOfMarriage", "conditional", "field", "contactInfo", "mobileNumber", "pattern", "alternateNumbers", "primaryEmailId", "alternateEmailIds", "website", "locationInfo", "workingState", "domesticState", "district", "address", "workingArea", "businessInfo", "nature", "transactionValue", "reraRegistrationNumber", "workingProfiles", "starRating", "min", "max", "source", "remarks", "associateInfo", "hasAssociate", "associate<PERSON><PERSON>", "associateRelation", "associate<PERSON><PERSON><PERSON>", "digitalPresence", "usingWebsite", "websiteLink", "usingCRMApp", "crmAppLink", "companyInfo", "firmName", "numberOfOffices", "numberOfBranches", "totalEmployeeStrength", "<PERSON><PERSON><PERSON>", "authorizedPersonName", "authorizedPersonEmail", "designation", "marketingInfo", "marketingContact", "marketingDesignation", "placeOfPosting", "department", "<PERSON><PERSON><PERSON><PERSON><PERSON>F<PERSON>s", "allFields", "values", "for<PERSON>ach", "push", "getRequiredFields", "filter", "getDefaultPersonData", "divisionId", "categoryId", "subCategoryId"], "sources": ["C:/Users/<USER>/Downloads/Advanced/Advanced CRM filed all use/frontend/src/constants/personConstants.js"], "sourcesContent": ["// Enums\nexport const PersonNature = {\n  Business: 1,\n  Corporate: 2,\n  Agriculture: 3,\n  Individual: 4\n};\n\nexport const PersonNatureLabels = {\n  [PersonNature.Business]: 'Business',\n  [PersonNature.Corporate]: 'Corporate',\n  [PersonNature.Agriculture]: 'Agriculture',\n  [PersonNature.Individual]: 'Individual'\n};\n\nexport const Gender = {\n  Male: 1,\n  Female: 2,\n  Other: 3\n};\n\nexport const GenderLabels = {\n  [Gender.Male]: 'Male',\n  [Gender.Female]: 'Female',\n  [Gender.Other]: 'Other'\n};\n\nexport const WorkingProfile = {\n  Business: 1,\n  Corporate: 2,\n  Agriculture: 3\n};\n\nexport const WorkingProfileLabels = {\n  [WorkingProfile.Business]: 'Business',\n  [WorkingProfile.Corporate]: 'Corporate',\n  [WorkingProfile.Agriculture]: 'Agriculture'\n};\n\n// Field definitions for form builder\nexport const PersonFieldDefinitions = {\n  // Personal Information Section\n  personalInfo: {\n    title: 'Personal Information',\n    fields: {\n      name: {\n        key: 'name',\n        label: 'Full Name',\n        type: 'text',\n        required: true,\n        validation: { maxLength: 255 },\n        section: 'personalInfo'\n      },\n      gender: {\n        key: 'gender',\n        label: 'Gender',\n        type: 'select',\n        required: false,\n        options: Object.entries(GenderLabels).map(([value, label]) => ({ value: parseInt(value), label })),\n        section: 'personalInfo'\n      },\n      dateOfBirth: {\n        key: 'dateOfBirth',\n        label: 'Date of Birth',\n        type: 'date',\n        required: false,\n        section: 'personalInfo'\n      },\n      isMarried: {\n        key: 'isMarried',\n        label: 'Is Married',\n        type: 'checkbox',\n        required: false,\n        section: 'personalInfo'\n      },\n      dateOfMarriage: {\n        key: 'dateOfMarriage',\n        label: 'Date of Marriage',\n        type: 'date',\n        required: false,\n        conditional: { field: 'isMarried', value: true },\n        section: 'personalInfo'\n      }\n    }\n  },\n\n  // Contact Information Section\n  contactInfo: {\n    title: 'Contact Information',\n    fields: {\n      mobileNumber: {\n        key: 'mobileNumber',\n        label: 'Mobile Number',\n        type: 'tel',\n        required: true,\n        validation: { pattern: '^(\\\\+91[\\\\-\\\\s]?)?[0]?(91)?[789]\\\\d{9}$' },\n        section: 'contactInfo'\n      },\n      alternateNumbers: {\n        key: 'alternateNumbers',\n        label: 'Alternate Numbers',\n        type: 'array',\n        required: false,\n        section: 'contactInfo'\n      },\n      primaryEmailId: {\n        key: 'primaryEmailId',\n        label: 'Primary Email',\n        type: 'email',\n        required: false,\n        section: 'contactInfo'\n      },\n      alternateEmailIds: {\n        key: 'alternateEmailIds',\n        label: 'Alternate Emails',\n        type: 'array',\n        required: false,\n        section: 'contactInfo'\n      },\n      website: {\n        key: 'website',\n        label: 'Website',\n        type: 'url',\n        required: false,\n        section: 'contactInfo'\n      }\n    }\n  },\n\n  // Location Information Section\n  locationInfo: {\n    title: 'Location Information',\n    fields: {\n      workingState: {\n        key: 'workingState',\n        label: 'Working State',\n        type: 'text',\n        required: false,\n        section: 'locationInfo'\n      },\n      domesticState: {\n        key: 'domesticState',\n        label: 'Domestic State',\n        type: 'text',\n        required: false,\n        section: 'locationInfo'\n      },\n      district: {\n        key: 'district',\n        label: 'District',\n        type: 'text',\n        required: false,\n        section: 'locationInfo'\n      },\n      address: {\n        key: 'address',\n        label: 'Address',\n        type: 'textarea',\n        required: false,\n        section: 'locationInfo'\n      },\n      workingArea: {\n        key: 'workingArea',\n        label: 'Working Area',\n        type: 'text',\n        required: false,\n        section: 'locationInfo'\n      }\n    }\n  },\n\n  // Business Information Section\n  businessInfo: {\n    title: 'Business Information',\n    fields: {\n      nature: {\n        key: 'nature',\n        label: 'Nature',\n        type: 'select',\n        required: true,\n        options: Object.entries(PersonNatureLabels).map(([value, label]) => ({ value: parseInt(value), label })),\n        section: 'businessInfo'\n      },\n      transactionValue: {\n        key: 'transactionValue',\n        label: 'Transaction Value',\n        type: 'number',\n        required: false,\n        section: 'businessInfo'\n      },\n      reraRegistrationNumber: {\n        key: 'reraRegistrationNumber',\n        label: 'RERA Registration Number',\n        type: 'text',\n        required: false,\n        section: 'businessInfo'\n      },\n      workingProfiles: {\n        key: 'workingProfiles',\n        label: 'Working Profiles',\n        type: 'multiselect',\n        required: false,\n        options: Object.entries(WorkingProfileLabels).map(([value, label]) => ({ value: parseInt(value), label })),\n        section: 'businessInfo'\n      },\n      starRating: {\n        key: 'starRating',\n        label: 'Star Rating',\n        type: 'number',\n        required: false,\n        validation: { min: 1, max: 5 },\n        section: 'businessInfo'\n      },\n      source: {\n        key: 'source',\n        label: 'Source',\n        type: 'text',\n        required: false,\n        section: 'businessInfo'\n      },\n      remarks: {\n        key: 'remarks',\n        label: 'Remarks',\n        type: 'textarea',\n        required: false,\n        section: 'businessInfo'\n      }\n    }\n  },\n\n  // Associate Information Section\n  associateInfo: {\n    title: 'Associate Information',\n    fields: {\n      hasAssociate: {\n        key: 'hasAssociate',\n        label: 'Has Associate',\n        type: 'checkbox',\n        required: false,\n        section: 'associateInfo'\n      },\n      associateName: {\n        key: 'associateName',\n        label: 'Associate Name',\n        type: 'text',\n        required: false,\n        conditional: { field: 'hasAssociate', value: true },\n        section: 'associateInfo'\n      },\n      associateRelation: {\n        key: 'associateRelation',\n        label: 'Associate Relation',\n        type: 'text',\n        required: false,\n        conditional: { field: 'hasAssociate', value: true },\n        section: 'associateInfo'\n      },\n      associateMobile: {\n        key: 'associateMobile',\n        label: 'Associate Mobile',\n        type: 'tel',\n        required: false,\n        conditional: { field: 'hasAssociate', value: true },\n        section: 'associateInfo'\n      }\n    }\n  },\n\n  // Digital Presence Section\n  digitalPresence: {\n    title: 'Digital Presence',\n    fields: {\n      usingWebsite: {\n        key: 'usingWebsite',\n        label: 'Using Website',\n        type: 'checkbox',\n        required: false,\n        section: 'digitalPresence'\n      },\n      websiteLink: {\n        key: 'websiteLink',\n        label: 'Website Link',\n        type: 'url',\n        required: false,\n        conditional: { field: 'usingWebsite', value: true },\n        section: 'digitalPresence'\n      },\n      usingCRMApp: {\n        key: 'usingCRMApp',\n        label: 'Using CRM App',\n        type: 'checkbox',\n        required: false,\n        section: 'digitalPresence'\n      },\n      crmAppLink: {\n        key: 'crmAppLink',\n        label: 'CRM App Link',\n        type: 'url',\n        required: false,\n        conditional: { field: 'usingCRMApp', value: true },\n        section: 'digitalPresence'\n      }\n    }\n  },\n\n  // Company Information Section\n  companyInfo: {\n    title: 'Company Information',\n    fields: {\n      firmName: {\n        key: 'firmName',\n        label: 'Firm Name',\n        type: 'text',\n        required: false,\n        section: 'companyInfo'\n      },\n      numberOfOffices: {\n        key: 'numberOfOffices',\n        label: 'Number of Offices',\n        type: 'number',\n        required: false,\n        section: 'companyInfo'\n      },\n      numberOfBranches: {\n        key: 'numberOfBranches',\n        label: 'Number of Branches',\n        type: 'number',\n        required: false,\n        section: 'companyInfo'\n      },\n      totalEmployeeStrength: {\n        key: 'totalEmployeeStrength',\n        label: 'Total Employee Strength',\n        type: 'number',\n        required: false,\n        section: 'companyInfo'\n      }\n    }\n  },\n\n  // Authorized Person Section\n  authorizedPerson: {\n    title: 'Authorized Person',\n    fields: {\n      authorizedPersonName: {\n        key: 'authorizedPersonName',\n        label: 'Authorized Person Name',\n        type: 'text',\n        required: false,\n        section: 'authorizedPerson'\n      },\n      authorizedPersonEmail: {\n        key: 'authorizedPersonEmail',\n        label: 'Authorized Person Email',\n        type: 'email',\n        required: false,\n        section: 'authorizedPerson'\n      },\n      designation: {\n        key: 'designation',\n        label: 'Designation',\n        type: 'text',\n        required: false,\n        section: 'authorizedPerson'\n      }\n    }\n  },\n\n  // Marketing Information Section\n  marketingInfo: {\n    title: 'Marketing Information',\n    fields: {\n      marketingContact: {\n        key: 'marketingContact',\n        label: 'Marketing Contact',\n        type: 'text',\n        required: false,\n        section: 'marketingInfo'\n      },\n      marketingDesignation: {\n        key: 'marketingDesignation',\n        label: 'Marketing Designation',\n        type: 'text',\n        required: false,\n        section: 'marketingInfo'\n      },\n      placeOfPosting: {\n        key: 'placeOfPosting',\n        label: 'Place of Posting',\n        type: 'text',\n        required: false,\n        section: 'marketingInfo'\n      },\n      department: {\n        key: 'department',\n        label: 'Department',\n        type: 'text',\n        required: false,\n        section: 'marketingInfo'\n      }\n    }\n  }\n};\n\n// Get all fields as a flat array\nexport const getAllPersonFields = () => {\n  const allFields = [];\n  Object.values(PersonFieldDefinitions).forEach(section => {\n    Object.values(section.fields).forEach(field => {\n      allFields.push(field);\n    });\n  });\n  return allFields;\n};\n\n// Get required fields\nexport const getRequiredFields = () => {\n  return getAllPersonFields().filter(field => field.required);\n};\n\n// Default form data structure\nexport const getDefaultPersonData = () => ({\n  divisionId: null,\n  categoryId: null,\n  subCategoryId: null,\n  name: '',\n  mobileNumber: '',\n  nature: null,\n  gender: null,\n  alternateNumbers: [],\n  primaryEmailId: '',\n  alternateEmailIds: [],\n  website: '',\n  dateOfBirth: null,\n  isMarried: false,\n  dateOfMarriage: null,\n  workingState: '',\n  domesticState: '',\n  district: '',\n  address: '',\n  workingArea: '',\n  hasAssociate: false,\n  associateName: '',\n  associateRelation: '',\n  associateMobile: '',\n  usingWebsite: false,\n  websiteLink: '',\n  usingCRMApp: false,\n  crmAppLink: '',\n  transactionValue: null,\n  reraRegistrationNumber: '',\n  workingProfiles: [],\n  starRating: null,\n  source: '',\n  remarks: '',\n  firmName: '',\n  numberOfOffices: null,\n  numberOfBranches: null,\n  totalEmployeeStrength: null,\n  authorizedPersonName: '',\n  authorizedPersonEmail: '',\n  designation: '',\n  marketingContact: '',\n  marketingDesignation: '',\n  placeOfPosting: '',\n  department: ''\n});\n"], "mappings": "AAAA;AACA,OAAO,MAAMA,YAAY,GAAG;EAC1BC,QAAQ,EAAE,CAAC;EACXC,SAAS,EAAE,CAAC;EACZC,WAAW,EAAE,CAAC;EACdC,UAAU,EAAE;AACd,CAAC;AAED,OAAO,MAAMC,kBAAkB,GAAG;EAChC,CAACL,YAAY,CAACC,QAAQ,GAAG,UAAU;EACnC,CAACD,YAAY,CAACE,SAAS,GAAG,WAAW;EACrC,CAACF,YAAY,CAACG,WAAW,GAAG,aAAa;EACzC,CAACH,YAAY,CAACI,UAAU,GAAG;AAC7B,CAAC;AAED,OAAO,MAAME,MAAM,GAAG;EACpBC,IAAI,EAAE,CAAC;EACPC,MAAM,EAAE,CAAC;EACTC,KAAK,EAAE;AACT,CAAC;AAED,OAAO,MAAMC,YAAY,GAAG;EAC1B,CAACJ,MAAM,CAACC,IAAI,GAAG,MAAM;EACrB,CAACD,MAAM,CAACE,MAAM,GAAG,QAAQ;EACzB,CAACF,MAAM,CAACG,KAAK,GAAG;AAClB,CAAC;AAED,OAAO,MAAME,cAAc,GAAG;EAC5BV,QAAQ,EAAE,CAAC;EACXC,SAAS,EAAE,CAAC;EACZC,WAAW,EAAE;AACf,CAAC;AAED,OAAO,MAAMS,oBAAoB,GAAG;EAClC,CAACD,cAAc,CAACV,QAAQ,GAAG,UAAU;EACrC,CAACU,cAAc,CAACT,SAAS,GAAG,WAAW;EACvC,CAACS,cAAc,CAACR,WAAW,GAAG;AAChC,CAAC;;AAED;AACA,OAAO,MAAMU,sBAAsB,GAAG;EACpC;EACAC,YAAY,EAAE;IACZC,KAAK,EAAE,sBAAsB;IAC7BC,MAAM,EAAE;MACNC,IAAI,EAAE;QACJC,GAAG,EAAE,MAAM;QACXC,KAAK,EAAE,WAAW;QAClBC,IAAI,EAAE,MAAM;QACZC,QAAQ,EAAE,IAAI;QACdC,UAAU,EAAE;UAAEC,SAAS,EAAE;QAAI,CAAC;QAC9BC,OAAO,EAAE;MACX,CAAC;MACDC,MAAM,EAAE;QACNP,GAAG,EAAE,QAAQ;QACbC,KAAK,EAAE,QAAQ;QACfC,IAAI,EAAE,QAAQ;QACdC,QAAQ,EAAE,KAAK;QACfK,OAAO,EAAEC,MAAM,CAACC,OAAO,CAAClB,YAAY,CAAC,CAACmB,GAAG,CAAC,CAAC,CAACC,KAAK,EAAEX,KAAK,CAAC,MAAM;UAAEW,KAAK,EAAEC,QAAQ,CAACD,KAAK,CAAC;UAAEX;QAAM,CAAC,CAAC,CAAC;QAClGK,OAAO,EAAE;MACX,CAAC;MACDQ,WAAW,EAAE;QACXd,GAAG,EAAE,aAAa;QAClBC,KAAK,EAAE,eAAe;QACtBC,IAAI,EAAE,MAAM;QACZC,QAAQ,EAAE,KAAK;QACfG,OAAO,EAAE;MACX,CAAC;MACDS,SAAS,EAAE;QACTf,GAAG,EAAE,WAAW;QAChBC,KAAK,EAAE,YAAY;QACnBC,IAAI,EAAE,UAAU;QAChBC,QAAQ,EAAE,KAAK;QACfG,OAAO,EAAE;MACX,CAAC;MACDU,cAAc,EAAE;QACdhB,GAAG,EAAE,gBAAgB;QACrBC,KAAK,EAAE,kBAAkB;QACzBC,IAAI,EAAE,MAAM;QACZC,QAAQ,EAAE,KAAK;QACfc,WAAW,EAAE;UAAEC,KAAK,EAAE,WAAW;UAAEN,KAAK,EAAE;QAAK,CAAC;QAChDN,OAAO,EAAE;MACX;IACF;EACF,CAAC;EAED;EACAa,WAAW,EAAE;IACXtB,KAAK,EAAE,qBAAqB;IAC5BC,MAAM,EAAE;MACNsB,YAAY,EAAE;QACZpB,GAAG,EAAE,cAAc;QACnBC,KAAK,EAAE,eAAe;QACtBC,IAAI,EAAE,KAAK;QACXC,QAAQ,EAAE,IAAI;QACdC,UAAU,EAAE;UAAEiB,OAAO,EAAE;QAA0C,CAAC;QAClEf,OAAO,EAAE;MACX,CAAC;MACDgB,gBAAgB,EAAE;QAChBtB,GAAG,EAAE,kBAAkB;QACvBC,KAAK,EAAE,mBAAmB;QAC1BC,IAAI,EAAE,OAAO;QACbC,QAAQ,EAAE,KAAK;QACfG,OAAO,EAAE;MACX,CAAC;MACDiB,cAAc,EAAE;QACdvB,GAAG,EAAE,gBAAgB;QACrBC,KAAK,EAAE,eAAe;QACtBC,IAAI,EAAE,OAAO;QACbC,QAAQ,EAAE,KAAK;QACfG,OAAO,EAAE;MACX,CAAC;MACDkB,iBAAiB,EAAE;QACjBxB,GAAG,EAAE,mBAAmB;QACxBC,KAAK,EAAE,kBAAkB;QACzBC,IAAI,EAAE,OAAO;QACbC,QAAQ,EAAE,KAAK;QACfG,OAAO,EAAE;MACX,CAAC;MACDmB,OAAO,EAAE;QACPzB,GAAG,EAAE,SAAS;QACdC,KAAK,EAAE,SAAS;QAChBC,IAAI,EAAE,KAAK;QACXC,QAAQ,EAAE,KAAK;QACfG,OAAO,EAAE;MACX;IACF;EACF,CAAC;EAED;EACAoB,YAAY,EAAE;IACZ7B,KAAK,EAAE,sBAAsB;IAC7BC,MAAM,EAAE;MACN6B,YAAY,EAAE;QACZ3B,GAAG,EAAE,cAAc;QACnBC,KAAK,EAAE,eAAe;QACtBC,IAAI,EAAE,MAAM;QACZC,QAAQ,EAAE,KAAK;QACfG,OAAO,EAAE;MACX,CAAC;MACDsB,aAAa,EAAE;QACb5B,GAAG,EAAE,eAAe;QACpBC,KAAK,EAAE,gBAAgB;QACvBC,IAAI,EAAE,MAAM;QACZC,QAAQ,EAAE,KAAK;QACfG,OAAO,EAAE;MACX,CAAC;MACDuB,QAAQ,EAAE;QACR7B,GAAG,EAAE,UAAU;QACfC,KAAK,EAAE,UAAU;QACjBC,IAAI,EAAE,MAAM;QACZC,QAAQ,EAAE,KAAK;QACfG,OAAO,EAAE;MACX,CAAC;MACDwB,OAAO,EAAE;QACP9B,GAAG,EAAE,SAAS;QACdC,KAAK,EAAE,SAAS;QAChBC,IAAI,EAAE,UAAU;QAChBC,QAAQ,EAAE,KAAK;QACfG,OAAO,EAAE;MACX,CAAC;MACDyB,WAAW,EAAE;QACX/B,GAAG,EAAE,aAAa;QAClBC,KAAK,EAAE,cAAc;QACrBC,IAAI,EAAE,MAAM;QACZC,QAAQ,EAAE,KAAK;QACfG,OAAO,EAAE;MACX;IACF;EACF,CAAC;EAED;EACA0B,YAAY,EAAE;IACZnC,KAAK,EAAE,sBAAsB;IAC7BC,MAAM,EAAE;MACNmC,MAAM,EAAE;QACNjC,GAAG,EAAE,QAAQ;QACbC,KAAK,EAAE,QAAQ;QACfC,IAAI,EAAE,QAAQ;QACdC,QAAQ,EAAE,IAAI;QACdK,OAAO,EAAEC,MAAM,CAACC,OAAO,CAACvB,kBAAkB,CAAC,CAACwB,GAAG,CAAC,CAAC,CAACC,KAAK,EAAEX,KAAK,CAAC,MAAM;UAAEW,KAAK,EAAEC,QAAQ,CAACD,KAAK,CAAC;UAAEX;QAAM,CAAC,CAAC,CAAC;QACxGK,OAAO,EAAE;MACX,CAAC;MACD4B,gBAAgB,EAAE;QAChBlC,GAAG,EAAE,kBAAkB;QACvBC,KAAK,EAAE,mBAAmB;QAC1BC,IAAI,EAAE,QAAQ;QACdC,QAAQ,EAAE,KAAK;QACfG,OAAO,EAAE;MACX,CAAC;MACD6B,sBAAsB,EAAE;QACtBnC,GAAG,EAAE,wBAAwB;QAC7BC,KAAK,EAAE,0BAA0B;QACjCC,IAAI,EAAE,MAAM;QACZC,QAAQ,EAAE,KAAK;QACfG,OAAO,EAAE;MACX,CAAC;MACD8B,eAAe,EAAE;QACfpC,GAAG,EAAE,iBAAiB;QACtBC,KAAK,EAAE,kBAAkB;QACzBC,IAAI,EAAE,aAAa;QACnBC,QAAQ,EAAE,KAAK;QACfK,OAAO,EAAEC,MAAM,CAACC,OAAO,CAAChB,oBAAoB,CAAC,CAACiB,GAAG,CAAC,CAAC,CAACC,KAAK,EAAEX,KAAK,CAAC,MAAM;UAAEW,KAAK,EAAEC,QAAQ,CAACD,KAAK,CAAC;UAAEX;QAAM,CAAC,CAAC,CAAC;QAC1GK,OAAO,EAAE;MACX,CAAC;MACD+B,UAAU,EAAE;QACVrC,GAAG,EAAE,YAAY;QACjBC,KAAK,EAAE,aAAa;QACpBC,IAAI,EAAE,QAAQ;QACdC,QAAQ,EAAE,KAAK;QACfC,UAAU,EAAE;UAAEkC,GAAG,EAAE,CAAC;UAAEC,GAAG,EAAE;QAAE,CAAC;QAC9BjC,OAAO,EAAE;MACX,CAAC;MACDkC,MAAM,EAAE;QACNxC,GAAG,EAAE,QAAQ;QACbC,KAAK,EAAE,QAAQ;QACfC,IAAI,EAAE,MAAM;QACZC,QAAQ,EAAE,KAAK;QACfG,OAAO,EAAE;MACX,CAAC;MACDmC,OAAO,EAAE;QACPzC,GAAG,EAAE,SAAS;QACdC,KAAK,EAAE,SAAS;QAChBC,IAAI,EAAE,UAAU;QAChBC,QAAQ,EAAE,KAAK;QACfG,OAAO,EAAE;MACX;IACF;EACF,CAAC;EAED;EACAoC,aAAa,EAAE;IACb7C,KAAK,EAAE,uBAAuB;IAC9BC,MAAM,EAAE;MACN6C,YAAY,EAAE;QACZ3C,GAAG,EAAE,cAAc;QACnBC,KAAK,EAAE,eAAe;QACtBC,IAAI,EAAE,UAAU;QAChBC,QAAQ,EAAE,KAAK;QACfG,OAAO,EAAE;MACX,CAAC;MACDsC,aAAa,EAAE;QACb5C,GAAG,EAAE,eAAe;QACpBC,KAAK,EAAE,gBAAgB;QACvBC,IAAI,EAAE,MAAM;QACZC,QAAQ,EAAE,KAAK;QACfc,WAAW,EAAE;UAAEC,KAAK,EAAE,cAAc;UAAEN,KAAK,EAAE;QAAK,CAAC;QACnDN,OAAO,EAAE;MACX,CAAC;MACDuC,iBAAiB,EAAE;QACjB7C,GAAG,EAAE,mBAAmB;QACxBC,KAAK,EAAE,oBAAoB;QAC3BC,IAAI,EAAE,MAAM;QACZC,QAAQ,EAAE,KAAK;QACfc,WAAW,EAAE;UAAEC,KAAK,EAAE,cAAc;UAAEN,KAAK,EAAE;QAAK,CAAC;QACnDN,OAAO,EAAE;MACX,CAAC;MACDwC,eAAe,EAAE;QACf9C,GAAG,EAAE,iBAAiB;QACtBC,KAAK,EAAE,kBAAkB;QACzBC,IAAI,EAAE,KAAK;QACXC,QAAQ,EAAE,KAAK;QACfc,WAAW,EAAE;UAAEC,KAAK,EAAE,cAAc;UAAEN,KAAK,EAAE;QAAK,CAAC;QACnDN,OAAO,EAAE;MACX;IACF;EACF,CAAC;EAED;EACAyC,eAAe,EAAE;IACflD,KAAK,EAAE,kBAAkB;IACzBC,MAAM,EAAE;MACNkD,YAAY,EAAE;QACZhD,GAAG,EAAE,cAAc;QACnBC,KAAK,EAAE,eAAe;QACtBC,IAAI,EAAE,UAAU;QAChBC,QAAQ,EAAE,KAAK;QACfG,OAAO,EAAE;MACX,CAAC;MACD2C,WAAW,EAAE;QACXjD,GAAG,EAAE,aAAa;QAClBC,KAAK,EAAE,cAAc;QACrBC,IAAI,EAAE,KAAK;QACXC,QAAQ,EAAE,KAAK;QACfc,WAAW,EAAE;UAAEC,KAAK,EAAE,cAAc;UAAEN,KAAK,EAAE;QAAK,CAAC;QACnDN,OAAO,EAAE;MACX,CAAC;MACD4C,WAAW,EAAE;QACXlD,GAAG,EAAE,aAAa;QAClBC,KAAK,EAAE,eAAe;QACtBC,IAAI,EAAE,UAAU;QAChBC,QAAQ,EAAE,KAAK;QACfG,OAAO,EAAE;MACX,CAAC;MACD6C,UAAU,EAAE;QACVnD,GAAG,EAAE,YAAY;QACjBC,KAAK,EAAE,cAAc;QACrBC,IAAI,EAAE,KAAK;QACXC,QAAQ,EAAE,KAAK;QACfc,WAAW,EAAE;UAAEC,KAAK,EAAE,aAAa;UAAEN,KAAK,EAAE;QAAK,CAAC;QAClDN,OAAO,EAAE;MACX;IACF;EACF,CAAC;EAED;EACA8C,WAAW,EAAE;IACXvD,KAAK,EAAE,qBAAqB;IAC5BC,MAAM,EAAE;MACNuD,QAAQ,EAAE;QACRrD,GAAG,EAAE,UAAU;QACfC,KAAK,EAAE,WAAW;QAClBC,IAAI,EAAE,MAAM;QACZC,QAAQ,EAAE,KAAK;QACfG,OAAO,EAAE;MACX,CAAC;MACDgD,eAAe,EAAE;QACftD,GAAG,EAAE,iBAAiB;QACtBC,KAAK,EAAE,mBAAmB;QAC1BC,IAAI,EAAE,QAAQ;QACdC,QAAQ,EAAE,KAAK;QACfG,OAAO,EAAE;MACX,CAAC;MACDiD,gBAAgB,EAAE;QAChBvD,GAAG,EAAE,kBAAkB;QACvBC,KAAK,EAAE,oBAAoB;QAC3BC,IAAI,EAAE,QAAQ;QACdC,QAAQ,EAAE,KAAK;QACfG,OAAO,EAAE;MACX,CAAC;MACDkD,qBAAqB,EAAE;QACrBxD,GAAG,EAAE,uBAAuB;QAC5BC,KAAK,EAAE,yBAAyB;QAChCC,IAAI,EAAE,QAAQ;QACdC,QAAQ,EAAE,KAAK;QACfG,OAAO,EAAE;MACX;IACF;EACF,CAAC;EAED;EACAmD,gBAAgB,EAAE;IAChB5D,KAAK,EAAE,mBAAmB;IAC1BC,MAAM,EAAE;MACN4D,oBAAoB,EAAE;QACpB1D,GAAG,EAAE,sBAAsB;QAC3BC,KAAK,EAAE,wBAAwB;QAC/BC,IAAI,EAAE,MAAM;QACZC,QAAQ,EAAE,KAAK;QACfG,OAAO,EAAE;MACX,CAAC;MACDqD,qBAAqB,EAAE;QACrB3D,GAAG,EAAE,uBAAuB;QAC5BC,KAAK,EAAE,yBAAyB;QAChCC,IAAI,EAAE,OAAO;QACbC,QAAQ,EAAE,KAAK;QACfG,OAAO,EAAE;MACX,CAAC;MACDsD,WAAW,EAAE;QACX5D,GAAG,EAAE,aAAa;QAClBC,KAAK,EAAE,aAAa;QACpBC,IAAI,EAAE,MAAM;QACZC,QAAQ,EAAE,KAAK;QACfG,OAAO,EAAE;MACX;IACF;EACF,CAAC;EAED;EACAuD,aAAa,EAAE;IACbhE,KAAK,EAAE,uBAAuB;IAC9BC,MAAM,EAAE;MACNgE,gBAAgB,EAAE;QAChB9D,GAAG,EAAE,kBAAkB;QACvBC,KAAK,EAAE,mBAAmB;QAC1BC,IAAI,EAAE,MAAM;QACZC,QAAQ,EAAE,KAAK;QACfG,OAAO,EAAE;MACX,CAAC;MACDyD,oBAAoB,EAAE;QACpB/D,GAAG,EAAE,sBAAsB;QAC3BC,KAAK,EAAE,uBAAuB;QAC9BC,IAAI,EAAE,MAAM;QACZC,QAAQ,EAAE,KAAK;QACfG,OAAO,EAAE;MACX,CAAC;MACD0D,cAAc,EAAE;QACdhE,GAAG,EAAE,gBAAgB;QACrBC,KAAK,EAAE,kBAAkB;QACzBC,IAAI,EAAE,MAAM;QACZC,QAAQ,EAAE,KAAK;QACfG,OAAO,EAAE;MACX,CAAC;MACD2D,UAAU,EAAE;QACVjE,GAAG,EAAE,YAAY;QACjBC,KAAK,EAAE,YAAY;QACnBC,IAAI,EAAE,MAAM;QACZC,QAAQ,EAAE,KAAK;QACfG,OAAO,EAAE;MACX;IACF;EACF;AACF,CAAC;;AAED;AACA,OAAO,MAAM4D,kBAAkB,GAAGA,CAAA,KAAM;EACtC,MAAMC,SAAS,GAAG,EAAE;EACpB1D,MAAM,CAAC2D,MAAM,CAACzE,sBAAsB,CAAC,CAAC0E,OAAO,CAAC/D,OAAO,IAAI;IACvDG,MAAM,CAAC2D,MAAM,CAAC9D,OAAO,CAACR,MAAM,CAAC,CAACuE,OAAO,CAACnD,KAAK,IAAI;MAC7CiD,SAAS,CAACG,IAAI,CAACpD,KAAK,CAAC;IACvB,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,OAAOiD,SAAS;AAClB,CAAC;;AAED;AACA,OAAO,MAAMI,iBAAiB,GAAGA,CAAA,KAAM;EACrC,OAAOL,kBAAkB,CAAC,CAAC,CAACM,MAAM,CAACtD,KAAK,IAAIA,KAAK,CAACf,QAAQ,CAAC;AAC7D,CAAC;;AAED;AACA,OAAO,MAAMsE,oBAAoB,GAAGA,CAAA,MAAO;EACzCC,UAAU,EAAE,IAAI;EAChBC,UAAU,EAAE,IAAI;EAChBC,aAAa,EAAE,IAAI;EACnB7E,IAAI,EAAE,EAAE;EACRqB,YAAY,EAAE,EAAE;EAChBa,MAAM,EAAE,IAAI;EACZ1B,MAAM,EAAE,IAAI;EACZe,gBAAgB,EAAE,EAAE;EACpBC,cAAc,EAAE,EAAE;EAClBC,iBAAiB,EAAE,EAAE;EACrBC,OAAO,EAAE,EAAE;EACXX,WAAW,EAAE,IAAI;EACjBC,SAAS,EAAE,KAAK;EAChBC,cAAc,EAAE,IAAI;EACpBW,YAAY,EAAE,EAAE;EAChBC,aAAa,EAAE,EAAE;EACjBC,QAAQ,EAAE,EAAE;EACZC,OAAO,EAAE,EAAE;EACXC,WAAW,EAAE,EAAE;EACfY,YAAY,EAAE,KAAK;EACnBC,aAAa,EAAE,EAAE;EACjBC,iBAAiB,EAAE,EAAE;EACrBC,eAAe,EAAE,EAAE;EACnBE,YAAY,EAAE,KAAK;EACnBC,WAAW,EAAE,EAAE;EACfC,WAAW,EAAE,KAAK;EAClBC,UAAU,EAAE,EAAE;EACdjB,gBAAgB,EAAE,IAAI;EACtBC,sBAAsB,EAAE,EAAE;EAC1BC,eAAe,EAAE,EAAE;EACnBC,UAAU,EAAE,IAAI;EAChBG,MAAM,EAAE,EAAE;EACVC,OAAO,EAAE,EAAE;EACXY,QAAQ,EAAE,EAAE;EACZC,eAAe,EAAE,IAAI;EACrBC,gBAAgB,EAAE,IAAI;EACtBC,qBAAqB,EAAE,IAAI;EAC3BE,oBAAoB,EAAE,EAAE;EACxBC,qBAAqB,EAAE,EAAE;EACzBC,WAAW,EAAE,EAAE;EACfE,gBAAgB,EAAE,EAAE;EACpBC,oBAAoB,EAAE,EAAE;EACxBC,cAAc,EAAE,EAAE;EAClBC,UAAU,EAAE;AACd,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}