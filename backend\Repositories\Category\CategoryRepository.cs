using Microsoft.EntityFrameworkCore;
using CrmApi.Data;
using CrmApi.Models.Category;

namespace CrmApi.Repositories.Category
{
    public class CategoryRepository : ICategoryRepository
    {
        private readonly CrmDbContext _context;

        public CategoryRepository(CrmDbContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<Models.Category.Category>> GetAllAsync()
        {
            return await _context.Categories.ToListAsync();
        }

        public async Task<IEnumerable<Models.Category.Category>> GetAllWithRelationsAsync()
        {
            return await _context.Categories
                .Include(c => c.Division)
                .Include(c => c.SubCategories)
                .ToListAsync();
        }

        public async Task<Models.Category.Category?> GetByIdAsync(int id)
        {
            return await _context.Categories.FindAsync(id);
        }

        public async Task<Models.Category.Category?> GetByIdWithRelationsAsync(int id)
        {
            return await _context.Categories
                .Include(c => c.Division)
                .Include(c => c.SubCategories)
                .FirstOrDefaultAsync(c => c.Id == id);
        }

        public async Task<IEnumerable<Models.Category.Category>> GetByDivisionIdAsync(int divisionId)
        {
            return await _context.Categories
                .Where(c => c.DivisionId == divisionId)
                .Include(c => c.SubCategories)
                .ToListAsync();
        }

        public async Task<Models.Category.Category> CreateAsync(Models.Category.Category category)
        {
            category.CreatedAt = DateTime.UtcNow;
            category.UpdatedAt = DateTime.UtcNow;
            
            _context.Categories.Add(category);
            await _context.SaveChangesAsync();
            return category;
        }

        public async Task<Models.Category.Category> UpdateAsync(Models.Category.Category category)
        {
            category.UpdatedAt = DateTime.UtcNow;
            _context.Entry(category).State = EntityState.Modified;
            await _context.SaveChangesAsync();
            return category;
        }

        public async Task<bool> DeleteAsync(int id)
        {
            var category = await _context.Categories.FindAsync(id);
            if (category == null)
                return false;

            _context.Categories.Remove(category);
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> ExistsAsync(int id)
        {
            return await _context.Categories.AnyAsync(c => c.Id == id);
        }

        public async Task<bool> NameExistsInDivisionAsync(string name, int divisionId, int? excludeId = null)
        {
            var query = _context.Categories.Where(c => c.Name == name && c.DivisionId == divisionId);
            if (excludeId.HasValue)
                query = query.Where(c => c.Id != excludeId.Value);
            
            return await query.AnyAsync();
        }
    }
}
