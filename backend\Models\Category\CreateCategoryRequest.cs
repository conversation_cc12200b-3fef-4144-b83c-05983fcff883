using System.ComponentModel.DataAnnotations;

namespace CrmApi.Models.Category
{
    public class CreateCategoryRequest
    {
        [Required(ErrorMessage = "Category name is required")]
        [StringLength(255, ErrorMessage = "Category name cannot exceed 255 characters")]
        public string Name { get; set; } = string.Empty;

        [Required(ErrorMessage = "Division ID is required")]
        [Range(1, int.MaxValue, ErrorMessage = "Division ID must be a positive number")]
        public int DivisionId { get; set; }
    }
}
