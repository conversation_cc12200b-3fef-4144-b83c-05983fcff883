import React, { useState, useEffect } from 'react';
import axios from 'axios';
import Pagination from './Pagination';

const API_BASE_URL = 'http://localhost:5000/api';

const DivisionSetup = () => {
  const [divisions, setDivisions] = useState([]);
  const [newDivision, setNewDivision] = useState('');
  const [loading, setLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);

  useEffect(() => {
    fetchDivisions();
  }, []);

  const fetchDivisions = async () => {
    try {
      const response = await axios.get(`${API_BASE_URL}/divisions`);
      setDivisions(response.data);
    } catch (error) {
      console.error('Error fetching divisions:', error);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!newDivision.trim()) return;

    setLoading(true);
    try {
      await axios.post(`${API_BASE_URL}/divisions`, {
        name: newDivision
      });
      setNewDivision('');
      fetchDivisions();
    } catch (error) {
      console.error('Error creating division:', error);
      alert('Error creating division. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id) => {
    if (window.confirm('Are you sure you want to delete this division?')) {
      try {
        await axios.delete(`${API_BASE_URL}/divisions/${id}`);
        fetchDivisions();
      } catch (error) {
        console.error('Error deleting division:', error);
        alert('Error deleting division. Please try again.');
      }
    }
  };

  // Calculate pagination
  const totalItems = divisions.length;
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentItems = divisions.slice(startIndex, endIndex);

  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  return (
    <div>
      <h1>Division Setup</h1>
      
      <div className="card">
        <h3>Add New Division</h3>
        <form onSubmit={handleSubmit}>
          <div className="form-group">
            <label htmlFor="divisionName">Division Name</label>
            <input
              type="text"
              id="divisionName"
              className="form-control"
              value={newDivision}
              onChange={(e) => setNewDivision(e.target.value)}
              placeholder="Enter division name"
              required
            />
          </div>
          <button type="submit" className="btn btn-primary" disabled={loading}>
            {loading ? 'Adding...' : 'Add Division'}
          </button>
        </form>
      </div>

      <div className="card">
        <h3>Existing Divisions</h3>
        {divisions.length === 0 ? (
          <p>No divisions found. Add your first division above.</p>
        ) : (
          <>
            <table className="table">
              <thead>
                <tr>
                  <th>ID</th>
                  <th>Name</th>
                  <th>Created At</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {currentItems.map((division) => (
                  <tr key={division.id}>
                    <td>{division.id}</td>
                    <td>{division.name}</td>
                    <td>{new Date(division.createdAt).toLocaleDateString()}</td>
                    <td>
                      <button
                        className="btn btn-danger btn-sm"
                        onClick={() => handleDelete(division.id)}
                      >
                        Delete
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
            <Pagination
              currentPage={currentPage}
              totalItems={totalItems}
              itemsPerPage={itemsPerPage}
              onPageChange={handlePageChange}
            />
          </>
        )}
      </div>
    </div>
  );
};

export default DivisionSetup;