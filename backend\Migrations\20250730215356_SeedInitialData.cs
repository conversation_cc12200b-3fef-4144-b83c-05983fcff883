﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace CrmApi.Migrations
{
    /// <inheritdoc />
    public partial class SeedInitialData : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Insert all Indian states and union territories
            migrationBuilder.Sql(@"
                INSERT INTO states (name, code, capital, region, is_active, created_at, updated_at) VALUES
                -- States
                ('Andhra Pradesh', 'AP', 'Amaravati', 'South', 1, NOW(), NOW()),
                ('Arunachal Pradesh', 'AR', 'Itanagar', 'Northeast', 1, NOW(), NOW()),
                ('Assam', 'AS', 'Dispur', 'Northeast', 1, NOW(), NOW()),
                ('Bihar', 'BR', 'Patna', 'East', 1, NOW(), NOW()),
                ('Chhattisgarh', 'CG', 'Raipur', 'Central', 1, NOW(), NOW()),
                ('Goa', 'GA', 'Panaji', 'West', 1, NOW(), NOW()),
                ('Gujarat', 'GJ', 'Gandhinagar', 'West', 1, NOW(), NOW()),
                ('Haryana', 'HR', 'Chandigarh', 'North', 1, NOW(), NOW()),
                ('Himachal Pradesh', 'HP', 'Shimla', 'North', 1, NOW(), NOW()),
                ('Jharkhand', 'JH', 'Ranchi', 'East', 1, NOW(), NOW()),
                ('Karnataka', 'KA', 'Bengaluru', 'South', 1, NOW(), NOW()),
                ('Kerala', 'KL', 'Thiruvananthapuram', 'South', 1, NOW(), NOW()),
                ('Madhya Pradesh', 'MP', 'Bhopal', 'Central', 1, NOW(), NOW()),
                ('Maharashtra', 'MH', 'Mumbai', 'West', 1, NOW(), NOW()),
                ('Manipur', 'MN', 'Imphal', 'Northeast', 1, NOW(), NOW()),
                ('Meghalaya', 'ML', 'Shillong', 'Northeast', 1, NOW(), NOW()),
                ('Mizoram', 'MZ', 'Aizawl', 'Northeast', 1, NOW(), NOW()),
                ('Nagaland', 'NL', 'Kohima', 'Northeast', 1, NOW(), NOW()),
                ('Odisha', 'OR', 'Bhubaneswar', 'East', 1, NOW(), NOW()),
                ('Punjab', 'PB', 'Chandigarh', 'North', 1, NOW(), NOW()),
                ('Rajasthan', 'RJ', 'Jaipur', 'West', 1, NOW(), NOW()),
                ('Sikkim', 'SK', 'Gangtok', 'Northeast', 1, NOW(), NOW()),
                ('Tamil Nadu', 'TN', 'Chennai', 'South', 1, NOW(), NOW()),
                ('Telangana', 'TG', 'Hyderabad', 'South', 1, NOW(), NOW()),
                ('Tripura', 'TR', 'Agartala', 'Northeast', 1, NOW(), NOW()),
                ('Uttar Pradesh', 'UP', 'Lucknow', 'North', 1, NOW(), NOW()),
                ('Uttarakhand', 'UK', 'Dehradun', 'North', 1, NOW(), NOW()),
                ('West Bengal', 'WB', 'Kolkata', 'East', 1, NOW(), NOW()),

                -- Union Territories
                ('Andaman and Nicobar Islands', 'AN', 'Port Blair', 'Islands', 1, NOW(), NOW()),
                ('Chandigarh', 'CH', 'Chandigarh', 'North', 1, NOW(), NOW()),
                ('Dadra and Nagar Haveli and Daman and Diu', 'DH', 'Daman', 'West', 1, NOW(), NOW()),
                ('Delhi', 'DL', 'New Delhi', 'North', 1, NOW(), NOW()),
                ('Jammu and Kashmir', 'JK', 'Srinagar (Summer), Jammu (Winter)', 'North', 1, NOW(), NOW()),
                ('Ladakh', 'LA', 'Leh', 'North', 1, NOW(), NOW()),
                ('Lakshadweep', 'LD', 'Kavaratti', 'Islands', 1, NOW(), NOW()),
                ('Puducherry', 'PY', 'Puducherry', 'South', 1, NOW(), NOW());
            ");

            // Insert sample divisions
            migrationBuilder.Sql(@"
                INSERT INTO divisions (name, created_at, updated_at) VALUES
                ('Real Estate', NOW(), NOW()),
                ('Construction', NOW(), NOW()),
                ('Finance', NOW(), NOW()),
                ('Technology', NOW(), NOW()),
                ('Healthcare', NOW(), NOW()),
                ('Education', NOW(), NOW()),
                ('Retail', NOW(), NOW()),
                ('Manufacturing', NOW(), NOW()),
                ('Agriculture', NOW(), NOW()),
                ('Government', NOW(), NOW());
            ");

            // Insert sample categories
            migrationBuilder.Sql(@"
                INSERT INTO categories (name, division_id, created_at, updated_at) VALUES
                -- Real Estate categories (division_id = 1)
                ('Residential', 1, NOW(), NOW()),
                ('Commercial', 1, NOW(), NOW()),
                ('Industrial', 1, NOW(), NOW()),
                ('Retail Spaces', 1, NOW(), NOW()),
                ('Hospitality', 1, NOW(), NOW()),

                -- Construction categories (division_id = 2)
                ('Building Construction', 2, NOW(), NOW()),
                ('Infrastructure', 2, NOW(), NOW()),
                ('Interior Design', 2, NOW(), NOW()),
                ('Architecture', 2, NOW(), NOW()),
                ('Project Management', 2, NOW(), NOW()),

                -- Finance categories (division_id = 3)
                ('Banking', 3, NOW(), NOW()),
                ('Insurance', 3, NOW(), NOW()),
                ('Investment', 3, NOW(), NOW()),
                ('Loans & Credit', 3, NOW(), NOW()),
                ('Financial Advisory', 3, NOW(), NOW());
            ");

            // Insert sample subcategories
            migrationBuilder.Sql(@"
                INSERT INTO sub_categories (name, category_id, created_at, updated_at) VALUES
                -- Residential subcategories (category_id = 1)
                ('Apartments', 1, NOW(), NOW()),
                ('Villas', 1, NOW(), NOW()),
                ('Plots', 1, NOW(), NOW()),
                ('Gated Communities', 1, NOW(), NOW()),
                ('Affordable Housing', 1, NOW(), NOW()),

                -- Commercial subcategories (category_id = 2)
                ('Office Spaces', 2, NOW(), NOW()),
                ('Shopping Malls', 2, NOW(), NOW()),
                ('Warehouses', 2, NOW(), NOW()),
                ('Co-working Spaces', 2, NOW(), NOW()),
                ('Business Parks', 2, NOW(), NOW());
            ");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // Remove sample data in reverse order
            migrationBuilder.Sql("DELETE FROM sub_categories;");
            migrationBuilder.Sql("DELETE FROM categories;");
            migrationBuilder.Sql("DELETE FROM divisions;");
            migrationBuilder.Sql("DELETE FROM states;");
        }
    }
}
