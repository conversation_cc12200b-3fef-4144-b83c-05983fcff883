{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Advanced\\\\Advanced CRM filed all use\\\\frontend\\\\src\\\\components\\\\forms\\\\FormBuilder.js\",\n  _s = $RefreshSig$();\nimport { useState, useEffect } from 'react';\nimport { PersonFieldDefinitions, getAllPersonFields } from '../../constants/personConstants';\nimport formConfigService from '../../services/formConfigService';\nimport apiService from '../../services/apiService';\nimport FieldConfigModal from './FieldConfigModal';\nimport FormPreview from './FormPreview';\nimport './FormBuilder.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst FormBuilder = ({\n  onSave,\n  onCancel,\n  initialConfig = null\n}) => {\n  _s();\n  const [selectedHierarchy, setSelectedHierarchy] = useState({});\n  const [formName, setFormName] = useState('');\n  const [formDescription, setFormDescription] = useState('');\n  const [availableFields, setAvailableFields] = useState([]);\n  const [selectedFields, setSelectedFields] = useState([]);\n  const [currentSection, setCurrentSection] = useState('all');\n  const [searchTerm, setSearchTerm] = useState('');\n  const [showPreview, setShowPreview] = useState(false);\n  const [showFieldConfig, setShowFieldConfig] = useState(false);\n  const [configField, setConfigField] = useState(null);\n  const [errors, setErrors] = useState({});\n  const [saving, setSaving] = useState(false);\n  const [savedForms, setSavedForms] = useState([]);\n  const [showSavedForms, setShowSavedForms] = useState(false);\n\n  // State for dropdown data\n  const [divisions, setDivisions] = useState([]);\n  const [categories, setCategories] = useState([]);\n  const [subCategories, setSubCategories] = useState([]);\n  const [loading, setLoading] = useState({\n    divisions: false,\n    categories: false,\n    subCategories: false\n  });\n  const [existingFormInfo, setExistingFormInfo] = useState(null);\n  useEffect(() => {\n    initializeFields();\n    loadSavedForms();\n    if (initialConfig) {\n      loadInitialConfig(initialConfig);\n    }\n  }, [initialConfig]);\n  const initializeFields = () => {\n    const allFields = getAllPersonFields();\n    setAvailableFields(allFields);\n  };\n\n  // Deduplicate fields based on field key\n  const deduplicateFields = fields => {\n    const seen = new Set();\n    const deduplicated = [];\n    fields.forEach(field => {\n      if (!seen.has(field.key)) {\n        seen.add(field.key);\n        deduplicated.push(field);\n      }\n    });\n    return deduplicated;\n  };\n  const loadSavedForms = () => {\n    const forms = formConfigService.getAllFormConfigs();\n    setSavedForms(forms);\n  };\n  const loadInitialConfig = config => {\n    setFormName(config.name || '');\n    setFormDescription(config.description || '');\n\n    // Deduplicate fields when loading initial config\n    const fields = config.fields || [];\n    const deduplicatedFields = deduplicateFields(fields);\n    setSelectedFields(deduplicatedFields);\n    if (config.type === 'division' && config.associatedId) {\n      // Load division info for hierarchy selector\n      setSelectedHierarchy({\n        divisionId: config.associatedId\n      });\n    } else if (config.type === 'category' && config.associatedId) {\n      // Load category info for hierarchy selector\n      setSelectedHierarchy({\n        categoryId: config.associatedId\n      });\n    } else if (config.type === 'subcategory' && config.associatedId) {\n      // Load subcategory info for hierarchy selector\n      setSelectedHierarchy({\n        subCategoryId: config.associatedId\n      });\n    }\n\n    // If hierarchy is stored in config, use that\n    if (config.hierarchy) {\n      setSelectedHierarchy(config.hierarchy);\n    }\n  };\n\n  // Load divisions on component mount\n  useEffect(() => {\n    loadDivisions();\n  }, []);\n\n  // Validate form creation rules when hierarchy changes\n  useEffect(() => {\n    if (selectedHierarchy.categoryId) {\n      const formValidation = formConfigService.validateFormCreation(selectedHierarchy.categoryId, selectedHierarchy.subCategoryId);\n      const formInfo = formConfigService.getExistingFormInfo(selectedHierarchy.categoryId, selectedHierarchy.subCategoryId);\n      setExistingFormInfo(formInfo);\n      if (!formValidation.isValid) {\n        setErrors(prev => ({\n          ...prev,\n          formCreation: formValidation.errors.join('. ')\n        }));\n      } else {\n        setErrors(prev => {\n          const newErrors = {\n            ...prev\n          };\n          delete newErrors.formCreation;\n          return newErrors;\n        });\n      }\n    } else {\n      setExistingFormInfo(null);\n      setErrors(prev => {\n        const newErrors = {\n          ...prev\n        };\n        delete newErrors.formCreation;\n        return newErrors;\n      });\n    }\n  }, [selectedHierarchy.categoryId, selectedHierarchy.subCategoryId]);\n  const loadDivisions = async () => {\n    setLoading(prev => ({\n      ...prev,\n      divisions: true\n    }));\n    try {\n      const response = await apiService.getDivisions();\n      setDivisions(response.data || []);\n    } catch (error) {\n      console.error('Error loading divisions:', error);\n    } finally {\n      setLoading(prev => ({\n        ...prev,\n        divisions: false\n      }));\n    }\n  };\n  const loadCategories = async divisionId => {\n    if (!divisionId) {\n      setCategories([]);\n      setSubCategories([]);\n      return;\n    }\n    setLoading(prev => ({\n      ...prev,\n      categories: true\n    }));\n    try {\n      const response = await apiService.getCategoriesByDivision(divisionId);\n      setCategories(response.data || []);\n    } catch (error) {\n      console.error('Error loading categories:', error);\n      setCategories([]);\n    } finally {\n      setLoading(prev => ({\n        ...prev,\n        categories: false\n      }));\n    }\n  };\n  const loadSubCategories = async categoryId => {\n    if (!categoryId) {\n      setSubCategories([]);\n      return;\n    }\n    setLoading(prev => ({\n      ...prev,\n      subCategories: true\n    }));\n    try {\n      const response = await apiService.getSubCategoriesByCategory(categoryId);\n      setSubCategories(response.data || []);\n    } catch (error) {\n      console.error('Error loading subcategories:', error);\n      setSubCategories([]);\n    } finally {\n      setLoading(prev => ({\n        ...prev,\n        subCategories: false\n      }));\n    }\n  };\n  const handleDivisionChange = e => {\n    const divisionId = e.target.value;\n    const division = divisions.find(d => d.id === parseInt(divisionId));\n    setSelectedHierarchy({\n      divisionId: divisionId || null,\n      categoryId: null,\n      subCategoryId: null,\n      division: division || null,\n      category: null,\n      subCategory: null\n    });\n    setCategories([]);\n    setSubCategories([]);\n    if (divisionId) {\n      loadCategories(divisionId);\n    }\n  };\n  const handleCategoryChange = e => {\n    const categoryId = e.target.value;\n    const category = categories.find(c => c.id === parseInt(categoryId));\n    setSelectedHierarchy(prev => ({\n      ...prev,\n      categoryId: categoryId || null,\n      subCategoryId: null,\n      category: category || null,\n      subCategory: null\n    }));\n    setSubCategories([]);\n    if (categoryId) {\n      loadSubCategories(categoryId);\n    }\n  };\n  const handleSubCategoryChange = e => {\n    const subCategoryId = e.target.value;\n    const subCategory = subCategories.find(sc => sc.id === parseInt(subCategoryId));\n    setSelectedHierarchy(prev => ({\n      ...prev,\n      subCategoryId: subCategoryId || null,\n      subCategory: subCategory || null\n    }));\n  };\n  const handleFieldToggle = field => {\n    const isSelected = selectedFields.some(f => f.key === field.key);\n    if (isSelected) {\n      // Remove all instances of this field key (in case there are duplicates)\n      setSelectedFields(prev => prev.filter(f => f.key !== field.key));\n    } else {\n      // Add field only if it doesn't already exist\n      setSelectedFields(prev => {\n        const exists = prev.some(f => f.key === field.key);\n        if (exists) {\n          console.warn(`Field ${field.key} already exists in selectedFields, skipping duplicate`);\n          return prev;\n        }\n        return [...prev, {\n          ...field\n        }];\n      });\n    }\n  };\n  const handleFieldConfig = field => {\n    setConfigField(field);\n    setShowFieldConfig(true);\n  };\n  const handleFieldConfigSave = updatedField => {\n    setSelectedFields(prev => prev.map(f => f.key === updatedField.key ? updatedField : f));\n    setShowFieldConfig(false);\n    setConfigField(null);\n  };\n  const handleFieldMove = (fromIndex, toIndex) => {\n    const newFields = [...selectedFields];\n    const [movedField] = newFields.splice(fromIndex, 1);\n    newFields.splice(toIndex, 0, movedField);\n    setSelectedFields(newFields);\n  };\n  const handleSave = async () => {\n    const validation = validateForm();\n    if (!validation.isValid) {\n      setErrors(validation.errors);\n      return;\n    }\n    setSaving(true);\n    try {\n      // Ensure complete hierarchy information is included\n      const completeHierarchy = {\n        ...selectedHierarchy,\n        // Include division info (required)\n        divisionId: selectedHierarchy.divisionId,\n        division: selectedHierarchy.division,\n        // Include category info (required)\n        categoryId: selectedHierarchy.categoryId,\n        category: selectedHierarchy.category,\n        // Include subcategory info if selected (optional)\n        subCategoryId: selectedHierarchy.subCategoryId || null,\n        subCategory: selectedHierarchy.subCategory || null\n      };\n      const config = {\n        name: formName,\n        description: formDescription,\n        fields: selectedFields,\n        hierarchy: completeHierarchy,\n        settings: {\n          showSections: true,\n          allowConditionalFields: true,\n          validateOnChange: true\n        }\n      };\n      let savedConfig;\n      // Save form with the most specific level selected, but include complete hierarchy\n      if (selectedHierarchy.subCategoryId) {\n        savedConfig = formConfigService.saveFormConfig('subcategory', selectedHierarchy.subCategoryId, config);\n      } else if (selectedHierarchy.categoryId) {\n        savedConfig = formConfigService.saveFormConfig('category', selectedHierarchy.categoryId, config);\n      } else {\n        throw new Error('Please select both division and category');\n      }\n\n      // Reload saved forms list\n      loadSavedForms();\n      if (onSave) {\n        onSave(savedConfig);\n      } else {\n        // Show success message for standalone usage\n        alert('Form configuration saved successfully!');\n        // Reset form\n        setFormName('');\n        setFormDescription('');\n        setSelectedFields([]);\n        setSelectedHierarchy({});\n      }\n    } catch (error) {\n      console.error('Error saving form:', error);\n      setErrors({\n        general: error.message\n      });\n    } finally {\n      setSaving(false);\n    }\n  };\n  const validateForm = () => {\n    const errors = {};\n    if (!formName.trim()) {\n      errors.formName = 'Form name is required';\n    }\n\n    // Division and category are required, subcategory is optional\n    if (!selectedHierarchy.divisionId) {\n      errors.hierarchy = 'Please select a division';\n    } else if (!selectedHierarchy.categoryId) {\n      errors.hierarchy = 'Please select a category';\n    } else if (selectedHierarchy.subCategoryId && !selectedHierarchy.categoryId) {\n      errors.hierarchy = 'Cannot select subcategory without selecting category';\n    }\n\n    // Validate form creation rules\n    if (selectedHierarchy.categoryId) {\n      const formValidation = formConfigService.validateFormCreation(selectedHierarchy.categoryId, selectedHierarchy.subCategoryId);\n      if (!formValidation.isValid) {\n        errors.formCreation = formValidation.errors.join('. ');\n      }\n    }\n    if (selectedFields.length === 0) {\n      errors.fields = 'Please select at least one field';\n    }\n\n    // Check required fields\n    const requiredFields = ['name', 'mobileNumber', 'nature'];\n    const selectedFieldKeys = selectedFields.map(f => f.key);\n    const missingRequired = requiredFields.filter(rf => !selectedFieldKeys.includes(rf));\n    if (missingRequired.length > 0) {\n      errors.requiredFields = `Required fields missing: ${missingRequired.join(', ')}`;\n    }\n    return {\n      isValid: Object.keys(errors).length === 0,\n      errors\n    };\n  };\n  const getFilteredFields = () => {\n    let filtered = availableFields;\n\n    // Filter by section\n    if (currentSection !== 'all') {\n      filtered = filtered.filter(field => field.section === currentSection);\n    }\n\n    // Filter by search term\n    if (searchTerm) {\n      filtered = filtered.filter(field => field.label.toLowerCase().includes(searchTerm.toLowerCase()) || field.key.toLowerCase().includes(searchTerm.toLowerCase()));\n    }\n    return filtered;\n  };\n  const getSectionCounts = () => {\n    const counts = {\n      all: availableFields.length\n    };\n    Object.keys(PersonFieldDefinitions).forEach(sectionKey => {\n      counts[sectionKey] = availableFields.filter(f => f.section === sectionKey).length;\n    });\n    return counts;\n  };\n  const sectionCounts = getSectionCounts();\n  const filteredFields = getFilteredFields();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"form-builder\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"form-builder-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Form Builder\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 429,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-actions\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          onClick: () => setShowPreview(true),\n          className: \"btn btn-secondary\",\n          disabled: selectedFields.length === 0,\n          children: \"Preview Form\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 431,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          onClick: handleSave,\n          className: \"btn btn-primary\",\n          disabled: saving || errors.formCreation,\n          children: saving ? 'Saving...' : 'Save Form'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 439,\n          columnNumber: 11\n        }, this), onCancel && /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          onClick: onCancel,\n          className: \"btn btn-outline\",\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 448,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 430,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 428,\n      columnNumber: 7\n    }, this), errors.general && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"alert alert-error\",\n      children: errors.general\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 460,\n      columnNumber: 9\n    }, this), Object.keys(errors).length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"validation-errors\",\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        children: \"Please fix the following issues:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 466,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n        children: Object.entries(errors).map(([key, message]) => /*#__PURE__*/_jsxDEV(\"li\", {\n          children: message\n        }, key, false, {\n          fileName: _jsxFileName,\n          lineNumber: 469,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 467,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 465,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"form-builder-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"config-panel\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"config-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"section-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: [\"Saved Forms (\", savedForms.length, \")\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 481,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              className: \"btn-link\",\n              onClick: () => setShowSavedForms(!showSavedForms),\n              children: showSavedForms ? 'Hide' : 'Show'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 482,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 480,\n            columnNumber: 13\n          }, this), showSavedForms && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"saved-forms-list\",\n            children: savedForms.length === 0 ? /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"no-forms-message\",\n              children: \"No saved forms yet. Create your first form below!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 494,\n              columnNumber: 19\n            }, this) : savedForms.map(form => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"saved-form-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-item-header\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  children: form.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 499,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"form-type-badge\",\n                  children: form.type\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 500,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 498,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"form-description\",\n                children: form.description || 'No description'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 502,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-meta\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [form.summary.fieldCount, \" fields\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 504,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"\\u2022\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 505,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [\"Updated \", new Date(form.updatedAt).toLocaleDateString()]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 506,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 503,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-actions\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"button\",\n                  className: \"btn-small btn-outline\",\n                  onClick: () => loadInitialConfig(form),\n                  children: \"Load\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 509,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"button\",\n                  className: \"btn-small btn-danger\",\n                  onClick: () => {\n                    if (window.confirm('Are you sure you want to delete this form?')) {\n                      formConfigService.deleteFormConfig(form.type, form.associatedId);\n                      loadSavedForms();\n                    }\n                  },\n                  children: \"Delete\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 516,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 508,\n                columnNumber: 23\n              }, this)]\n            }, form.key, true, {\n              fileName: _jsxFileName,\n              lineNumber: 497,\n              columnNumber: 21\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 492,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 479,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"config-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Form Configuration\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 538,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"Form Name *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 541,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: formName,\n              onChange: e => setFormName(e.target.value),\n              placeholder: \"Enter form name\",\n              className: errors.formName ? 'error' : ''\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 542,\n              columnNumber: 15\n            }, this), errors.formName && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"error-message\",\n              children: errors.formName\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 549,\n              columnNumber: 35\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 540,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"Description\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 553,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n              value: formDescription,\n              onChange: e => setFormDescription(e.target.value),\n              placeholder: \"Enter form description\",\n              rows: \"3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 554,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 552,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 537,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"config-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: [\"Selected Fields (\", selectedFields.length, \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 567,\n            columnNumber: 13\n          }, this), errors.fields && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"error-message\",\n            children: errors.fields\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 568,\n            columnNumber: 31\n          }, this), errors.requiredFields && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"error-message\",\n            children: errors.requiredFields\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 569,\n            columnNumber: 39\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"selected-fields\",\n            children: selectedFields.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"empty-state\",\n              children: \"No fields selected. Choose fields from the available fields panel.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 573,\n              columnNumber: 17\n            }, this) : selectedFields.map((field, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"selected-field\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"field-info\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"field-label\",\n                  children: field.label\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 580,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"field-type\",\n                  children: field.type\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 581,\n                  columnNumber: 23\n                }, this), field.required && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"required-badge\",\n                  children: \"Required\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 582,\n                  columnNumber: 42\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 579,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"field-actions\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"button\",\n                  onClick: () => handleFieldConfig(field),\n                  className: \"btn-icon\",\n                  title: \"Configure field\",\n                  children: \"\\u2699\\uFE0F\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 585,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"button\",\n                  onClick: () => handleFieldMove(index, Math.max(0, index - 1)),\n                  className: \"btn-icon\",\n                  disabled: index === 0,\n                  title: \"Move up\",\n                  children: \"\\u2191\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 593,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"button\",\n                  onClick: () => handleFieldMove(index, Math.min(selectedFields.length - 1, index + 1)),\n                  className: \"btn-icon\",\n                  disabled: index === selectedFields.length - 1,\n                  title: \"Move down\",\n                  children: \"\\u2193\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 602,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"button\",\n                  onClick: () => handleFieldToggle(field),\n                  className: \"btn-icon remove\",\n                  title: \"Remove field\",\n                  children: \"\\u2715\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 611,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 584,\n                columnNumber: 21\n              }, this)]\n            }, field.key, true, {\n              fileName: _jsxFileName,\n              lineNumber: 578,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 571,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 566,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 477,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"fields-panel\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"fields-header\",\n          children: /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Available Fields\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 630,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 629,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hierarchy-selection\",\n          style: {\n            backgroundColor: '#f8f9fa',\n            border: '1px solid #e1e5e9',\n            borderRadius: '8px',\n            padding: '1.5rem',\n            margin: '1rem 0'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            style: {\n              margin: '0 0 1rem 0',\n              color: '#495057'\n            },\n            children: \"Associate with Division & Category (Required) / SubCategory (Optional) *\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 641,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '0.875rem',\n              color: '#6c757d',\n              marginBottom: '1rem',\n              padding: '0.75rem',\n              backgroundColor: '#f8f9fa',\n              borderRadius: '4px',\n              border: '1px solid #e9ecef'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Form Creation Rules:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 653,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              style: {\n                margin: '0.5rem 0 0 1rem',\n                paddingLeft: '1rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"If you create a form for a category, you cannot create forms for its subcategories\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 655,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Each subcategory can have only one form\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 656,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"If subcategories already have forms, you cannot create a form for the parent category\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 657,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 654,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 644,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"hierarchy-dropdowns\",\n            style: {\n              display: 'grid',\n              gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n              gap: '1rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                style: {\n                  display: 'block',\n                  marginBottom: '0.5rem',\n                  fontWeight: '500',\n                  color: '#495057'\n                },\n                children: \"Division *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 668,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: selectedHierarchy.divisionId || '',\n                onChange: handleDivisionChange,\n                disabled: loading.divisions,\n                className: errors.divisionId ? 'error' : '',\n                style: {\n                  width: '100%',\n                  padding: '0.75rem',\n                  border: '1px solid #ced4da',\n                  borderRadius: '4px',\n                  fontSize: '1rem',\n                  backgroundColor: 'white'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Select Division\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 690,\n                  columnNumber: 19\n                }, this), divisions.map(division => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: division.id,\n                  children: division.name\n                }, division.id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 692,\n                  columnNumber: 21\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 676,\n                columnNumber: 17\n              }, this), errors.divisionId && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  color: '#dc3545',\n                  fontSize: '0.875rem',\n                  marginTop: '0.25rem'\n                },\n                children: errors.divisionId\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 698,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 667,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                style: {\n                  display: 'block',\n                  marginBottom: '0.5rem',\n                  fontWeight: '500',\n                  color: '#495057'\n                },\n                children: \"Category *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 706,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: selectedHierarchy.categoryId || '',\n                onChange: handleCategoryChange,\n                disabled: !selectedHierarchy.divisionId || loading.categories,\n                className: errors.categoryId ? 'error' : '',\n                style: {\n                  width: '100%',\n                  padding: '0.75rem',\n                  border: '1px solid #ced4da',\n                  borderRadius: '4px',\n                  fontSize: '1rem',\n                  backgroundColor: 'white'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: !selectedHierarchy.divisionId ? 'Select Division first' : 'Select Category'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 728,\n                  columnNumber: 19\n                }, this), categories.map(category => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: category.id,\n                  children: category.name\n                }, category.id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 732,\n                  columnNumber: 21\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 714,\n                columnNumber: 17\n              }, this), errors.categoryId && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  color: '#dc3545',\n                  fontSize: '0.875rem',\n                  marginTop: '0.25rem'\n                },\n                children: errors.categoryId\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 738,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 705,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                style: {\n                  display: 'block',\n                  marginBottom: '0.5rem',\n                  fontWeight: '500',\n                  color: '#495057'\n                },\n                children: \"SubCategory (Optional)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 746,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: selectedHierarchy.subCategoryId || '',\n                onChange: handleSubCategoryChange,\n                disabled: !selectedHierarchy.categoryId || loading.subCategories,\n                style: {\n                  width: '100%',\n                  padding: '0.75rem',\n                  border: '1px solid #ced4da',\n                  borderRadius: '4px',\n                  fontSize: '1rem',\n                  backgroundColor: 'white'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: !selectedHierarchy.categoryId ? 'Select Category first' : 'Select SubCategory (Optional)'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 767,\n                  columnNumber: 19\n                }, this), subCategories.map(subCategory => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: subCategory.id,\n                  children: subCategory.name\n                }, subCategory.id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 771,\n                  columnNumber: 21\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 754,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 745,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 661,\n            columnNumber: 13\n          }, this), errors.hierarchy && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              color: '#dc3545',\n              fontSize: '0.875rem',\n              marginTop: '0.5rem'\n            },\n            children: errors.hierarchy\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 780,\n            columnNumber: 15\n          }, this), errors.formCreation && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              color: '#dc3545',\n              fontSize: '0.875rem',\n              marginTop: '0.5rem',\n              fontWeight: 'bold'\n            },\n            children: [\"\\u26A0\\uFE0F \", errors.formCreation]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 785,\n            columnNumber: 15\n          }, this), existingFormInfo && existingFormInfo.existingForms.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginTop: '1rem',\n              padding: '0.75rem',\n              backgroundColor: '#fff3cd',\n              border: '1px solid #ffeaa7',\n              borderRadius: '4px',\n              fontSize: '0.875rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\uD83D\\uDCCB Existing Forms:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 800,\n              columnNumber: 17\n            }, this), existingFormInfo.existingForms.map((form, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginTop: '0.5rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: [form.type === 'category' ? 'Category' : 'SubCategory', \" Form:\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 803,\n                columnNumber: 21\n              }, this), \" \", form.name, form.description && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  color: '#6c757d'\n                },\n                children: form.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 804,\n                columnNumber: 42\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 802,\n              columnNumber: 19\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 792,\n            columnNumber: 15\n          }, this), existingFormInfo && existingFormInfo.subCategoriesWithForms.length > 0 && !selectedHierarchy.subCategoryId && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginTop: '1rem',\n              padding: '0.75rem',\n              backgroundColor: '#f8d7da',\n              border: '1px solid #f5c6cb',\n              borderRadius: '4px',\n              fontSize: '0.875rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u26A0\\uFE0F Warning:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 820,\n              columnNumber: 17\n            }, this), \" This category has \", existingFormInfo.subCategoriesWithForms.length, \" subcategory form(s). You cannot create a form for this category.\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 812,\n            columnNumber: 15\n          }, this), selectedHierarchy.categoryId && !errors.formCreation && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginTop: '1rem',\n              padding: '0.75rem',\n              backgroundColor: '#d4edda',\n              border: '1px solid #c3e6cb',\n              borderRadius: '4px',\n              fontSize: '0.875rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u2705 Valid Selection:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 835,\n              columnNumber: 17\n            }, this), \" You can create a form for this \", selectedHierarchy.subCategoryId ? 'subcategory' : 'category', \".\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 827,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 634,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"fields-controls\",\n          children: /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            placeholder: \"Search fields...\",\n            value: searchTerm,\n            onChange: e => setSearchTerm(e.target.value),\n            className: \"search-input\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 842,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 841,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"section-tabs\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            onClick: () => setCurrentSection('all'),\n            className: `section-tab ${currentSection === 'all' ? 'active' : ''}`,\n            children: [\"All (\", sectionCounts.all, \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 853,\n            columnNumber: 13\n          }, this), Object.entries(PersonFieldDefinitions).map(([sectionKey, section]) => /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            onClick: () => setCurrentSection(sectionKey),\n            className: `section-tab ${currentSection === sectionKey ? 'active' : ''}`,\n            children: [section.title, \" (\", sectionCounts[sectionKey], \")\"]\n          }, sectionKey, true, {\n            fileName: _jsxFileName,\n            lineNumber: 861,\n            columnNumber: 15\n          }, this))]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 852,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"fields-list\",\n          children: filteredFields.map(field => {\n            const isSelected = selectedFields.some(f => f.key === field.key);\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `field-item ${isSelected ? 'selected' : ''}`,\n              onClick: () => handleFieldToggle(field),\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"field-checkbox\",\n                children: /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"checkbox\",\n                  checked: isSelected,\n                  onChange: () => handleFieldToggle(field)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 883,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 882,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"field-details\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"field-name\",\n                  children: field.label\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 890,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"field-meta\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"field-type\",\n                    children: field.type\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 892,\n                    columnNumber: 23\n                  }, this), field.required && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"required-badge\",\n                    children: \"Required\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 893,\n                    columnNumber: 42\n                  }, this), field.conditional && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"conditional-badge\",\n                    children: \"Conditional\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 894,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 891,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 889,\n                columnNumber: 19\n              }, this)]\n            }, field.key, true, {\n              fileName: _jsxFileName,\n              lineNumber: 877,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 873,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 628,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 475,\n      columnNumber: 7\n    }, this), showFieldConfig && configField && /*#__PURE__*/_jsxDEV(FieldConfigModal, {\n      field: configField,\n      onSave: handleFieldConfigSave,\n      onCancel: () => {\n        setShowFieldConfig(false);\n        setConfigField(null);\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 906,\n      columnNumber: 9\n    }, this), showPreview && /*#__PURE__*/_jsxDEV(FormPreview, {\n      fields: selectedFields,\n      formName: formName,\n      onClose: () => setShowPreview(false)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 917,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 427,\n    columnNumber: 5\n  }, this);\n};\n_s(FormBuilder, \"0DqQGpKgpUZO5u8m04b/qhfBuMA=\");\n_c = FormBuilder;\nexport default FormBuilder;\nvar _c;\n$RefreshReg$(_c, \"FormBuilder\");", "map": {"version": 3, "names": ["useState", "useEffect", "PersonFieldDefinitions", "<PERSON><PERSON><PERSON><PERSON><PERSON>F<PERSON>s", "formConfigService", "apiService", "FieldConfigModal", "FormPreview", "jsxDEV", "_jsxDEV", "FormBuilder", "onSave", "onCancel", "initialConfig", "_s", "selectedHierarchy", "setSelectedHierarchy", "formName", "setFormName", "formDescription", "setFormDescription", "availableFields", "setAvailableFields", "<PERSON><PERSON><PERSON>s", "setSelectedFields", "currentSection", "setCurrentSection", "searchTerm", "setSearchTerm", "showPreview", "setShowPreview", "showFieldConfig", "setShowFieldConfig", "config<PERSON><PERSON>", "setConfigField", "errors", "setErrors", "saving", "setSaving", "savedForms", "setSavedForms", "showSavedForms", "setShowSavedForms", "divisions", "setDivisions", "categories", "setCategories", "subCategories", "setSubCategories", "loading", "setLoading", "existingFormInfo", "setExistingFormInfo", "initializeFields", "loadSavedForms", "loadInitialConfig", "allFields", "deduplicateFields", "fields", "seen", "Set", "deduplicated", "for<PERSON>ach", "field", "has", "key", "add", "push", "forms", "getAllFormConfigs", "config", "name", "description", "deduplicatedFields", "type", "associatedId", "divisionId", "categoryId", "subCategoryId", "hierarchy", "loadDivisions", "formValidation", "validateFormCreation", "formInfo", "getExistingFormInfo", "<PERSON><PERSON><PERSON><PERSON>", "prev", "formCreation", "join", "newErrors", "response", "getDivisions", "data", "error", "console", "loadCategories", "getCategoriesByDivision", "loadSubCategories", "getSubCategoriesByCategory", "handleDivisionChange", "e", "target", "value", "division", "find", "d", "id", "parseInt", "category", "subCategory", "handleCategoryChange", "c", "handleSubCategoryChange", "sc", "handleFieldToggle", "isSelected", "some", "f", "filter", "exists", "warn", "handleFieldConfig", "handleFieldConfigSave", "updatedField", "map", "handleFieldMove", "fromIndex", "toIndex", "new<PERSON>ields", "movedField", "splice", "handleSave", "validation", "validateForm", "completeHierarchy", "settings", "showSections", "allowConditionalFields", "validateOnChange", "savedConfig", "saveFormConfig", "Error", "alert", "general", "message", "trim", "length", "requiredFields", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "missingRequired", "rf", "includes", "Object", "keys", "getFilteredFields", "filtered", "section", "label", "toLowerCase", "getSectionCounts", "counts", "all", "sectionKey", "sectionCounts", "filteredFields", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "disabled", "entries", "form", "summary", "fieldCount", "Date", "updatedAt", "toLocaleDateString", "window", "confirm", "deleteFormConfig", "onChange", "placeholder", "rows", "index", "required", "title", "Math", "max", "min", "style", "backgroundColor", "border", "borderRadius", "padding", "margin", "color", "fontSize", "marginBottom", "paddingLeft", "display", "gridTemplateColumns", "gap", "fontWeight", "width", "marginTop", "existingForms", "subCategoriesWithForms", "checked", "conditional", "onClose", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/Advanced/Advanced CRM filed all use/frontend/src/components/forms/FormBuilder.js"], "sourcesContent": ["import { useState, useEffect } from 'react';\nimport { PersonFieldDefinitions, getAllPersonFields } from '../../constants/personConstants';\nimport formConfigService from '../../services/formConfigService';\nimport apiService from '../../services/apiService';\nimport FieldConfigModal from './FieldConfigModal';\nimport FormPreview from './FormPreview';\nimport './FormBuilder.css';\n\nconst FormBuilder = ({ onSave, onCancel, initialConfig = null }) => {\n  const [selectedHierarchy, setSelectedHierarchy] = useState({});\n  const [formName, setFormName] = useState('');\n  const [formDescription, setFormDescription] = useState('');\n  const [availableFields, setAvailableFields] = useState([]);\n  const [selectedFields, setSelectedFields] = useState([]);\n  const [currentSection, setCurrentSection] = useState('all');\n  const [searchTerm, setSearchTerm] = useState('');\n  const [showPreview, setShowPreview] = useState(false);\n  const [showFieldConfig, setShowFieldConfig] = useState(false);\n  const [configField, setConfigField] = useState(null);\n  const [errors, setErrors] = useState({});\n  const [saving, setSaving] = useState(false);\n  const [savedForms, setSavedForms] = useState([]);\n  const [showSavedForms, setShowSavedForms] = useState(false);\n\n  // State for dropdown data\n  const [divisions, setDivisions] = useState([]);\n  const [categories, setCategories] = useState([]);\n  const [subCategories, setSubCategories] = useState([]);\n  const [loading, setLoading] = useState({\n    divisions: false,\n    categories: false,\n    subCategories: false\n  });\n  const [existingFormInfo, setExistingFormInfo] = useState(null);\n\n  useEffect(() => {\n    initializeFields();\n    loadSavedForms();\n    if (initialConfig) {\n      loadInitialConfig(initialConfig);\n    }\n  }, [initialConfig]);\n\n  const initializeFields = () => {\n    const allFields = getAllPersonFields();\n    setAvailableFields(allFields);\n  };\n\n  // Deduplicate fields based on field key\n  const deduplicateFields = (fields) => {\n    const seen = new Set();\n    const deduplicated = [];\n\n    fields.forEach(field => {\n      if (!seen.has(field.key)) {\n        seen.add(field.key);\n        deduplicated.push(field);\n      }\n    });\n\n    return deduplicated;\n  };\n\n  const loadSavedForms = () => {\n    const forms = formConfigService.getAllFormConfigs();\n    setSavedForms(forms);\n  };\n\n  const loadInitialConfig = (config) => {\n    setFormName(config.name || '');\n    setFormDescription(config.description || '');\n\n    // Deduplicate fields when loading initial config\n    const fields = config.fields || [];\n    const deduplicatedFields = deduplicateFields(fields);\n    setSelectedFields(deduplicatedFields);\n    \n    if (config.type === 'division' && config.associatedId) {\n      // Load division info for hierarchy selector\n      setSelectedHierarchy({\n        divisionId: config.associatedId\n      });\n    } else if (config.type === 'category' && config.associatedId) {\n      // Load category info for hierarchy selector\n      setSelectedHierarchy({\n        categoryId: config.associatedId\n      });\n    } else if (config.type === 'subcategory' && config.associatedId) {\n      // Load subcategory info for hierarchy selector\n      setSelectedHierarchy({\n        subCategoryId: config.associatedId\n      });\n    }\n\n    // If hierarchy is stored in config, use that\n    if (config.hierarchy) {\n      setSelectedHierarchy(config.hierarchy);\n    }\n  };\n\n  // Load divisions on component mount\n  useEffect(() => {\n    loadDivisions();\n  }, []);\n\n  // Validate form creation rules when hierarchy changes\n  useEffect(() => {\n    if (selectedHierarchy.categoryId) {\n      const formValidation = formConfigService.validateFormCreation(\n        selectedHierarchy.categoryId,\n        selectedHierarchy.subCategoryId\n      );\n\n      const formInfo = formConfigService.getExistingFormInfo(\n        selectedHierarchy.categoryId,\n        selectedHierarchy.subCategoryId\n      );\n      setExistingFormInfo(formInfo);\n\n      if (!formValidation.isValid) {\n        setErrors(prev => ({\n          ...prev,\n          formCreation: formValidation.errors.join('. ')\n        }));\n      } else {\n        setErrors(prev => {\n          const newErrors = { ...prev };\n          delete newErrors.formCreation;\n          return newErrors;\n        });\n      }\n    } else {\n      setExistingFormInfo(null);\n      setErrors(prev => {\n        const newErrors = { ...prev };\n        delete newErrors.formCreation;\n        return newErrors;\n      });\n    }\n  }, [selectedHierarchy.categoryId, selectedHierarchy.subCategoryId]);\n\n  const loadDivisions = async () => {\n    setLoading(prev => ({ ...prev, divisions: true }));\n    try {\n      const response = await apiService.getDivisions();\n      setDivisions(response.data || []);\n    } catch (error) {\n      console.error('Error loading divisions:', error);\n    } finally {\n      setLoading(prev => ({ ...prev, divisions: false }));\n    }\n  };\n\n  const loadCategories = async (divisionId) => {\n    if (!divisionId) {\n      setCategories([]);\n      setSubCategories([]);\n      return;\n    }\n\n    setLoading(prev => ({ ...prev, categories: true }));\n    try {\n      const response = await apiService.getCategoriesByDivision(divisionId);\n      setCategories(response.data || []);\n    } catch (error) {\n      console.error('Error loading categories:', error);\n      setCategories([]);\n    } finally {\n      setLoading(prev => ({ ...prev, categories: false }));\n    }\n  };\n\n  const loadSubCategories = async (categoryId) => {\n    if (!categoryId) {\n      setSubCategories([]);\n      return;\n    }\n\n    setLoading(prev => ({ ...prev, subCategories: true }));\n    try {\n      const response = await apiService.getSubCategoriesByCategory(categoryId);\n      setSubCategories(response.data || []);\n    } catch (error) {\n      console.error('Error loading subcategories:', error);\n      setSubCategories([]);\n    } finally {\n      setLoading(prev => ({ ...prev, subCategories: false }));\n    }\n  };\n\n  const handleDivisionChange = (e) => {\n    const divisionId = e.target.value;\n    const division = divisions.find(d => d.id === parseInt(divisionId));\n\n    setSelectedHierarchy({\n      divisionId: divisionId || null,\n      categoryId: null,\n      subCategoryId: null,\n      division: division || null,\n      category: null,\n      subCategory: null\n    });\n\n    setCategories([]);\n    setSubCategories([]);\n\n    if (divisionId) {\n      loadCategories(divisionId);\n    }\n  };\n\n  const handleCategoryChange = (e) => {\n    const categoryId = e.target.value;\n    const category = categories.find(c => c.id === parseInt(categoryId));\n\n    setSelectedHierarchy(prev => ({\n      ...prev,\n      categoryId: categoryId || null,\n      subCategoryId: null,\n      category: category || null,\n      subCategory: null\n    }));\n\n    setSubCategories([]);\n\n    if (categoryId) {\n      loadSubCategories(categoryId);\n    }\n  };\n\n  const handleSubCategoryChange = (e) => {\n    const subCategoryId = e.target.value;\n    const subCategory = subCategories.find(sc => sc.id === parseInt(subCategoryId));\n\n    setSelectedHierarchy(prev => ({\n      ...prev,\n      subCategoryId: subCategoryId || null,\n      subCategory: subCategory || null\n    }));\n  };\n\n  const handleFieldToggle = (field) => {\n    const isSelected = selectedFields.some(f => f.key === field.key);\n\n    if (isSelected) {\n      // Remove all instances of this field key (in case there are duplicates)\n      setSelectedFields(prev => prev.filter(f => f.key !== field.key));\n    } else {\n      // Add field only if it doesn't already exist\n      setSelectedFields(prev => {\n        const exists = prev.some(f => f.key === field.key);\n        if (exists) {\n          console.warn(`Field ${field.key} already exists in selectedFields, skipping duplicate`);\n          return prev;\n        }\n        return [...prev, { ...field }];\n      });\n    }\n  };\n\n  const handleFieldConfig = (field) => {\n    setConfigField(field);\n    setShowFieldConfig(true);\n  };\n\n  const handleFieldConfigSave = (updatedField) => {\n    setSelectedFields(prev => \n      prev.map(f => f.key === updatedField.key ? updatedField : f)\n    );\n    setShowFieldConfig(false);\n    setConfigField(null);\n  };\n\n  const handleFieldMove = (fromIndex, toIndex) => {\n    const newFields = [...selectedFields];\n    const [movedField] = newFields.splice(fromIndex, 1);\n    newFields.splice(toIndex, 0, movedField);\n    setSelectedFields(newFields);\n  };\n\n  const handleSave = async () => {\n    const validation = validateForm();\n\n    if (!validation.isValid) {\n      setErrors(validation.errors);\n      return;\n    }\n\n    setSaving(true);\n    try {\n      // Ensure complete hierarchy information is included\n      const completeHierarchy = {\n        ...selectedHierarchy,\n        // Include division info (required)\n        divisionId: selectedHierarchy.divisionId,\n        division: selectedHierarchy.division,\n        // Include category info (required)\n        categoryId: selectedHierarchy.categoryId,\n        category: selectedHierarchy.category,\n        // Include subcategory info if selected (optional)\n        subCategoryId: selectedHierarchy.subCategoryId || null,\n        subCategory: selectedHierarchy.subCategory || null\n      };\n\n      const config = {\n        name: formName,\n        description: formDescription,\n        fields: selectedFields,\n        hierarchy: completeHierarchy,\n        settings: {\n          showSections: true,\n          allowConditionalFields: true,\n          validateOnChange: true\n        }\n      };\n\n      let savedConfig;\n      // Save form with the most specific level selected, but include complete hierarchy\n      if (selectedHierarchy.subCategoryId) {\n        savedConfig = formConfigService.saveFormConfig('subcategory', selectedHierarchy.subCategoryId, config);\n      } else if (selectedHierarchy.categoryId) {\n        savedConfig = formConfigService.saveFormConfig('category', selectedHierarchy.categoryId, config);\n      } else {\n        throw new Error('Please select both division and category');\n      }\n\n      // Reload saved forms list\n      loadSavedForms();\n\n      if (onSave) {\n        onSave(savedConfig);\n      } else {\n        // Show success message for standalone usage\n        alert('Form configuration saved successfully!');\n        // Reset form\n        setFormName('');\n        setFormDescription('');\n        setSelectedFields([]);\n        setSelectedHierarchy({});\n      }\n    } catch (error) {\n      console.error('Error saving form:', error);\n      setErrors({ general: error.message });\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  const validateForm = () => {\n    const errors = {};\n\n    if (!formName.trim()) {\n      errors.formName = 'Form name is required';\n    }\n\n    // Division and category are required, subcategory is optional\n    if (!selectedHierarchy.divisionId) {\n      errors.hierarchy = 'Please select a division';\n    } else if (!selectedHierarchy.categoryId) {\n      errors.hierarchy = 'Please select a category';\n    } else if (selectedHierarchy.subCategoryId && !selectedHierarchy.categoryId) {\n      errors.hierarchy = 'Cannot select subcategory without selecting category';\n    }\n\n    // Validate form creation rules\n    if (selectedHierarchy.categoryId) {\n      const formValidation = formConfigService.validateFormCreation(\n        selectedHierarchy.categoryId,\n        selectedHierarchy.subCategoryId\n      );\n\n      if (!formValidation.isValid) {\n        errors.formCreation = formValidation.errors.join('. ');\n      }\n    }\n\n    if (selectedFields.length === 0) {\n      errors.fields = 'Please select at least one field';\n    }\n\n    // Check required fields\n    const requiredFields = ['name', 'mobileNumber', 'nature'];\n    const selectedFieldKeys = selectedFields.map(f => f.key);\n    const missingRequired = requiredFields.filter(rf => !selectedFieldKeys.includes(rf));\n\n    if (missingRequired.length > 0) {\n      errors.requiredFields = `Required fields missing: ${missingRequired.join(', ')}`;\n    }\n\n    return {\n      isValid: Object.keys(errors).length === 0,\n      errors\n    };\n  };\n\n  const getFilteredFields = () => {\n    let filtered = availableFields;\n\n    // Filter by section\n    if (currentSection !== 'all') {\n      filtered = filtered.filter(field => field.section === currentSection);\n    }\n\n    // Filter by search term\n    if (searchTerm) {\n      filtered = filtered.filter(field => \n        field.label.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        field.key.toLowerCase().includes(searchTerm.toLowerCase())\n      );\n    }\n\n    return filtered;\n  };\n\n  const getSectionCounts = () => {\n    const counts = { all: availableFields.length };\n    Object.keys(PersonFieldDefinitions).forEach(sectionKey => {\n      counts[sectionKey] = availableFields.filter(f => f.section === sectionKey).length;\n    });\n    return counts;\n  };\n\n  const sectionCounts = getSectionCounts();\n  const filteredFields = getFilteredFields();\n\n  return (\n    <div className=\"form-builder\">\n      <div className=\"form-builder-header\">\n        <h2>Form Builder</h2>\n        <div className=\"header-actions\">\n          <button \n            type=\"button\" \n            onClick={() => setShowPreview(true)}\n            className=\"btn btn-secondary\"\n            disabled={selectedFields.length === 0}\n          >\n            Preview Form\n          </button>\n          <button\n            type=\"button\"\n            onClick={handleSave}\n            className=\"btn btn-primary\"\n            disabled={saving || errors.formCreation}\n          >\n            {saving ? 'Saving...' : 'Save Form'}\n          </button>\n          {onCancel && (\n            <button\n              type=\"button\"\n              onClick={onCancel}\n              className=\"btn btn-outline\"\n            >\n              Cancel\n            </button>\n          )}\n        </div>\n      </div>\n\n      {errors.general && (\n        <div className=\"alert alert-error\">{errors.general}</div>\n      )}\n\n      {/* Validation Status */}\n      {Object.keys(errors).length > 0 && (\n        <div className=\"validation-errors\">\n          <h4>Please fix the following issues:</h4>\n          <ul>\n            {Object.entries(errors).map(([key, message]) => (\n              <li key={key}>{message}</li>\n            ))}\n          </ul>\n        </div>\n      )}\n\n      <div className=\"form-builder-content\">\n        {/* Form Configuration Panel */}\n        <div className=\"config-panel\">\n          {/* Saved Forms Section */}\n          <div className=\"config-section\">\n            <div className=\"section-header\">\n              <h3>Saved Forms ({savedForms.length})</h3>\n              <button\n                type=\"button\"\n                className=\"btn-link\"\n                onClick={() => setShowSavedForms(!showSavedForms)}\n              >\n                {showSavedForms ? 'Hide' : 'Show'}\n              </button>\n            </div>\n\n            {showSavedForms && (\n              <div className=\"saved-forms-list\">\n                {savedForms.length === 0 ? (\n                  <p className=\"no-forms-message\">No saved forms yet. Create your first form below!</p>\n                ) : (\n                  savedForms.map((form) => (\n                    <div key={form.key} className=\"saved-form-item\">\n                      <div className=\"form-item-header\">\n                        <h4>{form.name}</h4>\n                        <span className=\"form-type-badge\">{form.type}</span>\n                      </div>\n                      <p className=\"form-description\">{form.description || 'No description'}</p>\n                      <div className=\"form-meta\">\n                        <span>{form.summary.fieldCount} fields</span>\n                        <span>•</span>\n                        <span>Updated {new Date(form.updatedAt).toLocaleDateString()}</span>\n                      </div>\n                      <div className=\"form-actions\">\n                        <button\n                          type=\"button\"\n                          className=\"btn-small btn-outline\"\n                          onClick={() => loadInitialConfig(form)}\n                        >\n                          Load\n                        </button>\n                        <button\n                          type=\"button\"\n                          className=\"btn-small btn-danger\"\n                          onClick={() => {\n                            if (window.confirm('Are you sure you want to delete this form?')) {\n                              formConfigService.deleteFormConfig(form.type, form.associatedId);\n                              loadSavedForms();\n                            }\n                          }}\n                        >\n                          Delete\n                        </button>\n                      </div>\n                    </div>\n                  ))\n                )}\n              </div>\n            )}\n          </div>\n\n          {/* Form Configuration Section */}\n          <div className=\"config-section\">\n            <h3>Form Configuration</h3>\n\n            <div className=\"form-group\">\n              <label>Form Name *</label>\n              <input\n                type=\"text\"\n                value={formName}\n                onChange={(e) => setFormName(e.target.value)}\n                placeholder=\"Enter form name\"\n                className={errors.formName ? 'error' : ''}\n              />\n              {errors.formName && <div className=\"error-message\">{errors.formName}</div>}\n            </div>\n\n            <div className=\"form-group\">\n              <label>Description</label>\n              <textarea\n                value={formDescription}\n                onChange={(e) => setFormDescription(e.target.value)}\n                placeholder=\"Enter form description\"\n                rows=\"3\"\n              />\n            </div>\n\n\n          </div>\n\n          {/* Selected Fields Panel */}\n          <div className=\"config-section\">\n            <h3>Selected Fields ({selectedFields.length})</h3>\n            {errors.fields && <div className=\"error-message\">{errors.fields}</div>}\n            {errors.requiredFields && <div className=\"error-message\">{errors.requiredFields}</div>}\n            \n            <div className=\"selected-fields\">\n              {selectedFields.length === 0 ? (\n                <div className=\"empty-state\">\n                  No fields selected. Choose fields from the available fields panel.\n                </div>\n              ) : (\n                selectedFields.map((field, index) => (\n                  <div key={field.key} className=\"selected-field\">\n                    <div className=\"field-info\">\n                      <span className=\"field-label\">{field.label}</span>\n                      <span className=\"field-type\">{field.type}</span>\n                      {field.required && <span className=\"required-badge\">Required</span>}\n                    </div>\n                    <div className=\"field-actions\">\n                      <button\n                        type=\"button\"\n                        onClick={() => handleFieldConfig(field)}\n                        className=\"btn-icon\"\n                        title=\"Configure field\"\n                      >\n                        ⚙️\n                      </button>\n                      <button\n                        type=\"button\"\n                        onClick={() => handleFieldMove(index, Math.max(0, index - 1))}\n                        className=\"btn-icon\"\n                        disabled={index === 0}\n                        title=\"Move up\"\n                      >\n                        ↑\n                      </button>\n                      <button\n                        type=\"button\"\n                        onClick={() => handleFieldMove(index, Math.min(selectedFields.length - 1, index + 1))}\n                        className=\"btn-icon\"\n                        disabled={index === selectedFields.length - 1}\n                        title=\"Move down\"\n                      >\n                        ↓\n                      </button>\n                      <button\n                        type=\"button\"\n                        onClick={() => handleFieldToggle(field)}\n                        className=\"btn-icon remove\"\n                        title=\"Remove field\"\n                      >\n                        ✕\n                      </button>\n                    </div>\n                  </div>\n                ))\n              )}\n            </div>\n          </div>\n        </div>\n\n        {/* Available Fields Panel */}\n        <div className=\"fields-panel\">\n          <div className=\"fields-header\">\n            <h3>Available Fields</h3>\n          </div>\n\n          {/* Division/Category/SubCategory Selection */}\n          <div className=\"hierarchy-selection\" style={{\n            backgroundColor: '#f8f9fa',\n            border: '1px solid #e1e5e9',\n            borderRadius: '8px',\n            padding: '1.5rem',\n            margin: '1rem 0'\n          }}>\n            <h4 style={{ margin: '0 0 1rem 0', color: '#495057' }}>\n              Associate with Division & Category (Required) / SubCategory (Optional) *\n            </h4>\n            <div style={{\n              fontSize: '0.875rem',\n              color: '#6c757d',\n              marginBottom: '1rem',\n              padding: '0.75rem',\n              backgroundColor: '#f8f9fa',\n              borderRadius: '4px',\n              border: '1px solid #e9ecef'\n            }}>\n              <strong>Form Creation Rules:</strong>\n              <ul style={{ margin: '0.5rem 0 0 1rem', paddingLeft: '1rem' }}>\n                <li>If you create a form for a category, you cannot create forms for its subcategories</li>\n                <li>Each subcategory can have only one form</li>\n                <li>If subcategories already have forms, you cannot create a form for the parent category</li>\n              </ul>\n            </div>\n\n            <div className=\"hierarchy-dropdowns\" style={{\n              display: 'grid',\n              gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n              gap: '1rem'\n            }}>\n              {/* Division Dropdown */}\n              <div className=\"form-group\">\n                <label style={{\n                  display: 'block',\n                  marginBottom: '0.5rem',\n                  fontWeight: '500',\n                  color: '#495057'\n                }}>\n                  Division *\n                </label>\n                <select\n                  value={selectedHierarchy.divisionId || ''}\n                  onChange={handleDivisionChange}\n                  disabled={loading.divisions}\n                  className={errors.divisionId ? 'error' : ''}\n                  style={{\n                    width: '100%',\n                    padding: '0.75rem',\n                    border: '1px solid #ced4da',\n                    borderRadius: '4px',\n                    fontSize: '1rem',\n                    backgroundColor: 'white'\n                  }}\n                >\n                  <option value=\"\">Select Division</option>\n                  {divisions.map(division => (\n                    <option key={division.id} value={division.id}>\n                      {division.name}\n                    </option>\n                  ))}\n                </select>\n                {errors.divisionId && (\n                  <div style={{ color: '#dc3545', fontSize: '0.875rem', marginTop: '0.25rem' }}>\n                    {errors.divisionId}\n                  </div>\n                )}\n              </div>\n\n              {/* Category Dropdown */}\n              <div className=\"form-group\">\n                <label style={{\n                  display: 'block',\n                  marginBottom: '0.5rem',\n                  fontWeight: '500',\n                  color: '#495057'\n                }}>\n                  Category *\n                </label>\n                <select\n                  value={selectedHierarchy.categoryId || ''}\n                  onChange={handleCategoryChange}\n                  disabled={!selectedHierarchy.divisionId || loading.categories}\n                  className={errors.categoryId ? 'error' : ''}\n                  style={{\n                    width: '100%',\n                    padding: '0.75rem',\n                    border: '1px solid #ced4da',\n                    borderRadius: '4px',\n                    fontSize: '1rem',\n                    backgroundColor: 'white'\n                  }}\n                >\n                  <option value=\"\">\n                    {!selectedHierarchy.divisionId ? 'Select Division first' : 'Select Category'}\n                  </option>\n                  {categories.map(category => (\n                    <option key={category.id} value={category.id}>\n                      {category.name}\n                    </option>\n                  ))}\n                </select>\n                {errors.categoryId && (\n                  <div style={{ color: '#dc3545', fontSize: '0.875rem', marginTop: '0.25rem' }}>\n                    {errors.categoryId}\n                  </div>\n                )}\n              </div>\n\n              {/* SubCategory Dropdown */}\n              <div className=\"form-group\">\n                <label style={{\n                  display: 'block',\n                  marginBottom: '0.5rem',\n                  fontWeight: '500',\n                  color: '#495057'\n                }}>\n                  SubCategory (Optional)\n                </label>\n                <select\n                  value={selectedHierarchy.subCategoryId || ''}\n                  onChange={handleSubCategoryChange}\n                  disabled={!selectedHierarchy.categoryId || loading.subCategories}\n                  style={{\n                    width: '100%',\n                    padding: '0.75rem',\n                    border: '1px solid #ced4da',\n                    borderRadius: '4px',\n                    fontSize: '1rem',\n                    backgroundColor: 'white'\n                  }}\n                >\n                  <option value=\"\">\n                    {!selectedHierarchy.categoryId ? 'Select Category first' : 'Select SubCategory (Optional)'}\n                  </option>\n                  {subCategories.map(subCategory => (\n                    <option key={subCategory.id} value={subCategory.id}>\n                      {subCategory.name}\n                    </option>\n                  ))}\n                </select>\n              </div>\n            </div>\n\n            {errors.hierarchy && (\n              <div style={{ color: '#dc3545', fontSize: '0.875rem', marginTop: '0.5rem' }}>\n                {errors.hierarchy}\n              </div>\n            )}\n            {errors.formCreation && (\n              <div style={{ color: '#dc3545', fontSize: '0.875rem', marginTop: '0.5rem', fontWeight: 'bold' }}>\n                ⚠️ {errors.formCreation}\n              </div>\n            )}\n\n            {/* Existing Form Information */}\n            {existingFormInfo && existingFormInfo.existingForms.length > 0 && (\n              <div style={{\n                marginTop: '1rem',\n                padding: '0.75rem',\n                backgroundColor: '#fff3cd',\n                border: '1px solid #ffeaa7',\n                borderRadius: '4px',\n                fontSize: '0.875rem'\n              }}>\n                <strong>📋 Existing Forms:</strong>\n                {existingFormInfo.existingForms.map((form, index) => (\n                  <div key={index} style={{ marginTop: '0.5rem' }}>\n                    <strong>{form.type === 'category' ? 'Category' : 'SubCategory'} Form:</strong> {form.name}\n                    {form.description && <div style={{ color: '#6c757d' }}>{form.description}</div>}\n                  </div>\n                ))}\n              </div>\n            )}\n\n            {/* Subcategories with Forms Warning */}\n            {existingFormInfo && existingFormInfo.subCategoriesWithForms.length > 0 && !selectedHierarchy.subCategoryId && (\n              <div style={{\n                marginTop: '1rem',\n                padding: '0.75rem',\n                backgroundColor: '#f8d7da',\n                border: '1px solid #f5c6cb',\n                borderRadius: '4px',\n                fontSize: '0.875rem'\n              }}>\n                <strong>⚠️ Warning:</strong> This category has {existingFormInfo.subCategoriesWithForms.length} subcategory form(s).\n                You cannot create a form for this category.\n              </div>\n            )}\n\n            {/* Success Message for Valid Selection */}\n            {selectedHierarchy.categoryId && !errors.formCreation && (\n              <div style={{\n                marginTop: '1rem',\n                padding: '0.75rem',\n                backgroundColor: '#d4edda',\n                border: '1px solid #c3e6cb',\n                borderRadius: '4px',\n                fontSize: '0.875rem'\n              }}>\n                <strong>✅ Valid Selection:</strong> You can create a form for this {selectedHierarchy.subCategoryId ? 'subcategory' : 'category'}.\n              </div>\n            )}\n          </div>\n\n          {/* Search and Filter Controls */}\n          <div className=\"fields-controls\">\n            <input\n              type=\"text\"\n              placeholder=\"Search fields...\"\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              className=\"search-input\"\n            />\n          </div>\n\n          {/* Section Tabs */}\n          <div className=\"section-tabs\">\n            <button\n              type=\"button\"\n              onClick={() => setCurrentSection('all')}\n              className={`section-tab ${currentSection === 'all' ? 'active' : ''}`}\n            >\n              All ({sectionCounts.all})\n            </button>\n            {Object.entries(PersonFieldDefinitions).map(([sectionKey, section]) => (\n              <button\n                key={sectionKey}\n                type=\"button\"\n                onClick={() => setCurrentSection(sectionKey)}\n                className={`section-tab ${currentSection === sectionKey ? 'active' : ''}`}\n              >\n                {section.title} ({sectionCounts[sectionKey]})\n              </button>\n            ))}\n          </div>\n\n          {/* Fields List */}\n          <div className=\"fields-list\">\n            {filteredFields.map(field => {\n              const isSelected = selectedFields.some(f => f.key === field.key);\n              return (\n                <div\n                  key={field.key}\n                  className={`field-item ${isSelected ? 'selected' : ''}`}\n                  onClick={() => handleFieldToggle(field)}\n                >\n                  <div className=\"field-checkbox\">\n                    <input\n                      type=\"checkbox\"\n                      checked={isSelected}\n                      onChange={() => handleFieldToggle(field)}\n                    />\n                  </div>\n                  <div className=\"field-details\">\n                    <div className=\"field-name\">{field.label}</div>\n                    <div className=\"field-meta\">\n                      <span className=\"field-type\">{field.type}</span>\n                      {field.required && <span className=\"required-badge\">Required</span>}\n                      {field.conditional && <span className=\"conditional-badge\">Conditional</span>}\n                    </div>\n                  </div>\n                </div>\n              );\n            })}\n          </div>\n        </div>\n      </div>\n\n      {/* Modals */}\n      {showFieldConfig && configField && (\n        <FieldConfigModal\n          field={configField}\n          onSave={handleFieldConfigSave}\n          onCancel={() => {\n            setShowFieldConfig(false);\n            setConfigField(null);\n          }}\n        />\n      )}\n\n      {showPreview && (\n        <FormPreview\n          fields={selectedFields}\n          formName={formName}\n          onClose={() => setShowPreview(false)}\n        />\n      )}\n    </div>\n  );\n};\n\nexport default FormBuilder;\n"], "mappings": ";;AAAA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC3C,SAASC,sBAAsB,EAAEC,kBAAkB,QAAQ,iCAAiC;AAC5F,OAAOC,iBAAiB,MAAM,kCAAkC;AAChE,OAAOC,UAAU,MAAM,2BAA2B;AAClD,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAO,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3B,MAAMC,WAAW,GAAGA,CAAC;EAAEC,MAAM;EAAEC,QAAQ;EAAEC,aAAa,GAAG;AAAK,CAAC,KAAK;EAAAC,EAAA;EAClE,MAAM,CAACC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGhB,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC9D,MAAM,CAACiB,QAAQ,EAAEC,WAAW,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACmB,eAAe,EAAEC,kBAAkB,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACqB,eAAe,EAAEC,kBAAkB,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACuB,cAAc,EAAEC,iBAAiB,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACyB,cAAc,EAAEC,iBAAiB,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAAC2B,UAAU,EAAEC,aAAa,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC6B,WAAW,EAAEC,cAAc,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC+B,eAAe,EAAEC,kBAAkB,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACiC,WAAW,EAAEC,cAAc,CAAC,GAAGlC,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACmC,MAAM,EAAEC,SAAS,CAAC,GAAGpC,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxC,MAAM,CAACqC,MAAM,EAAEC,SAAS,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAACuC,UAAU,EAAEC,aAAa,CAAC,GAAGxC,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACyC,cAAc,EAAEC,iBAAiB,CAAC,GAAG1C,QAAQ,CAAC,KAAK,CAAC;;EAE3D;EACA,MAAM,CAAC2C,SAAS,EAAEC,YAAY,CAAC,GAAG5C,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC6C,UAAU,EAAEC,aAAa,CAAC,GAAG9C,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC+C,aAAa,EAAEC,gBAAgB,CAAC,GAAGhD,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACiD,OAAO,EAAEC,UAAU,CAAC,GAAGlD,QAAQ,CAAC;IACrC2C,SAAS,EAAE,KAAK;IAChBE,UAAU,EAAE,KAAK;IACjBE,aAAa,EAAE;EACjB,CAAC,CAAC;EACF,MAAM,CAACI,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGpD,QAAQ,CAAC,IAAI,CAAC;EAE9DC,SAAS,CAAC,MAAM;IACdoD,gBAAgB,CAAC,CAAC;IAClBC,cAAc,CAAC,CAAC;IAChB,IAAIzC,aAAa,EAAE;MACjB0C,iBAAiB,CAAC1C,aAAa,CAAC;IAClC;EACF,CAAC,EAAE,CAACA,aAAa,CAAC,CAAC;EAEnB,MAAMwC,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,MAAMG,SAAS,GAAGrD,kBAAkB,CAAC,CAAC;IACtCmB,kBAAkB,CAACkC,SAAS,CAAC;EAC/B,CAAC;;EAED;EACA,MAAMC,iBAAiB,GAAIC,MAAM,IAAK;IACpC,MAAMC,IAAI,GAAG,IAAIC,GAAG,CAAC,CAAC;IACtB,MAAMC,YAAY,GAAG,EAAE;IAEvBH,MAAM,CAACI,OAAO,CAACC,KAAK,IAAI;MACtB,IAAI,CAACJ,IAAI,CAACK,GAAG,CAACD,KAAK,CAACE,GAAG,CAAC,EAAE;QACxBN,IAAI,CAACO,GAAG,CAACH,KAAK,CAACE,GAAG,CAAC;QACnBJ,YAAY,CAACM,IAAI,CAACJ,KAAK,CAAC;MAC1B;IACF,CAAC,CAAC;IAEF,OAAOF,YAAY;EACrB,CAAC;EAED,MAAMP,cAAc,GAAGA,CAAA,KAAM;IAC3B,MAAMc,KAAK,GAAGhE,iBAAiB,CAACiE,iBAAiB,CAAC,CAAC;IACnD7B,aAAa,CAAC4B,KAAK,CAAC;EACtB,CAAC;EAED,MAAMb,iBAAiB,GAAIe,MAAM,IAAK;IACpCpD,WAAW,CAACoD,MAAM,CAACC,IAAI,IAAI,EAAE,CAAC;IAC9BnD,kBAAkB,CAACkD,MAAM,CAACE,WAAW,IAAI,EAAE,CAAC;;IAE5C;IACA,MAAMd,MAAM,GAAGY,MAAM,CAACZ,MAAM,IAAI,EAAE;IAClC,MAAMe,kBAAkB,GAAGhB,iBAAiB,CAACC,MAAM,CAAC;IACpDlC,iBAAiB,CAACiD,kBAAkB,CAAC;IAErC,IAAIH,MAAM,CAACI,IAAI,KAAK,UAAU,IAAIJ,MAAM,CAACK,YAAY,EAAE;MACrD;MACA3D,oBAAoB,CAAC;QACnB4D,UAAU,EAAEN,MAAM,CAACK;MACrB,CAAC,CAAC;IACJ,CAAC,MAAM,IAAIL,MAAM,CAACI,IAAI,KAAK,UAAU,IAAIJ,MAAM,CAACK,YAAY,EAAE;MAC5D;MACA3D,oBAAoB,CAAC;QACnB6D,UAAU,EAAEP,MAAM,CAACK;MACrB,CAAC,CAAC;IACJ,CAAC,MAAM,IAAIL,MAAM,CAACI,IAAI,KAAK,aAAa,IAAIJ,MAAM,CAACK,YAAY,EAAE;MAC/D;MACA3D,oBAAoB,CAAC;QACnB8D,aAAa,EAAER,MAAM,CAACK;MACxB,CAAC,CAAC;IACJ;;IAEA;IACA,IAAIL,MAAM,CAACS,SAAS,EAAE;MACpB/D,oBAAoB,CAACsD,MAAM,CAACS,SAAS,CAAC;IACxC;EACF,CAAC;;EAED;EACA9E,SAAS,CAAC,MAAM;IACd+E,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA/E,SAAS,CAAC,MAAM;IACd,IAAIc,iBAAiB,CAAC8D,UAAU,EAAE;MAChC,MAAMI,cAAc,GAAG7E,iBAAiB,CAAC8E,oBAAoB,CAC3DnE,iBAAiB,CAAC8D,UAAU,EAC5B9D,iBAAiB,CAAC+D,aACpB,CAAC;MAED,MAAMK,QAAQ,GAAG/E,iBAAiB,CAACgF,mBAAmB,CACpDrE,iBAAiB,CAAC8D,UAAU,EAC5B9D,iBAAiB,CAAC+D,aACpB,CAAC;MACD1B,mBAAmB,CAAC+B,QAAQ,CAAC;MAE7B,IAAI,CAACF,cAAc,CAACI,OAAO,EAAE;QAC3BjD,SAAS,CAACkD,IAAI,KAAK;UACjB,GAAGA,IAAI;UACPC,YAAY,EAAEN,cAAc,CAAC9C,MAAM,CAACqD,IAAI,CAAC,IAAI;QAC/C,CAAC,CAAC,CAAC;MACL,CAAC,MAAM;QACLpD,SAAS,CAACkD,IAAI,IAAI;UAChB,MAAMG,SAAS,GAAG;YAAE,GAAGH;UAAK,CAAC;UAC7B,OAAOG,SAAS,CAACF,YAAY;UAC7B,OAAOE,SAAS;QAClB,CAAC,CAAC;MACJ;IACF,CAAC,MAAM;MACLrC,mBAAmB,CAAC,IAAI,CAAC;MACzBhB,SAAS,CAACkD,IAAI,IAAI;QAChB,MAAMG,SAAS,GAAG;UAAE,GAAGH;QAAK,CAAC;QAC7B,OAAOG,SAAS,CAACF,YAAY;QAC7B,OAAOE,SAAS;MAClB,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAAC1E,iBAAiB,CAAC8D,UAAU,EAAE9D,iBAAiB,CAAC+D,aAAa,CAAC,CAAC;EAEnE,MAAME,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC9B,UAAU,CAACoC,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE3C,SAAS,EAAE;IAAK,CAAC,CAAC,CAAC;IAClD,IAAI;MACF,MAAM+C,QAAQ,GAAG,MAAMrF,UAAU,CAACsF,YAAY,CAAC,CAAC;MAChD/C,YAAY,CAAC8C,QAAQ,CAACE,IAAI,IAAI,EAAE,CAAC;IACnC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;IAClD,CAAC,SAAS;MACR3C,UAAU,CAACoC,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE3C,SAAS,EAAE;MAAM,CAAC,CAAC,CAAC;IACrD;EACF,CAAC;EAED,MAAMoD,cAAc,GAAG,MAAOnB,UAAU,IAAK;IAC3C,IAAI,CAACA,UAAU,EAAE;MACf9B,aAAa,CAAC,EAAE,CAAC;MACjBE,gBAAgB,CAAC,EAAE,CAAC;MACpB;IACF;IAEAE,UAAU,CAACoC,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAEzC,UAAU,EAAE;IAAK,CAAC,CAAC,CAAC;IACnD,IAAI;MACF,MAAM6C,QAAQ,GAAG,MAAMrF,UAAU,CAAC2F,uBAAuB,CAACpB,UAAU,CAAC;MACrE9B,aAAa,CAAC4C,QAAQ,CAACE,IAAI,IAAI,EAAE,CAAC;IACpC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD/C,aAAa,CAAC,EAAE,CAAC;IACnB,CAAC,SAAS;MACRI,UAAU,CAACoC,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEzC,UAAU,EAAE;MAAM,CAAC,CAAC,CAAC;IACtD;EACF,CAAC;EAED,MAAMoD,iBAAiB,GAAG,MAAOpB,UAAU,IAAK;IAC9C,IAAI,CAACA,UAAU,EAAE;MACf7B,gBAAgB,CAAC,EAAE,CAAC;MACpB;IACF;IAEAE,UAAU,CAACoC,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAEvC,aAAa,EAAE;IAAK,CAAC,CAAC,CAAC;IACtD,IAAI;MACF,MAAM2C,QAAQ,GAAG,MAAMrF,UAAU,CAAC6F,0BAA0B,CAACrB,UAAU,CAAC;MACxE7B,gBAAgB,CAAC0C,QAAQ,CAACE,IAAI,IAAI,EAAE,CAAC;IACvC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD7C,gBAAgB,CAAC,EAAE,CAAC;IACtB,CAAC,SAAS;MACRE,UAAU,CAACoC,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEvC,aAAa,EAAE;MAAM,CAAC,CAAC,CAAC;IACzD;EACF,CAAC;EAED,MAAMoD,oBAAoB,GAAIC,CAAC,IAAK;IAClC,MAAMxB,UAAU,GAAGwB,CAAC,CAACC,MAAM,CAACC,KAAK;IACjC,MAAMC,QAAQ,GAAG5D,SAAS,CAAC6D,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAKC,QAAQ,CAAC/B,UAAU,CAAC,CAAC;IAEnE5D,oBAAoB,CAAC;MACnB4D,UAAU,EAAEA,UAAU,IAAI,IAAI;MAC9BC,UAAU,EAAE,IAAI;MAChBC,aAAa,EAAE,IAAI;MACnByB,QAAQ,EAAEA,QAAQ,IAAI,IAAI;MAC1BK,QAAQ,EAAE,IAAI;MACdC,WAAW,EAAE;IACf,CAAC,CAAC;IAEF/D,aAAa,CAAC,EAAE,CAAC;IACjBE,gBAAgB,CAAC,EAAE,CAAC;IAEpB,IAAI4B,UAAU,EAAE;MACdmB,cAAc,CAACnB,UAAU,CAAC;IAC5B;EACF,CAAC;EAED,MAAMkC,oBAAoB,GAAIV,CAAC,IAAK;IAClC,MAAMvB,UAAU,GAAGuB,CAAC,CAACC,MAAM,CAACC,KAAK;IACjC,MAAMM,QAAQ,GAAG/D,UAAU,CAAC2D,IAAI,CAACO,CAAC,IAAIA,CAAC,CAACL,EAAE,KAAKC,QAAQ,CAAC9B,UAAU,CAAC,CAAC;IAEpE7D,oBAAoB,CAACsE,IAAI,KAAK;MAC5B,GAAGA,IAAI;MACPT,UAAU,EAAEA,UAAU,IAAI,IAAI;MAC9BC,aAAa,EAAE,IAAI;MACnB8B,QAAQ,EAAEA,QAAQ,IAAI,IAAI;MAC1BC,WAAW,EAAE;IACf,CAAC,CAAC,CAAC;IAEH7D,gBAAgB,CAAC,EAAE,CAAC;IAEpB,IAAI6B,UAAU,EAAE;MACdoB,iBAAiB,CAACpB,UAAU,CAAC;IAC/B;EACF,CAAC;EAED,MAAMmC,uBAAuB,GAAIZ,CAAC,IAAK;IACrC,MAAMtB,aAAa,GAAGsB,CAAC,CAACC,MAAM,CAACC,KAAK;IACpC,MAAMO,WAAW,GAAG9D,aAAa,CAACyD,IAAI,CAACS,EAAE,IAAIA,EAAE,CAACP,EAAE,KAAKC,QAAQ,CAAC7B,aAAa,CAAC,CAAC;IAE/E9D,oBAAoB,CAACsE,IAAI,KAAK;MAC5B,GAAGA,IAAI;MACPR,aAAa,EAAEA,aAAa,IAAI,IAAI;MACpC+B,WAAW,EAAEA,WAAW,IAAI;IAC9B,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMK,iBAAiB,GAAInD,KAAK,IAAK;IACnC,MAAMoD,UAAU,GAAG5F,cAAc,CAAC6F,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACpD,GAAG,KAAKF,KAAK,CAACE,GAAG,CAAC;IAEhE,IAAIkD,UAAU,EAAE;MACd;MACA3F,iBAAiB,CAAC8D,IAAI,IAAIA,IAAI,CAACgC,MAAM,CAACD,CAAC,IAAIA,CAAC,CAACpD,GAAG,KAAKF,KAAK,CAACE,GAAG,CAAC,CAAC;IAClE,CAAC,MAAM;MACL;MACAzC,iBAAiB,CAAC8D,IAAI,IAAI;QACxB,MAAMiC,MAAM,GAAGjC,IAAI,CAAC8B,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACpD,GAAG,KAAKF,KAAK,CAACE,GAAG,CAAC;QAClD,IAAIsD,MAAM,EAAE;UACVzB,OAAO,CAAC0B,IAAI,CAAC,SAASzD,KAAK,CAACE,GAAG,uDAAuD,CAAC;UACvF,OAAOqB,IAAI;QACb;QACA,OAAO,CAAC,GAAGA,IAAI,EAAE;UAAE,GAAGvB;QAAM,CAAC,CAAC;MAChC,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAM0D,iBAAiB,GAAI1D,KAAK,IAAK;IACnC7B,cAAc,CAAC6B,KAAK,CAAC;IACrB/B,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,MAAM0F,qBAAqB,GAAIC,YAAY,IAAK;IAC9CnG,iBAAiB,CAAC8D,IAAI,IACpBA,IAAI,CAACsC,GAAG,CAACP,CAAC,IAAIA,CAAC,CAACpD,GAAG,KAAK0D,YAAY,CAAC1D,GAAG,GAAG0D,YAAY,GAAGN,CAAC,CAC7D,CAAC;IACDrF,kBAAkB,CAAC,KAAK,CAAC;IACzBE,cAAc,CAAC,IAAI,CAAC;EACtB,CAAC;EAED,MAAM2F,eAAe,GAAGA,CAACC,SAAS,EAAEC,OAAO,KAAK;IAC9C,MAAMC,SAAS,GAAG,CAAC,GAAGzG,cAAc,CAAC;IACrC,MAAM,CAAC0G,UAAU,CAAC,GAAGD,SAAS,CAACE,MAAM,CAACJ,SAAS,EAAE,CAAC,CAAC;IACnDE,SAAS,CAACE,MAAM,CAACH,OAAO,EAAE,CAAC,EAAEE,UAAU,CAAC;IACxCzG,iBAAiB,CAACwG,SAAS,CAAC;EAC9B,CAAC;EAED,MAAMG,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,MAAMC,UAAU,GAAGC,YAAY,CAAC,CAAC;IAEjC,IAAI,CAACD,UAAU,CAAC/C,OAAO,EAAE;MACvBjD,SAAS,CAACgG,UAAU,CAACjG,MAAM,CAAC;MAC5B;IACF;IAEAG,SAAS,CAAC,IAAI,CAAC;IACf,IAAI;MACF;MACA,MAAMgG,iBAAiB,GAAG;QACxB,GAAGvH,iBAAiB;QACpB;QACA6D,UAAU,EAAE7D,iBAAiB,CAAC6D,UAAU;QACxC2B,QAAQ,EAAExF,iBAAiB,CAACwF,QAAQ;QACpC;QACA1B,UAAU,EAAE9D,iBAAiB,CAAC8D,UAAU;QACxC+B,QAAQ,EAAE7F,iBAAiB,CAAC6F,QAAQ;QACpC;QACA9B,aAAa,EAAE/D,iBAAiB,CAAC+D,aAAa,IAAI,IAAI;QACtD+B,WAAW,EAAE9F,iBAAiB,CAAC8F,WAAW,IAAI;MAChD,CAAC;MAED,MAAMvC,MAAM,GAAG;QACbC,IAAI,EAAEtD,QAAQ;QACduD,WAAW,EAAErD,eAAe;QAC5BuC,MAAM,EAAEnC,cAAc;QACtBwD,SAAS,EAAEuD,iBAAiB;QAC5BC,QAAQ,EAAE;UACRC,YAAY,EAAE,IAAI;UAClBC,sBAAsB,EAAE,IAAI;UAC5BC,gBAAgB,EAAE;QACpB;MACF,CAAC;MAED,IAAIC,WAAW;MACf;MACA,IAAI5H,iBAAiB,CAAC+D,aAAa,EAAE;QACnC6D,WAAW,GAAGvI,iBAAiB,CAACwI,cAAc,CAAC,aAAa,EAAE7H,iBAAiB,CAAC+D,aAAa,EAAER,MAAM,CAAC;MACxG,CAAC,MAAM,IAAIvD,iBAAiB,CAAC8D,UAAU,EAAE;QACvC8D,WAAW,GAAGvI,iBAAiB,CAACwI,cAAc,CAAC,UAAU,EAAE7H,iBAAiB,CAAC8D,UAAU,EAAEP,MAAM,CAAC;MAClG,CAAC,MAAM;QACL,MAAM,IAAIuE,KAAK,CAAC,0CAA0C,CAAC;MAC7D;;MAEA;MACAvF,cAAc,CAAC,CAAC;MAEhB,IAAI3C,MAAM,EAAE;QACVA,MAAM,CAACgI,WAAW,CAAC;MACrB,CAAC,MAAM;QACL;QACAG,KAAK,CAAC,wCAAwC,CAAC;QAC/C;QACA5H,WAAW,CAAC,EAAE,CAAC;QACfE,kBAAkB,CAAC,EAAE,CAAC;QACtBI,iBAAiB,CAAC,EAAE,CAAC;QACrBR,oBAAoB,CAAC,CAAC,CAAC,CAAC;MAC1B;IACF,CAAC,CAAC,OAAO6E,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1CzD,SAAS,CAAC;QAAE2G,OAAO,EAAElD,KAAK,CAACmD;MAAQ,CAAC,CAAC;IACvC,CAAC,SAAS;MACR1G,SAAS,CAAC,KAAK,CAAC;IAClB;EACF,CAAC;EAED,MAAM+F,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMlG,MAAM,GAAG,CAAC,CAAC;IAEjB,IAAI,CAAClB,QAAQ,CAACgI,IAAI,CAAC,CAAC,EAAE;MACpB9G,MAAM,CAAClB,QAAQ,GAAG,uBAAuB;IAC3C;;IAEA;IACA,IAAI,CAACF,iBAAiB,CAAC6D,UAAU,EAAE;MACjCzC,MAAM,CAAC4C,SAAS,GAAG,0BAA0B;IAC/C,CAAC,MAAM,IAAI,CAAChE,iBAAiB,CAAC8D,UAAU,EAAE;MACxC1C,MAAM,CAAC4C,SAAS,GAAG,0BAA0B;IAC/C,CAAC,MAAM,IAAIhE,iBAAiB,CAAC+D,aAAa,IAAI,CAAC/D,iBAAiB,CAAC8D,UAAU,EAAE;MAC3E1C,MAAM,CAAC4C,SAAS,GAAG,sDAAsD;IAC3E;;IAEA;IACA,IAAIhE,iBAAiB,CAAC8D,UAAU,EAAE;MAChC,MAAMI,cAAc,GAAG7E,iBAAiB,CAAC8E,oBAAoB,CAC3DnE,iBAAiB,CAAC8D,UAAU,EAC5B9D,iBAAiB,CAAC+D,aACpB,CAAC;MAED,IAAI,CAACG,cAAc,CAACI,OAAO,EAAE;QAC3BlD,MAAM,CAACoD,YAAY,GAAGN,cAAc,CAAC9C,MAAM,CAACqD,IAAI,CAAC,IAAI,CAAC;MACxD;IACF;IAEA,IAAIjE,cAAc,CAAC2H,MAAM,KAAK,CAAC,EAAE;MAC/B/G,MAAM,CAACuB,MAAM,GAAG,kCAAkC;IACpD;;IAEA;IACA,MAAMyF,cAAc,GAAG,CAAC,MAAM,EAAE,cAAc,EAAE,QAAQ,CAAC;IACzD,MAAMC,iBAAiB,GAAG7H,cAAc,CAACqG,GAAG,CAACP,CAAC,IAAIA,CAAC,CAACpD,GAAG,CAAC;IACxD,MAAMoF,eAAe,GAAGF,cAAc,CAAC7B,MAAM,CAACgC,EAAE,IAAI,CAACF,iBAAiB,CAACG,QAAQ,CAACD,EAAE,CAAC,CAAC;IAEpF,IAAID,eAAe,CAACH,MAAM,GAAG,CAAC,EAAE;MAC9B/G,MAAM,CAACgH,cAAc,GAAG,4BAA4BE,eAAe,CAAC7D,IAAI,CAAC,IAAI,CAAC,EAAE;IAClF;IAEA,OAAO;MACLH,OAAO,EAAEmE,MAAM,CAACC,IAAI,CAACtH,MAAM,CAAC,CAAC+G,MAAM,KAAK,CAAC;MACzC/G;IACF,CAAC;EACH,CAAC;EAED,MAAMuH,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,IAAIC,QAAQ,GAAGtI,eAAe;;IAE9B;IACA,IAAII,cAAc,KAAK,KAAK,EAAE;MAC5BkI,QAAQ,GAAGA,QAAQ,CAACrC,MAAM,CAACvD,KAAK,IAAIA,KAAK,CAAC6F,OAAO,KAAKnI,cAAc,CAAC;IACvE;;IAEA;IACA,IAAIE,UAAU,EAAE;MACdgI,QAAQ,GAAGA,QAAQ,CAACrC,MAAM,CAACvD,KAAK,IAC9BA,KAAK,CAAC8F,KAAK,CAACC,WAAW,CAAC,CAAC,CAACP,QAAQ,CAAC5H,UAAU,CAACmI,WAAW,CAAC,CAAC,CAAC,IAC5D/F,KAAK,CAACE,GAAG,CAAC6F,WAAW,CAAC,CAAC,CAACP,QAAQ,CAAC5H,UAAU,CAACmI,WAAW,CAAC,CAAC,CAC3D,CAAC;IACH;IAEA,OAAOH,QAAQ;EACjB,CAAC;EAED,MAAMI,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,MAAMC,MAAM,GAAG;MAAEC,GAAG,EAAE5I,eAAe,CAAC6H;IAAO,CAAC;IAC9CM,MAAM,CAACC,IAAI,CAACvJ,sBAAsB,CAAC,CAAC4D,OAAO,CAACoG,UAAU,IAAI;MACxDF,MAAM,CAACE,UAAU,CAAC,GAAG7I,eAAe,CAACiG,MAAM,CAACD,CAAC,IAAIA,CAAC,CAACuC,OAAO,KAAKM,UAAU,CAAC,CAAChB,MAAM;IACnF,CAAC,CAAC;IACF,OAAOc,MAAM;EACf,CAAC;EAED,MAAMG,aAAa,GAAGJ,gBAAgB,CAAC,CAAC;EACxC,MAAMK,cAAc,GAAGV,iBAAiB,CAAC,CAAC;EAE1C,oBACEjJ,OAAA;IAAK4J,SAAS,EAAC,cAAc;IAAAC,QAAA,gBAC3B7J,OAAA;MAAK4J,SAAS,EAAC,qBAAqB;MAAAC,QAAA,gBAClC7J,OAAA;QAAA6J,QAAA,EAAI;MAAY;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACrBjK,OAAA;QAAK4J,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7B7J,OAAA;UACEiE,IAAI,EAAC,QAAQ;UACbiG,OAAO,EAAEA,CAAA,KAAM7I,cAAc,CAAC,IAAI,CAAE;UACpCuI,SAAS,EAAC,mBAAmB;UAC7BO,QAAQ,EAAErJ,cAAc,CAAC2H,MAAM,KAAK,CAAE;UAAAoB,QAAA,EACvC;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTjK,OAAA;UACEiE,IAAI,EAAC,QAAQ;UACbiG,OAAO,EAAExC,UAAW;UACpBkC,SAAS,EAAC,iBAAiB;UAC3BO,QAAQ,EAAEvI,MAAM,IAAIF,MAAM,CAACoD,YAAa;UAAA+E,QAAA,EAEvCjI,MAAM,GAAG,WAAW,GAAG;QAAW;UAAAkI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC,EACR9J,QAAQ,iBACPH,OAAA;UACEiE,IAAI,EAAC,QAAQ;UACbiG,OAAO,EAAE/J,QAAS;UAClByJ,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAC5B;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAELvI,MAAM,CAAC4G,OAAO,iBACbtI,OAAA;MAAK4J,SAAS,EAAC,mBAAmB;MAAAC,QAAA,EAAEnI,MAAM,CAAC4G;IAAO;MAAAwB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CACzD,EAGAlB,MAAM,CAACC,IAAI,CAACtH,MAAM,CAAC,CAAC+G,MAAM,GAAG,CAAC,iBAC7BzI,OAAA;MAAK4J,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChC7J,OAAA;QAAA6J,QAAA,EAAI;MAAgC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACzCjK,OAAA;QAAA6J,QAAA,EACGd,MAAM,CAACqB,OAAO,CAAC1I,MAAM,CAAC,CAACyF,GAAG,CAAC,CAAC,CAAC3D,GAAG,EAAE+E,OAAO,CAAC,kBACzCvI,OAAA;UAAA6J,QAAA,EAAetB;QAAO,GAAb/E,GAAG;UAAAsG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAe,CAC5B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CACN,eAEDjK,OAAA;MAAK4J,SAAS,EAAC,sBAAsB;MAAAC,QAAA,gBAEnC7J,OAAA;QAAK4J,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAE3B7J,OAAA;UAAK4J,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7B7J,OAAA;YAAK4J,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7B7J,OAAA;cAAA6J,QAAA,GAAI,eAAa,EAAC/H,UAAU,CAAC2G,MAAM,EAAC,GAAC;YAAA;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC1CjK,OAAA;cACEiE,IAAI,EAAC,QAAQ;cACb2F,SAAS,EAAC,UAAU;cACpBM,OAAO,EAAEA,CAAA,KAAMjI,iBAAiB,CAAC,CAACD,cAAc,CAAE;cAAA6H,QAAA,EAEjD7H,cAAc,GAAG,MAAM,GAAG;YAAM;cAAA8H,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,EAELjI,cAAc,iBACbhC,OAAA;YAAK4J,SAAS,EAAC,kBAAkB;YAAAC,QAAA,EAC9B/H,UAAU,CAAC2G,MAAM,KAAK,CAAC,gBACtBzI,OAAA;cAAG4J,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAC;YAAiD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,GAErFnI,UAAU,CAACqF,GAAG,CAAEkD,IAAI,iBAClBrK,OAAA;cAAoB4J,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAC7C7J,OAAA;gBAAK4J,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,gBAC/B7J,OAAA;kBAAA6J,QAAA,EAAKQ,IAAI,CAACvG;gBAAI;kBAAAgG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACpBjK,OAAA;kBAAM4J,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,EAAEQ,IAAI,CAACpG;gBAAI;kBAAA6F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD,CAAC,eACNjK,OAAA;gBAAG4J,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,EAAEQ,IAAI,CAACtG,WAAW,IAAI;cAAgB;gBAAA+F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC1EjK,OAAA;gBAAK4J,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxB7J,OAAA;kBAAA6J,QAAA,GAAOQ,IAAI,CAACC,OAAO,CAACC,UAAU,EAAC,SAAO;gBAAA;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC7CjK,OAAA;kBAAA6J,QAAA,EAAM;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACdjK,OAAA;kBAAA6J,QAAA,GAAM,UAAQ,EAAC,IAAIW,IAAI,CAACH,IAAI,CAACI,SAAS,CAAC,CAACC,kBAAkB,CAAC,CAAC;gBAAA;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjE,CAAC,eACNjK,OAAA;gBAAK4J,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBAC3B7J,OAAA;kBACEiE,IAAI,EAAC,QAAQ;kBACb2F,SAAS,EAAC,uBAAuB;kBACjCM,OAAO,EAAEA,CAAA,KAAMpH,iBAAiB,CAACuH,IAAI,CAAE;kBAAAR,QAAA,EACxC;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTjK,OAAA;kBACEiE,IAAI,EAAC,QAAQ;kBACb2F,SAAS,EAAC,sBAAsB;kBAChCM,OAAO,EAAEA,CAAA,KAAM;oBACb,IAAIS,MAAM,CAACC,OAAO,CAAC,4CAA4C,CAAC,EAAE;sBAChEjL,iBAAiB,CAACkL,gBAAgB,CAACR,IAAI,CAACpG,IAAI,EAAEoG,IAAI,CAACnG,YAAY,CAAC;sBAChErB,cAAc,CAAC,CAAC;oBAClB;kBACF,CAAE;kBAAAgH,QAAA,EACH;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA,GA/BEI,IAAI,CAAC7G,GAAG;cAAAsG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAgCb,CACN;UACF;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGNjK,OAAA;UAAK4J,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7B7J,OAAA;YAAA6J,QAAA,EAAI;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAE3BjK,OAAA;YAAK4J,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB7J,OAAA;cAAA6J,QAAA,EAAO;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC1BjK,OAAA;cACEiE,IAAI,EAAC,MAAM;cACX4B,KAAK,EAAErF,QAAS;cAChBsK,QAAQ,EAAGnF,CAAC,IAAKlF,WAAW,CAACkF,CAAC,CAACC,MAAM,CAACC,KAAK,CAAE;cAC7CkF,WAAW,EAAC,iBAAiB;cAC7BnB,SAAS,EAAElI,MAAM,CAAClB,QAAQ,GAAG,OAAO,GAAG;YAAG;cAAAsJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC,EACDvI,MAAM,CAAClB,QAAQ,iBAAIR,OAAA;cAAK4J,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAEnI,MAAM,CAAClB;YAAQ;cAAAsJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvE,CAAC,eAENjK,OAAA;YAAK4J,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB7J,OAAA;cAAA6J,QAAA,EAAO;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC1BjK,OAAA;cACE6F,KAAK,EAAEnF,eAAgB;cACvBoK,QAAQ,EAAGnF,CAAC,IAAKhF,kBAAkB,CAACgF,CAAC,CAACC,MAAM,CAACC,KAAK,CAAE;cACpDkF,WAAW,EAAC,wBAAwB;cACpCC,IAAI,EAAC;YAAG;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAGH,CAAC,eAGNjK,OAAA;UAAK4J,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7B7J,OAAA;YAAA6J,QAAA,GAAI,mBAAiB,EAAC/I,cAAc,CAAC2H,MAAM,EAAC,GAAC;UAAA;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EACjDvI,MAAM,CAACuB,MAAM,iBAAIjD,OAAA;YAAK4J,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAEnI,MAAM,CAACuB;UAAM;YAAA6G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,EACrEvI,MAAM,CAACgH,cAAc,iBAAI1I,OAAA;YAAK4J,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAEnI,MAAM,CAACgH;UAAc;YAAAoB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAEtFjK,OAAA;YAAK4J,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAC7B/I,cAAc,CAAC2H,MAAM,KAAK,CAAC,gBAC1BzI,OAAA;cAAK4J,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAE7B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,GAENnJ,cAAc,CAACqG,GAAG,CAAC,CAAC7D,KAAK,EAAE2H,KAAK,kBAC9BjL,OAAA;cAAqB4J,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7C7J,OAAA;gBAAK4J,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB7J,OAAA;kBAAM4J,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAEvG,KAAK,CAAC8F;gBAAK;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAClDjK,OAAA;kBAAM4J,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAEvG,KAAK,CAACW;gBAAI;kBAAA6F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,EAC/C3G,KAAK,CAAC4H,QAAQ,iBAAIlL,OAAA;kBAAM4J,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChE,CAAC,eACNjK,OAAA;gBAAK4J,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC5B7J,OAAA;kBACEiE,IAAI,EAAC,QAAQ;kBACbiG,OAAO,EAAEA,CAAA,KAAMlD,iBAAiB,CAAC1D,KAAK,CAAE;kBACxCsG,SAAS,EAAC,UAAU;kBACpBuB,KAAK,EAAC,iBAAiB;kBAAAtB,QAAA,EACxB;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTjK,OAAA;kBACEiE,IAAI,EAAC,QAAQ;kBACbiG,OAAO,EAAEA,CAAA,KAAM9C,eAAe,CAAC6D,KAAK,EAAEG,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEJ,KAAK,GAAG,CAAC,CAAC,CAAE;kBAC9DrB,SAAS,EAAC,UAAU;kBACpBO,QAAQ,EAAEc,KAAK,KAAK,CAAE;kBACtBE,KAAK,EAAC,SAAS;kBAAAtB,QAAA,EAChB;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTjK,OAAA;kBACEiE,IAAI,EAAC,QAAQ;kBACbiG,OAAO,EAAEA,CAAA,KAAM9C,eAAe,CAAC6D,KAAK,EAAEG,IAAI,CAACE,GAAG,CAACxK,cAAc,CAAC2H,MAAM,GAAG,CAAC,EAAEwC,KAAK,GAAG,CAAC,CAAC,CAAE;kBACtFrB,SAAS,EAAC,UAAU;kBACpBO,QAAQ,EAAEc,KAAK,KAAKnK,cAAc,CAAC2H,MAAM,GAAG,CAAE;kBAC9C0C,KAAK,EAAC,WAAW;kBAAAtB,QAAA,EAClB;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTjK,OAAA;kBACEiE,IAAI,EAAC,QAAQ;kBACbiG,OAAO,EAAEA,CAAA,KAAMzD,iBAAiB,CAACnD,KAAK,CAAE;kBACxCsG,SAAS,EAAC,iBAAiB;kBAC3BuB,KAAK,EAAC,cAAc;kBAAAtB,QAAA,EACrB;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA,GAzCE3G,KAAK,CAACE,GAAG;cAAAsG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA0Cd,CACN;UACF;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNjK,OAAA;QAAK4J,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3B7J,OAAA;UAAK4J,SAAS,EAAC,eAAe;UAAAC,QAAA,eAC5B7J,OAAA;YAAA6J,QAAA,EAAI;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC,eAGNjK,OAAA;UAAK4J,SAAS,EAAC,qBAAqB;UAAC2B,KAAK,EAAE;YAC1CC,eAAe,EAAE,SAAS;YAC1BC,MAAM,EAAE,mBAAmB;YAC3BC,YAAY,EAAE,KAAK;YACnBC,OAAO,EAAE,QAAQ;YACjBC,MAAM,EAAE;UACV,CAAE;UAAA/B,QAAA,gBACA7J,OAAA;YAAIuL,KAAK,EAAE;cAAEK,MAAM,EAAE,YAAY;cAAEC,KAAK,EAAE;YAAU,CAAE;YAAAhC,QAAA,EAAC;UAEvD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLjK,OAAA;YAAKuL,KAAK,EAAE;cACVO,QAAQ,EAAE,UAAU;cACpBD,KAAK,EAAE,SAAS;cAChBE,YAAY,EAAE,MAAM;cACpBJ,OAAO,EAAE,SAAS;cAClBH,eAAe,EAAE,SAAS;cAC1BE,YAAY,EAAE,KAAK;cACnBD,MAAM,EAAE;YACV,CAAE;YAAA5B,QAAA,gBACA7J,OAAA;cAAA6J,QAAA,EAAQ;YAAoB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACrCjK,OAAA;cAAIuL,KAAK,EAAE;gBAAEK,MAAM,EAAE,iBAAiB;gBAAEI,WAAW,EAAE;cAAO,CAAE;cAAAnC,QAAA,gBAC5D7J,OAAA;gBAAA6J,QAAA,EAAI;cAAkF;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC3FjK,OAAA;gBAAA6J,QAAA,EAAI;cAAuC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChDjK,OAAA;gBAAA6J,QAAA,EAAI;cAAqF;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5F,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAENjK,OAAA;YAAK4J,SAAS,EAAC,qBAAqB;YAAC2B,KAAK,EAAE;cAC1CU,OAAO,EAAE,MAAM;cACfC,mBAAmB,EAAE,sCAAsC;cAC3DC,GAAG,EAAE;YACP,CAAE;YAAAtC,QAAA,gBAEA7J,OAAA;cAAK4J,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzB7J,OAAA;gBAAOuL,KAAK,EAAE;kBACZU,OAAO,EAAE,OAAO;kBAChBF,YAAY,EAAE,QAAQ;kBACtBK,UAAU,EAAE,KAAK;kBACjBP,KAAK,EAAE;gBACT,CAAE;gBAAAhC,QAAA,EAAC;cAEH;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRjK,OAAA;gBACE6F,KAAK,EAAEvF,iBAAiB,CAAC6D,UAAU,IAAI,EAAG;gBAC1C2G,QAAQ,EAAEpF,oBAAqB;gBAC/ByE,QAAQ,EAAE3H,OAAO,CAACN,SAAU;gBAC5B0H,SAAS,EAAElI,MAAM,CAACyC,UAAU,GAAG,OAAO,GAAG,EAAG;gBAC5CoH,KAAK,EAAE;kBACLc,KAAK,EAAE,MAAM;kBACbV,OAAO,EAAE,SAAS;kBAClBF,MAAM,EAAE,mBAAmB;kBAC3BC,YAAY,EAAE,KAAK;kBACnBI,QAAQ,EAAE,MAAM;kBAChBN,eAAe,EAAE;gBACnB,CAAE;gBAAA3B,QAAA,gBAEF7J,OAAA;kBAAQ6F,KAAK,EAAC,EAAE;kBAAAgE,QAAA,EAAC;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EACxC/H,SAAS,CAACiF,GAAG,CAACrB,QAAQ,iBACrB9F,OAAA;kBAA0B6F,KAAK,EAAEC,QAAQ,CAACG,EAAG;kBAAA4D,QAAA,EAC1C/D,QAAQ,CAAChC;gBAAI,GADHgC,QAAQ,CAACG,EAAE;kBAAA6D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEhB,CACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC,EACRvI,MAAM,CAACyC,UAAU,iBAChBnE,OAAA;gBAAKuL,KAAK,EAAE;kBAAEM,KAAK,EAAE,SAAS;kBAAEC,QAAQ,EAAE,UAAU;kBAAEQ,SAAS,EAAE;gBAAU,CAAE;gBAAAzC,QAAA,EAC1EnI,MAAM,CAACyC;cAAU;gBAAA2F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAGNjK,OAAA;cAAK4J,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzB7J,OAAA;gBAAOuL,KAAK,EAAE;kBACZU,OAAO,EAAE,OAAO;kBAChBF,YAAY,EAAE,QAAQ;kBACtBK,UAAU,EAAE,KAAK;kBACjBP,KAAK,EAAE;gBACT,CAAE;gBAAAhC,QAAA,EAAC;cAEH;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRjK,OAAA;gBACE6F,KAAK,EAAEvF,iBAAiB,CAAC8D,UAAU,IAAI,EAAG;gBAC1C0G,QAAQ,EAAEzE,oBAAqB;gBAC/B8D,QAAQ,EAAE,CAAC7J,iBAAiB,CAAC6D,UAAU,IAAI3B,OAAO,CAACJ,UAAW;gBAC9DwH,SAAS,EAAElI,MAAM,CAAC0C,UAAU,GAAG,OAAO,GAAG,EAAG;gBAC5CmH,KAAK,EAAE;kBACLc,KAAK,EAAE,MAAM;kBACbV,OAAO,EAAE,SAAS;kBAClBF,MAAM,EAAE,mBAAmB;kBAC3BC,YAAY,EAAE,KAAK;kBACnBI,QAAQ,EAAE,MAAM;kBAChBN,eAAe,EAAE;gBACnB,CAAE;gBAAA3B,QAAA,gBAEF7J,OAAA;kBAAQ6F,KAAK,EAAC,EAAE;kBAAAgE,QAAA,EACb,CAACvJ,iBAAiB,CAAC6D,UAAU,GAAG,uBAAuB,GAAG;gBAAiB;kBAAA2F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtE,CAAC,EACR7H,UAAU,CAAC+E,GAAG,CAAChB,QAAQ,iBACtBnG,OAAA;kBAA0B6F,KAAK,EAAEM,QAAQ,CAACF,EAAG;kBAAA4D,QAAA,EAC1C1D,QAAQ,CAACrC;gBAAI,GADHqC,QAAQ,CAACF,EAAE;kBAAA6D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEhB,CACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC,EACRvI,MAAM,CAAC0C,UAAU,iBAChBpE,OAAA;gBAAKuL,KAAK,EAAE;kBAAEM,KAAK,EAAE,SAAS;kBAAEC,QAAQ,EAAE,UAAU;kBAAEQ,SAAS,EAAE;gBAAU,CAAE;gBAAAzC,QAAA,EAC1EnI,MAAM,CAAC0C;cAAU;gBAAA0F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAGNjK,OAAA;cAAK4J,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzB7J,OAAA;gBAAOuL,KAAK,EAAE;kBACZU,OAAO,EAAE,OAAO;kBAChBF,YAAY,EAAE,QAAQ;kBACtBK,UAAU,EAAE,KAAK;kBACjBP,KAAK,EAAE;gBACT,CAAE;gBAAAhC,QAAA,EAAC;cAEH;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRjK,OAAA;gBACE6F,KAAK,EAAEvF,iBAAiB,CAAC+D,aAAa,IAAI,EAAG;gBAC7CyG,QAAQ,EAAEvE,uBAAwB;gBAClC4D,QAAQ,EAAE,CAAC7J,iBAAiB,CAAC8D,UAAU,IAAI5B,OAAO,CAACF,aAAc;gBACjEiJ,KAAK,EAAE;kBACLc,KAAK,EAAE,MAAM;kBACbV,OAAO,EAAE,SAAS;kBAClBF,MAAM,EAAE,mBAAmB;kBAC3BC,YAAY,EAAE,KAAK;kBACnBI,QAAQ,EAAE,MAAM;kBAChBN,eAAe,EAAE;gBACnB,CAAE;gBAAA3B,QAAA,gBAEF7J,OAAA;kBAAQ6F,KAAK,EAAC,EAAE;kBAAAgE,QAAA,EACb,CAACvJ,iBAAiB,CAAC8D,UAAU,GAAG,uBAAuB,GAAG;gBAA+B;kBAAA0F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpF,CAAC,EACR3H,aAAa,CAAC6E,GAAG,CAACf,WAAW,iBAC5BpG,OAAA;kBAA6B6F,KAAK,EAAEO,WAAW,CAACH,EAAG;kBAAA4D,QAAA,EAChDzD,WAAW,CAACtC;gBAAI,GADNsC,WAAW,CAACH,EAAE;kBAAA6D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEnB,CACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAELvI,MAAM,CAAC4C,SAAS,iBACftE,OAAA;YAAKuL,KAAK,EAAE;cAAEM,KAAK,EAAE,SAAS;cAAEC,QAAQ,EAAE,UAAU;cAAEQ,SAAS,EAAE;YAAS,CAAE;YAAAzC,QAAA,EACzEnI,MAAM,CAAC4C;UAAS;YAAAwF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd,CACN,EACAvI,MAAM,CAACoD,YAAY,iBAClB9E,OAAA;YAAKuL,KAAK,EAAE;cAAEM,KAAK,EAAE,SAAS;cAAEC,QAAQ,EAAE,UAAU;cAAEQ,SAAS,EAAE,QAAQ;cAAEF,UAAU,EAAE;YAAO,CAAE;YAAAvC,QAAA,GAAC,eAC5F,EAACnI,MAAM,CAACoD,YAAY;UAAA;YAAAgF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB,CACN,EAGAvH,gBAAgB,IAAIA,gBAAgB,CAAC6J,aAAa,CAAC9D,MAAM,GAAG,CAAC,iBAC5DzI,OAAA;YAAKuL,KAAK,EAAE;cACVe,SAAS,EAAE,MAAM;cACjBX,OAAO,EAAE,SAAS;cAClBH,eAAe,EAAE,SAAS;cAC1BC,MAAM,EAAE,mBAAmB;cAC3BC,YAAY,EAAE,KAAK;cACnBI,QAAQ,EAAE;YACZ,CAAE;YAAAjC,QAAA,gBACA7J,OAAA;cAAA6J,QAAA,EAAQ;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EAClCvH,gBAAgB,CAAC6J,aAAa,CAACpF,GAAG,CAAC,CAACkD,IAAI,EAAEY,KAAK,kBAC9CjL,OAAA;cAAiBuL,KAAK,EAAE;gBAAEe,SAAS,EAAE;cAAS,CAAE;cAAAzC,QAAA,gBAC9C7J,OAAA;gBAAA6J,QAAA,GAASQ,IAAI,CAACpG,IAAI,KAAK,UAAU,GAAG,UAAU,GAAG,aAAa,EAAC,QAAM;cAAA;gBAAA6F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACI,IAAI,CAACvG,IAAI,EACxFuG,IAAI,CAACtG,WAAW,iBAAI/D,OAAA;gBAAKuL,KAAK,EAAE;kBAAEM,KAAK,EAAE;gBAAU,CAAE;gBAAAhC,QAAA,EAAEQ,IAAI,CAACtG;cAAW;gBAAA+F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA,GAFvEgB,KAAK;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAGV,CACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CACN,EAGAvH,gBAAgB,IAAIA,gBAAgB,CAAC8J,sBAAsB,CAAC/D,MAAM,GAAG,CAAC,IAAI,CAACnI,iBAAiB,CAAC+D,aAAa,iBACzGrE,OAAA;YAAKuL,KAAK,EAAE;cACVe,SAAS,EAAE,MAAM;cACjBX,OAAO,EAAE,SAAS;cAClBH,eAAe,EAAE,SAAS;cAC1BC,MAAM,EAAE,mBAAmB;cAC3BC,YAAY,EAAE,KAAK;cACnBI,QAAQ,EAAE;YACZ,CAAE;YAAAjC,QAAA,gBACA7J,OAAA;cAAA6J,QAAA,EAAQ;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,uBAAmB,EAACvH,gBAAgB,CAAC8J,sBAAsB,CAAC/D,MAAM,EAAC,mEAEjG;UAAA;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CACN,EAGA3J,iBAAiB,CAAC8D,UAAU,IAAI,CAAC1C,MAAM,CAACoD,YAAY,iBACnD9E,OAAA;YAAKuL,KAAK,EAAE;cACVe,SAAS,EAAE,MAAM;cACjBX,OAAO,EAAE,SAAS;cAClBH,eAAe,EAAE,SAAS;cAC1BC,MAAM,EAAE,mBAAmB;cAC3BC,YAAY,EAAE,KAAK;cACnBI,QAAQ,EAAE;YACZ,CAAE;YAAAjC,QAAA,gBACA7J,OAAA;cAAA6J,QAAA,EAAQ;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,oCAAgC,EAAC3J,iBAAiB,CAAC+D,aAAa,GAAG,aAAa,GAAG,UAAU,EAAC,GACnI;UAAA;YAAAyF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGNjK,OAAA;UAAK4J,SAAS,EAAC,iBAAiB;UAAAC,QAAA,eAC9B7J,OAAA;YACEiE,IAAI,EAAC,MAAM;YACX8G,WAAW,EAAC,kBAAkB;YAC9BlF,KAAK,EAAE3E,UAAW;YAClB4J,QAAQ,EAAGnF,CAAC,IAAKxE,aAAa,CAACwE,CAAC,CAACC,MAAM,CAACC,KAAK,CAAE;YAC/C+D,SAAS,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGNjK,OAAA;UAAK4J,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3B7J,OAAA;YACEiE,IAAI,EAAC,QAAQ;YACbiG,OAAO,EAAEA,CAAA,KAAMjJ,iBAAiB,CAAC,KAAK,CAAE;YACxC2I,SAAS,EAAE,eAAe5I,cAAc,KAAK,KAAK,GAAG,QAAQ,GAAG,EAAE,EAAG;YAAA6I,QAAA,GACtE,OACM,EAACH,aAAa,CAACF,GAAG,EAAC,GAC1B;UAAA;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EACRlB,MAAM,CAACqB,OAAO,CAAC3K,sBAAsB,CAAC,CAAC0H,GAAG,CAAC,CAAC,CAACsC,UAAU,EAAEN,OAAO,CAAC,kBAChEnJ,OAAA;YAEEiE,IAAI,EAAC,QAAQ;YACbiG,OAAO,EAAEA,CAAA,KAAMjJ,iBAAiB,CAACwI,UAAU,CAAE;YAC7CG,SAAS,EAAE,eAAe5I,cAAc,KAAKyI,UAAU,GAAG,QAAQ,GAAG,EAAE,EAAG;YAAAI,QAAA,GAEzEV,OAAO,CAACgC,KAAK,EAAC,IAAE,EAACzB,aAAa,CAACD,UAAU,CAAC,EAAC,GAC9C;UAAA,GANOA,UAAU;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAMT,CACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGNjK,OAAA;UAAK4J,SAAS,EAAC,aAAa;UAAAC,QAAA,EACzBF,cAAc,CAACxC,GAAG,CAAC7D,KAAK,IAAI;YAC3B,MAAMoD,UAAU,GAAG5F,cAAc,CAAC6F,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACpD,GAAG,KAAKF,KAAK,CAACE,GAAG,CAAC;YAChE,oBACExD,OAAA;cAEE4J,SAAS,EAAE,cAAclD,UAAU,GAAG,UAAU,GAAG,EAAE,EAAG;cACxDwD,OAAO,EAAEA,CAAA,KAAMzD,iBAAiB,CAACnD,KAAK,CAAE;cAAAuG,QAAA,gBAExC7J,OAAA;gBAAK4J,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,eAC7B7J,OAAA;kBACEiE,IAAI,EAAC,UAAU;kBACfwI,OAAO,EAAE/F,UAAW;kBACpBoE,QAAQ,EAAEA,CAAA,KAAMrE,iBAAiB,CAACnD,KAAK;gBAAE;kBAAAwG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1C;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNjK,OAAA;gBAAK4J,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC5B7J,OAAA;kBAAK4J,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAEvG,KAAK,CAAC8F;gBAAK;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC/CjK,OAAA;kBAAK4J,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzB7J,OAAA;oBAAM4J,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAEvG,KAAK,CAACW;kBAAI;oBAAA6F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,EAC/C3G,KAAK,CAAC4H,QAAQ,iBAAIlL,OAAA;oBAAM4J,SAAS,EAAC,gBAAgB;oBAAAC,QAAA,EAAC;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,EAClE3G,KAAK,CAACoJ,WAAW,iBAAI1M,OAAA;oBAAM4J,SAAS,EAAC,mBAAmB;oBAAAC,QAAA,EAAC;kBAAW;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA,GAlBD3G,KAAK,CAACE,GAAG;cAAAsG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAmBX,CAAC;UAEV,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGL3I,eAAe,IAAIE,WAAW,iBAC7BxB,OAAA,CAACH,gBAAgB;MACfyD,KAAK,EAAE9B,WAAY;MACnBtB,MAAM,EAAE+G,qBAAsB;MAC9B9G,QAAQ,EAAEA,CAAA,KAAM;QACdoB,kBAAkB,CAAC,KAAK,CAAC;QACzBE,cAAc,CAAC,IAAI,CAAC;MACtB;IAAE;MAAAqI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACF,EAEA7I,WAAW,iBACVpB,OAAA,CAACF,WAAW;MACVmD,MAAM,EAAEnC,cAAe;MACvBN,QAAQ,EAAEA,QAAS;MACnBmM,OAAO,EAAEA,CAAA,KAAMtL,cAAc,CAAC,KAAK;IAAE;MAAAyI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtC,CACF;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC5J,EAAA,CAp5BIJ,WAAW;AAAA2M,EAAA,GAAX3M,WAAW;AAs5BjB,eAAeA,WAAW;AAAC,IAAA2M,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}