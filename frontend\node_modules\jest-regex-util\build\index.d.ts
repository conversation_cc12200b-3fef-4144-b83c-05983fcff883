/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */
export declare const escapePathForRegex: (dir: string) => string;

export declare const escapeStrForRegex: (string: string) => string;

export declare const replacePathSepForRegex: (string: string) => string;

export {};
