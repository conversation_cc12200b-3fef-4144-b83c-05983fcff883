using CrmApi.Models.ImportExport;

namespace CrmApi.Services.ImportExport
{
    public interface IImportExportService
    {
        // Import Operations
        Task<PersonImportResponse> ImportPersonsAsync(PersonImportRequest request);
        Task<PersonImportResponse> ValidateImportFileAsync(PersonImportRequest request);
        Task<PersonImportResponse> GetImportStatusAsync(string jobId);
        Task<bool> CancelImportAsync(string jobId);

        // Export Operations
        Task<PersonExportResponse> ExportPersonsAsync(PersonExportRequest request);
        Task<PersonExportResponse> GenerateImportTemplateAsync(ExportTemplateRequest request);

        // File Processing
        Task<List<PersonImportData>> ParseFileAsync(Stream fileStream, FileFormat format);
        Task<List<ImportValidationError>> ValidateImportDataAsync(List<PersonImportData> importData, PersonImportRequest? request = null);
        
        // Progress Tracking
        Task<ImportProgressUpdate> GetImportProgressAsync(string jobId);
        Task UpdateImportProgressAsync(string jobId, ImportProgressUpdate progress);

        // Cleanup Operations
        Task CleanupTempFilesAsync(DateTime olderThan);
        Task CleanupCompletedJobsAsync(DateTime olderThan);
    }
}
