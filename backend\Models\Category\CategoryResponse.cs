namespace CrmApi.Models.Category
{
    public class CategoryResponse
    {
        public int Id { get; set; }
        public int DivisionId { get; set; }
        public string Name { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
        public Division.DivisionResponse? Division { get; set; }
        public List<SubCategory.SubCategoryResponse>? SubCategories { get; set; }
    }
}
