using Microsoft.AspNetCore.Mvc;
using CrmApi.Models.SubCategory;
using CrmApi.Services.SubCategory;

namespace CrmApi.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class SubCategoriesController : ControllerBase
    {
        private readonly ISubCategoryService _subCategoryService;

        public SubCategoriesController(ISubCategoryService subCategoryService)
        {
            _subCategoryService = subCategoryService;
        }

        [HttpGet]
        public async Task<ActionResult<IEnumerable<SubCategoryResponse>>> GetSubCategories()
        {
            var subCategories = await _subCategoryService.GetAllSubCategoriesAsync();
            return Ok(subCategories);
        }

        [HttpGet("category/{categoryId}")]
        public async Task<ActionResult<IEnumerable<SubCategoryResponse>>> GetSubCategoriesByCategory(int categoryId)
        {
            var subCategories = await _subCategoryService.GetSubCategoriesByCategoryAsync(categoryId);
            return Ok(subCategories);
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<SubCategoryResponse>> GetSubCategory(int id)
        {
            var subCategory = await _subCategoryService.GetSubCategoryByIdAsync(id);

            if (subCategory == null)
            {
                return NotFound();
            }

            return Ok(subCategory);
        }

        [HttpPost]
        public async Task<ActionResult<SubCategoryResponse>> PostSubCategory([FromBody] CreateSubCategoryRequest request)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var subCategory = await _subCategoryService.CreateSubCategoryAsync(request);
            return CreatedAtAction("GetSubCategory", new { id = subCategory.Id }, subCategory);
        }

        [HttpPut("{id}")]
        public async Task<ActionResult<SubCategoryResponse>> PutSubCategory(int id, [FromBody] UpdateSubCategoryRequest request)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var subCategory = await _subCategoryService.UpdateSubCategoryAsync(id, request);
            return Ok(subCategory);
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteSubCategory(int id)
        {
            var result = await _subCategoryService.DeleteSubCategoryAsync(id);
            if (!result)
            {
                return NotFound();
            }

            return NoContent();
        }
    }
}