import React, { useState } from 'react';
import FormBuilder from './forms/FormBuilder';
import DynamicPersonForm from './forms/DynamicPersonForm';
import PersonList from './PersonList';
import ImportPersons from './import/ImportPersons';
import formConfigService from '../services/formConfigService';
import './PersonManagement.css';

const PersonManagement = () => {
  const [currentView, setCurrentView] = useState('list'); // 'list', 'create', 'edit', 'formBuilder', 'import'
  const [selectedPerson, setSelectedPerson] = useState(null);
  const [notification, setNotification] = useState(null);
  const [showImportModal, setShowImportModal] = useState(false);

  const showNotification = (message, type = 'success') => {
    setNotification({ message, type });
    setTimeout(() => setNotification(null), 5000);
  };

  const handleCreatePerson = () => {
    setSelectedPerson(null);
    setCurrentView('create');
  };

  const handleEditPerson = (person) => {
    setSelectedPerson(person);
    setCurrentView('edit');
  };

  const handlePersonSubmit = (result) => {
    const action = currentView === 'create' ? 'created' : 'updated';
    showNotification(`Person ${action} successfully!`);
    setCurrentView('list');
    setSelectedPerson(null);
  };

  const handleFormBuilderOpen = () => {
    setCurrentView('formBuilder');
  };

  const handleFormBuilderSave = (config) => {
    showNotification(`Form configuration "${config.name}" saved successfully!`);
    setCurrentView('list');
  };

  const handleImportOpen = () => {
    setShowImportModal(true);
  };

  const handleImportSuccess = (results) => {
    showNotification(`Import completed! ${results.successfulRows} persons imported successfully.`);
    setShowImportModal(false);
    // Refresh the person list if we're on the list view
    if (currentView === 'list') {
      // The PersonList component should handle refreshing
    }
  };

  const handleImportClose = () => {
    setShowImportModal(false);
  };

  const handleCancel = () => {
    setCurrentView('list');
    setSelectedPerson(null);
  };

  const getFormStatistics = () => {
    return formConfigService.getFormStatistics();
  };

  const renderCurrentView = () => {
    switch (currentView) {
      case 'create':
        return (
          <DynamicPersonForm
            mode="create"
            onSubmit={handlePersonSubmit}
            onCancel={handleCancel}
          />
        );
      
      case 'edit':
        return (
          <DynamicPersonForm
            mode="edit"
            initialData={selectedPerson}
            onSubmit={handlePersonSubmit}
            onCancel={handleCancel}
          />
        );
      
      case 'formBuilder':
        return (
          <FormBuilder
            onSave={handleFormBuilderSave}
            onCancel={handleCancel}
          />
        );
      
      case 'list':
      default:
        return (
          <PersonList
            onCreatePerson={handleCreatePerson}
            onEditPerson={handleEditPerson}
            onFormBuilder={handleFormBuilderOpen}
            onImportPersons={handleImportOpen}
          />
        );
    }
  };

  const statistics = getFormStatistics();

  return (
    <div className="person-management">
      {/* Header */}
      <div className="management-header">
        <div className="header-content">
          <h1>Person Management System</h1>
          <div className="header-stats">
            <div className="stat-item">
              <span className="stat-label">Total Forms:</span>
              <span className="stat-value">{statistics.totalForms}</span>
            </div>
            <div className="stat-item">
              <span className="stat-label">Category Forms:</span>
              <span className="stat-value">{statistics.categoryForms}</span>
            </div>
            <div className="stat-item">
              <span className="stat-label">SubCategory Forms:</span>
              <span className="stat-value">{statistics.subCategoryForms}</span>
            </div>
          </div>
        </div>
        
        {/* Navigation */}
        <div className="header-nav">
          <button
            onClick={() => setCurrentView('list')}
            className={`nav-btn ${currentView === 'list' ? 'active' : ''}`}
          >
            📋 Person List
          </button>
          <button
            onClick={handleCreatePerson}
            className={`nav-btn ${currentView === 'create' ? 'active' : ''}`}
          >
            ➕ Create Person
          </button>
          <button
            onClick={handleFormBuilderOpen}
            className={`nav-btn ${currentView === 'formBuilder' ? 'active' : ''}`}
          >
            🔧 Form Builder
          </button>
          <button
            onClick={handleImportOpen}
            className="nav-btn"
          >
            📥 Import Persons
          </button>
        </div>
      </div>

      {/* Notification */}
      {notification && (
        <div className={`notification ${notification.type}`}>
          <span>{notification.message}</span>
          <button onClick={() => setNotification(null)} className="notification-close">
            ×
          </button>
        </div>
      )}

      {/* Main Content */}
      <div className="management-content">
        {renderCurrentView()}
      </div>

      {/* Footer */}
      <div className="management-footer">
        <div className="footer-content">
          <div className="footer-section">
            <h4>Quick Actions</h4>
            <div className="quick-actions">
              <button onClick={handleCreatePerson} className="quick-action-btn">
                Create New Person
              </button>
              <button onClick={handleFormBuilderOpen} className="quick-action-btn">
                Design Custom Form
              </button>
              <button onClick={handleImportOpen} className="quick-action-btn">
                Import Persons from File
              </button>
            </div>
          </div>
          
          <div className="footer-section">
            <h4>Form Statistics</h4>
            <div className="footer-stats">
              <div className="footer-stat">
                <span>Average Fields per Form:</span>
                <span>{statistics.averageFieldCount}</span>
              </div>
              <div className="footer-stat">
                <span>Most Used Fields:</span>
                <span>{statistics.mostUsedFields.slice(0, 3).map(f => f.field).join(', ')}</span>
              </div>
            </div>
          </div>
          
          <div className="footer-section">
            <h4>System Info</h4>
            <div className="footer-info">
              <div>Dynamic Form Builder v1.0</div>
              <div>React.js Frontend</div>
              <div>Local Storage Configuration</div>
            </div>
          </div>
        </div>
      </div>

      {/* Import Modal */}
      {showImportModal && (
        <ImportPersons
          onClose={handleImportClose}
          onSuccess={handleImportSuccess}
        />
      )}
    </div>
  );
};

export default PersonManagement;
