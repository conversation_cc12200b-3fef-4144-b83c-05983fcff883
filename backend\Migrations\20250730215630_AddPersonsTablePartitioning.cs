﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace CrmApi.Migrations
{
    /// <inheritdoc />
    public partial class AddPersonsTablePartitioning : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Step 1: Drop the existing persons table if it exists
            migrationBuilder.Sql(@"
                DROP TABLE IF EXISTS persons;
            ");

            // Step 2: Create the partitioned persons table (without foreign keys - MySQL limitation)
            migrationBuilder.Sql(@"
                CREATE TABLE persons (
                    id int NOT NULL AUTO_INCREMENT,
                    division_id int NOT NULL,
                    category_id int NOT NULL,
                    sub_category_id int NULL,
                    name varchar(255) CHARACTER SET utf8mb4 NOT NULL,
                    mobile_number varchar(15) CHARACTER SET utf8mb4 NOT NULL,
                    nature int NOT NULL,
                    gender int NULL,
                    alternate_numbers longtext CHARACTER SET utf8mb4 NOT NULL,
                    primary_email_id varchar(255) CHARACTER SET utf8mb4 NOT NULL,
                    alternate_email_ids longtext CHARACTER SET utf8mb4 NOT NULL,
                    website varchar(500) CHARACTER SET utf8mb4 NOT NULL,
                    date_of_birth datetime(6) NULL,
                    is_married tinyint(1) NULL,
                    date_of_marriage datetime(6) NULL,
                    working_state varchar(100) CHARACTER SET utf8mb4 NOT NULL,
                    domestic_state varchar(100) CHARACTER SET utf8mb4 NOT NULL,
                    district varchar(100) CHARACTER SET utf8mb4 NOT NULL,
                    address varchar(500) CHARACTER SET utf8mb4 NOT NULL,
                    working_area varchar(200) CHARACTER SET utf8mb4 NOT NULL,
                    has_associate tinyint(1) NOT NULL,
                    associate_name varchar(255) CHARACTER SET utf8mb4 NOT NULL,
                    associate_relation varchar(100) CHARACTER SET utf8mb4 NOT NULL,
                    associate_mobile varchar(15) CHARACTER SET utf8mb4 NOT NULL,
                    using_website tinyint(1) NOT NULL,
                    website_link varchar(500) CHARACTER SET utf8mb4 NOT NULL,
                    using_crm_app tinyint(1) NOT NULL,
                    crm_app_link varchar(500) CHARACTER SET utf8mb4 NOT NULL,
                    transaction_value decimal(18,2) NULL,
                    rera_registration_number varchar(50) CHARACTER SET utf8mb4 NOT NULL,
                    working_profiles longtext CHARACTER SET utf8mb4 NOT NULL,
                    star_rating int NULL,
                    source varchar(200) CHARACTER SET utf8mb4 NOT NULL,
                    remarks varchar(1000) CHARACTER SET utf8mb4 NOT NULL,
                    firm_name varchar(255) CHARACTER SET utf8mb4 NOT NULL,
                    number_of_offices int NULL,
                    number_of_branches int NULL,
                    total_employee_strength int NULL,
                    authorized_person_name varchar(255) CHARACTER SET utf8mb4 NOT NULL,
                    authorized_person_email varchar(255) CHARACTER SET utf8mb4 NOT NULL,
                    designation varchar(100) CHARACTER SET utf8mb4 NOT NULL,
                    marketing_contact varchar(255) CHARACTER SET utf8mb4 NOT NULL,
                    marketing_designation varchar(100) CHARACTER SET utf8mb4 NOT NULL,
                    place_of_posting varchar(200) CHARACTER SET utf8mb4 NOT NULL,
                    department varchar(100) CHARACTER SET utf8mb4 NOT NULL,
                    created_at datetime(6) NOT NULL,
                    updated_at datetime(6) NOT NULL,
                    is_deleted tinyint(1) NOT NULL,
                    deleted_at datetime(6) NULL,

                    PRIMARY KEY (id, division_id, category_id)

                ) CHARACTER SET=utf8mb4
                PARTITION BY HASH(division_id * 100 + category_id)
                PARTITIONS 20;
            ");

            // Step 3: Recreate indexes on the partitioned table
            migrationBuilder.Sql(@"
                CREATE INDEX IX_Persons_CategoryId ON persons(category_id);
                CREATE INDEX IX_Persons_CreatedAt ON persons(created_at);
                CREATE INDEX IX_Persons_District ON persons(district);
                CREATE INDEX IX_Persons_Division_Category ON persons(division_id, category_id);
                CREATE INDEX IX_Persons_Division_Category_Mobile ON persons(division_id, category_id, mobile_number);
                CREATE INDEX IX_Persons_DivisionId ON persons(division_id);
                CREATE INDEX IX_Persons_IsDeleted ON persons(is_deleted);
                CREATE INDEX IX_Persons_IsDeleted_CreatedAt ON persons(is_deleted, created_at);
                CREATE INDEX IX_Persons_MobileNumber ON persons(mobile_number);
                CREATE INDEX IX_Persons_Name ON persons(name);
                CREATE INDEX IX_Persons_Nature ON persons(nature);
                CREATE INDEX IX_Persons_Nature_IsDeleted ON persons(nature, is_deleted);
                CREATE INDEX IX_Persons_PrimaryEmailId ON persons(primary_email_id);
                CREATE INDEX IX_Persons_StarRating ON persons(star_rating);
                CREATE INDEX IX_Persons_SubCategoryId ON persons(sub_category_id);
                CREATE INDEX IX_Persons_WorkingState ON persons(working_state);
                CREATE UNIQUE INDEX UX_Persons_Mobile_Division_Category_NotDeleted
                    ON persons(mobile_number, division_id, category_id, is_deleted);
            ");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // Step 1: Create backup of partitioned data
            migrationBuilder.Sql(@"
                CREATE TABLE persons_partition_backup AS SELECT * FROM persons;
            ");

            // Step 2: Drop partitioned table
            migrationBuilder.Sql(@"
                DROP TABLE persons;
            ");

            // Step 3: Recreate non-partitioned table (same as original structure)
            migrationBuilder.Sql(@"
                CREATE TABLE persons (
                    id int NOT NULL AUTO_INCREMENT,
                    division_id int NOT NULL,
                    category_id int NOT NULL,
                    sub_category_id int NULL,
                    name varchar(255) CHARACTER SET utf8mb4 NOT NULL,
                    mobile_number varchar(15) CHARACTER SET utf8mb4 NOT NULL,
                    nature int NOT NULL,
                    gender int NULL,
                    alternate_numbers longtext CHARACTER SET utf8mb4 NOT NULL,
                    primary_email_id varchar(255) CHARACTER SET utf8mb4 NOT NULL,
                    alternate_email_ids longtext CHARACTER SET utf8mb4 NOT NULL,
                    website varchar(500) CHARACTER SET utf8mb4 NOT NULL,
                    date_of_birth datetime(6) NULL,
                    is_married tinyint(1) NULL,
                    date_of_marriage datetime(6) NULL,
                    working_state varchar(100) CHARACTER SET utf8mb4 NOT NULL,
                    domestic_state varchar(100) CHARACTER SET utf8mb4 NOT NULL,
                    district varchar(100) CHARACTER SET utf8mb4 NOT NULL,
                    address varchar(500) CHARACTER SET utf8mb4 NOT NULL,
                    working_area varchar(200) CHARACTER SET utf8mb4 NOT NULL,
                    has_associate tinyint(1) NOT NULL,
                    associate_name varchar(255) CHARACTER SET utf8mb4 NOT NULL,
                    associate_relation varchar(100) CHARACTER SET utf8mb4 NOT NULL,
                    associate_mobile varchar(15) CHARACTER SET utf8mb4 NOT NULL,
                    using_website tinyint(1) NOT NULL,
                    website_link varchar(500) CHARACTER SET utf8mb4 NOT NULL,
                    using_crm_app tinyint(1) NOT NULL,
                    crm_app_link varchar(500) CHARACTER SET utf8mb4 NOT NULL,
                    transaction_value decimal(18,2) NULL,
                    rera_registration_number varchar(50) CHARACTER SET utf8mb4 NOT NULL,
                    working_profiles longtext CHARACTER SET utf8mb4 NOT NULL,
                    star_rating int NULL,
                    source varchar(200) CHARACTER SET utf8mb4 NOT NULL,
                    remarks varchar(1000) CHARACTER SET utf8mb4 NOT NULL,
                    firm_name varchar(255) CHARACTER SET utf8mb4 NOT NULL,
                    number_of_offices int NULL,
                    number_of_branches int NULL,
                    total_employee_strength int NULL,
                    authorized_person_name varchar(255) CHARACTER SET utf8mb4 NOT NULL,
                    authorized_person_email varchar(255) CHARACTER SET utf8mb4 NOT NULL,
                    designation varchar(100) CHARACTER SET utf8mb4 NOT NULL,
                    marketing_contact varchar(255) CHARACTER SET utf8mb4 NOT NULL,
                    marketing_designation varchar(100) CHARACTER SET utf8mb4 NOT NULL,
                    place_of_posting varchar(200) CHARACTER SET utf8mb4 NOT NULL,
                    department varchar(100) CHARACTER SET utf8mb4 NOT NULL,
                    created_at datetime(6) NOT NULL,
                    updated_at datetime(6) NOT NULL,
                    is_deleted tinyint(1) NOT NULL,
                    deleted_at datetime(6) NULL,

                    PRIMARY KEY (id)

                ) CHARACTER SET=utf8mb4;
            ");

            // Step 4: Restore data
            migrationBuilder.Sql(@"
                INSERT INTO persons SELECT * FROM persons_partition_backup;
            ");

            // Step 5: Recreate original indexes
            migrationBuilder.Sql(@"
                CREATE INDEX IX_Persons_CategoryId ON persons(category_id);
                CREATE INDEX IX_Persons_CreatedAt ON persons(created_at);
                CREATE INDEX IX_Persons_District ON persons(district);
                CREATE INDEX IX_Persons_Division_Category ON persons(division_id, category_id);
                CREATE INDEX IX_Persons_Division_Category_Mobile ON persons(division_id, category_id, mobile_number);
                CREATE INDEX IX_Persons_DivisionId ON persons(division_id);
                CREATE INDEX IX_Persons_IsDeleted ON persons(is_deleted);
                CREATE INDEX IX_Persons_IsDeleted_CreatedAt ON persons(is_deleted, created_at);
                CREATE INDEX IX_Persons_MobileNumber ON persons(mobile_number);
                CREATE INDEX IX_Persons_Name ON persons(name);
                CREATE INDEX IX_Persons_Nature ON persons(nature);
                CREATE INDEX IX_Persons_Nature_IsDeleted ON persons(nature, is_deleted);
                CREATE INDEX IX_Persons_PrimaryEmailId ON persons(primary_email_id);
                CREATE INDEX IX_Persons_StarRating ON persons(star_rating);
                CREATE INDEX IX_Persons_SubCategoryId ON persons(sub_category_id);
                CREATE INDEX IX_Persons_WorkingState ON persons(working_state);
                CREATE UNIQUE INDEX UX_Persons_Mobile_Division_Category_NotDeleted
                    ON persons(mobile_number, division_id, category_id, is_deleted);
            ");

            // Step 6: Drop backup table
            migrationBuilder.Sql(@"
                DROP TABLE persons_partition_backup;
            ");
        }
    }
}
