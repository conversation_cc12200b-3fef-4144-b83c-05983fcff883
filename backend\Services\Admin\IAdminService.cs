using CrmApi.Models.Admin;

namespace CrmApi.Services.Admin
{
    public interface IAdminService
    {
        Task<IEnumerable<AdminResponse>> GetAllAdminsAsync();
        Task<AdminResponse?> GetAdminByIdAsync(int id);
        Task<AdminResponse> CreateAdminAsync(CreateAdminRequest request);
        Task<AdminResponse> UpdateAdminAsync(int id, UpdateAdminRequest request);
        Task<bool> DeleteAdminAsync(int id);
        Task<LoginResponse?> LoginAsync(LoginRequest request);
        Task<bool> ValidateTokenAsync(string token);
    }
}
