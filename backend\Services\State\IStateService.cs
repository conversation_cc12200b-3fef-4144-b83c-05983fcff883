using CrmApi.Models.State;

namespace CrmApi.Services.State
{
    public interface IStateService
    {
        Task<IEnumerable<StateResponse>> GetAllStatesAsync();
        Task<StateResponse?> GetStateByIdAsync(int id);
        Task<StateResponse> CreateStateAsync(CreateStateRequest request);
        Task<StateResponse> UpdateStateAsync(int id, UpdateStateRequest request);
        Task<bool> DeleteStateAsync(int id);
    }
}
