using Microsoft.EntityFrameworkCore;
using CrmApi.Data;
using CrmApi.Models.Admin;

namespace CrmApi.Repositories.Admin
{
    public class AdminRepository : IAdminRepository
    {
        private readonly CrmDbContext _context;

        public AdminRepository(CrmDbContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<Models.Admin.Admin>> GetAllAsync()
        {
            return await _context.Admins.ToListAsync();
        }

        public async Task<Models.Admin.Admin?> GetByIdAsync(int id)
        {
            return await _context.Admins.FindAsync(id);
        }

        public async Task<Models.Admin.Admin?> GetByUsernameAsync(string username)
        {
            return await _context.Admins
                .FirstOrDefaultAsync(a => a.Username == username);
        }

        public async Task<Models.Admin.Admin?> GetByEmailAsync(string email)
        {
            return await _context.Admins
                .FirstOrDefaultAsync(a => a.Email == email);
        }

        public async Task<Models.Admin.Admin> CreateAsync(Models.Admin.Admin admin)
        {
            admin.CreatedAt = DateTime.UtcNow;
            admin.UpdatedAt = DateTime.UtcNow;
            
            _context.Admins.Add(admin);
            await _context.SaveChangesAsync();
            return admin;
        }

        public async Task<Models.Admin.Admin> UpdateAsync(Models.Admin.Admin admin)
        {
            admin.UpdatedAt = DateTime.UtcNow;
            _context.Entry(admin).State = EntityState.Modified;
            await _context.SaveChangesAsync();
            return admin;
        }

        public async Task<bool> DeleteAsync(int id)
        {
            var admin = await _context.Admins.FindAsync(id);
            if (admin == null)
                return false;

            _context.Admins.Remove(admin);
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> ExistsAsync(int id)
        {
            return await _context.Admins.AnyAsync(a => a.Id == id);
        }

        public async Task<bool> UsernameExistsAsync(string username, int? excludeId = null)
        {
            var query = _context.Admins.Where(a => a.Username == username);
            if (excludeId.HasValue)
                query = query.Where(a => a.Id != excludeId.Value);
            
            return await query.AnyAsync();
        }

        public async Task<bool> EmailExistsAsync(string email, int? excludeId = null)
        {
            var query = _context.Admins.Where(a => a.Email == email);
            if (excludeId.HasValue)
                query = query.Where(a => a.Id != excludeId.Value);
            
            return await query.AnyAsync();
        }
    }
}
