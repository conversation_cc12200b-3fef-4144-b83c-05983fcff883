using CrmApi.Models.SubCategory;

namespace CrmApi.Repositories.SubCategory
{
    public interface ISubCategoryRepository
    {
        Task<IEnumerable<Models.SubCategory.SubCategory>> GetAllAsync();
        Task<IEnumerable<Models.SubCategory.SubCategory>> GetAllWithRelationsAsync();
        Task<Models.SubCategory.SubCategory?> GetByIdAsync(int id);
        Task<Models.SubCategory.SubCategory?> GetByIdWithRelationsAsync(int id);
        Task<IEnumerable<Models.SubCategory.SubCategory>> GetByCategoryIdAsync(int categoryId);
        Task<Models.SubCategory.SubCategory> CreateAsync(Models.SubCategory.SubCategory subCategory);
        Task<Models.SubCategory.SubCategory> UpdateAsync(Models.SubCategory.SubCategory subCategory);
        Task<bool> DeleteAsync(int id);
        Task<bool> ExistsAsync(int id);
        Task<bool> NameExistsInCategoryAsync(string name, int categoryId, int? excludeId = null);
    }
}
