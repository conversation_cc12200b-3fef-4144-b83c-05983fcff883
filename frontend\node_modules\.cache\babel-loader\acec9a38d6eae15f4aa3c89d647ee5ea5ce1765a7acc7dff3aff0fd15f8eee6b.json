{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Advanced\\\\Advanced CRM filed all use\\\\frontend\\\\src\\\\components\\\\PersonManagement.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport FormBuilder from './forms/FormBuilder';\nimport DynamicPersonForm from './forms/DynamicPersonForm';\nimport FormSelectionView from './forms/FormSelectionView';\nimport PersonList from './PersonList';\nimport ImportPersons from './import/ImportPersons';\nimport formConfigService from '../services/formConfigService';\nimport './PersonManagement.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PersonManagement = () => {\n  _s();\n  const [currentView, setCurrentView] = useState('list'); // 'list', 'formSelection', 'create', 'edit', 'formBuilder', 'import'\n  const [selectedPerson, setSelectedPerson] = useState(null);\n  const [selectedForm, setSelectedForm] = useState(null);\n  const [notification, setNotification] = useState(null);\n  const [showImportModal, setShowImportModal] = useState(false);\n  const showNotification = (message, type = 'success') => {\n    setNotification({\n      message,\n      type\n    });\n    setTimeout(() => setNotification(null), 5000);\n  };\n  const handleCreatePerson = () => {\n    setSelectedPerson(null);\n    setSelectedForm(null);\n    setCurrentView('formSelection');\n  };\n  const handleFormSelect = form => {\n    setSelectedForm(form);\n    setCurrentView('create');\n  };\n  const handleEditPerson = person => {\n    setSelectedPerson(person);\n    setCurrentView('edit');\n  };\n  const handlePersonSubmit = result => {\n    const action = currentView === 'create' ? 'created' : 'updated';\n    showNotification(`Person ${action} successfully!`);\n    setCurrentView('list');\n    setSelectedPerson(null);\n  };\n  const handleFormBuilderOpen = () => {\n    setCurrentView('formBuilder');\n  };\n  const handleFormBuilderSave = config => {\n    showNotification(`Form configuration \"${config.name}\" saved successfully!`);\n    setCurrentView('list');\n  };\n  const handleImportOpen = () => {\n    setShowImportModal(true);\n  };\n  const handleImportSuccess = results => {\n    showNotification(`Import completed! ${results.successfulRows} persons imported successfully.`);\n    setShowImportModal(false);\n    // Refresh the person list if we're on the list view\n    if (currentView === 'list') {\n      // The PersonList component should handle refreshing\n    }\n  };\n  const handleImportClose = () => {\n    setShowImportModal(false);\n  };\n  const handleCancel = () => {\n    setCurrentView('list');\n    setSelectedPerson(null);\n    setSelectedForm(null);\n  };\n  const getFormStatistics = () => {\n    return formConfigService.getFormStatistics();\n  };\n  const renderCurrentView = () => {\n    switch (currentView) {\n      case 'formSelection':\n        return /*#__PURE__*/_jsxDEV(FormSelectionView, {\n          onFormSelect: handleFormSelect,\n          onCancel: handleCancel\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 11\n        }, this);\n      case 'create':\n        return /*#__PURE__*/_jsxDEV(DynamicPersonForm, {\n          mode: \"create\",\n          selectedForm: selectedForm,\n          onSubmit: handlePersonSubmit,\n          onCancel: handleCancel\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 93,\n          columnNumber: 11\n        }, this);\n      case 'edit':\n        return /*#__PURE__*/_jsxDEV(DynamicPersonForm, {\n          mode: \"edit\",\n          initialData: selectedPerson,\n          onSubmit: handlePersonSubmit,\n          onCancel: handleCancel\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 11\n        }, this);\n      case 'formBuilder':\n        return /*#__PURE__*/_jsxDEV(FormBuilder, {\n          onSave: handleFormBuilderSave,\n          onCancel: handleCancel\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 113,\n          columnNumber: 11\n        }, this);\n      case 'list':\n      default:\n        return /*#__PURE__*/_jsxDEV(PersonList, {\n          onCreatePerson: handleCreatePerson,\n          onEditPerson: handleEditPerson,\n          onFormBuilder: handleFormBuilderOpen,\n          onImportPersons: handleImportOpen\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 122,\n          columnNumber: 11\n        }, this);\n    }\n  };\n  const statistics = getFormStatistics();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"person-management\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"management-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          children: \"Person Management System\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"header-stats\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"stat-label\",\n              children: \"Total Forms:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 142,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"stat-value\",\n              children: statistics.totalForms\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 143,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"stat-label\",\n              children: \"Category Forms:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 146,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"stat-value\",\n              children: statistics.categoryForms\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 147,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 145,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"stat-label\",\n              children: \"SubCategory Forms:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 150,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"stat-value\",\n              children: statistics.subCategoryForms\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 149,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 138,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-nav\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setCurrentView('list'),\n          className: `nav-btn ${currentView === 'list' ? 'active' : ''}`,\n          children: \"\\uD83D\\uDCCB Person List\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleCreatePerson,\n          className: `nav-btn ${currentView === 'formSelection' || currentView === 'create' ? 'active' : ''}`,\n          children: \"\\u2795 Create Person\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 164,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleFormBuilderOpen,\n          className: `nav-btn ${currentView === 'formBuilder' ? 'active' : ''}`,\n          children: \"\\uD83D\\uDD27 Form Builder\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleImportOpen,\n          className: \"nav-btn\",\n          children: \"\\uD83D\\uDCE5 Import Persons\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 176,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 157,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 137,\n      columnNumber: 7\n    }, this), notification && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `notification ${notification.type}`,\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        children: notification.message\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 188,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => setNotification(null),\n        className: \"notification-close\",\n        children: \"\\xD7\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 189,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 187,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"management-content\",\n      children: renderCurrentView()\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 196,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"management-footer\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"footer-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"footer-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            children: \"Quick Actions\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 204,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"quick-actions\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleCreatePerson,\n              className: \"quick-action-btn\",\n              children: \"Create New Person\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 206,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleFormBuilderOpen,\n              className: \"quick-action-btn\",\n              children: \"Design Custom Form\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 209,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleImportOpen,\n              className: \"quick-action-btn\",\n              children: \"Import Persons from File\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 212,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"footer-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            children: \"Form Statistics\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 219,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"footer-stats\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"footer-stat\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Average Fields per Form:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 222,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: statistics.averageFieldCount\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 223,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 221,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"footer-stat\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Most Used Fields:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 226,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: statistics.mostUsedFields.slice(0, 3).map(f => f.field).join(', ')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 227,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 225,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 220,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 218,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"footer-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            children: \"System Info\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"footer-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: \"Dynamic Form Builder v1.0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 235,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: \"React.js Frontend\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 236,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: \"Local Storage Configuration\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 237,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 234,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 232,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 202,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 201,\n      columnNumber: 7\n    }, this), showImportModal && /*#__PURE__*/_jsxDEV(ImportPersons, {\n      onClose: handleImportClose,\n      onSuccess: handleImportSuccess\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 245,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 135,\n    columnNumber: 5\n  }, this);\n};\n_s(PersonManagement, \"umKcPDXZ4TwGAjAYOQLpDXZt9bM=\");\n_c = PersonManagement;\nexport default PersonManagement;\nvar _c;\n$RefreshReg$(_c, \"PersonManagement\");", "map": {"version": 3, "names": ["React", "useState", "FormBuilder", "DynamicPersonForm", "FormSelectionView", "PersonList", "<PERSON><PERSON>rt<PERSON><PERSON><PERSON>", "formConfigService", "jsxDEV", "_jsxDEV", "PersonManagement", "_s", "current<PERSON>iew", "set<PERSON><PERSON><PERSON>View", "<PERSON><PERSON><PERSON>", "setSelected<PERSON><PERSON>", "selectedForm", "setSelectedForm", "notification", "setNotification", "showImportModal", "setShowImportModal", "showNotification", "message", "type", "setTimeout", "handleCreate<PERSON>erson", "handleFormSelect", "form", "handleEditPerson", "person", "handlePersonSubmit", "result", "action", "handleFormBuilderOpen", "handleFormBuilderSave", "config", "name", "handleImportOpen", "handleImportSuccess", "results", "successfulRows", "handleImportClose", "handleCancel", "getFormStatistics", "renderCurrentView", "onFormSelect", "onCancel", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "mode", "onSubmit", "initialData", "onSave", "on<PERSON><PERSON><PERSON><PERSON>", "onEdit<PERSON>erson", "onFormBuilder", "onImport<PERSON>ersons", "statistics", "className", "children", "totalForms", "categoryForms", "subCategoryForms", "onClick", "averageFieldCount", "mostUsedFields", "slice", "map", "f", "field", "join", "onClose", "onSuccess", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/Advanced/Advanced CRM filed all use/frontend/src/components/PersonManagement.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport FormBuilder from './forms/FormBuilder';\nimport DynamicPersonForm from './forms/DynamicPersonForm';\nimport FormSelectionView from './forms/FormSelectionView';\nimport PersonList from './PersonList';\nimport ImportPersons from './import/ImportPersons';\nimport formConfigService from '../services/formConfigService';\nimport './PersonManagement.css';\n\nconst PersonManagement = () => {\n  const [currentView, setCurrentView] = useState('list'); // 'list', 'formSelection', 'create', 'edit', 'formBuilder', 'import'\n  const [selectedPerson, setSelectedPerson] = useState(null);\n  const [selectedForm, setSelectedForm] = useState(null);\n  const [notification, setNotification] = useState(null);\n  const [showImportModal, setShowImportModal] = useState(false);\n\n  const showNotification = (message, type = 'success') => {\n    setNotification({ message, type });\n    setTimeout(() => setNotification(null), 5000);\n  };\n\n  const handleCreatePerson = () => {\n    setSelectedPerson(null);\n    setSelectedForm(null);\n    setCurrentView('formSelection');\n  };\n\n  const handleFormSelect = (form) => {\n    setSelectedForm(form);\n    setCurrentView('create');\n  };\n\n  const handleEditPerson = (person) => {\n    setSelectedPerson(person);\n    setCurrentView('edit');\n  };\n\n  const handlePersonSubmit = (result) => {\n    const action = currentView === 'create' ? 'created' : 'updated';\n    showNotification(`Person ${action} successfully!`);\n    setCurrentView('list');\n    setSelectedPerson(null);\n  };\n\n  const handleFormBuilderOpen = () => {\n    setCurrentView('formBuilder');\n  };\n\n  const handleFormBuilderSave = (config) => {\n    showNotification(`Form configuration \"${config.name}\" saved successfully!`);\n    setCurrentView('list');\n  };\n\n  const handleImportOpen = () => {\n    setShowImportModal(true);\n  };\n\n  const handleImportSuccess = (results) => {\n    showNotification(`Import completed! ${results.successfulRows} persons imported successfully.`);\n    setShowImportModal(false);\n    // Refresh the person list if we're on the list view\n    if (currentView === 'list') {\n      // The PersonList component should handle refreshing\n    }\n  };\n\n  const handleImportClose = () => {\n    setShowImportModal(false);\n  };\n\n  const handleCancel = () => {\n    setCurrentView('list');\n    setSelectedPerson(null);\n    setSelectedForm(null);\n  };\n\n  const getFormStatistics = () => {\n    return formConfigService.getFormStatistics();\n  };\n\n  const renderCurrentView = () => {\n    switch (currentView) {\n      case 'formSelection':\n        return (\n          <FormSelectionView\n            onFormSelect={handleFormSelect}\n            onCancel={handleCancel}\n          />\n        );\n\n      case 'create':\n        return (\n          <DynamicPersonForm\n            mode=\"create\"\n            selectedForm={selectedForm}\n            onSubmit={handlePersonSubmit}\n            onCancel={handleCancel}\n          />\n        );\n      \n      case 'edit':\n        return (\n          <DynamicPersonForm\n            mode=\"edit\"\n            initialData={selectedPerson}\n            onSubmit={handlePersonSubmit}\n            onCancel={handleCancel}\n          />\n        );\n      \n      case 'formBuilder':\n        return (\n          <FormBuilder\n            onSave={handleFormBuilderSave}\n            onCancel={handleCancel}\n          />\n        );\n      \n      case 'list':\n      default:\n        return (\n          <PersonList\n            onCreatePerson={handleCreatePerson}\n            onEditPerson={handleEditPerson}\n            onFormBuilder={handleFormBuilderOpen}\n            onImportPersons={handleImportOpen}\n          />\n        );\n    }\n  };\n\n  const statistics = getFormStatistics();\n\n  return (\n    <div className=\"person-management\">\n      {/* Header */}\n      <div className=\"management-header\">\n        <div className=\"header-content\">\n          <h1>Person Management System</h1>\n          <div className=\"header-stats\">\n            <div className=\"stat-item\">\n              <span className=\"stat-label\">Total Forms:</span>\n              <span className=\"stat-value\">{statistics.totalForms}</span>\n            </div>\n            <div className=\"stat-item\">\n              <span className=\"stat-label\">Category Forms:</span>\n              <span className=\"stat-value\">{statistics.categoryForms}</span>\n            </div>\n            <div className=\"stat-item\">\n              <span className=\"stat-label\">SubCategory Forms:</span>\n              <span className=\"stat-value\">{statistics.subCategoryForms}</span>\n            </div>\n          </div>\n        </div>\n        \n        {/* Navigation */}\n        <div className=\"header-nav\">\n          <button\n            onClick={() => setCurrentView('list')}\n            className={`nav-btn ${currentView === 'list' ? 'active' : ''}`}\n          >\n            📋 Person List\n          </button>\n          <button\n            onClick={handleCreatePerson}\n            className={`nav-btn ${currentView === 'formSelection' || currentView === 'create' ? 'active' : ''}`}\n          >\n            ➕ Create Person\n          </button>\n          <button\n            onClick={handleFormBuilderOpen}\n            className={`nav-btn ${currentView === 'formBuilder' ? 'active' : ''}`}\n          >\n            🔧 Form Builder\n          </button>\n          <button\n            onClick={handleImportOpen}\n            className=\"nav-btn\"\n          >\n            📥 Import Persons\n          </button>\n        </div>\n      </div>\n\n      {/* Notification */}\n      {notification && (\n        <div className={`notification ${notification.type}`}>\n          <span>{notification.message}</span>\n          <button onClick={() => setNotification(null)} className=\"notification-close\">\n            ×\n          </button>\n        </div>\n      )}\n\n      {/* Main Content */}\n      <div className=\"management-content\">\n        {renderCurrentView()}\n      </div>\n\n      {/* Footer */}\n      <div className=\"management-footer\">\n        <div className=\"footer-content\">\n          <div className=\"footer-section\">\n            <h4>Quick Actions</h4>\n            <div className=\"quick-actions\">\n              <button onClick={handleCreatePerson} className=\"quick-action-btn\">\n                Create New Person\n              </button>\n              <button onClick={handleFormBuilderOpen} className=\"quick-action-btn\">\n                Design Custom Form\n              </button>\n              <button onClick={handleImportOpen} className=\"quick-action-btn\">\n                Import Persons from File\n              </button>\n            </div>\n          </div>\n          \n          <div className=\"footer-section\">\n            <h4>Form Statistics</h4>\n            <div className=\"footer-stats\">\n              <div className=\"footer-stat\">\n                <span>Average Fields per Form:</span>\n                <span>{statistics.averageFieldCount}</span>\n              </div>\n              <div className=\"footer-stat\">\n                <span>Most Used Fields:</span>\n                <span>{statistics.mostUsedFields.slice(0, 3).map(f => f.field).join(', ')}</span>\n              </div>\n            </div>\n          </div>\n          \n          <div className=\"footer-section\">\n            <h4>System Info</h4>\n            <div className=\"footer-info\">\n              <div>Dynamic Form Builder v1.0</div>\n              <div>React.js Frontend</div>\n              <div>Local Storage Configuration</div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Import Modal */}\n      {showImportModal && (\n        <ImportPersons\n          onClose={handleImportClose}\n          onSuccess={handleImportSuccess}\n        />\n      )}\n    </div>\n  );\n};\n\nexport default PersonManagement;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,iBAAiB,MAAM,2BAA2B;AACzD,OAAOC,iBAAiB,MAAM,2BAA2B;AACzD,OAAOC,UAAU,MAAM,cAAc;AACrC,OAAOC,aAAa,MAAM,wBAAwB;AAClD,OAAOC,iBAAiB,MAAM,+BAA+B;AAC7D,OAAO,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhC,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC7B,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGZ,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;EACxD,MAAM,CAACa,cAAc,EAAEC,iBAAiB,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACe,YAAY,EAAEC,eAAe,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACiB,YAAY,EAAEC,eAAe,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACmB,eAAe,EAAEC,kBAAkB,CAAC,GAAGpB,QAAQ,CAAC,KAAK,CAAC;EAE7D,MAAMqB,gBAAgB,GAAGA,CAACC,OAAO,EAAEC,IAAI,GAAG,SAAS,KAAK;IACtDL,eAAe,CAAC;MAAEI,OAAO;MAAEC;IAAK,CAAC,CAAC;IAClCC,UAAU,CAAC,MAAMN,eAAe,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC;EAC/C,CAAC;EAED,MAAMO,kBAAkB,GAAGA,CAAA,KAAM;IAC/BX,iBAAiB,CAAC,IAAI,CAAC;IACvBE,eAAe,CAAC,IAAI,CAAC;IACrBJ,cAAc,CAAC,eAAe,CAAC;EACjC,CAAC;EAED,MAAMc,gBAAgB,GAAIC,IAAI,IAAK;IACjCX,eAAe,CAACW,IAAI,CAAC;IACrBf,cAAc,CAAC,QAAQ,CAAC;EAC1B,CAAC;EAED,MAAMgB,gBAAgB,GAAIC,MAAM,IAAK;IACnCf,iBAAiB,CAACe,MAAM,CAAC;IACzBjB,cAAc,CAAC,MAAM,CAAC;EACxB,CAAC;EAED,MAAMkB,kBAAkB,GAAIC,MAAM,IAAK;IACrC,MAAMC,MAAM,GAAGrB,WAAW,KAAK,QAAQ,GAAG,SAAS,GAAG,SAAS;IAC/DU,gBAAgB,CAAC,UAAUW,MAAM,gBAAgB,CAAC;IAClDpB,cAAc,CAAC,MAAM,CAAC;IACtBE,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAMmB,qBAAqB,GAAGA,CAAA,KAAM;IAClCrB,cAAc,CAAC,aAAa,CAAC;EAC/B,CAAC;EAED,MAAMsB,qBAAqB,GAAIC,MAAM,IAAK;IACxCd,gBAAgB,CAAC,uBAAuBc,MAAM,CAACC,IAAI,uBAAuB,CAAC;IAC3ExB,cAAc,CAAC,MAAM,CAAC;EACxB,CAAC;EAED,MAAMyB,gBAAgB,GAAGA,CAAA,KAAM;IAC7BjB,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,MAAMkB,mBAAmB,GAAIC,OAAO,IAAK;IACvClB,gBAAgB,CAAC,qBAAqBkB,OAAO,CAACC,cAAc,iCAAiC,CAAC;IAC9FpB,kBAAkB,CAAC,KAAK,CAAC;IACzB;IACA,IAAIT,WAAW,KAAK,MAAM,EAAE;MAC1B;IAAA;EAEJ,CAAC;EAED,MAAM8B,iBAAiB,GAAGA,CAAA,KAAM;IAC9BrB,kBAAkB,CAAC,KAAK,CAAC;EAC3B,CAAC;EAED,MAAMsB,YAAY,GAAGA,CAAA,KAAM;IACzB9B,cAAc,CAAC,MAAM,CAAC;IACtBE,iBAAiB,CAAC,IAAI,CAAC;IACvBE,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAM2B,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,OAAOrC,iBAAiB,CAACqC,iBAAiB,CAAC,CAAC;EAC9C,CAAC;EAED,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,QAAQjC,WAAW;MACjB,KAAK,eAAe;QAClB,oBACEH,OAAA,CAACL,iBAAiB;UAChB0C,YAAY,EAAEnB,gBAAiB;UAC/BoB,QAAQ,EAAEJ;QAAa;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC;MAGN,KAAK,QAAQ;QACX,oBACE1C,OAAA,CAACN,iBAAiB;UAChBiD,IAAI,EAAC,QAAQ;UACbpC,YAAY,EAAEA,YAAa;UAC3BqC,QAAQ,EAAEtB,kBAAmB;UAC7BgB,QAAQ,EAAEJ;QAAa;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC;MAGN,KAAK,MAAM;QACT,oBACE1C,OAAA,CAACN,iBAAiB;UAChBiD,IAAI,EAAC,MAAM;UACXE,WAAW,EAAExC,cAAe;UAC5BuC,QAAQ,EAAEtB,kBAAmB;UAC7BgB,QAAQ,EAAEJ;QAAa;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC;MAGN,KAAK,aAAa;QAChB,oBACE1C,OAAA,CAACP,WAAW;UACVqD,MAAM,EAAEpB,qBAAsB;UAC9BY,QAAQ,EAAEJ;QAAa;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC;MAGN,KAAK,MAAM;MACX;QACE,oBACE1C,OAAA,CAACJ,UAAU;UACTmD,cAAc,EAAE9B,kBAAmB;UACnC+B,YAAY,EAAE5B,gBAAiB;UAC/B6B,aAAa,EAAExB,qBAAsB;UACrCyB,eAAe,EAAErB;QAAiB;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC;IAER;EACF,CAAC;EAED,MAAMS,UAAU,GAAGhB,iBAAiB,CAAC,CAAC;EAEtC,oBACEnC,OAAA;IAAKoD,SAAS,EAAC,mBAAmB;IAAAC,QAAA,gBAEhCrD,OAAA;MAAKoD,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChCrD,OAAA;QAAKoD,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BrD,OAAA;UAAAqD,QAAA,EAAI;QAAwB;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACjC1C,OAAA;UAAKoD,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BrD,OAAA;YAAKoD,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBrD,OAAA;cAAMoD,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAY;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAChD1C,OAAA;cAAMoD,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAEF,UAAU,CAACG;YAAU;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxD,CAAC,eACN1C,OAAA;YAAKoD,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBrD,OAAA;cAAMoD,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAe;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACnD1C,OAAA;cAAMoD,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAEF,UAAU,CAACI;YAAa;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D,CAAC,eACN1C,OAAA;YAAKoD,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBrD,OAAA;cAAMoD,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAkB;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACtD1C,OAAA;cAAMoD,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAEF,UAAU,CAACK;YAAgB;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN1C,OAAA;QAAKoD,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBrD,OAAA;UACEyD,OAAO,EAAEA,CAAA,KAAMrD,cAAc,CAAC,MAAM,CAAE;UACtCgD,SAAS,EAAE,WAAWjD,WAAW,KAAK,MAAM,GAAG,QAAQ,GAAG,EAAE,EAAG;UAAAkD,QAAA,EAChE;QAED;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT1C,OAAA;UACEyD,OAAO,EAAExC,kBAAmB;UAC5BmC,SAAS,EAAE,WAAWjD,WAAW,KAAK,eAAe,IAAIA,WAAW,KAAK,QAAQ,GAAG,QAAQ,GAAG,EAAE,EAAG;UAAAkD,QAAA,EACrG;QAED;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT1C,OAAA;UACEyD,OAAO,EAAEhC,qBAAsB;UAC/B2B,SAAS,EAAE,WAAWjD,WAAW,KAAK,aAAa,GAAG,QAAQ,GAAG,EAAE,EAAG;UAAAkD,QAAA,EACvE;QAED;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT1C,OAAA;UACEyD,OAAO,EAAE5B,gBAAiB;UAC1BuB,SAAS,EAAC,SAAS;UAAAC,QAAA,EACpB;QAED;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLjC,YAAY,iBACXT,OAAA;MAAKoD,SAAS,EAAE,gBAAgB3C,YAAY,CAACM,IAAI,EAAG;MAAAsC,QAAA,gBAClDrD,OAAA;QAAAqD,QAAA,EAAO5C,YAAY,CAACK;MAAO;QAAAyB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACnC1C,OAAA;QAAQyD,OAAO,EAAEA,CAAA,KAAM/C,eAAe,CAAC,IAAI,CAAE;QAAC0C,SAAS,EAAC,oBAAoB;QAAAC,QAAA,EAAC;MAE7E;QAAAd,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CACN,eAGD1C,OAAA;MAAKoD,SAAS,EAAC,oBAAoB;MAAAC,QAAA,EAChCjB,iBAAiB,CAAC;IAAC;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CAAC,eAGN1C,OAAA;MAAKoD,SAAS,EAAC,mBAAmB;MAAAC,QAAA,eAChCrD,OAAA;QAAKoD,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BrD,OAAA;UAAKoD,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BrD,OAAA;YAAAqD,QAAA,EAAI;UAAa;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACtB1C,OAAA;YAAKoD,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5BrD,OAAA;cAAQyD,OAAO,EAAExC,kBAAmB;cAACmC,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAC;YAElE;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT1C,OAAA;cAAQyD,OAAO,EAAEhC,qBAAsB;cAAC2B,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAC;YAErE;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT1C,OAAA;cAAQyD,OAAO,EAAE5B,gBAAiB;cAACuB,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAC;YAEhE;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN1C,OAAA;UAAKoD,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BrD,OAAA;YAAAqD,QAAA,EAAI;UAAe;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxB1C,OAAA;YAAKoD,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BrD,OAAA;cAAKoD,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BrD,OAAA;gBAAAqD,QAAA,EAAM;cAAwB;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACrC1C,OAAA;gBAAAqD,QAAA,EAAOF,UAAU,CAACO;cAAiB;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CAAC,eACN1C,OAAA;cAAKoD,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BrD,OAAA;gBAAAqD,QAAA,EAAM;cAAiB;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC9B1C,OAAA;gBAAAqD,QAAA,EAAOF,UAAU,CAACQ,cAAc,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACC,KAAK,CAAC,CAACC,IAAI,CAAC,IAAI;cAAC;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9E,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN1C,OAAA;UAAKoD,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BrD,OAAA;YAAAqD,QAAA,EAAI;UAAW;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpB1C,OAAA;YAAKoD,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BrD,OAAA;cAAAqD,QAAA,EAAK;YAAyB;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACpC1C,OAAA;cAAAqD,QAAA,EAAK;YAAiB;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC5B1C,OAAA;cAAAqD,QAAA,EAAK;YAA2B;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGL/B,eAAe,iBACdX,OAAA,CAACH,aAAa;MACZoE,OAAO,EAAEhC,iBAAkB;MAC3BiC,SAAS,EAAEpC;IAAoB;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChC,CACF;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACxC,EAAA,CAlPID,gBAAgB;AAAAkE,EAAA,GAAhBlE,gBAAgB;AAoPtB,eAAeA,gBAAgB;AAAC,IAAAkE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}