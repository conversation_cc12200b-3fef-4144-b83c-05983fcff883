{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Advanced\\\\Advanced CRM filed all use\\\\frontend\\\\src\\\\components\\\\forms\\\\DynamicPersonForm.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { PersonNatureLabels, GenderLabels, WorkingProfileLabels, getDefaultPersonData } from '../../constants/personConstants';\nimport formConfigService from '../../services/formConfigService';\nimport apiService from '../../services/apiService';\nimport FormField from './FormField';\nimport './DynamicPersonForm.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DynamicPersonForm = ({\n  onSubmit,\n  onCancel,\n  initialData = null,\n  mode = 'create'\n}) => {\n  _s();\n  // Hierarchy state\n  const [divisions, setDivisions] = useState([]);\n  const [categories, setCategories] = useState([]);\n  const [subCategories, setSubCategories] = useState([]);\n\n  // Selection state\n  const [selectedDivision, setSelectedDivision] = useState('');\n  const [selectedCategory, setSelectedCategory] = useState('');\n  const [selectedSubCategory, setSelectedSubCategory] = useState('');\n\n  // Form state\n  const [formConfig, setFormConfig] = useState(null);\n  const [formData, setFormData] = useState(getDefaultPersonData());\n  const [errors, setErrors] = useState({});\n\n  // Loading states\n  const [loading, setLoading] = useState({\n    divisions: false,\n    categories: false,\n    subCategories: false,\n    form: false\n  });\n  const [submitting, setSubmitting] = useState(false);\n\n  // Form availability state\n  const [formAvailability, setFormAvailability] = useState({\n    categoryHasForm: false,\n    subCategoryHasForm: false,\n    showSubCategoryDropdown: false,\n    message: ''\n  });\n\n  // Load divisions on component mount\n  useEffect(() => {\n    loadDivisions();\n\n    // Test API connectivity\n    testApiConnectivity();\n\n    // Load initial data if provided\n    if (initialData) {\n      var _initialData$division, _initialData$category, _initialData$subCateg;\n      setFormData(initialData);\n      setSelectedDivision(((_initialData$division = initialData.divisionId) === null || _initialData$division === void 0 ? void 0 : _initialData$division.toString()) || '');\n      setSelectedCategory(((_initialData$category = initialData.categoryId) === null || _initialData$category === void 0 ? void 0 : _initialData$category.toString()) || '');\n      setSelectedSubCategory(((_initialData$subCateg = initialData.subCategoryId) === null || _initialData$subCateg === void 0 ? void 0 : _initialData$subCateg.toString()) || '');\n    } else {\n      // Load default form for new person creation\n      const defaultForm = formConfigService.getDefaultFormConfig();\n      // Ensure no duplicate fields in default form\n      if (defaultForm.fields) {\n        defaultForm.fields = deduplicateFields(defaultForm.fields);\n      }\n      setFormConfig(defaultForm);\n    }\n  }, [initialData]);\n\n  // Test API connectivity\n  const testApiConnectivity = async () => {\n    try {\n      await apiService.getDivisions();\n    } catch (error) {\n      console.error('API connectivity test failed:', error);\n    }\n  };\n\n  // Deduplicate fields based on field key\n  const deduplicateFields = fields => {\n    const seen = new Set();\n    const deduplicated = [];\n    fields.forEach(field => {\n      if (!seen.has(field.key)) {\n        seen.add(field.key);\n        deduplicated.push(field);\n      } else {\n        console.warn(`DynamicPersonForm: Removing duplicate field: ${field.key}`);\n      }\n    });\n    if (fields.length !== deduplicated.length) {\n      console.log(`DynamicPersonForm: Deduplicated ${fields.length} fields to ${deduplicated.length} unique fields`);\n    }\n    return deduplicated;\n  };\n\n  // Load categories when division changes\n  useEffect(() => {\n    if (selectedDivision) {\n      loadCategories(selectedDivision);\n      // Reset category and subcategory when division changes\n      setSelectedCategory('');\n      setSelectedSubCategory('');\n      setSubCategories([]);\n      setFormConfig(null);\n      setFormAvailability({\n        categoryHasForm: false,\n        subCategoryHasForm: false,\n        showSubCategoryDropdown: false,\n        message: ''\n      });\n    } else {\n      setCategories([]);\n      setSubCategories([]);\n      setSelectedCategory('');\n      setSelectedSubCategory('');\n      setFormConfig(null);\n    }\n  }, [selectedDivision]);\n\n  // Handle category selection and form loading\n  useEffect(() => {\n    if (selectedCategory) {\n      handleCategorySelection(selectedCategory);\n      // Reset subcategory when category changes\n      setSelectedSubCategory('');\n    } else {\n      setSubCategories([]);\n      setSelectedSubCategory('');\n      setFormConfig(null);\n      setFormAvailability({\n        categoryHasForm: false,\n        subCategoryHasForm: false,\n        showSubCategoryDropdown: false,\n        message: ''\n      });\n    }\n  }, [selectedCategory]);\n\n  // Handle subcategory selection and form loading\n  useEffect(() => {\n    if (selectedSubCategory) {\n      handleSubCategorySelection(selectedSubCategory);\n    }\n  }, [selectedSubCategory]);\n\n  // Load divisions from API\n  const loadDivisions = async () => {\n    setLoading(prev => ({\n      ...prev,\n      divisions: true\n    }));\n    try {\n      const response = await apiService.getDivisions();\n      setDivisions(response.data || []);\n    } catch (error) {\n      console.error('Error loading divisions:', error);\n      setErrors(prev => ({\n        ...prev,\n        divisions: 'Failed to load divisions'\n      }));\n    } finally {\n      setLoading(prev => ({\n        ...prev,\n        divisions: false\n      }));\n    }\n  };\n\n  // Load categories by division\n  const loadCategories = async divisionId => {\n    setLoading(prev => ({\n      ...prev,\n      categories: true\n    }));\n    try {\n      const response = await apiService.getCategoriesByDivision(divisionId);\n      setCategories(response.data || []);\n    } catch (error) {\n      console.error('Error loading categories:', error);\n      setErrors(prev => ({\n        ...prev,\n        categories: 'Failed to load categories'\n      }));\n      setCategories([]);\n    } finally {\n      setLoading(prev => ({\n        ...prev,\n        categories: false\n      }));\n    }\n  };\n\n  // Load subcategories by category\n  const loadSubCategories = async categoryId => {\n    setLoading(prev => ({\n      ...prev,\n      subCategories: true\n    }));\n    try {\n      const response = await apiService.getSubCategoriesByCategory(categoryId);\n      setSubCategories(response.data || []);\n    } catch (error) {\n      console.error('Error loading subcategories:', error);\n      setErrors(prev => ({\n        ...prev,\n        subCategories: 'Failed to load subcategories'\n      }));\n      setSubCategories([]);\n    } finally {\n      setLoading(prev => ({\n        ...prev,\n        subCategories: false\n      }));\n    }\n  };\n\n  // Handle category selection and form loading logic\n  const handleCategorySelection = async categoryId => {\n    setLoading(prev => ({\n      ...prev,\n      form: true\n    }));\n    try {\n      // Check if category has a form\n      const categoryHasForm = formConfigService.hasFormForCategory(parseInt(categoryId));\n      if (categoryHasForm) {\n        // Load category form and hide subcategory dropdown\n        const categoryForm = formConfigService.loadFormConfig('category', parseInt(categoryId));\n        // Deduplicate fields in loaded form\n        if (categoryForm && categoryForm.fields) {\n          categoryForm.fields = deduplicateFields(categoryForm.fields);\n        }\n        setFormConfig(categoryForm);\n        setFormAvailability({\n          categoryHasForm: true,\n          subCategoryHasForm: false,\n          showSubCategoryDropdown: false,\n          message: 'Using category-specific form'\n        });\n      } else {\n        // No category form, show subcategory dropdown and load subcategories\n        await loadSubCategories(categoryId);\n        // Load default form as fallback\n        const defaultForm = formConfigService.getDefaultFormConfig();\n        // Deduplicate fields in default form\n        if (defaultForm.fields) {\n          defaultForm.fields = deduplicateFields(defaultForm.fields);\n        }\n        setFormConfig(defaultForm);\n        setFormAvailability({\n          categoryHasForm: false,\n          subCategoryHasForm: false,\n          showSubCategoryDropdown: true,\n          message: 'Using default form. You can select a subcategory if available.'\n        });\n      }\n    } catch (error) {\n      console.error('Error handling category selection:', error);\n      setErrors(prev => ({\n        ...prev,\n        form: 'Error loading form configuration'\n      }));\n    } finally {\n      setLoading(prev => ({\n        ...prev,\n        form: false\n      }));\n    }\n  };\n\n  // Handle subcategory selection and form loading logic\n  const handleSubCategorySelection = async subCategoryId => {\n    setLoading(prev => ({\n      ...prev,\n      form: true\n    }));\n    try {\n      // Check if subcategory has a form\n      const subCategoryHasForm = formConfigService.hasFormForSubCategory(parseInt(subCategoryId));\n      if (subCategoryHasForm) {\n        // Load subcategory form\n        const subCategoryForm = formConfigService.loadFormConfig('subcategory', parseInt(subCategoryId));\n        // Deduplicate fields in loaded form\n        if (subCategoryForm && subCategoryForm.fields) {\n          subCategoryForm.fields = deduplicateFields(subCategoryForm.fields);\n        }\n        setFormConfig(subCategoryForm);\n        setFormAvailability({\n          categoryHasForm: false,\n          subCategoryHasForm: true,\n          showSubCategoryDropdown: true,\n          message: 'Using subcategory-specific form'\n        });\n      } else {\n        // No subcategory form available, use default form\n        const defaultForm = formConfigService.getDefaultFormConfig();\n        // Deduplicate fields in default form\n        if (defaultForm.fields) {\n          defaultForm.fields = deduplicateFields(defaultForm.fields);\n        }\n        setFormConfig(defaultForm);\n        setFormAvailability({\n          categoryHasForm: false,\n          subCategoryHasForm: false,\n          showSubCategoryDropdown: true,\n          message: 'No specific form found for this subcategory. Using default form.'\n        });\n      }\n    } catch (error) {\n      console.error('Error handling subcategory selection:', error);\n      setErrors(prev => ({\n        ...prev,\n        form: 'Error loading form configuration'\n      }));\n    } finally {\n      setLoading(prev => ({\n        ...prev,\n        form: false\n      }));\n    }\n  };\n\n  // Handle dropdown changes\n  const handleDivisionChange = e => {\n    const value = e.target.value;\n    setSelectedDivision(value);\n\n    // Update form data\n    setFormData(prev => ({\n      ...prev,\n      divisionId: value ? parseInt(value) : null,\n      categoryId: null,\n      subCategoryId: null\n    }));\n  };\n  const handleCategoryChange = e => {\n    const value = e.target.value;\n    setSelectedCategory(value);\n\n    // Update form data\n    setFormData(prev => ({\n      ...prev,\n      categoryId: value ? parseInt(value) : null,\n      subCategoryId: null\n    }));\n  };\n  const handleSubCategoryChange = e => {\n    const value = e.target.value;\n    setSelectedSubCategory(value);\n\n    // Update form data\n    setFormData(prev => ({\n      ...prev,\n      subCategoryId: value ? parseInt(value) : null\n    }));\n  };\n  const handleFieldChange = (fieldKey, value) => {\n    setFormData(prev => ({\n      ...prev,\n      [fieldKey]: value\n    }));\n\n    // Clear field error when user starts typing\n    if (errors[fieldKey]) {\n      setErrors(prev => ({\n        ...prev,\n        [fieldKey]: null\n      }));\n    }\n  };\n  const validateForm = () => {\n    const newErrors = {};\n\n    // Validate hierarchy selection\n    if (!selectedDivision) {\n      newErrors.division = 'Division is required';\n    }\n    if (!selectedCategory) {\n      newErrors.category = 'Category is required';\n    }\n\n    // Check if form is available (should always have default form as fallback)\n    if (!formConfig) {\n      newErrors.form = 'Form configuration not loaded. Please try again.';\n      return {\n        isValid: false,\n        errors: newErrors\n      };\n    }\n\n    // Validate form fields\n    formConfig.fields.forEach(field => {\n      const value = formData[field.key];\n\n      // Required field validation\n      if (field.required && (!value || typeof value === 'string' && value.trim() === '')) {\n        newErrors[field.key] = `${field.label} is required`;\n        return;\n      }\n\n      // Conditional field validation\n      if (field.conditional && shouldShowField(field)) {\n        if (field.required && (!value || typeof value === 'string' && value.trim() === '')) {\n          newErrors[field.key] = `${field.label} is required`;\n          return;\n        }\n      }\n\n      // Type-specific validation\n      if (value && typeof value === 'string' && value.trim() !== '') {\n        var _field$validation, _field$validation2, _field$validation3;\n        switch (field.type) {\n          case 'email':\n            const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n            if (!emailRegex.test(value)) {\n              newErrors[field.key] = 'Please enter a valid email address';\n            }\n            break;\n          case 'tel':\n            const phoneRegex = /^(\\+91[\\-\\s]?)?[0]?(91)?[789]\\d{9}$/;\n            if (!phoneRegex.test(value)) {\n              newErrors[field.key] = 'Please enter a valid mobile number';\n            }\n            break;\n          case 'url':\n            try {\n              new URL(value);\n            } catch {\n              newErrors[field.key] = 'Please enter a valid URL';\n            }\n            break;\n          case 'number':\n            if (field.validation) {\n              const numValue = parseFloat(value);\n              if (field.validation.min !== undefined && numValue < field.validation.min) {\n                newErrors[field.key] = `Value must be at least ${field.validation.min}`;\n              }\n              if (field.validation.max !== undefined && numValue > field.validation.max) {\n                newErrors[field.key] = `Value must be at most ${field.validation.max}`;\n              }\n            }\n            break;\n        }\n\n        // Pattern validation\n        if ((_field$validation = field.validation) !== null && _field$validation !== void 0 && _field$validation.pattern) {\n          const regex = new RegExp(field.validation.pattern);\n          if (!regex.test(value)) {\n            newErrors[field.key] = `${field.label} format is invalid`;\n          }\n        }\n\n        // Length validation\n        if ((_field$validation2 = field.validation) !== null && _field$validation2 !== void 0 && _field$validation2.minLength && value.length < field.validation.minLength) {\n          newErrors[field.key] = `${field.label} must be at least ${field.validation.minLength} characters`;\n        }\n        if ((_field$validation3 = field.validation) !== null && _field$validation3 !== void 0 && _field$validation3.maxLength && value.length > field.validation.maxLength) {\n          newErrors[field.key] = `${field.label} must be at most ${field.validation.maxLength} characters`;\n        }\n      }\n    });\n\n    // Business logic validation\n    if (formData.isMarried && formData.dateOfMarriage && formData.dateOfBirth) {\n      const birthDate = new Date(formData.dateOfBirth);\n      const marriageDate = new Date(formData.dateOfMarriage);\n      if (marriageDate <= birthDate) {\n        newErrors.dateOfMarriage = 'Marriage date must be after birth date';\n      }\n    }\n    return {\n      isValid: Object.keys(newErrors).length === 0,\n      errors: newErrors\n    };\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    const validation = validateForm();\n    if (!validation.isValid) {\n      setErrors(validation.errors);\n      return;\n    }\n    setSubmitting(true);\n    try {\n      // Prepare data for API - match backend model exactly\n      const submitData = {\n        // Required hierarchy fields\n        divisionId: parseInt(selectedDivision),\n        categoryId: parseInt(selectedCategory),\n        subCategoryId: selectedSubCategory ? parseInt(selectedSubCategory) : null,\n        // Required fields\n        name: formData.name || '',\n        mobileNumber: formData.mobileNumber || '',\n        nature: formData.nature ? parseInt(formData.nature) : null,\n        // Optional enum fields\n        gender: formData.gender ? parseInt(formData.gender) : null,\n        // Contact Information\n        alternateNumbers: Array.isArray(formData.alternateNumbers) ? formData.alternateNumbers : formData.alternateNumbers ? formData.alternateNumbers.split(',').map(s => s.trim()) : [],\n        alternateEmailIds: Array.isArray(formData.alternateEmailIds) ? formData.alternateEmailIds : formData.alternateEmailIds ? formData.alternateEmailIds.split(',').map(s => s.trim()) : [],\n        // Personal Information\n        dateOfBirth: formData.dateOfBirth || null,\n        isMarried: formData.isMarried || false,\n        dateOfMarriage: formData.dateOfMarriage || null,\n        // Location Information\n        workingState: formData.workingState || '',\n        domesticState: formData.domesticState || '',\n        district: formData.district || '',\n        address: formData.address || '',\n        workingArea: formData.workingArea || '',\n        // Associate Information\n        hasAssociate: formData.hasAssociate || false,\n        associateName: formData.associateName || '',\n        associateRelation: formData.associateRelation || '',\n        associateMobile: formData.associateMobile || '',\n        // Digital Presence\n        usingWebsite: formData.usingWebsite || false,\n        usingCRMApp: formData.usingCRMApp || false,\n        // Business Information\n        transactionValue: formData.transactionValue ? parseFloat(formData.transactionValue) : null,\n        reraRegistrationNumber: formData.reraRegistrationNumber || '',\n        workingProfiles: Array.isArray(formData.workingProfiles) ? formData.workingProfiles.map(wp => parseInt(wp)) : [],\n        starRating: formData.starRating ? parseInt(formData.starRating) : null,\n        source: formData.source || '',\n        remarks: formData.remarks || '',\n        // Company Information\n        firmName: formData.firmName || '',\n        numberOfOffices: formData.numberOfOffices ? parseInt(formData.numberOfOffices) : null,\n        numberOfBranches: formData.numberOfBranches ? parseInt(formData.numberOfBranches) : null,\n        totalEmployeeStrength: formData.totalEmployeeStrength ? parseInt(formData.totalEmployeeStrength) : null,\n        // Authorized Person\n        authorizedPersonName: formData.authorizedPersonName || '',\n        designation: formData.designation || '',\n        // Marketing Information\n        marketingContact: formData.marketingContact || '',\n        marketingDesignation: formData.marketingDesignation || '',\n        placeOfPosting: formData.placeOfPosting || '',\n        department: formData.department || ''\n      };\n\n      // Add optional fields only if they have valid values\n      // Debug: Check what values we have\n      console.log('Form data values for validation:');\n      console.log('primaryEmailId:', `\"${formData.primaryEmailId}\"`);\n      console.log('authorizedPersonEmail:', `\"${formData.authorizedPersonEmail}\"`);\n      console.log('website:', `\"${formData.website}\"`);\n      console.log('websiteLink:', `\"${formData.websiteLink}\"`);\n      console.log('crmAppLink:', `\"${formData.crmAppLink}\"`);\n\n      // Email fields - only add if not null/empty and not just whitespace\n      if (formData.primaryEmailId && formData.primaryEmailId !== null && typeof formData.primaryEmailId === 'string' && formData.primaryEmailId.trim() !== '') {\n        submitData['primaryEmailId'] = formData.primaryEmailId.trim();\n        console.log('Adding primaryEmailId:', formData.primaryEmailId.trim());\n      } else {\n        console.log('Skipping primaryEmailId - empty, null, or invalid');\n      }\n      if (formData.authorizedPersonEmail && formData.authorizedPersonEmail !== null && typeof formData.authorizedPersonEmail === 'string' && formData.authorizedPersonEmail.trim() !== '') {\n        submitData['authorizedPersonEmail'] = formData.authorizedPersonEmail.trim();\n        console.log('Adding authorizedPersonEmail:', formData.authorizedPersonEmail.trim());\n      } else {\n        console.log('Skipping authorizedPersonEmail - empty, null, or invalid');\n      }\n\n      // URL fields - only add if not null/empty and not just whitespace\n      if (formData.website && formData.website !== null && typeof formData.website === 'string' && formData.website.trim() !== '') {\n        submitData['website'] = formData.website.trim();\n        console.log('Adding website:', formData.website.trim());\n      } else {\n        console.log('Skipping website - empty, null, or invalid');\n      }\n      if (formData.websiteLink && formData.websiteLink !== null && typeof formData.websiteLink === 'string' && formData.websiteLink.trim() !== '') {\n        submitData['websiteLink'] = formData.websiteLink.trim();\n        console.log('Adding websiteLink:', formData.websiteLink.trim());\n      } else {\n        console.log('Skipping websiteLink - empty, null, or invalid');\n      }\n      if (formData.crmAppLink && formData.crmAppLink !== null && typeof formData.crmAppLink === 'string' && formData.crmAppLink.trim() !== '') {\n        submitData['crmAppLink'] = formData.crmAppLink.trim();\n        console.log('Adding crmAppLink:', formData.crmAppLink.trim());\n      } else {\n        console.log('Skipping crmAppLink - empty, null, or invalid');\n      }\n\n      // Debug logging\n      console.log('Final submitData object:', submitData);\n      console.log('Fields being sent to API:');\n      Object.keys(submitData).forEach(key => {\n        console.log(`  ${key}:`, typeof submitData[key], `\"${submitData[key]}\"`);\n      });\n      console.log('Selected Division:', selectedDivision);\n      console.log('Selected Category:', selectedCategory);\n      console.log('Selected SubCategory:', selectedSubCategory);\n\n      // Validate required fields before submission\n      const requiredFieldsCheck = {\n        divisionId: submitData.divisionId,\n        categoryId: submitData.categoryId,\n        name: submitData.name,\n        mobileNumber: submitData.mobileNumber,\n        nature: submitData.nature\n      };\n      console.log('Required fields check:', requiredFieldsCheck);\n\n      // Check for missing required fields\n      const missingFields = Object.entries(requiredFieldsCheck).filter(([, value]) => !value || value === '' || value === null).map(([key]) => key);\n      if (missingFields.length > 0) {\n        console.error('Missing required fields:', missingFields);\n        setErrors({\n          general: 'Missing required fields: ' + missingFields.join(', ')\n        });\n        return;\n      }\n\n      // Test endpoint accessibility before submission\n      const baseUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';\n      console.log('About to submit to:', `${baseUrl}/persons`);\n\n      // Quick connectivity test\n      try {\n        console.log('Testing connectivity to:', `${baseUrl}/divisions`);\n        const testResponse = await fetch(`${baseUrl}/divisions`, {\n          method: 'GET',\n          headers: {\n            'Content-Type': 'application/json'\n          }\n        });\n        console.log('Pre-submission connectivity test:', {\n          status: testResponse.status,\n          ok: testResponse.ok,\n          statusText: testResponse.statusText,\n          url: testResponse.url\n        });\n        if (!testResponse.ok) {\n          console.error('Connectivity test failed - server responded with error');\n          const errorText = await testResponse.text();\n          console.error('Error response:', errorText);\n        }\n      } catch (connectError) {\n        console.error('Backend connectivity issue:', connectError);\n        console.error('This usually means the backend server is not running on port 5000');\n        setErrors({\n          general: 'Cannot connect to server. Please check if the backend is running on http://localhost:5000'\n        });\n        return;\n      }\n\n      // Test the specific endpoint we're about to use\n      if (mode === 'create') {\n        console.log('Testing POST to persons endpoint...');\n        try {\n          const testPostResponse = await fetch(`${baseUrl}/persons`, {\n            method: 'OPTIONS',\n            // Use OPTIONS to test if endpoint accepts POST\n            headers: {\n              'Content-Type': 'application/json'\n            }\n          });\n          console.log('Persons endpoint OPTIONS test:', {\n            status: testPostResponse.status,\n            ok: testPostResponse.ok,\n            headers: [...testPostResponse.headers.entries()]\n          });\n        } catch (optionsError) {\n          console.error('Persons endpoint test failed:', optionsError);\n        }\n      }\n      let result;\n      if (mode === 'create') {\n        console.log('Calling apiService.createPerson with data:', submitData);\n        result = await apiService.createPerson(submitData);\n      } else {\n        console.log('Calling apiService.updatePerson with data:', submitData);\n        result = await apiService.updatePerson(initialData.id, submitData);\n      }\n      onSubmit(result);\n    } catch (error) {\n      var _error$data;\n      console.error('=== ERROR DETAILS START ===');\n      console.error('Error submitting form:', error);\n      console.error('Error name:', error.name);\n      console.error('Error message:', error.message);\n      console.error('Error status:', error.status);\n      console.error('Error data:', error.data);\n      console.error('Error data type:', typeof error.data);\n      console.error('Error data keys:', error.data ? Object.keys(error.data) : 'No data');\n      console.error('Full error object:', error);\n      console.error('Error constructor:', error.constructor.name);\n      console.error('Error stack:', error.stack);\n      console.error('=== ERROR DETAILS END ===');\n\n      // Try to extract meaningful error information\n      let errorInfo = {\n        hasData: !!error.data,\n        hasErrors: !!(error.data && error.data.errors),\n        hasMessage: !!(error.data && error.data.message),\n        hasTitle: !!(error.data && error.data.title),\n        status: error.status,\n        isValidationError: error.isValidationError ? error.isValidationError() : false\n      };\n      console.log('Error analysis:', errorInfo);\n      if ((_error$data = error.data) !== null && _error$data !== void 0 && _error$data.errors) {\n        // Handle validation errors from backend\n        const backendErrors = {};\n        Object.keys(error.data.errors).forEach(key => {\n          const errorMessages = error.data.errors[key];\n          backendErrors[key.toLowerCase()] = Array.isArray(errorMessages) ? errorMessages.join(', ') : errorMessages;\n        });\n        console.log('Backend validation errors:', backendErrors);\n        setErrors(backendErrors);\n      } else if (error.isValidationError && error.isValidationError()) {\n        const validationErrors = error.getValidationErrors();\n        console.log('Validation errors from method:', validationErrors);\n        setErrors(validationErrors);\n      } else {\n        var _error$data2, _error$data3;\n        const errorMessage = ((_error$data2 = error.data) === null || _error$data2 === void 0 ? void 0 : _error$data2.message) || ((_error$data3 = error.data) === null || _error$data3 === void 0 ? void 0 : _error$data3.title) || error.message || `HTTP ${error.status || 'Unknown'} - An error occurred while saving`;\n        console.log('General error message:', errorMessage);\n        setErrors({\n          general: errorMessage\n        });\n      }\n    } finally {\n      setSubmitting(false);\n    }\n  };\n  const shouldShowField = field => {\n    if (!field.conditional) return true;\n    const conditionValue = formData[field.conditional.field];\n    const expectedValue = field.conditional.value;\n\n    // Handle boolean conditions\n    if (typeof expectedValue === 'boolean' || expectedValue === 'true' || expectedValue === 'false') {\n      return conditionValue === (expectedValue === true || expectedValue === 'true');\n    }\n    return conditionValue === expectedValue;\n  };\n  const groupFieldsBySections = () => {\n    if (!formConfig || !formConfig.fields) return {};\n    const sections = {};\n    formConfig.fields.forEach(field => {\n      const sectionKey = field.section || 'general';\n      if (!sections[sectionKey]) {\n        sections[sectionKey] = {\n          title: getSectionTitle(sectionKey),\n          fields: []\n        };\n      }\n      sections[sectionKey].fields.push(field);\n    });\n    return sections;\n  };\n  const getSectionTitle = sectionKey => {\n    const titles = {\n      personalInfo: 'Personal Information',\n      contactInfo: 'Contact Information',\n      locationInfo: 'Location Information',\n      businessInfo: 'Business Information',\n      associateInfo: 'Associate Information',\n      digitalPresence: 'Digital Presence',\n      companyInfo: 'Company Information',\n      authorizedPerson: 'Authorized Person',\n      marketingInfo: 'Marketing Information',\n      general: 'General Information'\n    };\n    return titles[sectionKey] || sectionKey;\n  };\n  const sections = formConfig ? groupFieldsBySections() : {};\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"dynamic-person-form\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"form-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: mode === 'create' ? 'Create New Person' : 'Edit Person'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 764,\n        columnNumber: 9\n      }, this), formConfig && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-info\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"form-name\",\n          children: formConfig.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 767,\n          columnNumber: 13\n        }, this), formConfig.description && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"form-description\",\n          children: formConfig.description\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 769,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 766,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 763,\n      columnNumber: 7\n    }, this), errors.general && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"alert alert-error\",\n      children: errors.general\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 776,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmit,\n      className: \"person-form\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Division & Category Selection\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 782,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"form-label\",\n            children: [\"Division \", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"required\",\n              children: \"*\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 787,\n              columnNumber: 24\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 786,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: selectedDivision,\n            onChange: handleDivisionChange,\n            disabled: loading.divisions,\n            className: `form-select ${errors.division ? 'error' : ''}`,\n            required: true,\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"\",\n              children: loading.divisions ? 'Loading divisions...' : 'Select Division'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 796,\n              columnNumber: 15\n            }, this), divisions.map(division => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: division.id,\n              children: division.name\n            }, division.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 800,\n              columnNumber: 17\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 789,\n            columnNumber: 13\n          }, this), errors.division && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"error-message\",\n            children: errors.division\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 806,\n            columnNumber: 15\n          }, this), errors.divisions && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"error-message\",\n            children: errors.divisions\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 809,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 785,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"form-label\",\n            children: [\"Category \", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"required\",\n              children: \"*\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 816,\n              columnNumber: 24\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 815,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: selectedCategory,\n            onChange: handleCategoryChange,\n            disabled: !selectedDivision || loading.categories,\n            className: `form-select ${errors.category ? 'error' : ''}`,\n            required: true,\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"\",\n              children: !selectedDivision ? 'Select Division first' : loading.categories ? 'Loading categories...' : 'Select Category'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 825,\n              columnNumber: 15\n            }, this), categories.map(category => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: category.id,\n              children: category.name\n            }, category.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 834,\n              columnNumber: 17\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 818,\n            columnNumber: 13\n          }, this), errors.category && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"error-message\",\n            children: errors.category\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 840,\n            columnNumber: 15\n          }, this), errors.categories && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"error-message\",\n            children: errors.categories\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 843,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 814,\n          columnNumber: 11\n        }, this), formAvailability.showSubCategoryDropdown && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"form-label\",\n            children: \"Subcategory (Optional)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 850,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: selectedSubCategory,\n            onChange: handleSubCategoryChange,\n            disabled: !selectedCategory || loading.subCategories,\n            className: \"form-select\",\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"\",\n              children: !selectedCategory ? 'Select Category first' : loading.subCategories ? 'Loading subcategories...' : 'Select Subcategory (Optional)'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 859,\n              columnNumber: 17\n            }, this), subCategories.map(subCategory => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: subCategory.id,\n              children: subCategory.name\n            }, subCategory.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 868,\n              columnNumber: 19\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 853,\n            columnNumber: 15\n          }, this), errors.subCategories && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"error-message\",\n            children: errors.subCategories\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 874,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 849,\n          columnNumber: 13\n        }, this), loading.form && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading-message\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Loading form configuration...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 882,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 881,\n          columnNumber: 13\n        }, this), formAvailability.message && !loading.form && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `status-message ${formAvailability.categoryHasForm || formAvailability.subCategoryHasForm ? 'success' : formAvailability.message.includes('No form') ? 'error' : 'info'}`,\n          children: formAvailability.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 887,\n          columnNumber: 13\n        }, this), errors.form && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"error-message\",\n          children: errors.form\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 899,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 781,\n        columnNumber: 9\n      }, this), formConfig && Object.entries(sections).map(([sectionKey, section]) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: section.title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 906,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-fields\",\n          children: section.fields.filter(field => shouldShowField(field)).map((field, fieldIndex) => /*#__PURE__*/_jsxDEV(FormField, {\n            field: field,\n            value: formData[field.key],\n            onChange: value => handleFieldChange(field.key, value),\n            error: errors[field.key]\n          }, `${sectionKey}-${field.key}-${fieldIndex}`, false, {\n            fileName: _jsxFileName,\n            lineNumber: 911,\n            columnNumber: 19\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 907,\n          columnNumber: 13\n        }, this)]\n      }, sectionKey, true, {\n        fileName: _jsxFileName,\n        lineNumber: 905,\n        columnNumber: 11\n      }, this)), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-actions\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          onClick: onCancel,\n          className: \"btn btn-outline\",\n          disabled: submitting,\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 927,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          className: \"btn btn-primary\",\n          disabled: submitting || !selectedDivision || !selectedCategory,\n          children: submitting ? mode === 'create' ? 'Creating...' : 'Updating...' : mode === 'create' ? 'Create Person' : 'Update Person'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 935,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 926,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 779,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 762,\n    columnNumber: 5\n  }, this);\n};\n_s(DynamicPersonForm, \"HQTZ+LW5pRnY6eFU2EO5G2IvcY0=\");\n_c = DynamicPersonForm;\nexport default DynamicPersonForm;\nvar _c;\n$RefreshReg$(_c, \"DynamicPersonForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "PersonNatureLabels", "<PERSON><PERSON><PERSON><PERSON>", "WorkingProfileLabels", "getDefaultPersonData", "formConfigService", "apiService", "FormField", "jsxDEV", "_jsxDEV", "DynamicPersonForm", "onSubmit", "onCancel", "initialData", "mode", "_s", "divisions", "setDivisions", "categories", "setCategories", "subCategories", "setSubCategories", "selectedDivision", "setSelectedDivision", "selectedCate<PERSON><PERSON>", "setSelectedCategory", "selectedSubCategory", "setSelectedSubCategory", "formConfig", "setFormConfig", "formData", "setFormData", "errors", "setErrors", "loading", "setLoading", "form", "submitting", "setSubmitting", "formAvailability", "setFormAvailability", "categoryHasForm", "subCategoryHasForm", "showSubCategoryDropdown", "message", "loadDivisions", "testApiConnectivity", "_initialData$division", "_initialData$category", "_initialData$subCateg", "divisionId", "toString", "categoryId", "subCategoryId", "defaultForm", "getDefaultFormConfig", "fields", "deduplicateFields", "getDivisions", "error", "console", "seen", "Set", "deduplicated", "for<PERSON>ach", "field", "has", "key", "add", "push", "warn", "length", "log", "loadCategories", "handleCategorySelection", "handleSubCategorySelection", "prev", "response", "data", "getCategoriesByDivision", "loadSubCategories", "getSubCategoriesByCategory", "hasFormForCategory", "parseInt", "categoryForm", "loadFormConfig", "hasFormForSubCategory", "subCategoryForm", "handleDivisionChange", "e", "value", "target", "handleCategoryChange", "handleSubCategoryChange", "handleFieldChange", "<PERSON><PERSON><PERSON>", "validateForm", "newErrors", "division", "category", "<PERSON><PERSON><PERSON><PERSON>", "required", "trim", "label", "conditional", "shouldShowField", "_field$validation", "_field$validation2", "_field$validation3", "type", "emailRegex", "test", "phoneRegex", "URL", "validation", "numValue", "parseFloat", "min", "undefined", "max", "pattern", "regex", "RegExp", "<PERSON><PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "dateOfMarriage", "dateOfBirth", "birthDate", "Date", "marriageDate", "Object", "keys", "handleSubmit", "preventDefault", "submitData", "name", "mobileNumber", "nature", "gender", "alternateNumbers", "Array", "isArray", "split", "map", "s", "alternateEmailIds", "workingState", "domesticState", "district", "address", "workingArea", "hasAssociate", "associate<PERSON><PERSON>", "associateRelation", "associate<PERSON><PERSON><PERSON>", "usingWebsite", "usingCRMApp", "transactionValue", "reraRegistrationNumber", "workingProfiles", "wp", "starRating", "source", "remarks", "firmName", "numberOfOffices", "numberOfBranches", "totalEmployeeStrength", "authorizedPersonName", "designation", "marketingContact", "marketingDesignation", "placeOfPosting", "department", "primaryEmailId", "authorizedPersonEmail", "website", "websiteLink", "crmAppLink", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "missingFields", "entries", "filter", "general", "join", "baseUrl", "process", "env", "REACT_APP_API_URL", "testResponse", "fetch", "method", "headers", "status", "ok", "statusText", "url", "errorText", "text", "connectError", "testPostResponse", "optionsError", "result", "create<PERSON>erson", "update<PERSON><PERSON>", "id", "_error$data", "constructor", "stack", "errorInfo", "hasData", "hasErrors", "hasMessage", "hasTitle", "title", "isValidationError", "backendErrors", "errorMessages", "toLowerCase", "validationErrors", "getValidationErrors", "_error$data2", "_error$data3", "errorMessage", "conditionValue", "expectedValue", "groupFieldsBySections", "sections", "sectionKey", "section", "getSectionTitle", "titles", "personalInfo", "contactInfo", "locationInfo", "businessInfo", "associateInfo", "digitalPresence", "companyInfo", "<PERSON><PERSON><PERSON>", "marketingInfo", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "description", "onChange", "disabled", "subCategory", "includes", "fieldIndex", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/Advanced/Advanced CRM filed all use/frontend/src/components/forms/DynamicPersonForm.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { PersonNatureLabels, GenderLabels, WorkingProfileLabels, getDefaultPersonData } from '../../constants/personConstants';\nimport formConfigService from '../../services/formConfigService';\nimport apiService from '../../services/apiService';\nimport FormField from './FormField';\nimport './DynamicPersonForm.css';\n\nconst DynamicPersonForm = ({ onSubmit, onCancel, initialData = null, mode = 'create' }) => {\n  // Hierarchy state\n  const [divisions, setDivisions] = useState([]);\n  const [categories, setCategories] = useState([]);\n  const [subCategories, setSubCategories] = useState([]);\n\n  // Selection state\n  const [selectedDivision, setSelectedDivision] = useState('');\n  const [selectedCategory, setSelectedCategory] = useState('');\n  const [selectedSubCategory, setSelectedSubCategory] = useState('');\n\n  // Form state\n  const [formConfig, setFormConfig] = useState(null);\n  const [formData, setFormData] = useState(getDefaultPersonData());\n  const [errors, setErrors] = useState({});\n\n  // Loading states\n  const [loading, setLoading] = useState({\n    divisions: false,\n    categories: false,\n    subCategories: false,\n    form: false\n  });\n  const [submitting, setSubmitting] = useState(false);\n\n  // Form availability state\n  const [formAvailability, setFormAvailability] = useState({\n    categoryHasForm: false,\n    subCategoryHasForm: false,\n    showSubCategoryDropdown: false,\n    message: ''\n  });\n\n  // Load divisions on component mount\n  useEffect(() => {\n    loadDivisions();\n\n    // Test API connectivity\n    testApiConnectivity();\n\n    // Load initial data if provided\n    if (initialData) {\n      setFormData(initialData);\n      setSelectedDivision(initialData.divisionId?.toString() || '');\n      setSelectedCategory(initialData.categoryId?.toString() || '');\n      setSelectedSubCategory(initialData.subCategoryId?.toString() || '');\n    } else {\n      // Load default form for new person creation\n      const defaultForm = formConfigService.getDefaultFormConfig();\n      // Ensure no duplicate fields in default form\n      if (defaultForm.fields) {\n        defaultForm.fields = deduplicateFields(defaultForm.fields);\n      }\n      setFormConfig(defaultForm);\n    }\n  }, [initialData]);\n\n  // Test API connectivity\n  const testApiConnectivity = async () => {\n    try {\n      await apiService.getDivisions();\n    } catch (error) {\n      console.error('API connectivity test failed:', error);\n    }\n  };\n\n  // Deduplicate fields based on field key\n  const deduplicateFields = (fields) => {\n    const seen = new Set();\n    const deduplicated = [];\n\n    fields.forEach(field => {\n      if (!seen.has(field.key)) {\n        seen.add(field.key);\n        deduplicated.push(field);\n      } else {\n        console.warn(`DynamicPersonForm: Removing duplicate field: ${field.key}`);\n      }\n    });\n\n    if (fields.length !== deduplicated.length) {\n      console.log(`DynamicPersonForm: Deduplicated ${fields.length} fields to ${deduplicated.length} unique fields`);\n    }\n\n    return deduplicated;\n  };\n\n  // Load categories when division changes\n  useEffect(() => {\n    if (selectedDivision) {\n      loadCategories(selectedDivision);\n      // Reset category and subcategory when division changes\n      setSelectedCategory('');\n      setSelectedSubCategory('');\n      setSubCategories([]);\n      setFormConfig(null);\n      setFormAvailability({\n        categoryHasForm: false,\n        subCategoryHasForm: false,\n        showSubCategoryDropdown: false,\n        message: ''\n      });\n    } else {\n      setCategories([]);\n      setSubCategories([]);\n      setSelectedCategory('');\n      setSelectedSubCategory('');\n      setFormConfig(null);\n    }\n  }, [selectedDivision]);\n\n  // Handle category selection and form loading\n  useEffect(() => {\n    if (selectedCategory) {\n      handleCategorySelection(selectedCategory);\n      // Reset subcategory when category changes\n      setSelectedSubCategory('');\n    } else {\n      setSubCategories([]);\n      setSelectedSubCategory('');\n      setFormConfig(null);\n      setFormAvailability({\n        categoryHasForm: false,\n        subCategoryHasForm: false,\n        showSubCategoryDropdown: false,\n        message: ''\n      });\n    }\n  }, [selectedCategory]);\n\n  // Handle subcategory selection and form loading\n  useEffect(() => {\n    if (selectedSubCategory) {\n      handleSubCategorySelection(selectedSubCategory);\n    }\n  }, [selectedSubCategory]);\n\n  // Load divisions from API\n  const loadDivisions = async () => {\n    setLoading(prev => ({ ...prev, divisions: true }));\n    try {\n      const response = await apiService.getDivisions();\n      setDivisions(response.data || []);\n    } catch (error) {\n      console.error('Error loading divisions:', error);\n      setErrors(prev => ({ ...prev, divisions: 'Failed to load divisions' }));\n    } finally {\n      setLoading(prev => ({ ...prev, divisions: false }));\n    }\n  };\n\n  // Load categories by division\n  const loadCategories = async (divisionId) => {\n    setLoading(prev => ({ ...prev, categories: true }));\n    try {\n      const response = await apiService.getCategoriesByDivision(divisionId);\n      setCategories(response.data || []);\n    } catch (error) {\n      console.error('Error loading categories:', error);\n      setErrors(prev => ({ ...prev, categories: 'Failed to load categories' }));\n      setCategories([]);\n    } finally {\n      setLoading(prev => ({ ...prev, categories: false }));\n    }\n  };\n\n  // Load subcategories by category\n  const loadSubCategories = async (categoryId) => {\n    setLoading(prev => ({ ...prev, subCategories: true }));\n    try {\n      const response = await apiService.getSubCategoriesByCategory(categoryId);\n      setSubCategories(response.data || []);\n    } catch (error) {\n      console.error('Error loading subcategories:', error);\n      setErrors(prev => ({ ...prev, subCategories: 'Failed to load subcategories' }));\n      setSubCategories([]);\n    } finally {\n      setLoading(prev => ({ ...prev, subCategories: false }));\n    }\n  };\n\n  // Handle category selection and form loading logic\n  const handleCategorySelection = async (categoryId) => {\n    setLoading(prev => ({ ...prev, form: true }));\n\n    try {\n      // Check if category has a form\n      const categoryHasForm = formConfigService.hasFormForCategory(parseInt(categoryId));\n\n      if (categoryHasForm) {\n        // Load category form and hide subcategory dropdown\n        const categoryForm = formConfigService.loadFormConfig('category', parseInt(categoryId));\n        // Deduplicate fields in loaded form\n        if (categoryForm && categoryForm.fields) {\n          categoryForm.fields = deduplicateFields(categoryForm.fields);\n        }\n        setFormConfig(categoryForm);\n        setFormAvailability({\n          categoryHasForm: true,\n          subCategoryHasForm: false,\n          showSubCategoryDropdown: false,\n          message: 'Using category-specific form'\n        });\n      } else {\n        // No category form, show subcategory dropdown and load subcategories\n        await loadSubCategories(categoryId);\n        // Load default form as fallback\n        const defaultForm = formConfigService.getDefaultFormConfig();\n        // Deduplicate fields in default form\n        if (defaultForm.fields) {\n          defaultForm.fields = deduplicateFields(defaultForm.fields);\n        }\n        setFormConfig(defaultForm);\n        setFormAvailability({\n          categoryHasForm: false,\n          subCategoryHasForm: false,\n          showSubCategoryDropdown: true,\n          message: 'Using default form. You can select a subcategory if available.'\n        });\n      }\n    } catch (error) {\n      console.error('Error handling category selection:', error);\n      setErrors(prev => ({ ...prev, form: 'Error loading form configuration' }));\n    } finally {\n      setLoading(prev => ({ ...prev, form: false }));\n    }\n  };\n\n  // Handle subcategory selection and form loading logic\n  const handleSubCategorySelection = async (subCategoryId) => {\n    setLoading(prev => ({ ...prev, form: true }));\n\n    try {\n      // Check if subcategory has a form\n      const subCategoryHasForm = formConfigService.hasFormForSubCategory(parseInt(subCategoryId));\n\n      if (subCategoryHasForm) {\n        // Load subcategory form\n        const subCategoryForm = formConfigService.loadFormConfig('subcategory', parseInt(subCategoryId));\n        // Deduplicate fields in loaded form\n        if (subCategoryForm && subCategoryForm.fields) {\n          subCategoryForm.fields = deduplicateFields(subCategoryForm.fields);\n        }\n        setFormConfig(subCategoryForm);\n        setFormAvailability({\n          categoryHasForm: false,\n          subCategoryHasForm: true,\n          showSubCategoryDropdown: true,\n          message: 'Using subcategory-specific form'\n        });\n      } else {\n        // No subcategory form available, use default form\n        const defaultForm = formConfigService.getDefaultFormConfig();\n        // Deduplicate fields in default form\n        if (defaultForm.fields) {\n          defaultForm.fields = deduplicateFields(defaultForm.fields);\n        }\n        setFormConfig(defaultForm);\n        setFormAvailability({\n          categoryHasForm: false,\n          subCategoryHasForm: false,\n          showSubCategoryDropdown: true,\n          message: 'No specific form found for this subcategory. Using default form.'\n        });\n      }\n    } catch (error) {\n      console.error('Error handling subcategory selection:', error);\n      setErrors(prev => ({ ...prev, form: 'Error loading form configuration' }));\n    } finally {\n      setLoading(prev => ({ ...prev, form: false }));\n    }\n  };\n\n  // Handle dropdown changes\n  const handleDivisionChange = (e) => {\n    const value = e.target.value;\n    setSelectedDivision(value);\n\n    // Update form data\n    setFormData(prev => ({\n      ...prev,\n      divisionId: value ? parseInt(value) : null,\n      categoryId: null,\n      subCategoryId: null\n    }));\n  };\n\n  const handleCategoryChange = (e) => {\n    const value = e.target.value;\n    setSelectedCategory(value);\n\n    // Update form data\n    setFormData(prev => ({\n      ...prev,\n      categoryId: value ? parseInt(value) : null,\n      subCategoryId: null\n    }));\n  };\n\n  const handleSubCategoryChange = (e) => {\n    const value = e.target.value;\n    setSelectedSubCategory(value);\n\n    // Update form data\n    setFormData(prev => ({\n      ...prev,\n      subCategoryId: value ? parseInt(value) : null\n    }));\n  };\n\n  const handleFieldChange = (fieldKey, value) => {\n    setFormData(prev => ({\n      ...prev,\n      [fieldKey]: value\n    }));\n\n    // Clear field error when user starts typing\n    if (errors[fieldKey]) {\n      setErrors(prev => ({\n        ...prev,\n        [fieldKey]: null\n      }));\n    }\n  };\n\n  const validateForm = () => {\n    const newErrors = {};\n\n    // Validate hierarchy selection\n    if (!selectedDivision) {\n      newErrors.division = 'Division is required';\n    }\n    if (!selectedCategory) {\n      newErrors.category = 'Category is required';\n    }\n\n    // Check if form is available (should always have default form as fallback)\n    if (!formConfig) {\n      newErrors.form = 'Form configuration not loaded. Please try again.';\n      return { isValid: false, errors: newErrors };\n    }\n\n\n\n    // Validate form fields\n    formConfig.fields.forEach(field => {\n      const value = formData[field.key];\n      \n      // Required field validation\n      if (field.required && (!value || (typeof value === 'string' && value.trim() === ''))) {\n        newErrors[field.key] = `${field.label} is required`;\n        return;\n      }\n\n      // Conditional field validation\n      if (field.conditional && shouldShowField(field)) {\n        if (field.required && (!value || (typeof value === 'string' && value.trim() === ''))) {\n          newErrors[field.key] = `${field.label} is required`;\n          return;\n        }\n      }\n\n      // Type-specific validation\n      if (value && typeof value === 'string' && value.trim() !== '') {\n        switch (field.type) {\n          case 'email':\n            const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n            if (!emailRegex.test(value)) {\n              newErrors[field.key] = 'Please enter a valid email address';\n            }\n            break;\n          case 'tel':\n            const phoneRegex = /^(\\+91[\\-\\s]?)?[0]?(91)?[789]\\d{9}$/;\n            if (!phoneRegex.test(value)) {\n              newErrors[field.key] = 'Please enter a valid mobile number';\n            }\n            break;\n          case 'url':\n            try {\n              new URL(value);\n            } catch {\n              newErrors[field.key] = 'Please enter a valid URL';\n            }\n            break;\n          case 'number':\n            if (field.validation) {\n              const numValue = parseFloat(value);\n              if (field.validation.min !== undefined && numValue < field.validation.min) {\n                newErrors[field.key] = `Value must be at least ${field.validation.min}`;\n              }\n              if (field.validation.max !== undefined && numValue > field.validation.max) {\n                newErrors[field.key] = `Value must be at most ${field.validation.max}`;\n              }\n            }\n            break;\n        }\n\n        // Pattern validation\n        if (field.validation?.pattern) {\n          const regex = new RegExp(field.validation.pattern);\n          if (!regex.test(value)) {\n            newErrors[field.key] = `${field.label} format is invalid`;\n          }\n        }\n\n        // Length validation\n        if (field.validation?.minLength && value.length < field.validation.minLength) {\n          newErrors[field.key] = `${field.label} must be at least ${field.validation.minLength} characters`;\n        }\n        if (field.validation?.maxLength && value.length > field.validation.maxLength) {\n          newErrors[field.key] = `${field.label} must be at most ${field.validation.maxLength} characters`;\n        }\n      }\n    });\n\n    // Business logic validation\n    if (formData.isMarried && formData.dateOfMarriage && formData.dateOfBirth) {\n      const birthDate = new Date(formData.dateOfBirth);\n      const marriageDate = new Date(formData.dateOfMarriage);\n      if (marriageDate <= birthDate) {\n        newErrors.dateOfMarriage = 'Marriage date must be after birth date';\n      }\n    }\n\n    return {\n      isValid: Object.keys(newErrors).length === 0,\n      errors: newErrors\n    };\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    \n    const validation = validateForm();\n    if (!validation.isValid) {\n      setErrors(validation.errors);\n      return;\n    }\n\n    setSubmitting(true);\n    try {\n      // Prepare data for API - match backend model exactly\n      const submitData = {\n        // Required hierarchy fields\n        divisionId: parseInt(selectedDivision),\n        categoryId: parseInt(selectedCategory),\n        subCategoryId: selectedSubCategory ? parseInt(selectedSubCategory) : null,\n\n        // Required fields\n        name: formData.name || '',\n        mobileNumber: formData.mobileNumber || '',\n        nature: formData.nature ? parseInt(formData.nature) : null,\n\n        // Optional enum fields\n        gender: formData.gender ? parseInt(formData.gender) : null,\n\n        // Contact Information\n        alternateNumbers: Array.isArray(formData.alternateNumbers)\n          ? formData.alternateNumbers\n          : formData.alternateNumbers ? formData.alternateNumbers.split(',').map(s => s.trim()) : [],\n        alternateEmailIds: Array.isArray(formData.alternateEmailIds)\n          ? formData.alternateEmailIds\n          : formData.alternateEmailIds ? formData.alternateEmailIds.split(',').map(s => s.trim()) : [],\n\n        // Personal Information\n        dateOfBirth: formData.dateOfBirth || null,\n        isMarried: formData.isMarried || false,\n        dateOfMarriage: formData.dateOfMarriage || null,\n\n        // Location Information\n        workingState: formData.workingState || '',\n        domesticState: formData.domesticState || '',\n        district: formData.district || '',\n        address: formData.address || '',\n        workingArea: formData.workingArea || '',\n\n        // Associate Information\n        hasAssociate: formData.hasAssociate || false,\n        associateName: formData.associateName || '',\n        associateRelation: formData.associateRelation || '',\n        associateMobile: formData.associateMobile || '',\n\n        // Digital Presence\n        usingWebsite: formData.usingWebsite || false,\n        usingCRMApp: formData.usingCRMApp || false,\n\n        // Business Information\n        transactionValue: formData.transactionValue ? parseFloat(formData.transactionValue) : null,\n        reraRegistrationNumber: formData.reraRegistrationNumber || '',\n        workingProfiles: Array.isArray(formData.workingProfiles)\n          ? formData.workingProfiles.map(wp => parseInt(wp))\n          : [],\n        starRating: formData.starRating ? parseInt(formData.starRating) : null,\n        source: formData.source || '',\n        remarks: formData.remarks || '',\n\n        // Company Information\n        firmName: formData.firmName || '',\n        numberOfOffices: formData.numberOfOffices ? parseInt(formData.numberOfOffices) : null,\n        numberOfBranches: formData.numberOfBranches ? parseInt(formData.numberOfBranches) : null,\n        totalEmployeeStrength: formData.totalEmployeeStrength ? parseInt(formData.totalEmployeeStrength) : null,\n\n        // Authorized Person\n        authorizedPersonName: formData.authorizedPersonName || '',\n        designation: formData.designation || '',\n\n        // Marketing Information\n        marketingContact: formData.marketingContact || '',\n        marketingDesignation: formData.marketingDesignation || '',\n        placeOfPosting: formData.placeOfPosting || '',\n        department: formData.department || ''\n      };\n\n      // Add optional fields only if they have valid values\n      // Debug: Check what values we have\n      console.log('Form data values for validation:');\n      console.log('primaryEmailId:', `\"${formData.primaryEmailId}\"`);\n      console.log('authorizedPersonEmail:', `\"${formData.authorizedPersonEmail}\"`);\n      console.log('website:', `\"${formData.website}\"`);\n      console.log('websiteLink:', `\"${formData.websiteLink}\"`);\n      console.log('crmAppLink:', `\"${formData.crmAppLink}\"`);\n\n      // Email fields - only add if not null/empty and not just whitespace\n      if (formData.primaryEmailId && formData.primaryEmailId !== null && typeof formData.primaryEmailId === 'string' && formData.primaryEmailId.trim() !== '') {\n        submitData['primaryEmailId'] = formData.primaryEmailId.trim();\n        console.log('Adding primaryEmailId:', formData.primaryEmailId.trim());\n      } else {\n        console.log('Skipping primaryEmailId - empty, null, or invalid');\n      }\n\n      if (formData.authorizedPersonEmail && formData.authorizedPersonEmail !== null && typeof formData.authorizedPersonEmail === 'string' && formData.authorizedPersonEmail.trim() !== '') {\n        submitData['authorizedPersonEmail'] = formData.authorizedPersonEmail.trim();\n        console.log('Adding authorizedPersonEmail:', formData.authorizedPersonEmail.trim());\n      } else {\n        console.log('Skipping authorizedPersonEmail - empty, null, or invalid');\n      }\n\n      // URL fields - only add if not null/empty and not just whitespace\n      if (formData.website && formData.website !== null && typeof formData.website === 'string' && formData.website.trim() !== '') {\n        submitData['website'] = formData.website.trim();\n        console.log('Adding website:', formData.website.trim());\n      } else {\n        console.log('Skipping website - empty, null, or invalid');\n      }\n\n      if (formData.websiteLink && formData.websiteLink !== null && typeof formData.websiteLink === 'string' && formData.websiteLink.trim() !== '') {\n        submitData['websiteLink'] = formData.websiteLink.trim();\n        console.log('Adding websiteLink:', formData.websiteLink.trim());\n      } else {\n        console.log('Skipping websiteLink - empty, null, or invalid');\n      }\n\n      if (formData.crmAppLink && formData.crmAppLink !== null && typeof formData.crmAppLink === 'string' && formData.crmAppLink.trim() !== '') {\n        submitData['crmAppLink'] = formData.crmAppLink.trim();\n        console.log('Adding crmAppLink:', formData.crmAppLink.trim());\n      } else {\n        console.log('Skipping crmAppLink - empty, null, or invalid');\n      }\n\n      // Debug logging\n      console.log('Final submitData object:', submitData);\n      console.log('Fields being sent to API:');\n      Object.keys(submitData).forEach(key => {\n        console.log(`  ${key}:`, typeof submitData[key], `\"${submitData[key]}\"`);\n      });\n      console.log('Selected Division:', selectedDivision);\n      console.log('Selected Category:', selectedCategory);\n      console.log('Selected SubCategory:', selectedSubCategory);\n\n      // Validate required fields before submission\n      const requiredFieldsCheck = {\n        divisionId: submitData.divisionId,\n        categoryId: submitData.categoryId,\n        name: submitData.name,\n        mobileNumber: submitData.mobileNumber,\n        nature: submitData.nature\n      };\n      console.log('Required fields check:', requiredFieldsCheck);\n\n      // Check for missing required fields\n      const missingFields = Object.entries(requiredFieldsCheck)\n        .filter(([, value]) => !value || value === '' || value === null)\n        .map(([key]) => key);\n\n      if (missingFields.length > 0) {\n        console.error('Missing required fields:', missingFields);\n        setErrors({ general: 'Missing required fields: ' + missingFields.join(', ') });\n        return;\n      }\n\n      // Test endpoint accessibility before submission\n      const baseUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';\n      console.log('About to submit to:', `${baseUrl}/persons`);\n\n      // Quick connectivity test\n      try {\n        console.log('Testing connectivity to:', `${baseUrl}/divisions`);\n        const testResponse = await fetch(`${baseUrl}/divisions`, {\n          method: 'GET',\n          headers: { 'Content-Type': 'application/json' }\n        });\n        console.log('Pre-submission connectivity test:', {\n          status: testResponse.status,\n          ok: testResponse.ok,\n          statusText: testResponse.statusText,\n          url: testResponse.url\n        });\n\n        if (!testResponse.ok) {\n          console.error('Connectivity test failed - server responded with error');\n          const errorText = await testResponse.text();\n          console.error('Error response:', errorText);\n        }\n      } catch (connectError) {\n        console.error('Backend connectivity issue:', connectError);\n        console.error('This usually means the backend server is not running on port 5000');\n        setErrors({ general: 'Cannot connect to server. Please check if the backend is running on http://localhost:5000' });\n        return;\n      }\n\n      // Test the specific endpoint we're about to use\n      if (mode === 'create') {\n        console.log('Testing POST to persons endpoint...');\n        try {\n          const testPostResponse = await fetch(`${baseUrl}/persons`, {\n            method: 'OPTIONS', // Use OPTIONS to test if endpoint accepts POST\n            headers: { 'Content-Type': 'application/json' }\n          });\n          console.log('Persons endpoint OPTIONS test:', {\n            status: testPostResponse.status,\n            ok: testPostResponse.ok,\n            headers: [...testPostResponse.headers.entries()]\n          });\n        } catch (optionsError) {\n          console.error('Persons endpoint test failed:', optionsError);\n        }\n      }\n\n      let result;\n      if (mode === 'create') {\n        console.log('Calling apiService.createPerson with data:', submitData);\n        result = await apiService.createPerson(submitData);\n      } else {\n        console.log('Calling apiService.updatePerson with data:', submitData);\n        result = await apiService.updatePerson(initialData.id, submitData);\n      }\n\n      onSubmit(result);\n    } catch (error) {\n      console.error('=== ERROR DETAILS START ===');\n      console.error('Error submitting form:', error);\n      console.error('Error name:', error.name);\n      console.error('Error message:', error.message);\n      console.error('Error status:', error.status);\n      console.error('Error data:', error.data);\n      console.error('Error data type:', typeof error.data);\n      console.error('Error data keys:', error.data ? Object.keys(error.data) : 'No data');\n      console.error('Full error object:', error);\n      console.error('Error constructor:', error.constructor.name);\n      console.error('Error stack:', error.stack);\n      console.error('=== ERROR DETAILS END ===');\n\n      // Try to extract meaningful error information\n      let errorInfo = {\n        hasData: !!error.data,\n        hasErrors: !!(error.data && error.data.errors),\n        hasMessage: !!(error.data && error.data.message),\n        hasTitle: !!(error.data && error.data.title),\n        status: error.status,\n        isValidationError: error.isValidationError ? error.isValidationError() : false\n      };\n      console.log('Error analysis:', errorInfo);\n\n      if (error.data?.errors) {\n        // Handle validation errors from backend\n        const backendErrors = {};\n        Object.keys(error.data.errors).forEach(key => {\n          const errorMessages = error.data.errors[key];\n          backendErrors[key.toLowerCase()] = Array.isArray(errorMessages)\n            ? errorMessages.join(', ')\n            : errorMessages;\n        });\n        console.log('Backend validation errors:', backendErrors);\n        setErrors(backendErrors);\n      } else if (error.isValidationError && error.isValidationError()) {\n        const validationErrors = error.getValidationErrors();\n        console.log('Validation errors from method:', validationErrors);\n        setErrors(validationErrors);\n      } else {\n        const errorMessage = error.data?.message ||\n                           error.data?.title ||\n                           error.message ||\n                           `HTTP ${error.status || 'Unknown'} - An error occurred while saving`;\n        console.log('General error message:', errorMessage);\n        setErrors({ general: errorMessage });\n      }\n    } finally {\n      setSubmitting(false);\n    }\n  };\n\n  const shouldShowField = (field) => {\n    if (!field.conditional) return true;\n    \n    const conditionValue = formData[field.conditional.field];\n    const expectedValue = field.conditional.value;\n    \n    // Handle boolean conditions\n    if (typeof expectedValue === 'boolean' || expectedValue === 'true' || expectedValue === 'false') {\n      return conditionValue === (expectedValue === true || expectedValue === 'true');\n    }\n    \n    return conditionValue === expectedValue;\n  };\n\n  const groupFieldsBySections = () => {\n    if (!formConfig || !formConfig.fields) return {};\n\n    const sections = {};\n    formConfig.fields.forEach((field) => {\n      const sectionKey = field.section || 'general';\n      if (!sections[sectionKey]) {\n        sections[sectionKey] = {\n          title: getSectionTitle(sectionKey),\n          fields: []\n        };\n      }\n      sections[sectionKey].fields.push(field);\n    });\n\n    return sections;\n  };\n\n  const getSectionTitle = (sectionKey) => {\n    const titles = {\n      personalInfo: 'Personal Information',\n      contactInfo: 'Contact Information',\n      locationInfo: 'Location Information',\n      businessInfo: 'Business Information',\n      associateInfo: 'Associate Information',\n      digitalPresence: 'Digital Presence',\n      companyInfo: 'Company Information',\n      authorizedPerson: 'Authorized Person',\n      marketingInfo: 'Marketing Information',\n      general: 'General Information'\n    };\n    return titles[sectionKey] || sectionKey;\n  };\n\n  const sections = formConfig ? groupFieldsBySections() : {};\n\n\n\n  return (\n    <div className=\"dynamic-person-form\">\n      <div className=\"form-header\">\n        <h2>{mode === 'create' ? 'Create New Person' : 'Edit Person'}</h2>\n        {formConfig && (\n          <div className=\"form-info\">\n            <span className=\"form-name\">{formConfig.name}</span>\n            {formConfig.description && (\n              <span className=\"form-description\">{formConfig.description}</span>\n            )}\n          </div>\n        )}\n      </div>\n\n      {errors.general && (\n        <div className=\"alert alert-error\">{errors.general}</div>\n      )}\n\n      <form onSubmit={handleSubmit} className=\"person-form\">\n        {/* Hierarchical Dropdowns */}\n        <div className=\"form-section\">\n          <h3>Division & Category Selection</h3>\n\n          {/* Division Dropdown */}\n          <div className=\"form-group\">\n            <label className=\"form-label\">\n              Division <span className=\"required\">*</span>\n            </label>\n            <select\n              value={selectedDivision}\n              onChange={handleDivisionChange}\n              disabled={loading.divisions}\n              className={`form-select ${errors.division ? 'error' : ''}`}\n              required\n            >\n              <option value=\"\">\n                {loading.divisions ? 'Loading divisions...' : 'Select Division'}\n              </option>\n              {divisions.map(division => (\n                <option key={division.id} value={division.id}>\n                  {division.name}\n                </option>\n              ))}\n            </select>\n            {errors.division && (\n              <div className=\"error-message\">{errors.division}</div>\n            )}\n            {errors.divisions && (\n              <div className=\"error-message\">{errors.divisions}</div>\n            )}\n          </div>\n\n          {/* Category Dropdown */}\n          <div className=\"form-group\">\n            <label className=\"form-label\">\n              Category <span className=\"required\">*</span>\n            </label>\n            <select\n              value={selectedCategory}\n              onChange={handleCategoryChange}\n              disabled={!selectedDivision || loading.categories}\n              className={`form-select ${errors.category ? 'error' : ''}`}\n              required\n            >\n              <option value=\"\">\n                {!selectedDivision\n                  ? 'Select Division first'\n                  : loading.categories\n                    ? 'Loading categories...'\n                    : 'Select Category'\n                }\n              </option>\n              {categories.map(category => (\n                <option key={category.id} value={category.id}>\n                  {category.name}\n                </option>\n              ))}\n            </select>\n            {errors.category && (\n              <div className=\"error-message\">{errors.category}</div>\n            )}\n            {errors.categories && (\n              <div className=\"error-message\">{errors.categories}</div>\n            )}\n          </div>\n\n          {/* Subcategory Dropdown - Only show if category doesn't have a form */}\n          {formAvailability.showSubCategoryDropdown && (\n            <div className=\"form-group\">\n              <label className=\"form-label\">\n                Subcategory (Optional)\n              </label>\n              <select\n                value={selectedSubCategory}\n                onChange={handleSubCategoryChange}\n                disabled={!selectedCategory || loading.subCategories}\n                className=\"form-select\"\n              >\n                <option value=\"\">\n                  {!selectedCategory\n                    ? 'Select Category first'\n                    : loading.subCategories\n                      ? 'Loading subcategories...'\n                      : 'Select Subcategory (Optional)'\n                  }\n                </option>\n                {subCategories.map(subCategory => (\n                  <option key={subCategory.id} value={subCategory.id}>\n                    {subCategory.name}\n                  </option>\n                ))}\n              </select>\n              {errors.subCategories && (\n                <div className=\"error-message\">{errors.subCategories}</div>\n              )}\n            </div>\n          )}\n\n          {/* Form Status Messages */}\n          {loading.form && (\n            <div className=\"loading-message\">\n              <span>Loading form configuration...</span>\n            </div>\n          )}\n\n          {formAvailability.message && !loading.form && (\n            <div className={`status-message ${\n              formAvailability.categoryHasForm || formAvailability.subCategoryHasForm\n                ? 'success'\n                : formAvailability.message.includes('No form')\n                  ? 'error'\n                  : 'info'\n            }`}>\n              {formAvailability.message}\n            </div>\n          )}\n\n          {errors.form && (\n            <div className=\"error-message\">{errors.form}</div>\n          )}\n        </div>\n\n        {/* Dynamic Form Fields - Only show when form is available */}\n        {formConfig && Object.entries(sections).map(([sectionKey, section]) => (\n          <div key={sectionKey} className=\"form-section\">\n            <h3>{section.title}</h3>\n            <div className=\"form-fields\">\n              {section.fields\n                .filter(field => shouldShowField(field))\n                .map((field, fieldIndex) => (\n                  <FormField\n                    key={`${sectionKey}-${field.key}-${fieldIndex}`}\n                    field={field}\n                    value={formData[field.key]}\n                    onChange={(value) => handleFieldChange(field.key, value)}\n                    error={errors[field.key]}\n                  />\n                ))}\n            </div>\n          </div>\n        ))}\n\n\n\n        {/* Form Actions */}\n        <div className=\"form-actions\">\n          <button\n            type=\"button\"\n            onClick={onCancel}\n            className=\"btn btn-outline\"\n            disabled={submitting}\n          >\n            Cancel\n          </button>\n          <button\n            type=\"submit\"\n            className=\"btn btn-primary\"\n            disabled={submitting || !selectedDivision || !selectedCategory}\n          >\n            {submitting\n              ? (mode === 'create' ? 'Creating...' : 'Updating...')\n              : (mode === 'create' ? 'Create Person' : 'Update Person')\n            }\n          </button>\n        </div>\n      </form>\n    </div>\n  );\n};\n\nexport default DynamicPersonForm;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,kBAAkB,EAAEC,YAAY,EAAEC,oBAAoB,EAAEC,oBAAoB,QAAQ,iCAAiC;AAC9H,OAAOC,iBAAiB,MAAM,kCAAkC;AAChE,OAAOC,UAAU,MAAM,2BAA2B;AAClD,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAO,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjC,MAAMC,iBAAiB,GAAGA,CAAC;EAAEC,QAAQ;EAAEC,QAAQ;EAAEC,WAAW,GAAG,IAAI;EAAEC,IAAI,GAAG;AAAS,CAAC,KAAK;EAAAC,EAAA;EACzF;EACA,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACmB,UAAU,EAAEC,aAAa,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACqB,aAAa,EAAEC,gBAAgB,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;;EAEtD;EACA,MAAM,CAACuB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACyB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAAC2B,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;;EAElE;EACA,MAAM,CAAC6B,UAAU,EAAEC,aAAa,CAAC,GAAG9B,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAAC+B,QAAQ,EAAEC,WAAW,CAAC,GAAGhC,QAAQ,CAACK,oBAAoB,CAAC,CAAC,CAAC;EAChE,MAAM,CAAC4B,MAAM,EAAEC,SAAS,CAAC,GAAGlC,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAExC;EACA,MAAM,CAACmC,OAAO,EAAEC,UAAU,CAAC,GAAGpC,QAAQ,CAAC;IACrCiB,SAAS,EAAE,KAAK;IAChBE,UAAU,EAAE,KAAK;IACjBE,aAAa,EAAE,KAAK;IACpBgB,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGvC,QAAQ,CAAC,KAAK,CAAC;;EAEnD;EACA,MAAM,CAACwC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGzC,QAAQ,CAAC;IACvD0C,eAAe,EAAE,KAAK;IACtBC,kBAAkB,EAAE,KAAK;IACzBC,uBAAuB,EAAE,KAAK;IAC9BC,OAAO,EAAE;EACX,CAAC,CAAC;;EAEF;EACA5C,SAAS,CAAC,MAAM;IACd6C,aAAa,CAAC,CAAC;;IAEf;IACAC,mBAAmB,CAAC,CAAC;;IAErB;IACA,IAAIjC,WAAW,EAAE;MAAA,IAAAkC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA;MACflB,WAAW,CAAClB,WAAW,CAAC;MACxBU,mBAAmB,CAAC,EAAAwB,qBAAA,GAAAlC,WAAW,CAACqC,UAAU,cAAAH,qBAAA,uBAAtBA,qBAAA,CAAwBI,QAAQ,CAAC,CAAC,KAAI,EAAE,CAAC;MAC7D1B,mBAAmB,CAAC,EAAAuB,qBAAA,GAAAnC,WAAW,CAACuC,UAAU,cAAAJ,qBAAA,uBAAtBA,qBAAA,CAAwBG,QAAQ,CAAC,CAAC,KAAI,EAAE,CAAC;MAC7DxB,sBAAsB,CAAC,EAAAsB,qBAAA,GAAApC,WAAW,CAACwC,aAAa,cAAAJ,qBAAA,uBAAzBA,qBAAA,CAA2BE,QAAQ,CAAC,CAAC,KAAI,EAAE,CAAC;IACrE,CAAC,MAAM;MACL;MACA,MAAMG,WAAW,GAAGjD,iBAAiB,CAACkD,oBAAoB,CAAC,CAAC;MAC5D;MACA,IAAID,WAAW,CAACE,MAAM,EAAE;QACtBF,WAAW,CAACE,MAAM,GAAGC,iBAAiB,CAACH,WAAW,CAACE,MAAM,CAAC;MAC5D;MACA3B,aAAa,CAACyB,WAAW,CAAC;IAC5B;EACF,CAAC,EAAE,CAACzC,WAAW,CAAC,CAAC;;EAEjB;EACA,MAAMiC,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACF,MAAMxC,UAAU,CAACoD,YAAY,CAAC,CAAC;IACjC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;IACvD;EACF,CAAC;;EAED;EACA,MAAMF,iBAAiB,GAAID,MAAM,IAAK;IACpC,MAAMK,IAAI,GAAG,IAAIC,GAAG,CAAC,CAAC;IACtB,MAAMC,YAAY,GAAG,EAAE;IAEvBP,MAAM,CAACQ,OAAO,CAACC,KAAK,IAAI;MACtB,IAAI,CAACJ,IAAI,CAACK,GAAG,CAACD,KAAK,CAACE,GAAG,CAAC,EAAE;QACxBN,IAAI,CAACO,GAAG,CAACH,KAAK,CAACE,GAAG,CAAC;QACnBJ,YAAY,CAACM,IAAI,CAACJ,KAAK,CAAC;MAC1B,CAAC,MAAM;QACLL,OAAO,CAACU,IAAI,CAAC,gDAAgDL,KAAK,CAACE,GAAG,EAAE,CAAC;MAC3E;IACF,CAAC,CAAC;IAEF,IAAIX,MAAM,CAACe,MAAM,KAAKR,YAAY,CAACQ,MAAM,EAAE;MACzCX,OAAO,CAACY,GAAG,CAAC,mCAAmChB,MAAM,CAACe,MAAM,cAAcR,YAAY,CAACQ,MAAM,gBAAgB,CAAC;IAChH;IAEA,OAAOR,YAAY;EACrB,CAAC;;EAED;EACA/D,SAAS,CAAC,MAAM;IACd,IAAIsB,gBAAgB,EAAE;MACpBmD,cAAc,CAACnD,gBAAgB,CAAC;MAChC;MACAG,mBAAmB,CAAC,EAAE,CAAC;MACvBE,sBAAsB,CAAC,EAAE,CAAC;MAC1BN,gBAAgB,CAAC,EAAE,CAAC;MACpBQ,aAAa,CAAC,IAAI,CAAC;MACnBW,mBAAmB,CAAC;QAClBC,eAAe,EAAE,KAAK;QACtBC,kBAAkB,EAAE,KAAK;QACzBC,uBAAuB,EAAE,KAAK;QAC9BC,OAAO,EAAE;MACX,CAAC,CAAC;IACJ,CAAC,MAAM;MACLzB,aAAa,CAAC,EAAE,CAAC;MACjBE,gBAAgB,CAAC,EAAE,CAAC;MACpBI,mBAAmB,CAAC,EAAE,CAAC;MACvBE,sBAAsB,CAAC,EAAE,CAAC;MAC1BE,aAAa,CAAC,IAAI,CAAC;IACrB;EACF,CAAC,EAAE,CAACP,gBAAgB,CAAC,CAAC;;EAEtB;EACAtB,SAAS,CAAC,MAAM;IACd,IAAIwB,gBAAgB,EAAE;MACpBkD,uBAAuB,CAAClD,gBAAgB,CAAC;MACzC;MACAG,sBAAsB,CAAC,EAAE,CAAC;IAC5B,CAAC,MAAM;MACLN,gBAAgB,CAAC,EAAE,CAAC;MACpBM,sBAAsB,CAAC,EAAE,CAAC;MAC1BE,aAAa,CAAC,IAAI,CAAC;MACnBW,mBAAmB,CAAC;QAClBC,eAAe,EAAE,KAAK;QACtBC,kBAAkB,EAAE,KAAK;QACzBC,uBAAuB,EAAE,KAAK;QAC9BC,OAAO,EAAE;MACX,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACpB,gBAAgB,CAAC,CAAC;;EAEtB;EACAxB,SAAS,CAAC,MAAM;IACd,IAAI0B,mBAAmB,EAAE;MACvBiD,0BAA0B,CAACjD,mBAAmB,CAAC;IACjD;EACF,CAAC,EAAE,CAACA,mBAAmB,CAAC,CAAC;;EAEzB;EACA,MAAMmB,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChCV,UAAU,CAACyC,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE5D,SAAS,EAAE;IAAK,CAAC,CAAC,CAAC;IAClD,IAAI;MACF,MAAM6D,QAAQ,GAAG,MAAMvE,UAAU,CAACoD,YAAY,CAAC,CAAC;MAChDzC,YAAY,CAAC4D,QAAQ,CAACC,IAAI,IAAI,EAAE,CAAC;IACnC,CAAC,CAAC,OAAOnB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChD1B,SAAS,CAAC2C,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE5D,SAAS,EAAE;MAA2B,CAAC,CAAC,CAAC;IACzE,CAAC,SAAS;MACRmB,UAAU,CAACyC,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE5D,SAAS,EAAE;MAAM,CAAC,CAAC,CAAC;IACrD;EACF,CAAC;;EAED;EACA,MAAMyD,cAAc,GAAG,MAAOvB,UAAU,IAAK;IAC3Cf,UAAU,CAACyC,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE1D,UAAU,EAAE;IAAK,CAAC,CAAC,CAAC;IACnD,IAAI;MACF,MAAM2D,QAAQ,GAAG,MAAMvE,UAAU,CAACyE,uBAAuB,CAAC7B,UAAU,CAAC;MACrE/B,aAAa,CAAC0D,QAAQ,CAACC,IAAI,IAAI,EAAE,CAAC;IACpC,CAAC,CAAC,OAAOnB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD1B,SAAS,CAAC2C,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE1D,UAAU,EAAE;MAA4B,CAAC,CAAC,CAAC;MACzEC,aAAa,CAAC,EAAE,CAAC;IACnB,CAAC,SAAS;MACRgB,UAAU,CAACyC,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE1D,UAAU,EAAE;MAAM,CAAC,CAAC,CAAC;IACtD;EACF,CAAC;;EAED;EACA,MAAM8D,iBAAiB,GAAG,MAAO5B,UAAU,IAAK;IAC9CjB,UAAU,CAACyC,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAExD,aAAa,EAAE;IAAK,CAAC,CAAC,CAAC;IACtD,IAAI;MACF,MAAMyD,QAAQ,GAAG,MAAMvE,UAAU,CAAC2E,0BAA0B,CAAC7B,UAAU,CAAC;MACxE/B,gBAAgB,CAACwD,QAAQ,CAACC,IAAI,IAAI,EAAE,CAAC;IACvC,CAAC,CAAC,OAAOnB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD1B,SAAS,CAAC2C,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAExD,aAAa,EAAE;MAA+B,CAAC,CAAC,CAAC;MAC/EC,gBAAgB,CAAC,EAAE,CAAC;IACtB,CAAC,SAAS;MACRc,UAAU,CAACyC,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAExD,aAAa,EAAE;MAAM,CAAC,CAAC,CAAC;IACzD;EACF,CAAC;;EAED;EACA,MAAMsD,uBAAuB,GAAG,MAAOtB,UAAU,IAAK;IACpDjB,UAAU,CAACyC,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAExC,IAAI,EAAE;IAAK,CAAC,CAAC,CAAC;IAE7C,IAAI;MACF;MACA,MAAMK,eAAe,GAAGpC,iBAAiB,CAAC6E,kBAAkB,CAACC,QAAQ,CAAC/B,UAAU,CAAC,CAAC;MAElF,IAAIX,eAAe,EAAE;QACnB;QACA,MAAM2C,YAAY,GAAG/E,iBAAiB,CAACgF,cAAc,CAAC,UAAU,EAAEF,QAAQ,CAAC/B,UAAU,CAAC,CAAC;QACvF;QACA,IAAIgC,YAAY,IAAIA,YAAY,CAAC5B,MAAM,EAAE;UACvC4B,YAAY,CAAC5B,MAAM,GAAGC,iBAAiB,CAAC2B,YAAY,CAAC5B,MAAM,CAAC;QAC9D;QACA3B,aAAa,CAACuD,YAAY,CAAC;QAC3B5C,mBAAmB,CAAC;UAClBC,eAAe,EAAE,IAAI;UACrBC,kBAAkB,EAAE,KAAK;UACzBC,uBAAuB,EAAE,KAAK;UAC9BC,OAAO,EAAE;QACX,CAAC,CAAC;MACJ,CAAC,MAAM;QACL;QACA,MAAMoC,iBAAiB,CAAC5B,UAAU,CAAC;QACnC;QACA,MAAME,WAAW,GAAGjD,iBAAiB,CAACkD,oBAAoB,CAAC,CAAC;QAC5D;QACA,IAAID,WAAW,CAACE,MAAM,EAAE;UACtBF,WAAW,CAACE,MAAM,GAAGC,iBAAiB,CAACH,WAAW,CAACE,MAAM,CAAC;QAC5D;QACA3B,aAAa,CAACyB,WAAW,CAAC;QAC1Bd,mBAAmB,CAAC;UAClBC,eAAe,EAAE,KAAK;UACtBC,kBAAkB,EAAE,KAAK;UACzBC,uBAAuB,EAAE,IAAI;UAC7BC,OAAO,EAAE;QACX,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAOe,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;MAC1D1B,SAAS,CAAC2C,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAExC,IAAI,EAAE;MAAmC,CAAC,CAAC,CAAC;IAC5E,CAAC,SAAS;MACRD,UAAU,CAACyC,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAExC,IAAI,EAAE;MAAM,CAAC,CAAC,CAAC;IAChD;EACF,CAAC;;EAED;EACA,MAAMuC,0BAA0B,GAAG,MAAOtB,aAAa,IAAK;IAC1DlB,UAAU,CAACyC,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAExC,IAAI,EAAE;IAAK,CAAC,CAAC,CAAC;IAE7C,IAAI;MACF;MACA,MAAMM,kBAAkB,GAAGrC,iBAAiB,CAACiF,qBAAqB,CAACH,QAAQ,CAAC9B,aAAa,CAAC,CAAC;MAE3F,IAAIX,kBAAkB,EAAE;QACtB;QACA,MAAM6C,eAAe,GAAGlF,iBAAiB,CAACgF,cAAc,CAAC,aAAa,EAAEF,QAAQ,CAAC9B,aAAa,CAAC,CAAC;QAChG;QACA,IAAIkC,eAAe,IAAIA,eAAe,CAAC/B,MAAM,EAAE;UAC7C+B,eAAe,CAAC/B,MAAM,GAAGC,iBAAiB,CAAC8B,eAAe,CAAC/B,MAAM,CAAC;QACpE;QACA3B,aAAa,CAAC0D,eAAe,CAAC;QAC9B/C,mBAAmB,CAAC;UAClBC,eAAe,EAAE,KAAK;UACtBC,kBAAkB,EAAE,IAAI;UACxBC,uBAAuB,EAAE,IAAI;UAC7BC,OAAO,EAAE;QACX,CAAC,CAAC;MACJ,CAAC,MAAM;QACL;QACA,MAAMU,WAAW,GAAGjD,iBAAiB,CAACkD,oBAAoB,CAAC,CAAC;QAC5D;QACA,IAAID,WAAW,CAACE,MAAM,EAAE;UACtBF,WAAW,CAACE,MAAM,GAAGC,iBAAiB,CAACH,WAAW,CAACE,MAAM,CAAC;QAC5D;QACA3B,aAAa,CAACyB,WAAW,CAAC;QAC1Bd,mBAAmB,CAAC;UAClBC,eAAe,EAAE,KAAK;UACtBC,kBAAkB,EAAE,KAAK;UACzBC,uBAAuB,EAAE,IAAI;UAC7BC,OAAO,EAAE;QACX,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAOe,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;MAC7D1B,SAAS,CAAC2C,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAExC,IAAI,EAAE;MAAmC,CAAC,CAAC,CAAC;IAC5E,CAAC,SAAS;MACRD,UAAU,CAACyC,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAExC,IAAI,EAAE;MAAM,CAAC,CAAC,CAAC;IAChD;EACF,CAAC;;EAED;EACA,MAAMoD,oBAAoB,GAAIC,CAAC,IAAK;IAClC,MAAMC,KAAK,GAAGD,CAAC,CAACE,MAAM,CAACD,KAAK;IAC5BnE,mBAAmB,CAACmE,KAAK,CAAC;;IAE1B;IACA3D,WAAW,CAAC6C,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP1B,UAAU,EAAEwC,KAAK,GAAGP,QAAQ,CAACO,KAAK,CAAC,GAAG,IAAI;MAC1CtC,UAAU,EAAE,IAAI;MAChBC,aAAa,EAAE;IACjB,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMuC,oBAAoB,GAAIH,CAAC,IAAK;IAClC,MAAMC,KAAK,GAAGD,CAAC,CAACE,MAAM,CAACD,KAAK;IAC5BjE,mBAAmB,CAACiE,KAAK,CAAC;;IAE1B;IACA3D,WAAW,CAAC6C,IAAI,KAAK;MACnB,GAAGA,IAAI;MACPxB,UAAU,EAAEsC,KAAK,GAAGP,QAAQ,CAACO,KAAK,CAAC,GAAG,IAAI;MAC1CrC,aAAa,EAAE;IACjB,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMwC,uBAAuB,GAAIJ,CAAC,IAAK;IACrC,MAAMC,KAAK,GAAGD,CAAC,CAACE,MAAM,CAACD,KAAK;IAC5B/D,sBAAsB,CAAC+D,KAAK,CAAC;;IAE7B;IACA3D,WAAW,CAAC6C,IAAI,KAAK;MACnB,GAAGA,IAAI;MACPvB,aAAa,EAAEqC,KAAK,GAAGP,QAAQ,CAACO,KAAK,CAAC,GAAG;IAC3C,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMI,iBAAiB,GAAGA,CAACC,QAAQ,EAAEL,KAAK,KAAK;IAC7C3D,WAAW,CAAC6C,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACmB,QAAQ,GAAGL;IACd,CAAC,CAAC,CAAC;;IAEH;IACA,IAAI1D,MAAM,CAAC+D,QAAQ,CAAC,EAAE;MACpB9D,SAAS,CAAC2C,IAAI,KAAK;QACjB,GAAGA,IAAI;QACP,CAACmB,QAAQ,GAAG;MACd,CAAC,CAAC,CAAC;IACL;EACF,CAAC;EAED,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,SAAS,GAAG,CAAC,CAAC;;IAEpB;IACA,IAAI,CAAC3E,gBAAgB,EAAE;MACrB2E,SAAS,CAACC,QAAQ,GAAG,sBAAsB;IAC7C;IACA,IAAI,CAAC1E,gBAAgB,EAAE;MACrByE,SAAS,CAACE,QAAQ,GAAG,sBAAsB;IAC7C;;IAEA;IACA,IAAI,CAACvE,UAAU,EAAE;MACfqE,SAAS,CAAC7D,IAAI,GAAG,kDAAkD;MACnE,OAAO;QAAEgE,OAAO,EAAE,KAAK;QAAEpE,MAAM,EAAEiE;MAAU,CAAC;IAC9C;;IAIA;IACArE,UAAU,CAAC4B,MAAM,CAACQ,OAAO,CAACC,KAAK,IAAI;MACjC,MAAMyB,KAAK,GAAG5D,QAAQ,CAACmC,KAAK,CAACE,GAAG,CAAC;;MAEjC;MACA,IAAIF,KAAK,CAACoC,QAAQ,KAAK,CAACX,KAAK,IAAK,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACY,IAAI,CAAC,CAAC,KAAK,EAAG,CAAC,EAAE;QACpFL,SAAS,CAAChC,KAAK,CAACE,GAAG,CAAC,GAAG,GAAGF,KAAK,CAACsC,KAAK,cAAc;QACnD;MACF;;MAEA;MACA,IAAItC,KAAK,CAACuC,WAAW,IAAIC,eAAe,CAACxC,KAAK,CAAC,EAAE;QAC/C,IAAIA,KAAK,CAACoC,QAAQ,KAAK,CAACX,KAAK,IAAK,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACY,IAAI,CAAC,CAAC,KAAK,EAAG,CAAC,EAAE;UACpFL,SAAS,CAAChC,KAAK,CAACE,GAAG,CAAC,GAAG,GAAGF,KAAK,CAACsC,KAAK,cAAc;UACnD;QACF;MACF;;MAEA;MACA,IAAIb,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACY,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;QAAA,IAAAI,iBAAA,EAAAC,kBAAA,EAAAC,kBAAA;QAC7D,QAAQ3C,KAAK,CAAC4C,IAAI;UAChB,KAAK,OAAO;YACV,MAAMC,UAAU,GAAG,4BAA4B;YAC/C,IAAI,CAACA,UAAU,CAACC,IAAI,CAACrB,KAAK,CAAC,EAAE;cAC3BO,SAAS,CAAChC,KAAK,CAACE,GAAG,CAAC,GAAG,oCAAoC;YAC7D;YACA;UACF,KAAK,KAAK;YACR,MAAM6C,UAAU,GAAG,qCAAqC;YACxD,IAAI,CAACA,UAAU,CAACD,IAAI,CAACrB,KAAK,CAAC,EAAE;cAC3BO,SAAS,CAAChC,KAAK,CAACE,GAAG,CAAC,GAAG,oCAAoC;YAC7D;YACA;UACF,KAAK,KAAK;YACR,IAAI;cACF,IAAI8C,GAAG,CAACvB,KAAK,CAAC;YAChB,CAAC,CAAC,MAAM;cACNO,SAAS,CAAChC,KAAK,CAACE,GAAG,CAAC,GAAG,0BAA0B;YACnD;YACA;UACF,KAAK,QAAQ;YACX,IAAIF,KAAK,CAACiD,UAAU,EAAE;cACpB,MAAMC,QAAQ,GAAGC,UAAU,CAAC1B,KAAK,CAAC;cAClC,IAAIzB,KAAK,CAACiD,UAAU,CAACG,GAAG,KAAKC,SAAS,IAAIH,QAAQ,GAAGlD,KAAK,CAACiD,UAAU,CAACG,GAAG,EAAE;gBACzEpB,SAAS,CAAChC,KAAK,CAACE,GAAG,CAAC,GAAG,0BAA0BF,KAAK,CAACiD,UAAU,CAACG,GAAG,EAAE;cACzE;cACA,IAAIpD,KAAK,CAACiD,UAAU,CAACK,GAAG,KAAKD,SAAS,IAAIH,QAAQ,GAAGlD,KAAK,CAACiD,UAAU,CAACK,GAAG,EAAE;gBACzEtB,SAAS,CAAChC,KAAK,CAACE,GAAG,CAAC,GAAG,yBAAyBF,KAAK,CAACiD,UAAU,CAACK,GAAG,EAAE;cACxE;YACF;YACA;QACJ;;QAEA;QACA,KAAAb,iBAAA,GAAIzC,KAAK,CAACiD,UAAU,cAAAR,iBAAA,eAAhBA,iBAAA,CAAkBc,OAAO,EAAE;UAC7B,MAAMC,KAAK,GAAG,IAAIC,MAAM,CAACzD,KAAK,CAACiD,UAAU,CAACM,OAAO,CAAC;UAClD,IAAI,CAACC,KAAK,CAACV,IAAI,CAACrB,KAAK,CAAC,EAAE;YACtBO,SAAS,CAAChC,KAAK,CAACE,GAAG,CAAC,GAAG,GAAGF,KAAK,CAACsC,KAAK,oBAAoB;UAC3D;QACF;;QAEA;QACA,IAAI,CAAAI,kBAAA,GAAA1C,KAAK,CAACiD,UAAU,cAAAP,kBAAA,eAAhBA,kBAAA,CAAkBgB,SAAS,IAAIjC,KAAK,CAACnB,MAAM,GAAGN,KAAK,CAACiD,UAAU,CAACS,SAAS,EAAE;UAC5E1B,SAAS,CAAChC,KAAK,CAACE,GAAG,CAAC,GAAG,GAAGF,KAAK,CAACsC,KAAK,qBAAqBtC,KAAK,CAACiD,UAAU,CAACS,SAAS,aAAa;QACnG;QACA,IAAI,CAAAf,kBAAA,GAAA3C,KAAK,CAACiD,UAAU,cAAAN,kBAAA,eAAhBA,kBAAA,CAAkBgB,SAAS,IAAIlC,KAAK,CAACnB,MAAM,GAAGN,KAAK,CAACiD,UAAU,CAACU,SAAS,EAAE;UAC5E3B,SAAS,CAAChC,KAAK,CAACE,GAAG,CAAC,GAAG,GAAGF,KAAK,CAACsC,KAAK,oBAAoBtC,KAAK,CAACiD,UAAU,CAACU,SAAS,aAAa;QAClG;MACF;IACF,CAAC,CAAC;;IAEF;IACA,IAAI9F,QAAQ,CAAC+F,SAAS,IAAI/F,QAAQ,CAACgG,cAAc,IAAIhG,QAAQ,CAACiG,WAAW,EAAE;MACzE,MAAMC,SAAS,GAAG,IAAIC,IAAI,CAACnG,QAAQ,CAACiG,WAAW,CAAC;MAChD,MAAMG,YAAY,GAAG,IAAID,IAAI,CAACnG,QAAQ,CAACgG,cAAc,CAAC;MACtD,IAAII,YAAY,IAAIF,SAAS,EAAE;QAC7B/B,SAAS,CAAC6B,cAAc,GAAG,wCAAwC;MACrE;IACF;IAEA,OAAO;MACL1B,OAAO,EAAE+B,MAAM,CAACC,IAAI,CAACnC,SAAS,CAAC,CAAC1B,MAAM,KAAK,CAAC;MAC5CvC,MAAM,EAAEiE;IACV,CAAC;EACH,CAAC;EAED,MAAMoC,YAAY,GAAG,MAAO5C,CAAC,IAAK;IAChCA,CAAC,CAAC6C,cAAc,CAAC,CAAC;IAElB,MAAMpB,UAAU,GAAGlB,YAAY,CAAC,CAAC;IACjC,IAAI,CAACkB,UAAU,CAACd,OAAO,EAAE;MACvBnE,SAAS,CAACiF,UAAU,CAAClF,MAAM,CAAC;MAC5B;IACF;IAEAM,aAAa,CAAC,IAAI,CAAC;IACnB,IAAI;MACF;MACA,MAAMiG,UAAU,GAAG;QACjB;QACArF,UAAU,EAAEiC,QAAQ,CAAC7D,gBAAgB,CAAC;QACtC8B,UAAU,EAAE+B,QAAQ,CAAC3D,gBAAgB,CAAC;QACtC6B,aAAa,EAAE3B,mBAAmB,GAAGyD,QAAQ,CAACzD,mBAAmB,CAAC,GAAG,IAAI;QAEzE;QACA8G,IAAI,EAAE1G,QAAQ,CAAC0G,IAAI,IAAI,EAAE;QACzBC,YAAY,EAAE3G,QAAQ,CAAC2G,YAAY,IAAI,EAAE;QACzCC,MAAM,EAAE5G,QAAQ,CAAC4G,MAAM,GAAGvD,QAAQ,CAACrD,QAAQ,CAAC4G,MAAM,CAAC,GAAG,IAAI;QAE1D;QACAC,MAAM,EAAE7G,QAAQ,CAAC6G,MAAM,GAAGxD,QAAQ,CAACrD,QAAQ,CAAC6G,MAAM,CAAC,GAAG,IAAI;QAE1D;QACAC,gBAAgB,EAAEC,KAAK,CAACC,OAAO,CAAChH,QAAQ,CAAC8G,gBAAgB,CAAC,GACtD9G,QAAQ,CAAC8G,gBAAgB,GACzB9G,QAAQ,CAAC8G,gBAAgB,GAAG9G,QAAQ,CAAC8G,gBAAgB,CAACG,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,CAAC,IAAIA,CAAC,CAAC3C,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE;QAC5F4C,iBAAiB,EAAEL,KAAK,CAACC,OAAO,CAAChH,QAAQ,CAACoH,iBAAiB,CAAC,GACxDpH,QAAQ,CAACoH,iBAAiB,GAC1BpH,QAAQ,CAACoH,iBAAiB,GAAGpH,QAAQ,CAACoH,iBAAiB,CAACH,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,CAAC,IAAIA,CAAC,CAAC3C,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE;QAE9F;QACAyB,WAAW,EAAEjG,QAAQ,CAACiG,WAAW,IAAI,IAAI;QACzCF,SAAS,EAAE/F,QAAQ,CAAC+F,SAAS,IAAI,KAAK;QACtCC,cAAc,EAAEhG,QAAQ,CAACgG,cAAc,IAAI,IAAI;QAE/C;QACAqB,YAAY,EAAErH,QAAQ,CAACqH,YAAY,IAAI,EAAE;QACzCC,aAAa,EAAEtH,QAAQ,CAACsH,aAAa,IAAI,EAAE;QAC3CC,QAAQ,EAAEvH,QAAQ,CAACuH,QAAQ,IAAI,EAAE;QACjCC,OAAO,EAAExH,QAAQ,CAACwH,OAAO,IAAI,EAAE;QAC/BC,WAAW,EAAEzH,QAAQ,CAACyH,WAAW,IAAI,EAAE;QAEvC;QACAC,YAAY,EAAE1H,QAAQ,CAAC0H,YAAY,IAAI,KAAK;QAC5CC,aAAa,EAAE3H,QAAQ,CAAC2H,aAAa,IAAI,EAAE;QAC3CC,iBAAiB,EAAE5H,QAAQ,CAAC4H,iBAAiB,IAAI,EAAE;QACnDC,eAAe,EAAE7H,QAAQ,CAAC6H,eAAe,IAAI,EAAE;QAE/C;QACAC,YAAY,EAAE9H,QAAQ,CAAC8H,YAAY,IAAI,KAAK;QAC5CC,WAAW,EAAE/H,QAAQ,CAAC+H,WAAW,IAAI,KAAK;QAE1C;QACAC,gBAAgB,EAAEhI,QAAQ,CAACgI,gBAAgB,GAAG1C,UAAU,CAACtF,QAAQ,CAACgI,gBAAgB,CAAC,GAAG,IAAI;QAC1FC,sBAAsB,EAAEjI,QAAQ,CAACiI,sBAAsB,IAAI,EAAE;QAC7DC,eAAe,EAAEnB,KAAK,CAACC,OAAO,CAAChH,QAAQ,CAACkI,eAAe,CAAC,GACpDlI,QAAQ,CAACkI,eAAe,CAAChB,GAAG,CAACiB,EAAE,IAAI9E,QAAQ,CAAC8E,EAAE,CAAC,CAAC,GAChD,EAAE;QACNC,UAAU,EAAEpI,QAAQ,CAACoI,UAAU,GAAG/E,QAAQ,CAACrD,QAAQ,CAACoI,UAAU,CAAC,GAAG,IAAI;QACtEC,MAAM,EAAErI,QAAQ,CAACqI,MAAM,IAAI,EAAE;QAC7BC,OAAO,EAAEtI,QAAQ,CAACsI,OAAO,IAAI,EAAE;QAE/B;QACAC,QAAQ,EAAEvI,QAAQ,CAACuI,QAAQ,IAAI,EAAE;QACjCC,eAAe,EAAExI,QAAQ,CAACwI,eAAe,GAAGnF,QAAQ,CAACrD,QAAQ,CAACwI,eAAe,CAAC,GAAG,IAAI;QACrFC,gBAAgB,EAAEzI,QAAQ,CAACyI,gBAAgB,GAAGpF,QAAQ,CAACrD,QAAQ,CAACyI,gBAAgB,CAAC,GAAG,IAAI;QACxFC,qBAAqB,EAAE1I,QAAQ,CAAC0I,qBAAqB,GAAGrF,QAAQ,CAACrD,QAAQ,CAAC0I,qBAAqB,CAAC,GAAG,IAAI;QAEvG;QACAC,oBAAoB,EAAE3I,QAAQ,CAAC2I,oBAAoB,IAAI,EAAE;QACzDC,WAAW,EAAE5I,QAAQ,CAAC4I,WAAW,IAAI,EAAE;QAEvC;QACAC,gBAAgB,EAAE7I,QAAQ,CAAC6I,gBAAgB,IAAI,EAAE;QACjDC,oBAAoB,EAAE9I,QAAQ,CAAC8I,oBAAoB,IAAI,EAAE;QACzDC,cAAc,EAAE/I,QAAQ,CAAC+I,cAAc,IAAI,EAAE;QAC7CC,UAAU,EAAEhJ,QAAQ,CAACgJ,UAAU,IAAI;MACrC,CAAC;;MAED;MACA;MACAlH,OAAO,CAACY,GAAG,CAAC,kCAAkC,CAAC;MAC/CZ,OAAO,CAACY,GAAG,CAAC,iBAAiB,EAAE,IAAI1C,QAAQ,CAACiJ,cAAc,GAAG,CAAC;MAC9DnH,OAAO,CAACY,GAAG,CAAC,wBAAwB,EAAE,IAAI1C,QAAQ,CAACkJ,qBAAqB,GAAG,CAAC;MAC5EpH,OAAO,CAACY,GAAG,CAAC,UAAU,EAAE,IAAI1C,QAAQ,CAACmJ,OAAO,GAAG,CAAC;MAChDrH,OAAO,CAACY,GAAG,CAAC,cAAc,EAAE,IAAI1C,QAAQ,CAACoJ,WAAW,GAAG,CAAC;MACxDtH,OAAO,CAACY,GAAG,CAAC,aAAa,EAAE,IAAI1C,QAAQ,CAACqJ,UAAU,GAAG,CAAC;;MAEtD;MACA,IAAIrJ,QAAQ,CAACiJ,cAAc,IAAIjJ,QAAQ,CAACiJ,cAAc,KAAK,IAAI,IAAI,OAAOjJ,QAAQ,CAACiJ,cAAc,KAAK,QAAQ,IAAIjJ,QAAQ,CAACiJ,cAAc,CAACzE,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;QACvJiC,UAAU,CAAC,gBAAgB,CAAC,GAAGzG,QAAQ,CAACiJ,cAAc,CAACzE,IAAI,CAAC,CAAC;QAC7D1C,OAAO,CAACY,GAAG,CAAC,wBAAwB,EAAE1C,QAAQ,CAACiJ,cAAc,CAACzE,IAAI,CAAC,CAAC,CAAC;MACvE,CAAC,MAAM;QACL1C,OAAO,CAACY,GAAG,CAAC,mDAAmD,CAAC;MAClE;MAEA,IAAI1C,QAAQ,CAACkJ,qBAAqB,IAAIlJ,QAAQ,CAACkJ,qBAAqB,KAAK,IAAI,IAAI,OAAOlJ,QAAQ,CAACkJ,qBAAqB,KAAK,QAAQ,IAAIlJ,QAAQ,CAACkJ,qBAAqB,CAAC1E,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;QACnLiC,UAAU,CAAC,uBAAuB,CAAC,GAAGzG,QAAQ,CAACkJ,qBAAqB,CAAC1E,IAAI,CAAC,CAAC;QAC3E1C,OAAO,CAACY,GAAG,CAAC,+BAA+B,EAAE1C,QAAQ,CAACkJ,qBAAqB,CAAC1E,IAAI,CAAC,CAAC,CAAC;MACrF,CAAC,MAAM;QACL1C,OAAO,CAACY,GAAG,CAAC,0DAA0D,CAAC;MACzE;;MAEA;MACA,IAAI1C,QAAQ,CAACmJ,OAAO,IAAInJ,QAAQ,CAACmJ,OAAO,KAAK,IAAI,IAAI,OAAOnJ,QAAQ,CAACmJ,OAAO,KAAK,QAAQ,IAAInJ,QAAQ,CAACmJ,OAAO,CAAC3E,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;QAC3HiC,UAAU,CAAC,SAAS,CAAC,GAAGzG,QAAQ,CAACmJ,OAAO,CAAC3E,IAAI,CAAC,CAAC;QAC/C1C,OAAO,CAACY,GAAG,CAAC,iBAAiB,EAAE1C,QAAQ,CAACmJ,OAAO,CAAC3E,IAAI,CAAC,CAAC,CAAC;MACzD,CAAC,MAAM;QACL1C,OAAO,CAACY,GAAG,CAAC,4CAA4C,CAAC;MAC3D;MAEA,IAAI1C,QAAQ,CAACoJ,WAAW,IAAIpJ,QAAQ,CAACoJ,WAAW,KAAK,IAAI,IAAI,OAAOpJ,QAAQ,CAACoJ,WAAW,KAAK,QAAQ,IAAIpJ,QAAQ,CAACoJ,WAAW,CAAC5E,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;QAC3IiC,UAAU,CAAC,aAAa,CAAC,GAAGzG,QAAQ,CAACoJ,WAAW,CAAC5E,IAAI,CAAC,CAAC;QACvD1C,OAAO,CAACY,GAAG,CAAC,qBAAqB,EAAE1C,QAAQ,CAACoJ,WAAW,CAAC5E,IAAI,CAAC,CAAC,CAAC;MACjE,CAAC,MAAM;QACL1C,OAAO,CAACY,GAAG,CAAC,gDAAgD,CAAC;MAC/D;MAEA,IAAI1C,QAAQ,CAACqJ,UAAU,IAAIrJ,QAAQ,CAACqJ,UAAU,KAAK,IAAI,IAAI,OAAOrJ,QAAQ,CAACqJ,UAAU,KAAK,QAAQ,IAAIrJ,QAAQ,CAACqJ,UAAU,CAAC7E,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;QACvIiC,UAAU,CAAC,YAAY,CAAC,GAAGzG,QAAQ,CAACqJ,UAAU,CAAC7E,IAAI,CAAC,CAAC;QACrD1C,OAAO,CAACY,GAAG,CAAC,oBAAoB,EAAE1C,QAAQ,CAACqJ,UAAU,CAAC7E,IAAI,CAAC,CAAC,CAAC;MAC/D,CAAC,MAAM;QACL1C,OAAO,CAACY,GAAG,CAAC,+CAA+C,CAAC;MAC9D;;MAEA;MACAZ,OAAO,CAACY,GAAG,CAAC,0BAA0B,EAAE+D,UAAU,CAAC;MACnD3E,OAAO,CAACY,GAAG,CAAC,2BAA2B,CAAC;MACxC2D,MAAM,CAACC,IAAI,CAACG,UAAU,CAAC,CAACvE,OAAO,CAACG,GAAG,IAAI;QACrCP,OAAO,CAACY,GAAG,CAAC,KAAKL,GAAG,GAAG,EAAE,OAAOoE,UAAU,CAACpE,GAAG,CAAC,EAAE,IAAIoE,UAAU,CAACpE,GAAG,CAAC,GAAG,CAAC;MAC1E,CAAC,CAAC;MACFP,OAAO,CAACY,GAAG,CAAC,oBAAoB,EAAElD,gBAAgB,CAAC;MACnDsC,OAAO,CAACY,GAAG,CAAC,oBAAoB,EAAEhD,gBAAgB,CAAC;MACnDoC,OAAO,CAACY,GAAG,CAAC,uBAAuB,EAAE9C,mBAAmB,CAAC;;MAEzD;MACA,MAAM0J,mBAAmB,GAAG;QAC1BlI,UAAU,EAAEqF,UAAU,CAACrF,UAAU;QACjCE,UAAU,EAAEmF,UAAU,CAACnF,UAAU;QACjCoF,IAAI,EAAED,UAAU,CAACC,IAAI;QACrBC,YAAY,EAAEF,UAAU,CAACE,YAAY;QACrCC,MAAM,EAAEH,UAAU,CAACG;MACrB,CAAC;MACD9E,OAAO,CAACY,GAAG,CAAC,wBAAwB,EAAE4G,mBAAmB,CAAC;;MAE1D;MACA,MAAMC,aAAa,GAAGlD,MAAM,CAACmD,OAAO,CAACF,mBAAmB,CAAC,CACtDG,MAAM,CAAC,CAAC,GAAG7F,KAAK,CAAC,KAAK,CAACA,KAAK,IAAIA,KAAK,KAAK,EAAE,IAAIA,KAAK,KAAK,IAAI,CAAC,CAC/DsD,GAAG,CAAC,CAAC,CAAC7E,GAAG,CAAC,KAAKA,GAAG,CAAC;MAEtB,IAAIkH,aAAa,CAAC9G,MAAM,GAAG,CAAC,EAAE;QAC5BX,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAE0H,aAAa,CAAC;QACxDpJ,SAAS,CAAC;UAAEuJ,OAAO,EAAE,2BAA2B,GAAGH,aAAa,CAACI,IAAI,CAAC,IAAI;QAAE,CAAC,CAAC;QAC9E;MACF;;MAEA;MACA,MAAMC,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,2BAA2B;MAC5EjI,OAAO,CAACY,GAAG,CAAC,qBAAqB,EAAE,GAAGkH,OAAO,UAAU,CAAC;;MAExD;MACA,IAAI;QACF9H,OAAO,CAACY,GAAG,CAAC,0BAA0B,EAAE,GAAGkH,OAAO,YAAY,CAAC;QAC/D,MAAMI,YAAY,GAAG,MAAMC,KAAK,CAAC,GAAGL,OAAO,YAAY,EAAE;UACvDM,MAAM,EAAE,KAAK;UACbC,OAAO,EAAE;YAAE,cAAc,EAAE;UAAmB;QAChD,CAAC,CAAC;QACFrI,OAAO,CAACY,GAAG,CAAC,mCAAmC,EAAE;UAC/C0H,MAAM,EAAEJ,YAAY,CAACI,MAAM;UAC3BC,EAAE,EAAEL,YAAY,CAACK,EAAE;UACnBC,UAAU,EAAEN,YAAY,CAACM,UAAU;UACnCC,GAAG,EAAEP,YAAY,CAACO;QACpB,CAAC,CAAC;QAEF,IAAI,CAACP,YAAY,CAACK,EAAE,EAAE;UACpBvI,OAAO,CAACD,KAAK,CAAC,wDAAwD,CAAC;UACvE,MAAM2I,SAAS,GAAG,MAAMR,YAAY,CAACS,IAAI,CAAC,CAAC;UAC3C3I,OAAO,CAACD,KAAK,CAAC,iBAAiB,EAAE2I,SAAS,CAAC;QAC7C;MACF,CAAC,CAAC,OAAOE,YAAY,EAAE;QACrB5I,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAE6I,YAAY,CAAC;QAC1D5I,OAAO,CAACD,KAAK,CAAC,mEAAmE,CAAC;QAClF1B,SAAS,CAAC;UAAEuJ,OAAO,EAAE;QAA4F,CAAC,CAAC;QACnH;MACF;;MAEA;MACA,IAAI1K,IAAI,KAAK,QAAQ,EAAE;QACrB8C,OAAO,CAACY,GAAG,CAAC,qCAAqC,CAAC;QAClD,IAAI;UACF,MAAMiI,gBAAgB,GAAG,MAAMV,KAAK,CAAC,GAAGL,OAAO,UAAU,EAAE;YACzDM,MAAM,EAAE,SAAS;YAAE;YACnBC,OAAO,EAAE;cAAE,cAAc,EAAE;YAAmB;UAChD,CAAC,CAAC;UACFrI,OAAO,CAACY,GAAG,CAAC,gCAAgC,EAAE;YAC5C0H,MAAM,EAAEO,gBAAgB,CAACP,MAAM;YAC/BC,EAAE,EAAEM,gBAAgB,CAACN,EAAE;YACvBF,OAAO,EAAE,CAAC,GAAGQ,gBAAgB,CAACR,OAAO,CAACX,OAAO,CAAC,CAAC;UACjD,CAAC,CAAC;QACJ,CAAC,CAAC,OAAOoB,YAAY,EAAE;UACrB9I,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAE+I,YAAY,CAAC;QAC9D;MACF;MAEA,IAAIC,MAAM;MACV,IAAI7L,IAAI,KAAK,QAAQ,EAAE;QACrB8C,OAAO,CAACY,GAAG,CAAC,4CAA4C,EAAE+D,UAAU,CAAC;QACrEoE,MAAM,GAAG,MAAMrM,UAAU,CAACsM,YAAY,CAACrE,UAAU,CAAC;MACpD,CAAC,MAAM;QACL3E,OAAO,CAACY,GAAG,CAAC,4CAA4C,EAAE+D,UAAU,CAAC;QACrEoE,MAAM,GAAG,MAAMrM,UAAU,CAACuM,YAAY,CAAChM,WAAW,CAACiM,EAAE,EAAEvE,UAAU,CAAC;MACpE;MAEA5H,QAAQ,CAACgM,MAAM,CAAC;IAClB,CAAC,CAAC,OAAOhJ,KAAK,EAAE;MAAA,IAAAoJ,WAAA;MACdnJ,OAAO,CAACD,KAAK,CAAC,6BAA6B,CAAC;MAC5CC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9CC,OAAO,CAACD,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC6E,IAAI,CAAC;MACxC5E,OAAO,CAACD,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAACf,OAAO,CAAC;MAC9CgB,OAAO,CAACD,KAAK,CAAC,eAAe,EAAEA,KAAK,CAACuI,MAAM,CAAC;MAC5CtI,OAAO,CAACD,KAAK,CAAC,aAAa,EAAEA,KAAK,CAACmB,IAAI,CAAC;MACxClB,OAAO,CAACD,KAAK,CAAC,kBAAkB,EAAE,OAAOA,KAAK,CAACmB,IAAI,CAAC;MACpDlB,OAAO,CAACD,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAACmB,IAAI,GAAGqD,MAAM,CAACC,IAAI,CAACzE,KAAK,CAACmB,IAAI,CAAC,GAAG,SAAS,CAAC;MACnFlB,OAAO,CAACD,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1CC,OAAO,CAACD,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAACqJ,WAAW,CAACxE,IAAI,CAAC;MAC3D5E,OAAO,CAACD,KAAK,CAAC,cAAc,EAAEA,KAAK,CAACsJ,KAAK,CAAC;MAC1CrJ,OAAO,CAACD,KAAK,CAAC,2BAA2B,CAAC;;MAE1C;MACA,IAAIuJ,SAAS,GAAG;QACdC,OAAO,EAAE,CAAC,CAACxJ,KAAK,CAACmB,IAAI;QACrBsI,SAAS,EAAE,CAAC,EAAEzJ,KAAK,CAACmB,IAAI,IAAInB,KAAK,CAACmB,IAAI,CAAC9C,MAAM,CAAC;QAC9CqL,UAAU,EAAE,CAAC,EAAE1J,KAAK,CAACmB,IAAI,IAAInB,KAAK,CAACmB,IAAI,CAAClC,OAAO,CAAC;QAChD0K,QAAQ,EAAE,CAAC,EAAE3J,KAAK,CAACmB,IAAI,IAAInB,KAAK,CAACmB,IAAI,CAACyI,KAAK,CAAC;QAC5CrB,MAAM,EAAEvI,KAAK,CAACuI,MAAM;QACpBsB,iBAAiB,EAAE7J,KAAK,CAAC6J,iBAAiB,GAAG7J,KAAK,CAAC6J,iBAAiB,CAAC,CAAC,GAAG;MAC3E,CAAC;MACD5J,OAAO,CAACY,GAAG,CAAC,iBAAiB,EAAE0I,SAAS,CAAC;MAEzC,KAAAH,WAAA,GAAIpJ,KAAK,CAACmB,IAAI,cAAAiI,WAAA,eAAVA,WAAA,CAAY/K,MAAM,EAAE;QACtB;QACA,MAAMyL,aAAa,GAAG,CAAC,CAAC;QACxBtF,MAAM,CAACC,IAAI,CAACzE,KAAK,CAACmB,IAAI,CAAC9C,MAAM,CAAC,CAACgC,OAAO,CAACG,GAAG,IAAI;UAC5C,MAAMuJ,aAAa,GAAG/J,KAAK,CAACmB,IAAI,CAAC9C,MAAM,CAACmC,GAAG,CAAC;UAC5CsJ,aAAa,CAACtJ,GAAG,CAACwJ,WAAW,CAAC,CAAC,CAAC,GAAG9E,KAAK,CAACC,OAAO,CAAC4E,aAAa,CAAC,GAC3DA,aAAa,CAACjC,IAAI,CAAC,IAAI,CAAC,GACxBiC,aAAa;QACnB,CAAC,CAAC;QACF9J,OAAO,CAACY,GAAG,CAAC,4BAA4B,EAAEiJ,aAAa,CAAC;QACxDxL,SAAS,CAACwL,aAAa,CAAC;MAC1B,CAAC,MAAM,IAAI9J,KAAK,CAAC6J,iBAAiB,IAAI7J,KAAK,CAAC6J,iBAAiB,CAAC,CAAC,EAAE;QAC/D,MAAMI,gBAAgB,GAAGjK,KAAK,CAACkK,mBAAmB,CAAC,CAAC;QACpDjK,OAAO,CAACY,GAAG,CAAC,gCAAgC,EAAEoJ,gBAAgB,CAAC;QAC/D3L,SAAS,CAAC2L,gBAAgB,CAAC;MAC7B,CAAC,MAAM;QAAA,IAAAE,YAAA,EAAAC,YAAA;QACL,MAAMC,YAAY,GAAG,EAAAF,YAAA,GAAAnK,KAAK,CAACmB,IAAI,cAAAgJ,YAAA,uBAAVA,YAAA,CAAYlL,OAAO,OAAAmL,YAAA,GACrBpK,KAAK,CAACmB,IAAI,cAAAiJ,YAAA,uBAAVA,YAAA,CAAYR,KAAK,KACjB5J,KAAK,CAACf,OAAO,IACb,QAAQe,KAAK,CAACuI,MAAM,IAAI,SAAS,mCAAmC;QACvFtI,OAAO,CAACY,GAAG,CAAC,wBAAwB,EAAEwJ,YAAY,CAAC;QACnD/L,SAAS,CAAC;UAAEuJ,OAAO,EAAEwC;QAAa,CAAC,CAAC;MACtC;IACF,CAAC,SAAS;MACR1L,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;EAED,MAAMmE,eAAe,GAAIxC,KAAK,IAAK;IACjC,IAAI,CAACA,KAAK,CAACuC,WAAW,EAAE,OAAO,IAAI;IAEnC,MAAMyH,cAAc,GAAGnM,QAAQ,CAACmC,KAAK,CAACuC,WAAW,CAACvC,KAAK,CAAC;IACxD,MAAMiK,aAAa,GAAGjK,KAAK,CAACuC,WAAW,CAACd,KAAK;;IAE7C;IACA,IAAI,OAAOwI,aAAa,KAAK,SAAS,IAAIA,aAAa,KAAK,MAAM,IAAIA,aAAa,KAAK,OAAO,EAAE;MAC/F,OAAOD,cAAc,MAAMC,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,MAAM,CAAC;IAChF;IAEA,OAAOD,cAAc,KAAKC,aAAa;EACzC,CAAC;EAED,MAAMC,qBAAqB,GAAGA,CAAA,KAAM;IAClC,IAAI,CAACvM,UAAU,IAAI,CAACA,UAAU,CAAC4B,MAAM,EAAE,OAAO,CAAC,CAAC;IAEhD,MAAM4K,QAAQ,GAAG,CAAC,CAAC;IACnBxM,UAAU,CAAC4B,MAAM,CAACQ,OAAO,CAAEC,KAAK,IAAK;MACnC,MAAMoK,UAAU,GAAGpK,KAAK,CAACqK,OAAO,IAAI,SAAS;MAC7C,IAAI,CAACF,QAAQ,CAACC,UAAU,CAAC,EAAE;QACzBD,QAAQ,CAACC,UAAU,CAAC,GAAG;UACrBd,KAAK,EAAEgB,eAAe,CAACF,UAAU,CAAC;UAClC7K,MAAM,EAAE;QACV,CAAC;MACH;MACA4K,QAAQ,CAACC,UAAU,CAAC,CAAC7K,MAAM,CAACa,IAAI,CAACJ,KAAK,CAAC;IACzC,CAAC,CAAC;IAEF,OAAOmK,QAAQ;EACjB,CAAC;EAED,MAAMG,eAAe,GAAIF,UAAU,IAAK;IACtC,MAAMG,MAAM,GAAG;MACbC,YAAY,EAAE,sBAAsB;MACpCC,WAAW,EAAE,qBAAqB;MAClCC,YAAY,EAAE,sBAAsB;MACpCC,YAAY,EAAE,sBAAsB;MACpCC,aAAa,EAAE,uBAAuB;MACtCC,eAAe,EAAE,kBAAkB;MACnCC,WAAW,EAAE,qBAAqB;MAClCC,gBAAgB,EAAE,mBAAmB;MACrCC,aAAa,EAAE,uBAAuB;MACtCzD,OAAO,EAAE;IACX,CAAC;IACD,OAAOgD,MAAM,CAACH,UAAU,CAAC,IAAIA,UAAU;EACzC,CAAC;EAED,MAAMD,QAAQ,GAAGxM,UAAU,GAAGuM,qBAAqB,CAAC,CAAC,GAAG,CAAC,CAAC;EAI1D,oBACE1N,OAAA;IAAKyO,SAAS,EAAC,qBAAqB;IAAAC,QAAA,gBAClC1O,OAAA;MAAKyO,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1B1O,OAAA;QAAA0O,QAAA,EAAKrO,IAAI,KAAK,QAAQ,GAAG,mBAAmB,GAAG;MAAa;QAAAsO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,EACjE3N,UAAU,iBACTnB,OAAA;QAAKyO,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxB1O,OAAA;UAAMyO,SAAS,EAAC,WAAW;UAAAC,QAAA,EAAEvN,UAAU,CAAC4G;QAAI;UAAA4G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,EACnD3N,UAAU,CAAC4N,WAAW,iBACrB/O,OAAA;UAAMyO,SAAS,EAAC,kBAAkB;UAAAC,QAAA,EAAEvN,UAAU,CAAC4N;QAAW;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAClE;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAELvN,MAAM,CAACwJ,OAAO,iBACb/K,OAAA;MAAKyO,SAAS,EAAC,mBAAmB;MAAAC,QAAA,EAAEnN,MAAM,CAACwJ;IAAO;MAAA4D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CACzD,eAED9O,OAAA;MAAME,QAAQ,EAAE0H,YAAa;MAAC6G,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAEnD1O,OAAA;QAAKyO,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3B1O,OAAA;UAAA0O,QAAA,EAAI;QAA6B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAGtC9O,OAAA;UAAKyO,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzB1O,OAAA;YAAOyO,SAAS,EAAC,YAAY;YAAAC,QAAA,GAAC,WACnB,eAAA1O,OAAA;cAAMyO,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC,eACR9O,OAAA;YACEiF,KAAK,EAAEpE,gBAAiB;YACxBmO,QAAQ,EAAEjK,oBAAqB;YAC/BkK,QAAQ,EAAExN,OAAO,CAAClB,SAAU;YAC5BkO,SAAS,EAAE,eAAelN,MAAM,CAACkE,QAAQ,GAAG,OAAO,GAAG,EAAE,EAAG;YAC3DG,QAAQ;YAAA8I,QAAA,gBAER1O,OAAA;cAAQiF,KAAK,EAAC,EAAE;cAAAyJ,QAAA,EACbjN,OAAO,CAAClB,SAAS,GAAG,sBAAsB,GAAG;YAAiB;cAAAoO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzD,CAAC,EACRvO,SAAS,CAACgI,GAAG,CAAC9C,QAAQ,iBACrBzF,OAAA;cAA0BiF,KAAK,EAAEQ,QAAQ,CAAC4G,EAAG;cAAAqC,QAAA,EAC1CjJ,QAAQ,CAACsC;YAAI,GADHtC,QAAQ,CAAC4G,EAAE;cAAAsC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEhB,CACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,EACRvN,MAAM,CAACkE,QAAQ,iBACdzF,OAAA;YAAKyO,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAEnN,MAAM,CAACkE;UAAQ;YAAAkJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACtD,EACAvN,MAAM,CAAChB,SAAS,iBACfP,OAAA;YAAKyO,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAEnN,MAAM,CAAChB;UAAS;YAAAoO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACvD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGN9O,OAAA;UAAKyO,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzB1O,OAAA;YAAOyO,SAAS,EAAC,YAAY;YAAAC,QAAA,GAAC,WACnB,eAAA1O,OAAA;cAAMyO,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC,eACR9O,OAAA;YACEiF,KAAK,EAAElE,gBAAiB;YACxBiO,QAAQ,EAAE7J,oBAAqB;YAC/B8J,QAAQ,EAAE,CAACpO,gBAAgB,IAAIY,OAAO,CAAChB,UAAW;YAClDgO,SAAS,EAAE,eAAelN,MAAM,CAACmE,QAAQ,GAAG,OAAO,GAAG,EAAE,EAAG;YAC3DE,QAAQ;YAAA8I,QAAA,gBAER1O,OAAA;cAAQiF,KAAK,EAAC,EAAE;cAAAyJ,QAAA,EACb,CAAC7N,gBAAgB,GACd,uBAAuB,GACvBY,OAAO,CAAChB,UAAU,GAChB,uBAAuB,GACvB;YAAiB;cAAAkO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEjB,CAAC,EACRrO,UAAU,CAAC8H,GAAG,CAAC7C,QAAQ,iBACtB1F,OAAA;cAA0BiF,KAAK,EAAES,QAAQ,CAAC2G,EAAG;cAAAqC,QAAA,EAC1ChJ,QAAQ,CAACqC;YAAI,GADHrC,QAAQ,CAAC2G,EAAE;cAAAsC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEhB,CACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,EACRvN,MAAM,CAACmE,QAAQ,iBACd1F,OAAA;YAAKyO,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAEnN,MAAM,CAACmE;UAAQ;YAAAiJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACtD,EACAvN,MAAM,CAACd,UAAU,iBAChBT,OAAA;YAAKyO,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAEnN,MAAM,CAACd;UAAU;YAAAkO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACxD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,EAGLhN,gBAAgB,CAACI,uBAAuB,iBACvClC,OAAA;UAAKyO,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzB1O,OAAA;YAAOyO,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAE9B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACR9O,OAAA;YACEiF,KAAK,EAAEhE,mBAAoB;YAC3B+N,QAAQ,EAAE5J,uBAAwB;YAClC6J,QAAQ,EAAE,CAAClO,gBAAgB,IAAIU,OAAO,CAACd,aAAc;YACrD8N,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAEvB1O,OAAA;cAAQiF,KAAK,EAAC,EAAE;cAAAyJ,QAAA,EACb,CAAC3N,gBAAgB,GACd,uBAAuB,GACvBU,OAAO,CAACd,aAAa,GACnB,0BAA0B,GAC1B;YAA+B;cAAAgO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAE/B,CAAC,EACRnO,aAAa,CAAC4H,GAAG,CAAC2G,WAAW,iBAC5BlP,OAAA;cAA6BiF,KAAK,EAAEiK,WAAW,CAAC7C,EAAG;cAAAqC,QAAA,EAChDQ,WAAW,CAACnH;YAAI,GADNmH,WAAW,CAAC7C,EAAE;cAAAsC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEnB,CACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,EACRvN,MAAM,CAACZ,aAAa,iBACnBX,OAAA;YAAKyO,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAEnN,MAAM,CAACZ;UAAa;YAAAgO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAC3D;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN,EAGArN,OAAO,CAACE,IAAI,iBACX3B,OAAA;UAAKyO,SAAS,EAAC,iBAAiB;UAAAC,QAAA,eAC9B1O,OAAA;YAAA0O,QAAA,EAAM;UAA6B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CACN,EAEAhN,gBAAgB,CAACK,OAAO,IAAI,CAACV,OAAO,CAACE,IAAI,iBACxC3B,OAAA;UAAKyO,SAAS,EAAE,kBACd3M,gBAAgB,CAACE,eAAe,IAAIF,gBAAgB,CAACG,kBAAkB,GACnE,SAAS,GACTH,gBAAgB,CAACK,OAAO,CAACgN,QAAQ,CAAC,SAAS,CAAC,GAC1C,OAAO,GACP,MAAM,EACX;UAAAT,QAAA,EACA5M,gBAAgB,CAACK;QAAO;UAAAwM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CACN,EAEAvN,MAAM,CAACI,IAAI,iBACV3B,OAAA;UAAKyO,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAEnN,MAAM,CAACI;QAAI;UAAAgN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAClD;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,EAGL3N,UAAU,IAAIuG,MAAM,CAACmD,OAAO,CAAC8C,QAAQ,CAAC,CAACpF,GAAG,CAAC,CAAC,CAACqF,UAAU,EAAEC,OAAO,CAAC,kBAChE7N,OAAA;QAAsByO,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC5C1O,OAAA;UAAA0O,QAAA,EAAKb,OAAO,CAACf;QAAK;UAAA6B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACxB9O,OAAA;UAAKyO,SAAS,EAAC,aAAa;UAAAC,QAAA,EACzBb,OAAO,CAAC9K,MAAM,CACZ+H,MAAM,CAACtH,KAAK,IAAIwC,eAAe,CAACxC,KAAK,CAAC,CAAC,CACvC+E,GAAG,CAAC,CAAC/E,KAAK,EAAE4L,UAAU,kBACrBpP,OAAA,CAACF,SAAS;YAER0D,KAAK,EAAEA,KAAM;YACbyB,KAAK,EAAE5D,QAAQ,CAACmC,KAAK,CAACE,GAAG,CAAE;YAC3BsL,QAAQ,EAAG/J,KAAK,IAAKI,iBAAiB,CAAC7B,KAAK,CAACE,GAAG,EAAEuB,KAAK,CAAE;YACzD/B,KAAK,EAAE3B,MAAM,CAACiC,KAAK,CAACE,GAAG;UAAE,GAJpB,GAAGkK,UAAU,IAAIpK,KAAK,CAACE,GAAG,IAAI0L,UAAU,EAAE;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAKhD,CACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA,GAdElB,UAAU;QAAAe,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAef,CACN,CAAC,eAKF9O,OAAA;QAAKyO,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3B1O,OAAA;UACEoG,IAAI,EAAC,QAAQ;UACbiJ,OAAO,EAAElP,QAAS;UAClBsO,SAAS,EAAC,iBAAiB;UAC3BQ,QAAQ,EAAErN,UAAW;UAAA8M,QAAA,EACtB;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT9O,OAAA;UACEoG,IAAI,EAAC,QAAQ;UACbqI,SAAS,EAAC,iBAAiB;UAC3BQ,QAAQ,EAAErN,UAAU,IAAI,CAACf,gBAAgB,IAAI,CAACE,gBAAiB;UAAA2N,QAAA,EAE9D9M,UAAU,GACNvB,IAAI,KAAK,QAAQ,GAAG,aAAa,GAAG,aAAa,GACjDA,IAAI,KAAK,QAAQ,GAAG,eAAe,GAAG;QAAgB;UAAAsO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAErD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACxO,EAAA,CA76BIL,iBAAiB;AAAAqP,EAAA,GAAjBrP,iBAAiB;AA+6BvB,eAAeA,iBAAiB;AAAC,IAAAqP,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}