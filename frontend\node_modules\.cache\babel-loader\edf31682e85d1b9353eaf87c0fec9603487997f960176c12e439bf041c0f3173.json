{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Advanced\\\\Advanced CRM filed all use\\\\frontend\\\\src\\\\components\\\\ProtectedRoute.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { useAuth } from '../context/AuthContext';\nimport Login from './Login';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProtectedRoute = ({\n  children\n}) => {\n  _s();\n  const {\n    isAuthenticated,\n    loading\n  } = useAuth();\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"loading-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-spinner-large\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 11,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Loading...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 12,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 10,\n      columnNumber: 7\n    }, this);\n  }\n  return isAuthenticated ? children : /*#__PURE__*/_jsxDEV(Login, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 17,\n    columnNumber: 39\n  }, this);\n};\n_s(ProtectedRoute, \"F3aPsg481KjBH7Z7iYl6LJifZz0=\", false, function () {\n  return [useAuth];\n});\n_c = ProtectedRoute;\nexport default ProtectedRoute;\nvar _c;\n$RefreshReg$(_c, \"ProtectedRoute\");", "map": {"version": 3, "names": ["React", "useAuth", "<PERSON><PERSON>", "jsxDEV", "_jsxDEV", "ProtectedRoute", "children", "_s", "isAuthenticated", "loading", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/Advanced/Advanced CRM filed all use/frontend/src/components/ProtectedRoute.js"], "sourcesContent": ["import React from 'react';\r\nimport { useAuth } from '../context/AuthContext';\r\nimport Login from './Login';\r\n\r\nconst ProtectedRoute = ({ children }) => {\r\n  const { isAuthenticated, loading } = useAuth();\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\"loading-container\">\r\n        <div className=\"loading-spinner-large\"></div>\r\n        <p>Loading...</p>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return isAuthenticated ? children : <Login />;\r\n};\r\n\r\nexport default ProtectedRoute;"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,QAAQ,wBAAwB;AAChD,OAAOC,KAAK,MAAM,SAAS;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5B,MAAMC,cAAc,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACvC,MAAM;IAAEC,eAAe;IAAEC;EAAQ,CAAC,GAAGR,OAAO,CAAC,CAAC;EAE9C,IAAIQ,OAAO,EAAE;IACX,oBACEL,OAAA;MAAKM,SAAS,EAAC,mBAAmB;MAAAJ,QAAA,gBAChCF,OAAA;QAAKM,SAAS,EAAC;MAAuB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC7CV,OAAA;QAAAE,QAAA,EAAG;MAAU;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACd,CAAC;EAEV;EAEA,OAAON,eAAe,GAAGF,QAAQ,gBAAGF,OAAA,CAACF,KAAK;IAAAS,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;AAC/C,CAAC;AAACP,EAAA,CAbIF,cAAc;EAAA,QACmBJ,OAAO;AAAA;AAAAc,EAAA,GADxCV,cAAc;AAepB,eAAeA,cAAc;AAAC,IAAAU,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}