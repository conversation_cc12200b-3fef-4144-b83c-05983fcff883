{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Advanced\\\\Advanced CRM filed all use\\\\frontend\\\\src\\\\components\\\\Pagination.js\";\nimport React from 'react';\nimport './Pagination.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Pagination = ({\n  currentPage,\n  totalItems,\n  itemsPerPage,\n  onPageChange,\n  showInfo = true\n}) => {\n  const totalPages = Math.ceil(totalItems / itemsPerPage);\n  const startItem = (currentPage - 1) * itemsPerPage + 1;\n  const endItem = Math.min(currentPage * itemsPerPage, totalItems);\n  if (totalPages <= 1) return null;\n  const getPageNumbers = () => {\n    const pages = [];\n    const maxVisiblePages = 3; // Reduced for single row\n\n    if (totalPages <= maxVisiblePages) {\n      for (let i = 1; i <= totalPages; i++) {\n        pages.push(i);\n      }\n    } else {\n      // Simplified logic for single row\n      if (currentPage === 1) {\n        pages.push(1, 2, '...');\n      } else if (currentPage === totalPages) {\n        pages.push('...', totalPages - 1, totalPages);\n      } else {\n        pages.push('...', currentPage, '...');\n      }\n    }\n    return pages;\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"pagination-wrapper-single\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"pagination-container\",\n      children: [showInfo && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"pagination-info\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"entries-text\",\n          children: [startItem, \"-\", endItem, \" of \", totalItems]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 44,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"pagination-controls\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: `pagination-btn pagination-prev ${currentPage === 1 ? 'disabled' : ''}`,\n          onClick: () => onPageChange(currentPage - 1),\n          disabled: currentPage === 1,\n          title: \"Previous page\",\n          children: \"\\u2039\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"pagination-pages\",\n          children: getPageNumbers().map((page, index) => /*#__PURE__*/_jsxDEV(\"button\", {\n            className: `pagination-btn pagination-page ${page === currentPage ? 'active' : ''} ${page === '...' ? 'dots' : ''}`,\n            onClick: () => page !== '...' && onPageChange(page),\n            disabled: page === '...',\n            title: page === '...' ? '' : `Go to page ${page}`,\n            children: page\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 62,\n            columnNumber: 29\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 60,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: `pagination-btn pagination-next ${currentPage === totalPages ? 'disabled' : ''}`,\n          onClick: () => onPageChange(currentPage + 1),\n          disabled: currentPage === totalPages,\n          title: \"Next page\",\n          children: \"\\u203A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 41,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 40,\n    columnNumber: 9\n  }, this);\n};\n_c = Pagination;\nexport default Pagination;\nvar _c;\n$RefreshReg$(_c, \"Pagination\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "Pagination", "currentPage", "totalItems", "itemsPerPage", "onPageChange", "showInfo", "totalPages", "Math", "ceil", "startItem", "endItem", "min", "getPageNumbers", "pages", "maxVisiblePages", "i", "push", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "disabled", "title", "map", "page", "index", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/Advanced/Advanced CRM filed all use/frontend/src/components/Pagination.js"], "sourcesContent": ["import React from 'react';\r\nimport './Pagination.css';\r\n\r\nconst Pagination = ({\r\n    currentPage,\r\n    totalItems,\r\n    itemsPerPage,\r\n    onPageChange,\r\n    showInfo = true\r\n}) => {\r\n    const totalPages = Math.ceil(totalItems / itemsPerPage);\r\n    const startItem = (currentPage - 1) * itemsPerPage + 1;\r\n    const endItem = Math.min(currentPage * itemsPerPage, totalItems);\r\n\r\n    if (totalPages <= 1) return null;\r\n\r\n    const getPageNumbers = () => {\r\n        const pages = [];\r\n        const maxVisiblePages = 3; // Reduced for single row\r\n\r\n        if (totalPages <= maxVisiblePages) {\r\n            for (let i = 1; i <= totalPages; i++) {\r\n                pages.push(i);\r\n            }\r\n        } else {\r\n            // Simplified logic for single row\r\n            if (currentPage === 1) {\r\n                pages.push(1, 2, '...');\r\n            } else if (currentPage === totalPages) {\r\n                pages.push('...', totalPages - 1, totalPages);\r\n            } else {\r\n                pages.push('...', currentPage, '...');\r\n            }\r\n        }\r\n\r\n        return pages;\r\n    };\r\n\r\n    return (\r\n        <div className=\"pagination-wrapper-single\">\r\n            <div className=\"pagination-container\">\r\n                {showInfo && (\r\n                    <div className=\"pagination-info\">\r\n                        <span className=\"entries-text\">\r\n                            {startItem}-{endItem} of {totalItems}\r\n                        </span>\r\n                    </div>\r\n                )}\r\n\r\n                <div className=\"pagination-controls\">\r\n                    <button\r\n                        className={`pagination-btn pagination-prev ${currentPage === 1 ? 'disabled' : ''}`}\r\n                        onClick={() => onPageChange(currentPage - 1)}\r\n                        disabled={currentPage === 1}\r\n                        title=\"Previous page\"\r\n                    >\r\n                        ‹\r\n                    </button>\r\n\r\n                    <div className=\"pagination-pages\">\r\n                        {getPageNumbers().map((page, index) => (\r\n                            <button\r\n                                key={index}\r\n                                className={`pagination-btn pagination-page ${page === currentPage ? 'active' : ''\r\n                                    } ${page === '...' ? 'dots' : ''}`}\r\n                                onClick={() => page !== '...' && onPageChange(page)}\r\n                                disabled={page === '...'}\r\n                                title={page === '...' ? '' : `Go to page ${page}`}\r\n                            >\r\n                                {page}\r\n                            </button>\r\n                        ))}\r\n                    </div>\r\n\r\n                    <button\r\n                        className={`pagination-btn pagination-next ${currentPage === totalPages ? 'disabled' : ''}`}\r\n                        onClick={() => onPageChange(currentPage + 1)}\r\n                        disabled={currentPage === totalPages}\r\n                        title=\"Next page\"\r\n                    >\r\n                        ›\r\n                    </button>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default Pagination;"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,UAAU,GAAGA,CAAC;EAChBC,WAAW;EACXC,UAAU;EACVC,YAAY;EACZC,YAAY;EACZC,QAAQ,GAAG;AACf,CAAC,KAAK;EACF,MAAMC,UAAU,GAAGC,IAAI,CAACC,IAAI,CAACN,UAAU,GAAGC,YAAY,CAAC;EACvD,MAAMM,SAAS,GAAG,CAACR,WAAW,GAAG,CAAC,IAAIE,YAAY,GAAG,CAAC;EACtD,MAAMO,OAAO,GAAGH,IAAI,CAACI,GAAG,CAACV,WAAW,GAAGE,YAAY,EAAED,UAAU,CAAC;EAEhE,IAAII,UAAU,IAAI,CAAC,EAAE,OAAO,IAAI;EAEhC,MAAMM,cAAc,GAAGA,CAAA,KAAM;IACzB,MAAMC,KAAK,GAAG,EAAE;IAChB,MAAMC,eAAe,GAAG,CAAC,CAAC,CAAC;;IAE3B,IAAIR,UAAU,IAAIQ,eAAe,EAAE;MAC/B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIT,UAAU,EAAES,CAAC,EAAE,EAAE;QAClCF,KAAK,CAACG,IAAI,CAACD,CAAC,CAAC;MACjB;IACJ,CAAC,MAAM;MACH;MACA,IAAId,WAAW,KAAK,CAAC,EAAE;QACnBY,KAAK,CAACG,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC;MAC3B,CAAC,MAAM,IAAIf,WAAW,KAAKK,UAAU,EAAE;QACnCO,KAAK,CAACG,IAAI,CAAC,KAAK,EAAEV,UAAU,GAAG,CAAC,EAAEA,UAAU,CAAC;MACjD,CAAC,MAAM;QACHO,KAAK,CAACG,IAAI,CAAC,KAAK,EAAEf,WAAW,EAAE,KAAK,CAAC;MACzC;IACJ;IAEA,OAAOY,KAAK;EAChB,CAAC;EAED,oBACId,OAAA;IAAKkB,SAAS,EAAC,2BAA2B;IAAAC,QAAA,eACtCnB,OAAA;MAAKkB,SAAS,EAAC,sBAAsB;MAAAC,QAAA,GAChCb,QAAQ,iBACLN,OAAA;QAAKkB,SAAS,EAAC,iBAAiB;QAAAC,QAAA,eAC5BnB,OAAA;UAAMkB,SAAS,EAAC,cAAc;UAAAC,QAAA,GACzBT,SAAS,EAAC,GAAC,EAACC,OAAO,EAAC,MAAI,EAACR,UAAU;QAAA;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACR,eAEDvB,OAAA;QAAKkB,SAAS,EAAC,qBAAqB;QAAAC,QAAA,gBAChCnB,OAAA;UACIkB,SAAS,EAAE,kCAAkChB,WAAW,KAAK,CAAC,GAAG,UAAU,GAAG,EAAE,EAAG;UACnFsB,OAAO,EAAEA,CAAA,KAAMnB,YAAY,CAACH,WAAW,GAAG,CAAC,CAAE;UAC7CuB,QAAQ,EAAEvB,WAAW,KAAK,CAAE;UAC5BwB,KAAK,EAAC,eAAe;UAAAP,QAAA,EACxB;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAETvB,OAAA;UAAKkB,SAAS,EAAC,kBAAkB;UAAAC,QAAA,EAC5BN,cAAc,CAAC,CAAC,CAACc,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBAC9B7B,OAAA;YAEIkB,SAAS,EAAE,kCAAkCU,IAAI,KAAK1B,WAAW,GAAG,QAAQ,GAAG,EAAE,IACzE0B,IAAI,KAAK,KAAK,GAAG,MAAM,GAAG,EAAE,EAAG;YACvCJ,OAAO,EAAEA,CAAA,KAAMI,IAAI,KAAK,KAAK,IAAIvB,YAAY,CAACuB,IAAI,CAAE;YACpDH,QAAQ,EAAEG,IAAI,KAAK,KAAM;YACzBF,KAAK,EAAEE,IAAI,KAAK,KAAK,GAAG,EAAE,GAAG,cAAcA,IAAI,EAAG;YAAAT,QAAA,EAEjDS;UAAI,GAPAC,KAAK;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAQN,CACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAENvB,OAAA;UACIkB,SAAS,EAAE,kCAAkChB,WAAW,KAAKK,UAAU,GAAG,UAAU,GAAG,EAAE,EAAG;UAC5FiB,OAAO,EAAEA,CAAA,KAAMnB,YAAY,CAACH,WAAW,GAAG,CAAC,CAAE;UAC7CuB,QAAQ,EAAEvB,WAAW,KAAKK,UAAW;UACrCmB,KAAK,EAAC,WAAW;UAAAP,QAAA,EACpB;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAACO,EAAA,GAnFI7B,UAAU;AAqFhB,eAAeA,UAAU;AAAC,IAAA6B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}