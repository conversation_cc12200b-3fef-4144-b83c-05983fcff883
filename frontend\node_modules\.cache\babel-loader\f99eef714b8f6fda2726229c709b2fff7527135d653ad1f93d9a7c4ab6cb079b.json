{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Advanced\\\\Advanced CRM filed all use\\\\frontend\\\\src\\\\components\\\\forms\\\\DynamicPersonForm.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { PersonNatureLabels, GenderLabels, WorkingProfileLabels, getDefaultPersonData } from '../../constants/personConstants';\nimport formConfigService from '../../services/formConfigService';\nimport apiService from '../../services/apiService';\nimport FormField from './FormField';\nimport './DynamicPersonForm.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DynamicPersonForm = ({\n  onSubmit,\n  onCancel,\n  initialData = null,\n  mode = 'create'\n}) => {\n  _s();\n  // Hierarchy state\n  const [divisions, setDivisions] = useState([]);\n  const [categories, setCategories] = useState([]);\n  const [subCategories, setSubCategories] = useState([]);\n\n  // Selection state\n  const [selectedDivision, setSelectedDivision] = useState('');\n  const [selectedCategory, setSelectedCategory] = useState('');\n  const [selectedSubCategory, setSelectedSubCategory] = useState('');\n\n  // Form state\n  const [formConfig, setFormConfig] = useState(null);\n  const [formData, setFormData] = useState(getDefaultPersonData());\n  const [errors, setErrors] = useState({});\n\n  // Loading states\n  const [loading, setLoading] = useState({\n    divisions: false,\n    categories: false,\n    subCategories: false,\n    form: false\n  });\n  const [submitting, setSubmitting] = useState(false);\n\n  // Form availability state\n  const [formAvailability, setFormAvailability] = useState({\n    categoryHasForm: false,\n    subCategoryHasForm: false,\n    showSubCategoryDropdown: false,\n    message: ''\n  });\n\n  // Load divisions on component mount\n  useEffect(() => {\n    loadDivisions();\n\n    // Load initial data if provided\n    if (initialData) {\n      var _initialData$division, _initialData$category, _initialData$subCateg;\n      setFormData(initialData);\n      setSelectedDivision(((_initialData$division = initialData.divisionId) === null || _initialData$division === void 0 ? void 0 : _initialData$division.toString()) || '');\n      setSelectedCategory(((_initialData$category = initialData.categoryId) === null || _initialData$category === void 0 ? void 0 : _initialData$category.toString()) || '');\n      setSelectedSubCategory(((_initialData$subCateg = initialData.subCategoryId) === null || _initialData$subCateg === void 0 ? void 0 : _initialData$subCateg.toString()) || '');\n    } else {\n      // Load default form for new person creation\n      const defaultForm = formConfigService.getDefaultFormConfig();\n      setFormConfig(defaultForm);\n    }\n  }, [initialData]);\n\n  // Load categories when division changes\n  useEffect(() => {\n    if (selectedDivision) {\n      loadCategories(selectedDivision);\n      // Reset category and subcategory when division changes\n      setSelectedCategory('');\n      setSelectedSubCategory('');\n      setSubCategories([]);\n      setFormConfig(null);\n      setFormAvailability({\n        categoryHasForm: false,\n        subCategoryHasForm: false,\n        showSubCategoryDropdown: false,\n        message: ''\n      });\n    } else {\n      setCategories([]);\n      setSubCategories([]);\n      setSelectedCategory('');\n      setSelectedSubCategory('');\n      setFormConfig(null);\n    }\n  }, [selectedDivision]);\n\n  // Handle category selection and form loading\n  useEffect(() => {\n    if (selectedCategory) {\n      handleCategorySelection(selectedCategory);\n      // Reset subcategory when category changes\n      setSelectedSubCategory('');\n    } else {\n      setSubCategories([]);\n      setSelectedSubCategory('');\n      setFormConfig(null);\n      setFormAvailability({\n        categoryHasForm: false,\n        subCategoryHasForm: false,\n        showSubCategoryDropdown: false,\n        message: ''\n      });\n    }\n  }, [selectedCategory]);\n\n  // Handle subcategory selection and form loading\n  useEffect(() => {\n    if (selectedSubCategory) {\n      handleSubCategorySelection(selectedSubCategory);\n    }\n  }, [selectedSubCategory]);\n\n  // Load divisions from API\n  const loadDivisions = async () => {\n    setLoading(prev => ({\n      ...prev,\n      divisions: true\n    }));\n    try {\n      const response = await apiService.getDivisions();\n      setDivisions(response.data || []);\n    } catch (error) {\n      console.error('Error loading divisions:', error);\n      setErrors(prev => ({\n        ...prev,\n        divisions: 'Failed to load divisions'\n      }));\n    } finally {\n      setLoading(prev => ({\n        ...prev,\n        divisions: false\n      }));\n    }\n  };\n\n  // Load categories by division\n  const loadCategories = async divisionId => {\n    setLoading(prev => ({\n      ...prev,\n      categories: true\n    }));\n    try {\n      const response = await apiService.getCategoriesByDivision(divisionId);\n      setCategories(response.data || []);\n    } catch (error) {\n      console.error('Error loading categories:', error);\n      setErrors(prev => ({\n        ...prev,\n        categories: 'Failed to load categories'\n      }));\n      setCategories([]);\n    } finally {\n      setLoading(prev => ({\n        ...prev,\n        categories: false\n      }));\n    }\n  };\n\n  // Load subcategories by category\n  const loadSubCategories = async categoryId => {\n    setLoading(prev => ({\n      ...prev,\n      subCategories: true\n    }));\n    try {\n      const response = await apiService.getSubCategoriesByCategory(categoryId);\n      setSubCategories(response.data || []);\n    } catch (error) {\n      console.error('Error loading subcategories:', error);\n      setErrors(prev => ({\n        ...prev,\n        subCategories: 'Failed to load subcategories'\n      }));\n      setSubCategories([]);\n    } finally {\n      setLoading(prev => ({\n        ...prev,\n        subCategories: false\n      }));\n    }\n  };\n\n  // Handle category selection and form loading logic\n  const handleCategorySelection = async categoryId => {\n    setLoading(prev => ({\n      ...prev,\n      form: true\n    }));\n    try {\n      // Check if category has a form\n      const categoryHasForm = formConfigService.hasFormForCategory(parseInt(categoryId));\n      if (categoryHasForm) {\n        // Load category form and hide subcategory dropdown\n        const categoryForm = formConfigService.loadFormConfig('category', parseInt(categoryId));\n        setFormConfig(categoryForm);\n        setFormAvailability({\n          categoryHasForm: true,\n          subCategoryHasForm: false,\n          showSubCategoryDropdown: false,\n          message: 'Using category-specific form'\n        });\n      } else {\n        // No category form, show subcategory dropdown and load subcategories\n        await loadSubCategories(categoryId);\n        // Load default form as fallback\n        const defaultForm = formConfigService.getDefaultFormConfig();\n        setFormConfig(defaultForm);\n        setFormAvailability({\n          categoryHasForm: false,\n          subCategoryHasForm: false,\n          showSubCategoryDropdown: true,\n          message: 'Using default form. You can select a subcategory if available.'\n        });\n      }\n    } catch (error) {\n      console.error('Error handling category selection:', error);\n      setErrors(prev => ({\n        ...prev,\n        form: 'Error loading form configuration'\n      }));\n    } finally {\n      setLoading(prev => ({\n        ...prev,\n        form: false\n      }));\n    }\n  };\n\n  // Handle subcategory selection and form loading logic\n  const handleSubCategorySelection = async subCategoryId => {\n    setLoading(prev => ({\n      ...prev,\n      form: true\n    }));\n    try {\n      // Check if subcategory has a form\n      const subCategoryHasForm = formConfigService.hasFormForSubCategory(parseInt(subCategoryId));\n      if (subCategoryHasForm) {\n        // Load subcategory form\n        const subCategoryForm = formConfigService.loadFormConfig('subcategory', parseInt(subCategoryId));\n        setFormConfig(subCategoryForm);\n        setFormAvailability({\n          categoryHasForm: false,\n          subCategoryHasForm: true,\n          showSubCategoryDropdown: true,\n          message: 'Using subcategory-specific form'\n        });\n      } else {\n        // No subcategory form available, use default form\n        const defaultForm = formConfigService.getDefaultFormConfig();\n        setFormConfig(defaultForm);\n        setFormAvailability({\n          categoryHasForm: false,\n          subCategoryHasForm: false,\n          showSubCategoryDropdown: true,\n          message: 'No specific form found for this subcategory. Using default form.'\n        });\n      }\n    } catch (error) {\n      console.error('Error handling subcategory selection:', error);\n      setErrors(prev => ({\n        ...prev,\n        form: 'Error loading form configuration'\n      }));\n    } finally {\n      setLoading(prev => ({\n        ...prev,\n        form: false\n      }));\n    }\n  };\n\n  // Handle dropdown changes\n  const handleDivisionChange = e => {\n    const value = e.target.value;\n    setSelectedDivision(value);\n\n    // Update form data\n    setFormData(prev => ({\n      ...prev,\n      divisionId: value ? parseInt(value) : null,\n      categoryId: null,\n      subCategoryId: null\n    }));\n  };\n  const handleCategoryChange = e => {\n    const value = e.target.value;\n    setSelectedCategory(value);\n\n    // Update form data\n    setFormData(prev => ({\n      ...prev,\n      categoryId: value ? parseInt(value) : null,\n      subCategoryId: null\n    }));\n  };\n  const handleSubCategoryChange = e => {\n    const value = e.target.value;\n    setSelectedSubCategory(value);\n\n    // Update form data\n    setFormData(prev => ({\n      ...prev,\n      subCategoryId: value ? parseInt(value) : null\n    }));\n  };\n  const handleFieldChange = (fieldKey, value) => {\n    setFormData(prev => ({\n      ...prev,\n      [fieldKey]: value\n    }));\n\n    // Clear field error when user starts typing\n    if (errors[fieldKey]) {\n      setErrors(prev => ({\n        ...prev,\n        [fieldKey]: null\n      }));\n    }\n  };\n  const validateForm = () => {\n    const newErrors = {};\n\n    // Validate hierarchy selection\n    if (!selectedDivision) {\n      newErrors.division = 'Division is required';\n    }\n    if (!selectedCategory) {\n      newErrors.category = 'Category is required';\n    }\n\n    // Check if form is available\n    if (!formConfig) {\n      if (selectedCategory && selectedSubCategory && !formAvailability.subCategoryHasForm) {\n        newErrors.form = 'No form is available for the selected subcategory';\n      } else if (selectedCategory && !formAvailability.categoryHasForm && !formAvailability.showSubCategoryDropdown) {\n        newErrors.form = 'No form is available for the selected category';\n      } else if (!selectedCategory) {\n        newErrors.form = 'Please select a category to load the form';\n      }\n      return {\n        isValid: false,\n        errors: newErrors\n      };\n    }\n\n    // Validate form fields\n    formConfig.fields.forEach(field => {\n      const value = formData[field.key];\n\n      // Required field validation\n      if (field.required && (!value || typeof value === 'string' && value.trim() === '')) {\n        newErrors[field.key] = `${field.label} is required`;\n        return;\n      }\n\n      // Conditional field validation\n      if (field.conditional && shouldShowField(field)) {\n        if (field.required && (!value || typeof value === 'string' && value.trim() === '')) {\n          newErrors[field.key] = `${field.label} is required`;\n          return;\n        }\n      }\n\n      // Type-specific validation\n      if (value && typeof value === 'string' && value.trim() !== '') {\n        var _field$validation, _field$validation2, _field$validation3;\n        switch (field.type) {\n          case 'email':\n            const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n            if (!emailRegex.test(value)) {\n              newErrors[field.key] = 'Please enter a valid email address';\n            }\n            break;\n          case 'tel':\n            const phoneRegex = /^(\\+91[\\-\\s]?)?[0]?(91)?[789]\\d{9}$/;\n            if (!phoneRegex.test(value)) {\n              newErrors[field.key] = 'Please enter a valid mobile number';\n            }\n            break;\n          case 'url':\n            try {\n              new URL(value);\n            } catch {\n              newErrors[field.key] = 'Please enter a valid URL';\n            }\n            break;\n          case 'number':\n            if (field.validation) {\n              const numValue = parseFloat(value);\n              if (field.validation.min !== undefined && numValue < field.validation.min) {\n                newErrors[field.key] = `Value must be at least ${field.validation.min}`;\n              }\n              if (field.validation.max !== undefined && numValue > field.validation.max) {\n                newErrors[field.key] = `Value must be at most ${field.validation.max}`;\n              }\n            }\n            break;\n        }\n\n        // Pattern validation\n        if ((_field$validation = field.validation) !== null && _field$validation !== void 0 && _field$validation.pattern) {\n          const regex = new RegExp(field.validation.pattern);\n          if (!regex.test(value)) {\n            newErrors[field.key] = `${field.label} format is invalid`;\n          }\n        }\n\n        // Length validation\n        if ((_field$validation2 = field.validation) !== null && _field$validation2 !== void 0 && _field$validation2.minLength && value.length < field.validation.minLength) {\n          newErrors[field.key] = `${field.label} must be at least ${field.validation.minLength} characters`;\n        }\n        if ((_field$validation3 = field.validation) !== null && _field$validation3 !== void 0 && _field$validation3.maxLength && value.length > field.validation.maxLength) {\n          newErrors[field.key] = `${field.label} must be at most ${field.validation.maxLength} characters`;\n        }\n      }\n    });\n\n    // Business logic validation\n    if (formData.isMarried && formData.dateOfMarriage && formData.dateOfBirth) {\n      const birthDate = new Date(formData.dateOfBirth);\n      const marriageDate = new Date(formData.dateOfMarriage);\n      if (marriageDate <= birthDate) {\n        newErrors.dateOfMarriage = 'Marriage date must be after birth date';\n      }\n    }\n    return {\n      isValid: Object.keys(newErrors).length === 0,\n      errors: newErrors\n    };\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    const validation = validateForm();\n    if (!validation.isValid) {\n      setErrors(validation.errors);\n      return;\n    }\n    setSubmitting(true);\n    try {\n      // Prepare data for API\n      const submitData = {\n        ...formData,\n        // Ensure hierarchy IDs are included\n        divisionId: parseInt(selectedDivision),\n        categoryId: parseInt(selectedCategory),\n        subCategoryId: selectedSubCategory ? parseInt(selectedSubCategory) : null,\n        // Convert arrays to proper format\n        alternateNumbers: Array.isArray(formData.alternateNumbers) ? formData.alternateNumbers : formData.alternateNumbers ? formData.alternateNumbers.split(',').map(s => s.trim()) : [],\n        alternateEmailIds: Array.isArray(formData.alternateEmailIds) ? formData.alternateEmailIds : formData.alternateEmailIds ? formData.alternateEmailIds.split(',').map(s => s.trim()) : [],\n        workingProfiles: Array.isArray(formData.workingProfiles) ? formData.workingProfiles : []\n      };\n\n      // Debug logging\n      console.log('Submitting person data:', submitData);\n      console.log('Selected Division:', selectedDivision);\n      console.log('Selected Category:', selectedCategory);\n      console.log('Selected SubCategory:', selectedSubCategory);\n      let result;\n      if (mode === 'create') {\n        result = await apiService.createPerson(submitData);\n      } else {\n        result = await apiService.updatePerson(initialData.id, submitData);\n      }\n      onSubmit(result);\n    } catch (error) {\n      var _error$response, _error$response2, _error$response2$data;\n      console.error('Error submitting form:', error);\n      console.error('Error details:', (_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.data);\n      if ((_error$response2 = error.response) !== null && _error$response2 !== void 0 && (_error$response2$data = _error$response2.data) !== null && _error$response2$data !== void 0 && _error$response2$data.errors) {\n        // Handle validation errors from backend\n        const backendErrors = {};\n        Object.keys(error.response.data.errors).forEach(key => {\n          const errorMessages = error.response.data.errors[key];\n          backendErrors[key.toLowerCase()] = Array.isArray(errorMessages) ? errorMessages.join(', ') : errorMessages;\n        });\n        setErrors(backendErrors);\n      } else if (error.isValidationError && error.isValidationError()) {\n        setErrors(error.getValidationErrors());\n      } else {\n        var _error$response3, _error$response3$data, _error$response4, _error$response4$data;\n        const errorMessage = ((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : (_error$response3$data = _error$response3.data) === null || _error$response3$data === void 0 ? void 0 : _error$response3$data.message) || ((_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : (_error$response4$data = _error$response4.data) === null || _error$response4$data === void 0 ? void 0 : _error$response4$data.title) || error.message || 'An error occurred while saving';\n        setErrors({\n          general: errorMessage\n        });\n      }\n    } finally {\n      setSubmitting(false);\n    }\n  };\n  const shouldShowField = field => {\n    if (!field.conditional) return true;\n    const conditionValue = formData[field.conditional.field];\n    const expectedValue = field.conditional.value;\n\n    // Handle boolean conditions\n    if (typeof expectedValue === 'boolean' || expectedValue === 'true' || expectedValue === 'false') {\n      return conditionValue === (expectedValue === true || expectedValue === 'true');\n    }\n    return conditionValue === expectedValue;\n  };\n  const groupFieldsBySections = () => {\n    if (!formConfig || !formConfig.fields) return {};\n    const sections = {};\n    formConfig.fields.forEach(field => {\n      const sectionKey = field.section || 'general';\n      if (!sections[sectionKey]) {\n        sections[sectionKey] = {\n          title: getSectionTitle(sectionKey),\n          fields: []\n        };\n      }\n      sections[sectionKey].fields.push(field);\n    });\n    return sections;\n  };\n  const getSectionTitle = sectionKey => {\n    const titles = {\n      personalInfo: 'Personal Information',\n      contactInfo: 'Contact Information',\n      locationInfo: 'Location Information',\n      businessInfo: 'Business Information',\n      associateInfo: 'Associate Information',\n      digitalPresence: 'Digital Presence',\n      companyInfo: 'Company Information',\n      authorizedPerson: 'Authorized Person',\n      marketingInfo: 'Marketing Information',\n      general: 'General Information'\n    };\n    return titles[sectionKey] || sectionKey;\n  };\n  const sections = formConfig ? groupFieldsBySections() : {};\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"dynamic-person-form\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"form-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: mode === 'create' ? 'Create New Person' : 'Edit Person'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 516,\n        columnNumber: 9\n      }, this), formConfig && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-info\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"form-name\",\n          children: formConfig.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 519,\n          columnNumber: 13\n        }, this), formConfig.description && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"form-description\",\n          children: formConfig.description\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 521,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 518,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 515,\n      columnNumber: 7\n    }, this), errors.general && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"alert alert-error\",\n      children: errors.general\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 528,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmit,\n      className: \"person-form\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Division & Category Selection\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 534,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"form-label\",\n            children: [\"Division \", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"required\",\n              children: \"*\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 539,\n              columnNumber: 24\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 538,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: selectedDivision,\n            onChange: handleDivisionChange,\n            disabled: loading.divisions,\n            className: `form-select ${errors.division ? 'error' : ''}`,\n            required: true,\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"\",\n              children: loading.divisions ? 'Loading divisions...' : 'Select Division'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 548,\n              columnNumber: 15\n            }, this), divisions.map(division => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: division.id,\n              children: division.name\n            }, division.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 552,\n              columnNumber: 17\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 541,\n            columnNumber: 13\n          }, this), errors.division && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"error-message\",\n            children: errors.division\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 558,\n            columnNumber: 15\n          }, this), errors.divisions && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"error-message\",\n            children: errors.divisions\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 561,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 537,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"form-label\",\n            children: [\"Category \", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"required\",\n              children: \"*\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 568,\n              columnNumber: 24\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 567,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: selectedCategory,\n            onChange: handleCategoryChange,\n            disabled: !selectedDivision || loading.categories,\n            className: `form-select ${errors.category ? 'error' : ''}`,\n            required: true,\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"\",\n              children: !selectedDivision ? 'Select Division first' : loading.categories ? 'Loading categories...' : 'Select Category'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 577,\n              columnNumber: 15\n            }, this), categories.map(category => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: category.id,\n              children: category.name\n            }, category.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 586,\n              columnNumber: 17\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 570,\n            columnNumber: 13\n          }, this), errors.category && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"error-message\",\n            children: errors.category\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 592,\n            columnNumber: 15\n          }, this), errors.categories && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"error-message\",\n            children: errors.categories\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 595,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 566,\n          columnNumber: 11\n        }, this), formAvailability.showSubCategoryDropdown && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"form-label\",\n            children: \"Subcategory (Optional)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 602,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: selectedSubCategory,\n            onChange: handleSubCategoryChange,\n            disabled: !selectedCategory || loading.subCategories,\n            className: \"form-select\",\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"\",\n              children: !selectedCategory ? 'Select Category first' : loading.subCategories ? 'Loading subcategories...' : 'Select Subcategory (Optional)'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 611,\n              columnNumber: 17\n            }, this), subCategories.map(subCategory => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: subCategory.id,\n              children: subCategory.name\n            }, subCategory.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 620,\n              columnNumber: 19\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 605,\n            columnNumber: 15\n          }, this), errors.subCategories && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"error-message\",\n            children: errors.subCategories\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 626,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 601,\n          columnNumber: 13\n        }, this), loading.form && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading-message\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Loading form configuration...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 634,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 633,\n          columnNumber: 13\n        }, this), formAvailability.message && !loading.form && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `status-message ${formAvailability.categoryHasForm || formAvailability.subCategoryHasForm ? 'success' : formAvailability.message.includes('No form') ? 'error' : 'info'}`,\n          children: formAvailability.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 639,\n          columnNumber: 13\n        }, this), errors.form && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"error-message\",\n          children: errors.form\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 651,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 533,\n        columnNumber: 9\n      }, this), formConfig && Object.entries(sections).map(([sectionKey, section]) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: section.title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 658,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-fields\",\n          children: section.fields.filter(field => shouldShowField(field)).map(field => /*#__PURE__*/_jsxDEV(FormField, {\n            field: field,\n            value: formData[field.key],\n            onChange: value => handleFieldChange(field.key, value),\n            error: errors[field.key]\n          }, field.key, false, {\n            fileName: _jsxFileName,\n            lineNumber: 663,\n            columnNumber: 19\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 659,\n          columnNumber: 13\n        }, this)]\n      }, sectionKey, true, {\n        fileName: _jsxFileName,\n        lineNumber: 657,\n        columnNumber: 11\n      }, this)), !formConfig && selectedCategory && formAvailability.message.includes('No form') && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-section\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"no-form-message\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"No Form Available\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 679,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: formAvailability.message\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 680,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Please contact your administrator to create a form for this subcategory.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 681,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 678,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 677,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-actions\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          onClick: onCancel,\n          className: \"btn btn-outline\",\n          disabled: submitting,\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 688,\n          columnNumber: 11\n        }, this), formConfig && /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          className: \"btn btn-primary\",\n          disabled: submitting || !selectedDivision || !selectedCategory,\n          children: submitting ? mode === 'create' ? 'Creating...' : 'Updating...' : mode === 'create' ? 'Create Person' : 'Update Person'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 697,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 687,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 531,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 514,\n    columnNumber: 5\n  }, this);\n};\n_s(DynamicPersonForm, \"HQTZ+LW5pRnY6eFU2EO5G2IvcY0=\");\n_c = DynamicPersonForm;\nexport default DynamicPersonForm;\nvar _c;\n$RefreshReg$(_c, \"DynamicPersonForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "PersonNatureLabels", "<PERSON><PERSON><PERSON><PERSON>", "WorkingProfileLabels", "getDefaultPersonData", "formConfigService", "apiService", "FormField", "jsxDEV", "_jsxDEV", "DynamicPersonForm", "onSubmit", "onCancel", "initialData", "mode", "_s", "divisions", "setDivisions", "categories", "setCategories", "subCategories", "setSubCategories", "selectedDivision", "setSelectedDivision", "selectedCate<PERSON><PERSON>", "setSelectedCategory", "selectedSubCategory", "setSelectedSubCategory", "formConfig", "setFormConfig", "formData", "setFormData", "errors", "setErrors", "loading", "setLoading", "form", "submitting", "setSubmitting", "formAvailability", "setFormAvailability", "categoryHasForm", "subCategoryHasForm", "showSubCategoryDropdown", "message", "loadDivisions", "_initialData$division", "_initialData$category", "_initialData$subCateg", "divisionId", "toString", "categoryId", "subCategoryId", "defaultForm", "getDefaultFormConfig", "loadCategories", "handleCategorySelection", "handleSubCategorySelection", "prev", "response", "getDivisions", "data", "error", "console", "getCategoriesByDivision", "loadSubCategories", "getSubCategoriesByCategory", "hasFormForCategory", "parseInt", "categoryForm", "loadFormConfig", "hasFormForSubCategory", "subCategoryForm", "handleDivisionChange", "e", "value", "target", "handleCategoryChange", "handleSubCategoryChange", "handleFieldChange", "<PERSON><PERSON><PERSON>", "validateForm", "newErrors", "division", "category", "<PERSON><PERSON><PERSON><PERSON>", "fields", "for<PERSON>ach", "field", "key", "required", "trim", "label", "conditional", "shouldShowField", "_field$validation", "_field$validation2", "_field$validation3", "type", "emailRegex", "test", "phoneRegex", "URL", "validation", "numValue", "parseFloat", "min", "undefined", "max", "pattern", "regex", "RegExp", "<PERSON><PERSON><PERSON><PERSON>", "length", "max<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "dateOfMarriage", "dateOfBirth", "birthDate", "Date", "marriageDate", "Object", "keys", "handleSubmit", "preventDefault", "submitData", "alternateNumbers", "Array", "isArray", "split", "map", "s", "alternateEmailIds", "workingProfiles", "log", "result", "create<PERSON>erson", "update<PERSON><PERSON>", "id", "_error$response", "_error$response2", "_error$response2$data", "backendErrors", "errorMessages", "toLowerCase", "join", "isValidationError", "getValidationErrors", "_error$response3", "_error$response3$data", "_error$response4", "_error$response4$data", "errorMessage", "title", "general", "conditionValue", "expectedValue", "groupFieldsBySections", "sections", "sectionKey", "section", "getSectionTitle", "push", "titles", "personalInfo", "contactInfo", "locationInfo", "businessInfo", "associateInfo", "digitalPresence", "companyInfo", "<PERSON><PERSON><PERSON>", "marketingInfo", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "name", "description", "onChange", "disabled", "subCategory", "includes", "entries", "filter", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/Advanced/Advanced CRM filed all use/frontend/src/components/forms/DynamicPersonForm.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { PersonNatureLabels, GenderLabels, WorkingProfileLabels, getDefaultPersonData } from '../../constants/personConstants';\nimport formConfigService from '../../services/formConfigService';\nimport apiService from '../../services/apiService';\nimport FormField from './FormField';\nimport './DynamicPersonForm.css';\n\nconst DynamicPersonForm = ({ onSubmit, onCancel, initialData = null, mode = 'create' }) => {\n  // Hierarchy state\n  const [divisions, setDivisions] = useState([]);\n  const [categories, setCategories] = useState([]);\n  const [subCategories, setSubCategories] = useState([]);\n\n  // Selection state\n  const [selectedDivision, setSelectedDivision] = useState('');\n  const [selectedCategory, setSelectedCategory] = useState('');\n  const [selectedSubCategory, setSelectedSubCategory] = useState('');\n\n  // Form state\n  const [formConfig, setFormConfig] = useState(null);\n  const [formData, setFormData] = useState(getDefaultPersonData());\n  const [errors, setErrors] = useState({});\n\n  // Loading states\n  const [loading, setLoading] = useState({\n    divisions: false,\n    categories: false,\n    subCategories: false,\n    form: false\n  });\n  const [submitting, setSubmitting] = useState(false);\n\n  // Form availability state\n  const [formAvailability, setFormAvailability] = useState({\n    categoryHasForm: false,\n    subCategoryHasForm: false,\n    showSubCategoryDropdown: false,\n    message: ''\n  });\n\n  // Load divisions on component mount\n  useEffect(() => {\n    loadDivisions();\n\n    // Load initial data if provided\n    if (initialData) {\n      setFormData(initialData);\n      setSelectedDivision(initialData.divisionId?.toString() || '');\n      setSelectedCategory(initialData.categoryId?.toString() || '');\n      setSelectedSubCategory(initialData.subCategoryId?.toString() || '');\n    } else {\n      // Load default form for new person creation\n      const defaultForm = formConfigService.getDefaultFormConfig();\n      setFormConfig(defaultForm);\n    }\n  }, [initialData]);\n\n  // Load categories when division changes\n  useEffect(() => {\n    if (selectedDivision) {\n      loadCategories(selectedDivision);\n      // Reset category and subcategory when division changes\n      setSelectedCategory('');\n      setSelectedSubCategory('');\n      setSubCategories([]);\n      setFormConfig(null);\n      setFormAvailability({\n        categoryHasForm: false,\n        subCategoryHasForm: false,\n        showSubCategoryDropdown: false,\n        message: ''\n      });\n    } else {\n      setCategories([]);\n      setSubCategories([]);\n      setSelectedCategory('');\n      setSelectedSubCategory('');\n      setFormConfig(null);\n    }\n  }, [selectedDivision]);\n\n  // Handle category selection and form loading\n  useEffect(() => {\n    if (selectedCategory) {\n      handleCategorySelection(selectedCategory);\n      // Reset subcategory when category changes\n      setSelectedSubCategory('');\n    } else {\n      setSubCategories([]);\n      setSelectedSubCategory('');\n      setFormConfig(null);\n      setFormAvailability({\n        categoryHasForm: false,\n        subCategoryHasForm: false,\n        showSubCategoryDropdown: false,\n        message: ''\n      });\n    }\n  }, [selectedCategory]);\n\n  // Handle subcategory selection and form loading\n  useEffect(() => {\n    if (selectedSubCategory) {\n      handleSubCategorySelection(selectedSubCategory);\n    }\n  }, [selectedSubCategory]);\n\n  // Load divisions from API\n  const loadDivisions = async () => {\n    setLoading(prev => ({ ...prev, divisions: true }));\n    try {\n      const response = await apiService.getDivisions();\n      setDivisions(response.data || []);\n    } catch (error) {\n      console.error('Error loading divisions:', error);\n      setErrors(prev => ({ ...prev, divisions: 'Failed to load divisions' }));\n    } finally {\n      setLoading(prev => ({ ...prev, divisions: false }));\n    }\n  };\n\n  // Load categories by division\n  const loadCategories = async (divisionId) => {\n    setLoading(prev => ({ ...prev, categories: true }));\n    try {\n      const response = await apiService.getCategoriesByDivision(divisionId);\n      setCategories(response.data || []);\n    } catch (error) {\n      console.error('Error loading categories:', error);\n      setErrors(prev => ({ ...prev, categories: 'Failed to load categories' }));\n      setCategories([]);\n    } finally {\n      setLoading(prev => ({ ...prev, categories: false }));\n    }\n  };\n\n  // Load subcategories by category\n  const loadSubCategories = async (categoryId) => {\n    setLoading(prev => ({ ...prev, subCategories: true }));\n    try {\n      const response = await apiService.getSubCategoriesByCategory(categoryId);\n      setSubCategories(response.data || []);\n    } catch (error) {\n      console.error('Error loading subcategories:', error);\n      setErrors(prev => ({ ...prev, subCategories: 'Failed to load subcategories' }));\n      setSubCategories([]);\n    } finally {\n      setLoading(prev => ({ ...prev, subCategories: false }));\n    }\n  };\n\n  // Handle category selection and form loading logic\n  const handleCategorySelection = async (categoryId) => {\n    setLoading(prev => ({ ...prev, form: true }));\n\n    try {\n      // Check if category has a form\n      const categoryHasForm = formConfigService.hasFormForCategory(parseInt(categoryId));\n\n      if (categoryHasForm) {\n        // Load category form and hide subcategory dropdown\n        const categoryForm = formConfigService.loadFormConfig('category', parseInt(categoryId));\n        setFormConfig(categoryForm);\n        setFormAvailability({\n          categoryHasForm: true,\n          subCategoryHasForm: false,\n          showSubCategoryDropdown: false,\n          message: 'Using category-specific form'\n        });\n      } else {\n        // No category form, show subcategory dropdown and load subcategories\n        await loadSubCategories(categoryId);\n        // Load default form as fallback\n        const defaultForm = formConfigService.getDefaultFormConfig();\n        setFormConfig(defaultForm);\n        setFormAvailability({\n          categoryHasForm: false,\n          subCategoryHasForm: false,\n          showSubCategoryDropdown: true,\n          message: 'Using default form. You can select a subcategory if available.'\n        });\n      }\n    } catch (error) {\n      console.error('Error handling category selection:', error);\n      setErrors(prev => ({ ...prev, form: 'Error loading form configuration' }));\n    } finally {\n      setLoading(prev => ({ ...prev, form: false }));\n    }\n  };\n\n  // Handle subcategory selection and form loading logic\n  const handleSubCategorySelection = async (subCategoryId) => {\n    setLoading(prev => ({ ...prev, form: true }));\n\n    try {\n      // Check if subcategory has a form\n      const subCategoryHasForm = formConfigService.hasFormForSubCategory(parseInt(subCategoryId));\n\n      if (subCategoryHasForm) {\n        // Load subcategory form\n        const subCategoryForm = formConfigService.loadFormConfig('subcategory', parseInt(subCategoryId));\n        setFormConfig(subCategoryForm);\n        setFormAvailability({\n          categoryHasForm: false,\n          subCategoryHasForm: true,\n          showSubCategoryDropdown: true,\n          message: 'Using subcategory-specific form'\n        });\n      } else {\n        // No subcategory form available, use default form\n        const defaultForm = formConfigService.getDefaultFormConfig();\n        setFormConfig(defaultForm);\n        setFormAvailability({\n          categoryHasForm: false,\n          subCategoryHasForm: false,\n          showSubCategoryDropdown: true,\n          message: 'No specific form found for this subcategory. Using default form.'\n        });\n      }\n    } catch (error) {\n      console.error('Error handling subcategory selection:', error);\n      setErrors(prev => ({ ...prev, form: 'Error loading form configuration' }));\n    } finally {\n      setLoading(prev => ({ ...prev, form: false }));\n    }\n  };\n\n  // Handle dropdown changes\n  const handleDivisionChange = (e) => {\n    const value = e.target.value;\n    setSelectedDivision(value);\n\n    // Update form data\n    setFormData(prev => ({\n      ...prev,\n      divisionId: value ? parseInt(value) : null,\n      categoryId: null,\n      subCategoryId: null\n    }));\n  };\n\n  const handleCategoryChange = (e) => {\n    const value = e.target.value;\n    setSelectedCategory(value);\n\n    // Update form data\n    setFormData(prev => ({\n      ...prev,\n      categoryId: value ? parseInt(value) : null,\n      subCategoryId: null\n    }));\n  };\n\n  const handleSubCategoryChange = (e) => {\n    const value = e.target.value;\n    setSelectedSubCategory(value);\n\n    // Update form data\n    setFormData(prev => ({\n      ...prev,\n      subCategoryId: value ? parseInt(value) : null\n    }));\n  };\n\n  const handleFieldChange = (fieldKey, value) => {\n    setFormData(prev => ({\n      ...prev,\n      [fieldKey]: value\n    }));\n\n    // Clear field error when user starts typing\n    if (errors[fieldKey]) {\n      setErrors(prev => ({\n        ...prev,\n        [fieldKey]: null\n      }));\n    }\n  };\n\n  const validateForm = () => {\n    const newErrors = {};\n\n    // Validate hierarchy selection\n    if (!selectedDivision) {\n      newErrors.division = 'Division is required';\n    }\n    if (!selectedCategory) {\n      newErrors.category = 'Category is required';\n    }\n\n    // Check if form is available\n    if (!formConfig) {\n      if (selectedCategory && selectedSubCategory && !formAvailability.subCategoryHasForm) {\n        newErrors.form = 'No form is available for the selected subcategory';\n      } else if (selectedCategory && !formAvailability.categoryHasForm && !formAvailability.showSubCategoryDropdown) {\n        newErrors.form = 'No form is available for the selected category';\n      } else if (!selectedCategory) {\n        newErrors.form = 'Please select a category to load the form';\n      }\n      return { isValid: false, errors: newErrors };\n    }\n\n    // Validate form fields\n    formConfig.fields.forEach(field => {\n      const value = formData[field.key];\n      \n      // Required field validation\n      if (field.required && (!value || (typeof value === 'string' && value.trim() === ''))) {\n        newErrors[field.key] = `${field.label} is required`;\n        return;\n      }\n\n      // Conditional field validation\n      if (field.conditional && shouldShowField(field)) {\n        if (field.required && (!value || (typeof value === 'string' && value.trim() === ''))) {\n          newErrors[field.key] = `${field.label} is required`;\n          return;\n        }\n      }\n\n      // Type-specific validation\n      if (value && typeof value === 'string' && value.trim() !== '') {\n        switch (field.type) {\n          case 'email':\n            const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n            if (!emailRegex.test(value)) {\n              newErrors[field.key] = 'Please enter a valid email address';\n            }\n            break;\n          case 'tel':\n            const phoneRegex = /^(\\+91[\\-\\s]?)?[0]?(91)?[789]\\d{9}$/;\n            if (!phoneRegex.test(value)) {\n              newErrors[field.key] = 'Please enter a valid mobile number';\n            }\n            break;\n          case 'url':\n            try {\n              new URL(value);\n            } catch {\n              newErrors[field.key] = 'Please enter a valid URL';\n            }\n            break;\n          case 'number':\n            if (field.validation) {\n              const numValue = parseFloat(value);\n              if (field.validation.min !== undefined && numValue < field.validation.min) {\n                newErrors[field.key] = `Value must be at least ${field.validation.min}`;\n              }\n              if (field.validation.max !== undefined && numValue > field.validation.max) {\n                newErrors[field.key] = `Value must be at most ${field.validation.max}`;\n              }\n            }\n            break;\n        }\n\n        // Pattern validation\n        if (field.validation?.pattern) {\n          const regex = new RegExp(field.validation.pattern);\n          if (!regex.test(value)) {\n            newErrors[field.key] = `${field.label} format is invalid`;\n          }\n        }\n\n        // Length validation\n        if (field.validation?.minLength && value.length < field.validation.minLength) {\n          newErrors[field.key] = `${field.label} must be at least ${field.validation.minLength} characters`;\n        }\n        if (field.validation?.maxLength && value.length > field.validation.maxLength) {\n          newErrors[field.key] = `${field.label} must be at most ${field.validation.maxLength} characters`;\n        }\n      }\n    });\n\n    // Business logic validation\n    if (formData.isMarried && formData.dateOfMarriage && formData.dateOfBirth) {\n      const birthDate = new Date(formData.dateOfBirth);\n      const marriageDate = new Date(formData.dateOfMarriage);\n      if (marriageDate <= birthDate) {\n        newErrors.dateOfMarriage = 'Marriage date must be after birth date';\n      }\n    }\n\n    return {\n      isValid: Object.keys(newErrors).length === 0,\n      errors: newErrors\n    };\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    \n    const validation = validateForm();\n    if (!validation.isValid) {\n      setErrors(validation.errors);\n      return;\n    }\n\n    setSubmitting(true);\n    try {\n      // Prepare data for API\n      const submitData = {\n        ...formData,\n        // Ensure hierarchy IDs are included\n        divisionId: parseInt(selectedDivision),\n        categoryId: parseInt(selectedCategory),\n        subCategoryId: selectedSubCategory ? parseInt(selectedSubCategory) : null,\n        // Convert arrays to proper format\n        alternateNumbers: Array.isArray(formData.alternateNumbers)\n          ? formData.alternateNumbers\n          : formData.alternateNumbers ? formData.alternateNumbers.split(',').map(s => s.trim()) : [],\n        alternateEmailIds: Array.isArray(formData.alternateEmailIds)\n          ? formData.alternateEmailIds\n          : formData.alternateEmailIds ? formData.alternateEmailIds.split(',').map(s => s.trim()) : [],\n        workingProfiles: Array.isArray(formData.workingProfiles)\n          ? formData.workingProfiles\n          : []\n      };\n\n      // Debug logging\n      console.log('Submitting person data:', submitData);\n      console.log('Selected Division:', selectedDivision);\n      console.log('Selected Category:', selectedCategory);\n      console.log('Selected SubCategory:', selectedSubCategory);\n\n      let result;\n      if (mode === 'create') {\n        result = await apiService.createPerson(submitData);\n      } else {\n        result = await apiService.updatePerson(initialData.id, submitData);\n      }\n\n      onSubmit(result);\n    } catch (error) {\n      console.error('Error submitting form:', error);\n      console.error('Error details:', error.response?.data);\n\n      if (error.response?.data?.errors) {\n        // Handle validation errors from backend\n        const backendErrors = {};\n        Object.keys(error.response.data.errors).forEach(key => {\n          const errorMessages = error.response.data.errors[key];\n          backendErrors[key.toLowerCase()] = Array.isArray(errorMessages)\n            ? errorMessages.join(', ')\n            : errorMessages;\n        });\n        setErrors(backendErrors);\n      } else if (error.isValidationError && error.isValidationError()) {\n        setErrors(error.getValidationErrors());\n      } else {\n        const errorMessage = error.response?.data?.message ||\n                           error.response?.data?.title ||\n                           error.message ||\n                           'An error occurred while saving';\n        setErrors({ general: errorMessage });\n      }\n    } finally {\n      setSubmitting(false);\n    }\n  };\n\n  const shouldShowField = (field) => {\n    if (!field.conditional) return true;\n    \n    const conditionValue = formData[field.conditional.field];\n    const expectedValue = field.conditional.value;\n    \n    // Handle boolean conditions\n    if (typeof expectedValue === 'boolean' || expectedValue === 'true' || expectedValue === 'false') {\n      return conditionValue === (expectedValue === true || expectedValue === 'true');\n    }\n    \n    return conditionValue === expectedValue;\n  };\n\n  const groupFieldsBySections = () => {\n    if (!formConfig || !formConfig.fields) return {};\n    \n    const sections = {};\n    formConfig.fields.forEach(field => {\n      const sectionKey = field.section || 'general';\n      if (!sections[sectionKey]) {\n        sections[sectionKey] = {\n          title: getSectionTitle(sectionKey),\n          fields: []\n        };\n      }\n      sections[sectionKey].fields.push(field);\n    });\n    \n    return sections;\n  };\n\n  const getSectionTitle = (sectionKey) => {\n    const titles = {\n      personalInfo: 'Personal Information',\n      contactInfo: 'Contact Information',\n      locationInfo: 'Location Information',\n      businessInfo: 'Business Information',\n      associateInfo: 'Associate Information',\n      digitalPresence: 'Digital Presence',\n      companyInfo: 'Company Information',\n      authorizedPerson: 'Authorized Person',\n      marketingInfo: 'Marketing Information',\n      general: 'General Information'\n    };\n    return titles[sectionKey] || sectionKey;\n  };\n\n  const sections = formConfig ? groupFieldsBySections() : {};\n\n\n\n  return (\n    <div className=\"dynamic-person-form\">\n      <div className=\"form-header\">\n        <h2>{mode === 'create' ? 'Create New Person' : 'Edit Person'}</h2>\n        {formConfig && (\n          <div className=\"form-info\">\n            <span className=\"form-name\">{formConfig.name}</span>\n            {formConfig.description && (\n              <span className=\"form-description\">{formConfig.description}</span>\n            )}\n          </div>\n        )}\n      </div>\n\n      {errors.general && (\n        <div className=\"alert alert-error\">{errors.general}</div>\n      )}\n\n      <form onSubmit={handleSubmit} className=\"person-form\">\n        {/* Hierarchical Dropdowns */}\n        <div className=\"form-section\">\n          <h3>Division & Category Selection</h3>\n\n          {/* Division Dropdown */}\n          <div className=\"form-group\">\n            <label className=\"form-label\">\n              Division <span className=\"required\">*</span>\n            </label>\n            <select\n              value={selectedDivision}\n              onChange={handleDivisionChange}\n              disabled={loading.divisions}\n              className={`form-select ${errors.division ? 'error' : ''}`}\n              required\n            >\n              <option value=\"\">\n                {loading.divisions ? 'Loading divisions...' : 'Select Division'}\n              </option>\n              {divisions.map(division => (\n                <option key={division.id} value={division.id}>\n                  {division.name}\n                </option>\n              ))}\n            </select>\n            {errors.division && (\n              <div className=\"error-message\">{errors.division}</div>\n            )}\n            {errors.divisions && (\n              <div className=\"error-message\">{errors.divisions}</div>\n            )}\n          </div>\n\n          {/* Category Dropdown */}\n          <div className=\"form-group\">\n            <label className=\"form-label\">\n              Category <span className=\"required\">*</span>\n            </label>\n            <select\n              value={selectedCategory}\n              onChange={handleCategoryChange}\n              disabled={!selectedDivision || loading.categories}\n              className={`form-select ${errors.category ? 'error' : ''}`}\n              required\n            >\n              <option value=\"\">\n                {!selectedDivision\n                  ? 'Select Division first'\n                  : loading.categories\n                    ? 'Loading categories...'\n                    : 'Select Category'\n                }\n              </option>\n              {categories.map(category => (\n                <option key={category.id} value={category.id}>\n                  {category.name}\n                </option>\n              ))}\n            </select>\n            {errors.category && (\n              <div className=\"error-message\">{errors.category}</div>\n            )}\n            {errors.categories && (\n              <div className=\"error-message\">{errors.categories}</div>\n            )}\n          </div>\n\n          {/* Subcategory Dropdown - Only show if category doesn't have a form */}\n          {formAvailability.showSubCategoryDropdown && (\n            <div className=\"form-group\">\n              <label className=\"form-label\">\n                Subcategory (Optional)\n              </label>\n              <select\n                value={selectedSubCategory}\n                onChange={handleSubCategoryChange}\n                disabled={!selectedCategory || loading.subCategories}\n                className=\"form-select\"\n              >\n                <option value=\"\">\n                  {!selectedCategory\n                    ? 'Select Category first'\n                    : loading.subCategories\n                      ? 'Loading subcategories...'\n                      : 'Select Subcategory (Optional)'\n                  }\n                </option>\n                {subCategories.map(subCategory => (\n                  <option key={subCategory.id} value={subCategory.id}>\n                    {subCategory.name}\n                  </option>\n                ))}\n              </select>\n              {errors.subCategories && (\n                <div className=\"error-message\">{errors.subCategories}</div>\n              )}\n            </div>\n          )}\n\n          {/* Form Status Messages */}\n          {loading.form && (\n            <div className=\"loading-message\">\n              <span>Loading form configuration...</span>\n            </div>\n          )}\n\n          {formAvailability.message && !loading.form && (\n            <div className={`status-message ${\n              formAvailability.categoryHasForm || formAvailability.subCategoryHasForm\n                ? 'success'\n                : formAvailability.message.includes('No form')\n                  ? 'error'\n                  : 'info'\n            }`}>\n              {formAvailability.message}\n            </div>\n          )}\n\n          {errors.form && (\n            <div className=\"error-message\">{errors.form}</div>\n          )}\n        </div>\n\n        {/* Dynamic Form Fields - Only show when form is available */}\n        {formConfig && Object.entries(sections).map(([sectionKey, section]) => (\n          <div key={sectionKey} className=\"form-section\">\n            <h3>{section.title}</h3>\n            <div className=\"form-fields\">\n              {section.fields\n                .filter(field => shouldShowField(field))\n                .map(field => (\n                  <FormField\n                    key={field.key}\n                    field={field}\n                    value={formData[field.key]}\n                    onChange={(value) => handleFieldChange(field.key, value)}\n                    error={errors[field.key]}\n                  />\n                ))}\n            </div>\n          </div>\n        ))}\n\n        {/* No Form Available Message */}\n        {!formConfig && selectedCategory && formAvailability.message.includes('No form') && (\n          <div className=\"form-section\">\n            <div className=\"no-form-message\">\n              <h3>No Form Available</h3>\n              <p>{formAvailability.message}</p>\n              <p>Please contact your administrator to create a form for this subcategory.</p>\n            </div>\n          </div>\n        )}\n\n        {/* Form Actions */}\n        <div className=\"form-actions\">\n          <button\n            type=\"button\"\n            onClick={onCancel}\n            className=\"btn btn-outline\"\n            disabled={submitting}\n          >\n            Cancel\n          </button>\n          {formConfig && (\n            <button\n              type=\"submit\"\n              className=\"btn btn-primary\"\n              disabled={submitting || !selectedDivision || !selectedCategory}\n            >\n              {submitting\n                ? (mode === 'create' ? 'Creating...' : 'Updating...')\n                : (mode === 'create' ? 'Create Person' : 'Update Person')\n              }\n            </button>\n          )}\n        </div>\n      </form>\n    </div>\n  );\n};\n\nexport default DynamicPersonForm;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,kBAAkB,EAAEC,YAAY,EAAEC,oBAAoB,EAAEC,oBAAoB,QAAQ,iCAAiC;AAC9H,OAAOC,iBAAiB,MAAM,kCAAkC;AAChE,OAAOC,UAAU,MAAM,2BAA2B;AAClD,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAO,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjC,MAAMC,iBAAiB,GAAGA,CAAC;EAAEC,QAAQ;EAAEC,QAAQ;EAAEC,WAAW,GAAG,IAAI;EAAEC,IAAI,GAAG;AAAS,CAAC,KAAK;EAAAC,EAAA;EACzF;EACA,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACmB,UAAU,EAAEC,aAAa,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACqB,aAAa,EAAEC,gBAAgB,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;;EAEtD;EACA,MAAM,CAACuB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACyB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAAC2B,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;;EAElE;EACA,MAAM,CAAC6B,UAAU,EAAEC,aAAa,CAAC,GAAG9B,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAAC+B,QAAQ,EAAEC,WAAW,CAAC,GAAGhC,QAAQ,CAACK,oBAAoB,CAAC,CAAC,CAAC;EAChE,MAAM,CAAC4B,MAAM,EAAEC,SAAS,CAAC,GAAGlC,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAExC;EACA,MAAM,CAACmC,OAAO,EAAEC,UAAU,CAAC,GAAGpC,QAAQ,CAAC;IACrCiB,SAAS,EAAE,KAAK;IAChBE,UAAU,EAAE,KAAK;IACjBE,aAAa,EAAE,KAAK;IACpBgB,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGvC,QAAQ,CAAC,KAAK,CAAC;;EAEnD;EACA,MAAM,CAACwC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGzC,QAAQ,CAAC;IACvD0C,eAAe,EAAE,KAAK;IACtBC,kBAAkB,EAAE,KAAK;IACzBC,uBAAuB,EAAE,KAAK;IAC9BC,OAAO,EAAE;EACX,CAAC,CAAC;;EAEF;EACA5C,SAAS,CAAC,MAAM;IACd6C,aAAa,CAAC,CAAC;;IAEf;IACA,IAAIhC,WAAW,EAAE;MAAA,IAAAiC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA;MACfjB,WAAW,CAAClB,WAAW,CAAC;MACxBU,mBAAmB,CAAC,EAAAuB,qBAAA,GAAAjC,WAAW,CAACoC,UAAU,cAAAH,qBAAA,uBAAtBA,qBAAA,CAAwBI,QAAQ,CAAC,CAAC,KAAI,EAAE,CAAC;MAC7DzB,mBAAmB,CAAC,EAAAsB,qBAAA,GAAAlC,WAAW,CAACsC,UAAU,cAAAJ,qBAAA,uBAAtBA,qBAAA,CAAwBG,QAAQ,CAAC,CAAC,KAAI,EAAE,CAAC;MAC7DvB,sBAAsB,CAAC,EAAAqB,qBAAA,GAAAnC,WAAW,CAACuC,aAAa,cAAAJ,qBAAA,uBAAzBA,qBAAA,CAA2BE,QAAQ,CAAC,CAAC,KAAI,EAAE,CAAC;IACrE,CAAC,MAAM;MACL;MACA,MAAMG,WAAW,GAAGhD,iBAAiB,CAACiD,oBAAoB,CAAC,CAAC;MAC5DzB,aAAa,CAACwB,WAAW,CAAC;IAC5B;EACF,CAAC,EAAE,CAACxC,WAAW,CAAC,CAAC;;EAEjB;EACAb,SAAS,CAAC,MAAM;IACd,IAAIsB,gBAAgB,EAAE;MACpBiC,cAAc,CAACjC,gBAAgB,CAAC;MAChC;MACAG,mBAAmB,CAAC,EAAE,CAAC;MACvBE,sBAAsB,CAAC,EAAE,CAAC;MAC1BN,gBAAgB,CAAC,EAAE,CAAC;MACpBQ,aAAa,CAAC,IAAI,CAAC;MACnBW,mBAAmB,CAAC;QAClBC,eAAe,EAAE,KAAK;QACtBC,kBAAkB,EAAE,KAAK;QACzBC,uBAAuB,EAAE,KAAK;QAC9BC,OAAO,EAAE;MACX,CAAC,CAAC;IACJ,CAAC,MAAM;MACLzB,aAAa,CAAC,EAAE,CAAC;MACjBE,gBAAgB,CAAC,EAAE,CAAC;MACpBI,mBAAmB,CAAC,EAAE,CAAC;MACvBE,sBAAsB,CAAC,EAAE,CAAC;MAC1BE,aAAa,CAAC,IAAI,CAAC;IACrB;EACF,CAAC,EAAE,CAACP,gBAAgB,CAAC,CAAC;;EAEtB;EACAtB,SAAS,CAAC,MAAM;IACd,IAAIwB,gBAAgB,EAAE;MACpBgC,uBAAuB,CAAChC,gBAAgB,CAAC;MACzC;MACAG,sBAAsB,CAAC,EAAE,CAAC;IAC5B,CAAC,MAAM;MACLN,gBAAgB,CAAC,EAAE,CAAC;MACpBM,sBAAsB,CAAC,EAAE,CAAC;MAC1BE,aAAa,CAAC,IAAI,CAAC;MACnBW,mBAAmB,CAAC;QAClBC,eAAe,EAAE,KAAK;QACtBC,kBAAkB,EAAE,KAAK;QACzBC,uBAAuB,EAAE,KAAK;QAC9BC,OAAO,EAAE;MACX,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACpB,gBAAgB,CAAC,CAAC;;EAEtB;EACAxB,SAAS,CAAC,MAAM;IACd,IAAI0B,mBAAmB,EAAE;MACvB+B,0BAA0B,CAAC/B,mBAAmB,CAAC;IACjD;EACF,CAAC,EAAE,CAACA,mBAAmB,CAAC,CAAC;;EAEzB;EACA,MAAMmB,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChCV,UAAU,CAACuB,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE1C,SAAS,EAAE;IAAK,CAAC,CAAC,CAAC;IAClD,IAAI;MACF,MAAM2C,QAAQ,GAAG,MAAMrD,UAAU,CAACsD,YAAY,CAAC,CAAC;MAChD3C,YAAY,CAAC0C,QAAQ,CAACE,IAAI,IAAI,EAAE,CAAC;IACnC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChD7B,SAAS,CAACyB,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE1C,SAAS,EAAE;MAA2B,CAAC,CAAC,CAAC;IACzE,CAAC,SAAS;MACRmB,UAAU,CAACuB,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE1C,SAAS,EAAE;MAAM,CAAC,CAAC,CAAC;IACrD;EACF,CAAC;;EAED;EACA,MAAMuC,cAAc,GAAG,MAAON,UAAU,IAAK;IAC3Cd,UAAU,CAACuB,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAExC,UAAU,EAAE;IAAK,CAAC,CAAC,CAAC;IACnD,IAAI;MACF,MAAMyC,QAAQ,GAAG,MAAMrD,UAAU,CAAC0D,uBAAuB,CAACf,UAAU,CAAC;MACrE9B,aAAa,CAACwC,QAAQ,CAACE,IAAI,IAAI,EAAE,CAAC;IACpC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD7B,SAAS,CAACyB,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAExC,UAAU,EAAE;MAA4B,CAAC,CAAC,CAAC;MACzEC,aAAa,CAAC,EAAE,CAAC;IACnB,CAAC,SAAS;MACRgB,UAAU,CAACuB,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAExC,UAAU,EAAE;MAAM,CAAC,CAAC,CAAC;IACtD;EACF,CAAC;;EAED;EACA,MAAM+C,iBAAiB,GAAG,MAAOd,UAAU,IAAK;IAC9ChB,UAAU,CAACuB,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAEtC,aAAa,EAAE;IAAK,CAAC,CAAC,CAAC;IACtD,IAAI;MACF,MAAMuC,QAAQ,GAAG,MAAMrD,UAAU,CAAC4D,0BAA0B,CAACf,UAAU,CAAC;MACxE9B,gBAAgB,CAACsC,QAAQ,CAACE,IAAI,IAAI,EAAE,CAAC;IACvC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD7B,SAAS,CAACyB,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEtC,aAAa,EAAE;MAA+B,CAAC,CAAC,CAAC;MAC/EC,gBAAgB,CAAC,EAAE,CAAC;IACtB,CAAC,SAAS;MACRc,UAAU,CAACuB,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEtC,aAAa,EAAE;MAAM,CAAC,CAAC,CAAC;IACzD;EACF,CAAC;;EAED;EACA,MAAMoC,uBAAuB,GAAG,MAAOL,UAAU,IAAK;IACpDhB,UAAU,CAACuB,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAEtB,IAAI,EAAE;IAAK,CAAC,CAAC,CAAC;IAE7C,IAAI;MACF;MACA,MAAMK,eAAe,GAAGpC,iBAAiB,CAAC8D,kBAAkB,CAACC,QAAQ,CAACjB,UAAU,CAAC,CAAC;MAElF,IAAIV,eAAe,EAAE;QACnB;QACA,MAAM4B,YAAY,GAAGhE,iBAAiB,CAACiE,cAAc,CAAC,UAAU,EAAEF,QAAQ,CAACjB,UAAU,CAAC,CAAC;QACvFtB,aAAa,CAACwC,YAAY,CAAC;QAC3B7B,mBAAmB,CAAC;UAClBC,eAAe,EAAE,IAAI;UACrBC,kBAAkB,EAAE,KAAK;UACzBC,uBAAuB,EAAE,KAAK;UAC9BC,OAAO,EAAE;QACX,CAAC,CAAC;MACJ,CAAC,MAAM;QACL;QACA,MAAMqB,iBAAiB,CAACd,UAAU,CAAC;QACnC;QACA,MAAME,WAAW,GAAGhD,iBAAiB,CAACiD,oBAAoB,CAAC,CAAC;QAC5DzB,aAAa,CAACwB,WAAW,CAAC;QAC1Bb,mBAAmB,CAAC;UAClBC,eAAe,EAAE,KAAK;UACtBC,kBAAkB,EAAE,KAAK;UACzBC,uBAAuB,EAAE,IAAI;UAC7BC,OAAO,EAAE;QACX,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAOkB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;MAC1D7B,SAAS,CAACyB,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEtB,IAAI,EAAE;MAAmC,CAAC,CAAC,CAAC;IAC5E,CAAC,SAAS;MACRD,UAAU,CAACuB,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEtB,IAAI,EAAE;MAAM,CAAC,CAAC,CAAC;IAChD;EACF,CAAC;;EAED;EACA,MAAMqB,0BAA0B,GAAG,MAAOL,aAAa,IAAK;IAC1DjB,UAAU,CAACuB,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAEtB,IAAI,EAAE;IAAK,CAAC,CAAC,CAAC;IAE7C,IAAI;MACF;MACA,MAAMM,kBAAkB,GAAGrC,iBAAiB,CAACkE,qBAAqB,CAACH,QAAQ,CAAChB,aAAa,CAAC,CAAC;MAE3F,IAAIV,kBAAkB,EAAE;QACtB;QACA,MAAM8B,eAAe,GAAGnE,iBAAiB,CAACiE,cAAc,CAAC,aAAa,EAAEF,QAAQ,CAAChB,aAAa,CAAC,CAAC;QAChGvB,aAAa,CAAC2C,eAAe,CAAC;QAC9BhC,mBAAmB,CAAC;UAClBC,eAAe,EAAE,KAAK;UACtBC,kBAAkB,EAAE,IAAI;UACxBC,uBAAuB,EAAE,IAAI;UAC7BC,OAAO,EAAE;QACX,CAAC,CAAC;MACJ,CAAC,MAAM;QACL;QACA,MAAMS,WAAW,GAAGhD,iBAAiB,CAACiD,oBAAoB,CAAC,CAAC;QAC5DzB,aAAa,CAACwB,WAAW,CAAC;QAC1Bb,mBAAmB,CAAC;UAClBC,eAAe,EAAE,KAAK;UACtBC,kBAAkB,EAAE,KAAK;UACzBC,uBAAuB,EAAE,IAAI;UAC7BC,OAAO,EAAE;QACX,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAOkB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;MAC7D7B,SAAS,CAACyB,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEtB,IAAI,EAAE;MAAmC,CAAC,CAAC,CAAC;IAC5E,CAAC,SAAS;MACRD,UAAU,CAACuB,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEtB,IAAI,EAAE;MAAM,CAAC,CAAC,CAAC;IAChD;EACF,CAAC;;EAED;EACA,MAAMqC,oBAAoB,GAAIC,CAAC,IAAK;IAClC,MAAMC,KAAK,GAAGD,CAAC,CAACE,MAAM,CAACD,KAAK;IAC5BpD,mBAAmB,CAACoD,KAAK,CAAC;;IAE1B;IACA5C,WAAW,CAAC2B,IAAI,KAAK;MACnB,GAAGA,IAAI;MACPT,UAAU,EAAE0B,KAAK,GAAGP,QAAQ,CAACO,KAAK,CAAC,GAAG,IAAI;MAC1CxB,UAAU,EAAE,IAAI;MAChBC,aAAa,EAAE;IACjB,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMyB,oBAAoB,GAAIH,CAAC,IAAK;IAClC,MAAMC,KAAK,GAAGD,CAAC,CAACE,MAAM,CAACD,KAAK;IAC5BlD,mBAAmB,CAACkD,KAAK,CAAC;;IAE1B;IACA5C,WAAW,CAAC2B,IAAI,KAAK;MACnB,GAAGA,IAAI;MACPP,UAAU,EAAEwB,KAAK,GAAGP,QAAQ,CAACO,KAAK,CAAC,GAAG,IAAI;MAC1CvB,aAAa,EAAE;IACjB,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAM0B,uBAAuB,GAAIJ,CAAC,IAAK;IACrC,MAAMC,KAAK,GAAGD,CAAC,CAACE,MAAM,CAACD,KAAK;IAC5BhD,sBAAsB,CAACgD,KAAK,CAAC;;IAE7B;IACA5C,WAAW,CAAC2B,IAAI,KAAK;MACnB,GAAGA,IAAI;MACPN,aAAa,EAAEuB,KAAK,GAAGP,QAAQ,CAACO,KAAK,CAAC,GAAG;IAC3C,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMI,iBAAiB,GAAGA,CAACC,QAAQ,EAAEL,KAAK,KAAK;IAC7C5C,WAAW,CAAC2B,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACsB,QAAQ,GAAGL;IACd,CAAC,CAAC,CAAC;;IAEH;IACA,IAAI3C,MAAM,CAACgD,QAAQ,CAAC,EAAE;MACpB/C,SAAS,CAACyB,IAAI,KAAK;QACjB,GAAGA,IAAI;QACP,CAACsB,QAAQ,GAAG;MACd,CAAC,CAAC,CAAC;IACL;EACF,CAAC;EAED,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,SAAS,GAAG,CAAC,CAAC;;IAEpB;IACA,IAAI,CAAC5D,gBAAgB,EAAE;MACrB4D,SAAS,CAACC,QAAQ,GAAG,sBAAsB;IAC7C;IACA,IAAI,CAAC3D,gBAAgB,EAAE;MACrB0D,SAAS,CAACE,QAAQ,GAAG,sBAAsB;IAC7C;;IAEA;IACA,IAAI,CAACxD,UAAU,EAAE;MACf,IAAIJ,gBAAgB,IAAIE,mBAAmB,IAAI,CAACa,gBAAgB,CAACG,kBAAkB,EAAE;QACnFwC,SAAS,CAAC9C,IAAI,GAAG,mDAAmD;MACtE,CAAC,MAAM,IAAIZ,gBAAgB,IAAI,CAACe,gBAAgB,CAACE,eAAe,IAAI,CAACF,gBAAgB,CAACI,uBAAuB,EAAE;QAC7GuC,SAAS,CAAC9C,IAAI,GAAG,gDAAgD;MACnE,CAAC,MAAM,IAAI,CAACZ,gBAAgB,EAAE;QAC5B0D,SAAS,CAAC9C,IAAI,GAAG,2CAA2C;MAC9D;MACA,OAAO;QAAEiD,OAAO,EAAE,KAAK;QAAErD,MAAM,EAAEkD;MAAU,CAAC;IAC9C;;IAEA;IACAtD,UAAU,CAAC0D,MAAM,CAACC,OAAO,CAACC,KAAK,IAAI;MACjC,MAAMb,KAAK,GAAG7C,QAAQ,CAAC0D,KAAK,CAACC,GAAG,CAAC;;MAEjC;MACA,IAAID,KAAK,CAACE,QAAQ,KAAK,CAACf,KAAK,IAAK,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACgB,IAAI,CAAC,CAAC,KAAK,EAAG,CAAC,EAAE;QACpFT,SAAS,CAACM,KAAK,CAACC,GAAG,CAAC,GAAG,GAAGD,KAAK,CAACI,KAAK,cAAc;QACnD;MACF;;MAEA;MACA,IAAIJ,KAAK,CAACK,WAAW,IAAIC,eAAe,CAACN,KAAK,CAAC,EAAE;QAC/C,IAAIA,KAAK,CAACE,QAAQ,KAAK,CAACf,KAAK,IAAK,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACgB,IAAI,CAAC,CAAC,KAAK,EAAG,CAAC,EAAE;UACpFT,SAAS,CAACM,KAAK,CAACC,GAAG,CAAC,GAAG,GAAGD,KAAK,CAACI,KAAK,cAAc;UACnD;QACF;MACF;;MAEA;MACA,IAAIjB,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACgB,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;QAAA,IAAAI,iBAAA,EAAAC,kBAAA,EAAAC,kBAAA;QAC7D,QAAQT,KAAK,CAACU,IAAI;UAChB,KAAK,OAAO;YACV,MAAMC,UAAU,GAAG,4BAA4B;YAC/C,IAAI,CAACA,UAAU,CAACC,IAAI,CAACzB,KAAK,CAAC,EAAE;cAC3BO,SAAS,CAACM,KAAK,CAACC,GAAG,CAAC,GAAG,oCAAoC;YAC7D;YACA;UACF,KAAK,KAAK;YACR,MAAMY,UAAU,GAAG,qCAAqC;YACxD,IAAI,CAACA,UAAU,CAACD,IAAI,CAACzB,KAAK,CAAC,EAAE;cAC3BO,SAAS,CAACM,KAAK,CAACC,GAAG,CAAC,GAAG,oCAAoC;YAC7D;YACA;UACF,KAAK,KAAK;YACR,IAAI;cACF,IAAIa,GAAG,CAAC3B,KAAK,CAAC;YAChB,CAAC,CAAC,MAAM;cACNO,SAAS,CAACM,KAAK,CAACC,GAAG,CAAC,GAAG,0BAA0B;YACnD;YACA;UACF,KAAK,QAAQ;YACX,IAAID,KAAK,CAACe,UAAU,EAAE;cACpB,MAAMC,QAAQ,GAAGC,UAAU,CAAC9B,KAAK,CAAC;cAClC,IAAIa,KAAK,CAACe,UAAU,CAACG,GAAG,KAAKC,SAAS,IAAIH,QAAQ,GAAGhB,KAAK,CAACe,UAAU,CAACG,GAAG,EAAE;gBACzExB,SAAS,CAACM,KAAK,CAACC,GAAG,CAAC,GAAG,0BAA0BD,KAAK,CAACe,UAAU,CAACG,GAAG,EAAE;cACzE;cACA,IAAIlB,KAAK,CAACe,UAAU,CAACK,GAAG,KAAKD,SAAS,IAAIH,QAAQ,GAAGhB,KAAK,CAACe,UAAU,CAACK,GAAG,EAAE;gBACzE1B,SAAS,CAACM,KAAK,CAACC,GAAG,CAAC,GAAG,yBAAyBD,KAAK,CAACe,UAAU,CAACK,GAAG,EAAE;cACxE;YACF;YACA;QACJ;;QAEA;QACA,KAAAb,iBAAA,GAAIP,KAAK,CAACe,UAAU,cAAAR,iBAAA,eAAhBA,iBAAA,CAAkBc,OAAO,EAAE;UAC7B,MAAMC,KAAK,GAAG,IAAIC,MAAM,CAACvB,KAAK,CAACe,UAAU,CAACM,OAAO,CAAC;UAClD,IAAI,CAACC,KAAK,CAACV,IAAI,CAACzB,KAAK,CAAC,EAAE;YACtBO,SAAS,CAACM,KAAK,CAACC,GAAG,CAAC,GAAG,GAAGD,KAAK,CAACI,KAAK,oBAAoB;UAC3D;QACF;;QAEA;QACA,IAAI,CAAAI,kBAAA,GAAAR,KAAK,CAACe,UAAU,cAAAP,kBAAA,eAAhBA,kBAAA,CAAkBgB,SAAS,IAAIrC,KAAK,CAACsC,MAAM,GAAGzB,KAAK,CAACe,UAAU,CAACS,SAAS,EAAE;UAC5E9B,SAAS,CAACM,KAAK,CAACC,GAAG,CAAC,GAAG,GAAGD,KAAK,CAACI,KAAK,qBAAqBJ,KAAK,CAACe,UAAU,CAACS,SAAS,aAAa;QACnG;QACA,IAAI,CAAAf,kBAAA,GAAAT,KAAK,CAACe,UAAU,cAAAN,kBAAA,eAAhBA,kBAAA,CAAkBiB,SAAS,IAAIvC,KAAK,CAACsC,MAAM,GAAGzB,KAAK,CAACe,UAAU,CAACW,SAAS,EAAE;UAC5EhC,SAAS,CAACM,KAAK,CAACC,GAAG,CAAC,GAAG,GAAGD,KAAK,CAACI,KAAK,oBAAoBJ,KAAK,CAACe,UAAU,CAACW,SAAS,aAAa;QAClG;MACF;IACF,CAAC,CAAC;;IAEF;IACA,IAAIpF,QAAQ,CAACqF,SAAS,IAAIrF,QAAQ,CAACsF,cAAc,IAAItF,QAAQ,CAACuF,WAAW,EAAE;MACzE,MAAMC,SAAS,GAAG,IAAIC,IAAI,CAACzF,QAAQ,CAACuF,WAAW,CAAC;MAChD,MAAMG,YAAY,GAAG,IAAID,IAAI,CAACzF,QAAQ,CAACsF,cAAc,CAAC;MACtD,IAAII,YAAY,IAAIF,SAAS,EAAE;QAC7BpC,SAAS,CAACkC,cAAc,GAAG,wCAAwC;MACrE;IACF;IAEA,OAAO;MACL/B,OAAO,EAAEoC,MAAM,CAACC,IAAI,CAACxC,SAAS,CAAC,CAAC+B,MAAM,KAAK,CAAC;MAC5CjF,MAAM,EAAEkD;IACV,CAAC;EACH,CAAC;EAED,MAAMyC,YAAY,GAAG,MAAOjD,CAAC,IAAK;IAChCA,CAAC,CAACkD,cAAc,CAAC,CAAC;IAElB,MAAMrB,UAAU,GAAGtB,YAAY,CAAC,CAAC;IACjC,IAAI,CAACsB,UAAU,CAAClB,OAAO,EAAE;MACvBpD,SAAS,CAACsE,UAAU,CAACvE,MAAM,CAAC;MAC5B;IACF;IAEAM,aAAa,CAAC,IAAI,CAAC;IACnB,IAAI;MACF;MACA,MAAMuF,UAAU,GAAG;QACjB,GAAG/F,QAAQ;QACX;QACAmB,UAAU,EAAEmB,QAAQ,CAAC9C,gBAAgB,CAAC;QACtC6B,UAAU,EAAEiB,QAAQ,CAAC5C,gBAAgB,CAAC;QACtC4B,aAAa,EAAE1B,mBAAmB,GAAG0C,QAAQ,CAAC1C,mBAAmB,CAAC,GAAG,IAAI;QACzE;QACAoG,gBAAgB,EAAEC,KAAK,CAACC,OAAO,CAAClG,QAAQ,CAACgG,gBAAgB,CAAC,GACtDhG,QAAQ,CAACgG,gBAAgB,GACzBhG,QAAQ,CAACgG,gBAAgB,GAAGhG,QAAQ,CAACgG,gBAAgB,CAACG,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACxC,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE;QAC5FyC,iBAAiB,EAAEL,KAAK,CAACC,OAAO,CAAClG,QAAQ,CAACsG,iBAAiB,CAAC,GACxDtG,QAAQ,CAACsG,iBAAiB,GAC1BtG,QAAQ,CAACsG,iBAAiB,GAAGtG,QAAQ,CAACsG,iBAAiB,CAACH,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACxC,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE;QAC9F0C,eAAe,EAAEN,KAAK,CAACC,OAAO,CAAClG,QAAQ,CAACuG,eAAe,CAAC,GACpDvG,QAAQ,CAACuG,eAAe,GACxB;MACN,CAAC;;MAED;MACAtE,OAAO,CAACuE,GAAG,CAAC,yBAAyB,EAAET,UAAU,CAAC;MAClD9D,OAAO,CAACuE,GAAG,CAAC,oBAAoB,EAAEhH,gBAAgB,CAAC;MACnDyC,OAAO,CAACuE,GAAG,CAAC,oBAAoB,EAAE9G,gBAAgB,CAAC;MACnDuC,OAAO,CAACuE,GAAG,CAAC,uBAAuB,EAAE5G,mBAAmB,CAAC;MAEzD,IAAI6G,MAAM;MACV,IAAIzH,IAAI,KAAK,QAAQ,EAAE;QACrByH,MAAM,GAAG,MAAMjI,UAAU,CAACkI,YAAY,CAACX,UAAU,CAAC;MACpD,CAAC,MAAM;QACLU,MAAM,GAAG,MAAMjI,UAAU,CAACmI,YAAY,CAAC5H,WAAW,CAAC6H,EAAE,EAAEb,UAAU,CAAC;MACpE;MAEAlH,QAAQ,CAAC4H,MAAM,CAAC;IAClB,CAAC,CAAC,OAAOzE,KAAK,EAAE;MAAA,IAAA6E,eAAA,EAAAC,gBAAA,EAAAC,qBAAA;MACd9E,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9CC,OAAO,CAACD,KAAK,CAAC,gBAAgB,GAAA6E,eAAA,GAAE7E,KAAK,CAACH,QAAQ,cAAAgF,eAAA,uBAAdA,eAAA,CAAgB9E,IAAI,CAAC;MAErD,KAAA+E,gBAAA,GAAI9E,KAAK,CAACH,QAAQ,cAAAiF,gBAAA,gBAAAC,qBAAA,GAAdD,gBAAA,CAAgB/E,IAAI,cAAAgF,qBAAA,eAApBA,qBAAA,CAAsB7G,MAAM,EAAE;QAChC;QACA,MAAM8G,aAAa,GAAG,CAAC,CAAC;QACxBrB,MAAM,CAACC,IAAI,CAAC5D,KAAK,CAACH,QAAQ,CAACE,IAAI,CAAC7B,MAAM,CAAC,CAACuD,OAAO,CAACE,GAAG,IAAI;UACrD,MAAMsD,aAAa,GAAGjF,KAAK,CAACH,QAAQ,CAACE,IAAI,CAAC7B,MAAM,CAACyD,GAAG,CAAC;UACrDqD,aAAa,CAACrD,GAAG,CAACuD,WAAW,CAAC,CAAC,CAAC,GAAGjB,KAAK,CAACC,OAAO,CAACe,aAAa,CAAC,GAC3DA,aAAa,CAACE,IAAI,CAAC,IAAI,CAAC,GACxBF,aAAa;QACnB,CAAC,CAAC;QACF9G,SAAS,CAAC6G,aAAa,CAAC;MAC1B,CAAC,MAAM,IAAIhF,KAAK,CAACoF,iBAAiB,IAAIpF,KAAK,CAACoF,iBAAiB,CAAC,CAAC,EAAE;QAC/DjH,SAAS,CAAC6B,KAAK,CAACqF,mBAAmB,CAAC,CAAC,CAAC;MACxC,CAAC,MAAM;QAAA,IAAAC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA;QACL,MAAMC,YAAY,GAAG,EAAAJ,gBAAA,GAAAtF,KAAK,CAACH,QAAQ,cAAAyF,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBvF,IAAI,cAAAwF,qBAAA,uBAApBA,qBAAA,CAAsBzG,OAAO,OAAA0G,gBAAA,GAC/BxF,KAAK,CAACH,QAAQ,cAAA2F,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBzF,IAAI,cAAA0F,qBAAA,uBAApBA,qBAAA,CAAsBE,KAAK,KAC3B3F,KAAK,CAAClB,OAAO,IACb,gCAAgC;QACnDX,SAAS,CAAC;UAAEyH,OAAO,EAAEF;QAAa,CAAC,CAAC;MACtC;IACF,CAAC,SAAS;MACRlH,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;EAED,MAAMwD,eAAe,GAAIN,KAAK,IAAK;IACjC,IAAI,CAACA,KAAK,CAACK,WAAW,EAAE,OAAO,IAAI;IAEnC,MAAM8D,cAAc,GAAG7H,QAAQ,CAAC0D,KAAK,CAACK,WAAW,CAACL,KAAK,CAAC;IACxD,MAAMoE,aAAa,GAAGpE,KAAK,CAACK,WAAW,CAAClB,KAAK;;IAE7C;IACA,IAAI,OAAOiF,aAAa,KAAK,SAAS,IAAIA,aAAa,KAAK,MAAM,IAAIA,aAAa,KAAK,OAAO,EAAE;MAC/F,OAAOD,cAAc,MAAMC,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,MAAM,CAAC;IAChF;IAEA,OAAOD,cAAc,KAAKC,aAAa;EACzC,CAAC;EAED,MAAMC,qBAAqB,GAAGA,CAAA,KAAM;IAClC,IAAI,CAACjI,UAAU,IAAI,CAACA,UAAU,CAAC0D,MAAM,EAAE,OAAO,CAAC,CAAC;IAEhD,MAAMwE,QAAQ,GAAG,CAAC,CAAC;IACnBlI,UAAU,CAAC0D,MAAM,CAACC,OAAO,CAACC,KAAK,IAAI;MACjC,MAAMuE,UAAU,GAAGvE,KAAK,CAACwE,OAAO,IAAI,SAAS;MAC7C,IAAI,CAACF,QAAQ,CAACC,UAAU,CAAC,EAAE;QACzBD,QAAQ,CAACC,UAAU,CAAC,GAAG;UACrBN,KAAK,EAAEQ,eAAe,CAACF,UAAU,CAAC;UAClCzE,MAAM,EAAE;QACV,CAAC;MACH;MACAwE,QAAQ,CAACC,UAAU,CAAC,CAACzE,MAAM,CAAC4E,IAAI,CAAC1E,KAAK,CAAC;IACzC,CAAC,CAAC;IAEF,OAAOsE,QAAQ;EACjB,CAAC;EAED,MAAMG,eAAe,GAAIF,UAAU,IAAK;IACtC,MAAMI,MAAM,GAAG;MACbC,YAAY,EAAE,sBAAsB;MACpCC,WAAW,EAAE,qBAAqB;MAClCC,YAAY,EAAE,sBAAsB;MACpCC,YAAY,EAAE,sBAAsB;MACpCC,aAAa,EAAE,uBAAuB;MACtCC,eAAe,EAAE,kBAAkB;MACnCC,WAAW,EAAE,qBAAqB;MAClCC,gBAAgB,EAAE,mBAAmB;MACrCC,aAAa,EAAE,uBAAuB;MACtClB,OAAO,EAAE;IACX,CAAC;IACD,OAAOS,MAAM,CAACJ,UAAU,CAAC,IAAIA,UAAU;EACzC,CAAC;EAED,MAAMD,QAAQ,GAAGlI,UAAU,GAAGiI,qBAAqB,CAAC,CAAC,GAAG,CAAC,CAAC;EAI1D,oBACEpJ,OAAA;IAAKoK,SAAS,EAAC,qBAAqB;IAAAC,QAAA,gBAClCrK,OAAA;MAAKoK,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BrK,OAAA;QAAAqK,QAAA,EAAKhK,IAAI,KAAK,QAAQ,GAAG,mBAAmB,GAAG;MAAa;QAAAiK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,EACjEtJ,UAAU,iBACTnB,OAAA;QAAKoK,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBrK,OAAA;UAAMoK,SAAS,EAAC,WAAW;UAAAC,QAAA,EAAElJ,UAAU,CAACuJ;QAAI;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,EACnDtJ,UAAU,CAACwJ,WAAW,iBACrB3K,OAAA;UAAMoK,SAAS,EAAC,kBAAkB;UAAAC,QAAA,EAAElJ,UAAU,CAACwJ;QAAW;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAClE;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAELlJ,MAAM,CAAC0H,OAAO,iBACbjJ,OAAA;MAAKoK,SAAS,EAAC,mBAAmB;MAAAC,QAAA,EAAE9I,MAAM,CAAC0H;IAAO;MAAAqB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CACzD,eAEDzK,OAAA;MAAME,QAAQ,EAAEgH,YAAa;MAACkD,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAEnDrK,OAAA;QAAKoK,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BrK,OAAA;UAAAqK,QAAA,EAAI;QAA6B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAGtCzK,OAAA;UAAKoK,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBrK,OAAA;YAAOoK,SAAS,EAAC,YAAY;YAAAC,QAAA,GAAC,WACnB,eAAArK,OAAA;cAAMoK,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC,eACRzK,OAAA;YACEkE,KAAK,EAAErD,gBAAiB;YACxB+J,QAAQ,EAAE5G,oBAAqB;YAC/B6G,QAAQ,EAAEpJ,OAAO,CAAClB,SAAU;YAC5B6J,SAAS,EAAE,eAAe7I,MAAM,CAACmD,QAAQ,GAAG,OAAO,GAAG,EAAE,EAAG;YAC3DO,QAAQ;YAAAoF,QAAA,gBAERrK,OAAA;cAAQkE,KAAK,EAAC,EAAE;cAAAmG,QAAA,EACb5I,OAAO,CAAClB,SAAS,GAAG,sBAAsB,GAAG;YAAiB;cAAA+J,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzD,CAAC,EACRlK,SAAS,CAACkH,GAAG,CAAC/C,QAAQ,iBACrB1E,OAAA;cAA0BkE,KAAK,EAAEQ,QAAQ,CAACuD,EAAG;cAAAoC,QAAA,EAC1C3F,QAAQ,CAACgG;YAAI,GADHhG,QAAQ,CAACuD,EAAE;cAAAqC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEhB,CACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,EACRlJ,MAAM,CAACmD,QAAQ,iBACd1E,OAAA;YAAKoK,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAE9I,MAAM,CAACmD;UAAQ;YAAA4F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACtD,EACAlJ,MAAM,CAAChB,SAAS,iBACfP,OAAA;YAAKoK,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAE9I,MAAM,CAAChB;UAAS;YAAA+J,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACvD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGNzK,OAAA;UAAKoK,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBrK,OAAA;YAAOoK,SAAS,EAAC,YAAY;YAAAC,QAAA,GAAC,WACnB,eAAArK,OAAA;cAAMoK,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC,eACRzK,OAAA;YACEkE,KAAK,EAAEnD,gBAAiB;YACxB6J,QAAQ,EAAExG,oBAAqB;YAC/ByG,QAAQ,EAAE,CAAChK,gBAAgB,IAAIY,OAAO,CAAChB,UAAW;YAClD2J,SAAS,EAAE,eAAe7I,MAAM,CAACoD,QAAQ,GAAG,OAAO,GAAG,EAAE,EAAG;YAC3DM,QAAQ;YAAAoF,QAAA,gBAERrK,OAAA;cAAQkE,KAAK,EAAC,EAAE;cAAAmG,QAAA,EACb,CAACxJ,gBAAgB,GACd,uBAAuB,GACvBY,OAAO,CAAChB,UAAU,GAChB,uBAAuB,GACvB;YAAiB;cAAA6J,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEjB,CAAC,EACRhK,UAAU,CAACgH,GAAG,CAAC9C,QAAQ,iBACtB3E,OAAA;cAA0BkE,KAAK,EAAES,QAAQ,CAACsD,EAAG;cAAAoC,QAAA,EAC1C1F,QAAQ,CAAC+F;YAAI,GADH/F,QAAQ,CAACsD,EAAE;cAAAqC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEhB,CACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,EACRlJ,MAAM,CAACoD,QAAQ,iBACd3E,OAAA;YAAKoK,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAE9I,MAAM,CAACoD;UAAQ;YAAA2F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACtD,EACAlJ,MAAM,CAACd,UAAU,iBAChBT,OAAA;YAAKoK,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAE9I,MAAM,CAACd;UAAU;YAAA6J,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACxD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,EAGL3I,gBAAgB,CAACI,uBAAuB,iBACvClC,OAAA;UAAKoK,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBrK,OAAA;YAAOoK,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAE9B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRzK,OAAA;YACEkE,KAAK,EAAEjD,mBAAoB;YAC3B2J,QAAQ,EAAEvG,uBAAwB;YAClCwG,QAAQ,EAAE,CAAC9J,gBAAgB,IAAIU,OAAO,CAACd,aAAc;YACrDyJ,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAEvBrK,OAAA;cAAQkE,KAAK,EAAC,EAAE;cAAAmG,QAAA,EACb,CAACtJ,gBAAgB,GACd,uBAAuB,GACvBU,OAAO,CAACd,aAAa,GACnB,0BAA0B,GAC1B;YAA+B;cAAA2J,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAE/B,CAAC,EACR9J,aAAa,CAAC8G,GAAG,CAACqD,WAAW,iBAC5B9K,OAAA;cAA6BkE,KAAK,EAAE4G,WAAW,CAAC7C,EAAG;cAAAoC,QAAA,EAChDS,WAAW,CAACJ;YAAI,GADNI,WAAW,CAAC7C,EAAE;cAAAqC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEnB,CACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,EACRlJ,MAAM,CAACZ,aAAa,iBACnBX,OAAA;YAAKoK,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAE9I,MAAM,CAACZ;UAAa;YAAA2J,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAC3D;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN,EAGAhJ,OAAO,CAACE,IAAI,iBACX3B,OAAA;UAAKoK,SAAS,EAAC,iBAAiB;UAAAC,QAAA,eAC9BrK,OAAA;YAAAqK,QAAA,EAAM;UAA6B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CACN,EAEA3I,gBAAgB,CAACK,OAAO,IAAI,CAACV,OAAO,CAACE,IAAI,iBACxC3B,OAAA;UAAKoK,SAAS,EAAE,kBACdtI,gBAAgB,CAACE,eAAe,IAAIF,gBAAgB,CAACG,kBAAkB,GACnE,SAAS,GACTH,gBAAgB,CAACK,OAAO,CAAC4I,QAAQ,CAAC,SAAS,CAAC,GAC1C,OAAO,GACP,MAAM,EACX;UAAAV,QAAA,EACAvI,gBAAgB,CAACK;QAAO;UAAAmI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CACN,EAEAlJ,MAAM,CAACI,IAAI,iBACV3B,OAAA;UAAKoK,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAE9I,MAAM,CAACI;QAAI;UAAA2I,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAClD;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,EAGLtJ,UAAU,IAAI6F,MAAM,CAACgE,OAAO,CAAC3B,QAAQ,CAAC,CAAC5B,GAAG,CAAC,CAAC,CAAC6B,UAAU,EAAEC,OAAO,CAAC,kBAChEvJ,OAAA;QAAsBoK,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC5CrK,OAAA;UAAAqK,QAAA,EAAKd,OAAO,CAACP;QAAK;UAAAsB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACxBzK,OAAA;UAAKoK,SAAS,EAAC,aAAa;UAAAC,QAAA,EACzBd,OAAO,CAAC1E,MAAM,CACZoG,MAAM,CAAClG,KAAK,IAAIM,eAAe,CAACN,KAAK,CAAC,CAAC,CACvC0C,GAAG,CAAC1C,KAAK,iBACR/E,OAAA,CAACF,SAAS;YAERiF,KAAK,EAAEA,KAAM;YACbb,KAAK,EAAE7C,QAAQ,CAAC0D,KAAK,CAACC,GAAG,CAAE;YAC3B4F,QAAQ,EAAG1G,KAAK,IAAKI,iBAAiB,CAACS,KAAK,CAACC,GAAG,EAAEd,KAAK,CAAE;YACzDb,KAAK,EAAE9B,MAAM,CAACwD,KAAK,CAACC,GAAG;UAAE,GAJpBD,KAAK,CAACC,GAAG;YAAAsF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAKf,CACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA,GAdEnB,UAAU;QAAAgB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAef,CACN,CAAC,EAGD,CAACtJ,UAAU,IAAIJ,gBAAgB,IAAIe,gBAAgB,CAACK,OAAO,CAAC4I,QAAQ,CAAC,SAAS,CAAC,iBAC9E/K,OAAA;QAAKoK,SAAS,EAAC,cAAc;QAAAC,QAAA,eAC3BrK,OAAA;UAAKoK,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9BrK,OAAA;YAAAqK,QAAA,EAAI;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1BzK,OAAA;YAAAqK,QAAA,EAAIvI,gBAAgB,CAACK;UAAO;YAAAmI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjCzK,OAAA;YAAAqK,QAAA,EAAG;UAAwE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5E;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,eAGDzK,OAAA;QAAKoK,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BrK,OAAA;UACEyF,IAAI,EAAC,QAAQ;UACbyF,OAAO,EAAE/K,QAAS;UAClBiK,SAAS,EAAC,iBAAiB;UAC3BS,QAAQ,EAAEjJ,UAAW;UAAAyI,QAAA,EACtB;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,EACRtJ,UAAU,iBACTnB,OAAA;UACEyF,IAAI,EAAC,QAAQ;UACb2E,SAAS,EAAC,iBAAiB;UAC3BS,QAAQ,EAAEjJ,UAAU,IAAI,CAACf,gBAAgB,IAAI,CAACE,gBAAiB;UAAAsJ,QAAA,EAE9DzI,UAAU,GACNvB,IAAI,KAAK,QAAQ,GAAG,aAAa,GAAG,aAAa,GACjDA,IAAI,KAAK,QAAQ,GAAG,eAAe,GAAG;QAAgB;UAAAiK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAErD,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACnK,EAAA,CAhsBIL,iBAAiB;AAAAkL,EAAA,GAAjBlL,iBAAiB;AAksBvB,eAAeA,iBAAiB;AAAC,IAAAkL,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}