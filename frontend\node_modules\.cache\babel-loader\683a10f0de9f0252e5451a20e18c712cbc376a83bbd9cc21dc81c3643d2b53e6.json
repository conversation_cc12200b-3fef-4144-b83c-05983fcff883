{"ast": null, "code": "import { useMotionValueEvent } from '../utils/use-motion-value-event.mjs';\nimport { useMotionValue } from './use-motion-value.mjs';\n\n/**\n * Creates a `MotionValue` that updates when the velocity of the provided `MotionValue` changes.\n *\n * ```javascript\n * const x = useMotionValue(0)\n * const xVelocity = useVelocity(x)\n * const xAcceleration = useVelocity(xVelocity)\n * ```\n *\n * @public\n */\nfunction useVelocity(value) {\n  const velocity = useMotionValue(value.getVelocity());\n  useMotionValueEvent(value, \"velocityChange\", newVelocity => {\n    velocity.set(newVelocity);\n  });\n  return velocity;\n}\nexport { useVelocity };", "map": {"version": 3, "names": ["useMotionValueEvent", "useMotionValue", "useVelocity", "value", "velocity", "getVelocity", "newVelocity", "set"], "sources": ["C:/Users/<USER>/Downloads/Advanced/Advanced CRM filed all use/frontend/node_modules/framer-motion/dist/es/value/use-velocity.mjs"], "sourcesContent": ["import { useMotionValueEvent } from '../utils/use-motion-value-event.mjs';\nimport { useMotionValue } from './use-motion-value.mjs';\n\n/**\n * Creates a `MotionValue` that updates when the velocity of the provided `MotionValue` changes.\n *\n * ```javascript\n * const x = useMotionValue(0)\n * const xVelocity = useVelocity(x)\n * const xAcceleration = useVelocity(xVelocity)\n * ```\n *\n * @public\n */\nfunction useVelocity(value) {\n    const velocity = useMotionValue(value.getVelocity());\n    useMotionValueEvent(value, \"velocityChange\", (newVelocity) => {\n        velocity.set(newVelocity);\n    });\n    return velocity;\n}\n\nexport { useVelocity };\n"], "mappings": "AAAA,SAASA,mBAAmB,QAAQ,qCAAqC;AACzE,SAASC,cAAc,QAAQ,wBAAwB;;AAEvD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,WAAWA,CAACC,KAAK,EAAE;EACxB,MAAMC,QAAQ,GAAGH,cAAc,CAACE,KAAK,CAACE,WAAW,CAAC,CAAC,CAAC;EACpDL,mBAAmB,CAACG,KAAK,EAAE,gBAAgB,EAAGG,WAAW,IAAK;IAC1DF,QAAQ,CAACG,GAAG,CAACD,WAAW,CAAC;EAC7B,CAAC,CAAC;EACF,OAAOF,QAAQ;AACnB;AAEA,SAASF,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}