using CrmApi.Repositories.Division;
using CrmApi.Repositories.Category;
using CrmApi.Repositories.SubCategory;
using CrmApi.Repositories.State;

namespace CrmApi.Services.Dashboard
{
    public class DashboardService : IDashboardService
    {
        private readonly IDivisionRepository _divisionRepository;
        private readonly ICategoryRepository _categoryRepository;
        private readonly ISubCategoryRepository _subCategoryRepository;
        private readonly IStateRepository _stateRepository;

        public DashboardService(
            IDivisionRepository divisionRepository,
            ICategoryRepository categoryRepository,
            ISubCategoryRepository subCategoryRepository,
            IStateRepository stateRepository)
        {
            _divisionRepository = divisionRepository;
            _categoryRepository = categoryRepository;
            _subCategoryRepository = subCategoryRepository;
            _stateRepository = stateRepository;
        }

        public async Task<DashboardStatsResponse> GetDashboardStatsAsync()
        {
            var divisions = await _divisionRepository.GetAllAsync();
            var categories = await _categoryRepository.GetAllAsync();
            var subCategories = await _subCategoryRepository.GetAllAsync();
            var states = await _stateRepository.GetAllAsync();

            return new DashboardStatsResponse
            {
                TotalDivisions = divisions.Count(),
                TotalCategories = categories.Count(),
                TotalSubCategories = subCategories.Count(),
                TotalStates = states.Count()
            };
        }

        public async Task<DashboardSummaryResponse> GetSummaryAsync()
        {
            var divisions = await _divisionRepository.GetAllAsync();
            var categories = await _categoryRepository.GetAllAsync();
            var subCategories = await _subCategoryRepository.GetAllAsync();
            var states = await _stateRepository.GetAllAsync();

            return new DashboardSummaryResponse
            {
                TotalDivisions = divisions.Count(),
                TotalCategories = categories.Count(),
                TotalSubCategories = subCategories.Count(),
                TotalStates = states.Count()
            };
        }
    }
}
