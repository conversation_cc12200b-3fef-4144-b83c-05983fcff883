using CrmApi.Models.State;
using CrmApi.Repositories.State;
using CrmApi.Exceptions;

namespace CrmApi.Services.State
{
    public class StateService : IStateService
    {
        private readonly IStateRepository _stateRepository;

        public StateService(IStateRepository stateRepository)
        {
            _stateRepository = stateRepository;
        }

        public async Task<IEnumerable<StateResponse>> GetAllStatesAsync()
        {
            var states = await _stateRepository.GetAllAsync();
            return states.Select(MapToResponse);
        }

        public async Task<StateResponse?> GetStateByIdAsync(int id)
        {
            var state = await _stateRepository.GetByIdAsync(id);
            return state != null ? MapToResponse(state) : null;
        }

        public async Task<StateResponse> CreateStateAsync(CreateStateRequest request)
        {
            // Check if name already exists
            if (await _stateRepository.NameExistsAsync(request.Name))
                throw new BusinessException($"State with name '{request.Name}' already exists");

            var state = new Models.State.State
            {
                Name = request.Name
            };

            var createdState = await _stateRepository.CreateAsync(state);
            return MapToResponse(createdState);
        }

        public async Task<StateResponse> UpdateStateAsync(int id, UpdateStateRequest request)
        {
            var state = await _stateRepository.GetByIdAsync(id);
            if (state == null)
                throw new NotFoundException($"State with ID {id} not found");

            // Check name uniqueness
            if (await _stateRepository.NameExistsAsync(request.Name, id))
                throw new BusinessException($"State with name '{request.Name}' already exists");

            state.Name = request.Name;
            var updatedState = await _stateRepository.UpdateAsync(state);
            return MapToResponse(updatedState);
        }

        public async Task<bool> DeleteStateAsync(int id)
        {
            if (!await _stateRepository.ExistsAsync(id))
                throw new NotFoundException($"State with ID {id} not found");

            return await _stateRepository.DeleteAsync(id);
        }

        private StateResponse MapToResponse(Models.State.State state)
        {
            return new StateResponse
            {
                Id = state.Id,
                Name = state.Name,
                CreatedAt = state.CreatedAt
            };
        }
    }
}
