{"ast": null, "code": "import { scaleCorrectors } from '../../projection/styles/scale-correction.mjs';\nimport { transformProps } from '../../render/html/utils/transform.mjs';\nfunction isForcedMotionValue(key, {\n  layout,\n  layoutId\n}) {\n  return transformProps.has(key) || key.startsWith(\"origin\") || (layout || layoutId !== undefined) && (!!scaleCorrectors[key] || key === \"opacity\");\n}\nexport { isForcedMotionValue };", "map": {"version": 3, "names": ["scaleCorrectors", "transformProps", "isForcedMotionValue", "key", "layout", "layoutId", "has", "startsWith", "undefined"], "sources": ["C:/Users/<USER>/Downloads/Advanced/Advanced CRM filed all use/frontend/node_modules/framer-motion/dist/es/motion/utils/is-forced-motion-value.mjs"], "sourcesContent": ["import { scaleCorrectors } from '../../projection/styles/scale-correction.mjs';\nimport { transformProps } from '../../render/html/utils/transform.mjs';\n\nfunction isForcedMotionValue(key, { layout, layoutId }) {\n    return (transformProps.has(key) ||\n        key.startsWith(\"origin\") ||\n        ((layout || layoutId !== undefined) &&\n            (!!scaleCorrectors[key] || key === \"opacity\")));\n}\n\nexport { isForcedMotionValue };\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,8CAA8C;AAC9E,SAASC,cAAc,QAAQ,uCAAuC;AAEtE,SAASC,mBAAmBA,CAACC,GAAG,EAAE;EAAEC,MAAM;EAAEC;AAAS,CAAC,EAAE;EACpD,OAAQJ,cAAc,CAACK,GAAG,CAACH,GAAG,CAAC,IAC3BA,GAAG,CAACI,UAAU,CAAC,QAAQ,CAAC,IACvB,CAACH,MAAM,IAAIC,QAAQ,KAAKG,SAAS,MAC7B,CAAC,CAACR,eAAe,CAACG,GAAG,CAAC,IAAIA,GAAG,KAAK,SAAS,CAAE;AAC1D;AAEA,SAASD,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}