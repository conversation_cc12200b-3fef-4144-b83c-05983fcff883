namespace CrmApi.Models.ImportExport
{
    public class PersonImportResponse
    {
        public string JobId { get; set; } = string.Empty;
        public ImportStatus Status { get; set; }
        public DateTime StartedAt { get; set; }
        public DateTime? CompletedAt { get; set; }
        public int TotalRows { get; set; }
        public int ProcessedRows { get; set; }
        public int SuccessfulRows { get; set; }
        public int FailedRows { get; set; }
        public int SkippedRows { get; set; }
        public int UpdatedRows { get; set; }
        public List<ImportValidationError> Errors { get; set; } = new List<ImportValidationError>();
        public string? ErrorMessage { get; set; }
        public ImportSummary? Summary { get; set; }
    }

    public class ImportSummary
    {
        public int NewPersonsCreated { get; set; }
        public int ExistingPersonsUpdated { get; set; }
        public int DuplicatesSkipped { get; set; }
        public int ValidationFailures { get; set; }
        public Dictionary<string, int> ErrorsByType { get; set; } = new Dictionary<string, int>();
        public Dictionary<string, int> PersonsByDivision { get; set; } = new Dictionary<string, int>();
        public Dictionary<string, int> PersonsByCategory { get; set; } = new Dictionary<string, int>();
        public TimeSpan ProcessingTime { get; set; }
    }

    public enum ImportStatus
    {
        Pending = 1,
        Processing = 2,
        Completed = 3,
        Failed = 4,
        Cancelled = 5
    }
}
