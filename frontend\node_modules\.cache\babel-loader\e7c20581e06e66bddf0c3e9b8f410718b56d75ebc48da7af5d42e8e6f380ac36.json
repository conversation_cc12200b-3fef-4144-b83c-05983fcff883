{"ast": null, "code": "import { invariant } from './errors.mjs';\nimport { color } from '../value/types/color/index.mjs';\nimport { clamp } from './clamp.mjs';\nimport { mix } from './mix.mjs';\nimport { mixColor } from './mix-color.mjs';\nimport { mixComplex, mixArray, mixObject } from './mix-complex.mjs';\nimport { pipe } from './pipe.mjs';\nimport { progress } from './progress.mjs';\nimport { noop } from './noop.mjs';\nconst mixNumber = (from, to) => p => mix(from, to, p);\nfunction detectMixerFactory(v) {\n  if (typeof v === \"number\") {\n    return mixNumber;\n  } else if (typeof v === \"string\") {\n    return color.test(v) ? mixColor : mixComplex;\n  } else if (Array.isArray(v)) {\n    return mixArray;\n  } else if (typeof v === \"object\") {\n    return mixObject;\n  }\n  return mixNumber;\n}\nfunction createMixers(output, ease, customMixer) {\n  const mixers = [];\n  const mixerFactory = customMixer || detectMixerFactory(output[0]);\n  const numMixers = output.length - 1;\n  for (let i = 0; i < numMixers; i++) {\n    let mixer = mixerFactory(output[i], output[i + 1]);\n    if (ease) {\n      const easingFunction = Array.isArray(ease) ? ease[i] || noop : ease;\n      mixer = pipe(easingFunction, mixer);\n    }\n    mixers.push(mixer);\n  }\n  return mixers;\n}\n/**\n * Create a function that maps from a numerical input array to a generic output array.\n *\n * Accepts:\n *   - Numbers\n *   - Colors (hex, hsl, hsla, rgb, rgba)\n *   - Complex (combinations of one or more numbers or strings)\n *\n * ```jsx\n * const mixColor = interpolate([0, 1], ['#fff', '#000'])\n *\n * mixColor(0.5) // 'rgba(128, 128, 128, 1)'\n * ```\n *\n * TODO Revist this approach once we've moved to data models for values,\n * probably not needed to pregenerate mixer functions.\n *\n * @public\n */\nfunction interpolate(input, output, {\n  clamp: isClamp = true,\n  ease,\n  mixer\n} = {}) {\n  const inputLength = input.length;\n  invariant(inputLength === output.length, \"Both input and output ranges must be the same length\");\n  /**\n   * If we're only provided a single input, we can just make a function\n   * that returns the output.\n   */\n  if (inputLength === 1) return () => output[0];\n  // If input runs highest -> lowest, reverse both arrays\n  if (input[0] > input[inputLength - 1]) {\n    input = [...input].reverse();\n    output = [...output].reverse();\n  }\n  const mixers = createMixers(output, ease, mixer);\n  const numMixers = mixers.length;\n  const interpolator = v => {\n    let i = 0;\n    if (numMixers > 1) {\n      for (; i < input.length - 2; i++) {\n        if (v < input[i + 1]) break;\n      }\n    }\n    const progressInRange = progress(input[i], input[i + 1], v);\n    return mixers[i](progressInRange);\n  };\n  return isClamp ? v => interpolator(clamp(input[0], input[inputLength - 1], v)) : interpolator;\n}\nexport { interpolate };", "map": {"version": 3, "names": ["invariant", "color", "clamp", "mix", "mixColor", "mixComplex", "mixArray", "mixObject", "pipe", "progress", "noop", "mixNumber", "from", "to", "p", "detectMixerFactory", "v", "test", "Array", "isArray", "createMixers", "output", "ease", "customMixer", "mixers", "mixerFactory", "numMixers", "length", "i", "mixer", "easingFunction", "push", "interpolate", "input", "isClamp", "inputLength", "reverse", "interpolator", "progressInRange"], "sources": ["C:/Users/<USER>/Downloads/Advanced/Advanced CRM filed all use/frontend/node_modules/framer-motion/dist/es/utils/interpolate.mjs"], "sourcesContent": ["import { invariant } from './errors.mjs';\nimport { color } from '../value/types/color/index.mjs';\nimport { clamp } from './clamp.mjs';\nimport { mix } from './mix.mjs';\nimport { mixColor } from './mix-color.mjs';\nimport { mixComplex, mixArray, mixObject } from './mix-complex.mjs';\nimport { pipe } from './pipe.mjs';\nimport { progress } from './progress.mjs';\nimport { noop } from './noop.mjs';\n\nconst mixNumber = (from, to) => (p) => mix(from, to, p);\nfunction detectMixerFactory(v) {\n    if (typeof v === \"number\") {\n        return mixNumber;\n    }\n    else if (typeof v === \"string\") {\n        return color.test(v) ? mixColor : mixComplex;\n    }\n    else if (Array.isArray(v)) {\n        return mixArray;\n    }\n    else if (typeof v === \"object\") {\n        return mixObject;\n    }\n    return mixNumber;\n}\nfunction createMixers(output, ease, customMixer) {\n    const mixers = [];\n    const mixerFactory = customMixer || detectMixerFactory(output[0]);\n    const numMixers = output.length - 1;\n    for (let i = 0; i < numMixers; i++) {\n        let mixer = mixerFactory(output[i], output[i + 1]);\n        if (ease) {\n            const easingFunction = Array.isArray(ease) ? ease[i] || noop : ease;\n            mixer = pipe(easingFunction, mixer);\n        }\n        mixers.push(mixer);\n    }\n    return mixers;\n}\n/**\n * Create a function that maps from a numerical input array to a generic output array.\n *\n * Accepts:\n *   - Numbers\n *   - Colors (hex, hsl, hsla, rgb, rgba)\n *   - Complex (combinations of one or more numbers or strings)\n *\n * ```jsx\n * const mixColor = interpolate([0, 1], ['#fff', '#000'])\n *\n * mixColor(0.5) // 'rgba(128, 128, 128, 1)'\n * ```\n *\n * TODO Revist this approach once we've moved to data models for values,\n * probably not needed to pregenerate mixer functions.\n *\n * @public\n */\nfunction interpolate(input, output, { clamp: isClamp = true, ease, mixer } = {}) {\n    const inputLength = input.length;\n    invariant(inputLength === output.length, \"Both input and output ranges must be the same length\");\n    /**\n     * If we're only provided a single input, we can just make a function\n     * that returns the output.\n     */\n    if (inputLength === 1)\n        return () => output[0];\n    // If input runs highest -> lowest, reverse both arrays\n    if (input[0] > input[inputLength - 1]) {\n        input = [...input].reverse();\n        output = [...output].reverse();\n    }\n    const mixers = createMixers(output, ease, mixer);\n    const numMixers = mixers.length;\n    const interpolator = (v) => {\n        let i = 0;\n        if (numMixers > 1) {\n            for (; i < input.length - 2; i++) {\n                if (v < input[i + 1])\n                    break;\n            }\n        }\n        const progressInRange = progress(input[i], input[i + 1], v);\n        return mixers[i](progressInRange);\n    };\n    return isClamp\n        ? (v) => interpolator(clamp(input[0], input[inputLength - 1], v))\n        : interpolator;\n}\n\nexport { interpolate };\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,cAAc;AACxC,SAASC,KAAK,QAAQ,gCAAgC;AACtD,SAASC,KAAK,QAAQ,aAAa;AACnC,SAASC,GAAG,QAAQ,WAAW;AAC/B,SAASC,QAAQ,QAAQ,iBAAiB;AAC1C,SAASC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,mBAAmB;AACnE,SAASC,IAAI,QAAQ,YAAY;AACjC,SAASC,QAAQ,QAAQ,gBAAgB;AACzC,SAASC,IAAI,QAAQ,YAAY;AAEjC,MAAMC,SAAS,GAAGA,CAACC,IAAI,EAAEC,EAAE,KAAMC,CAAC,IAAKX,GAAG,CAACS,IAAI,EAAEC,EAAE,EAAEC,CAAC,CAAC;AACvD,SAASC,kBAAkBA,CAACC,CAAC,EAAE;EAC3B,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE;IACvB,OAAOL,SAAS;EACpB,CAAC,MACI,IAAI,OAAOK,CAAC,KAAK,QAAQ,EAAE;IAC5B,OAAOf,KAAK,CAACgB,IAAI,CAACD,CAAC,CAAC,GAAGZ,QAAQ,GAAGC,UAAU;EAChD,CAAC,MACI,IAAIa,KAAK,CAACC,OAAO,CAACH,CAAC,CAAC,EAAE;IACvB,OAAOV,QAAQ;EACnB,CAAC,MACI,IAAI,OAAOU,CAAC,KAAK,QAAQ,EAAE;IAC5B,OAAOT,SAAS;EACpB;EACA,OAAOI,SAAS;AACpB;AACA,SAASS,YAAYA,CAACC,MAAM,EAAEC,IAAI,EAAEC,WAAW,EAAE;EAC7C,MAAMC,MAAM,GAAG,EAAE;EACjB,MAAMC,YAAY,GAAGF,WAAW,IAAIR,kBAAkB,CAACM,MAAM,CAAC,CAAC,CAAC,CAAC;EACjE,MAAMK,SAAS,GAAGL,MAAM,CAACM,MAAM,GAAG,CAAC;EACnC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,SAAS,EAAEE,CAAC,EAAE,EAAE;IAChC,IAAIC,KAAK,GAAGJ,YAAY,CAACJ,MAAM,CAACO,CAAC,CAAC,EAAEP,MAAM,CAACO,CAAC,GAAG,CAAC,CAAC,CAAC;IAClD,IAAIN,IAAI,EAAE;MACN,MAAMQ,cAAc,GAAGZ,KAAK,CAACC,OAAO,CAACG,IAAI,CAAC,GAAGA,IAAI,CAACM,CAAC,CAAC,IAAIlB,IAAI,GAAGY,IAAI;MACnEO,KAAK,GAAGrB,IAAI,CAACsB,cAAc,EAAED,KAAK,CAAC;IACvC;IACAL,MAAM,CAACO,IAAI,CAACF,KAAK,CAAC;EACtB;EACA,OAAOL,MAAM;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASQ,WAAWA,CAACC,KAAK,EAAEZ,MAAM,EAAE;EAAEnB,KAAK,EAAEgC,OAAO,GAAG,IAAI;EAAEZ,IAAI;EAAEO;AAAM,CAAC,GAAG,CAAC,CAAC,EAAE;EAC7E,MAAMM,WAAW,GAAGF,KAAK,CAACN,MAAM;EAChC3B,SAAS,CAACmC,WAAW,KAAKd,MAAM,CAACM,MAAM,EAAE,sDAAsD,CAAC;EAChG;AACJ;AACA;AACA;EACI,IAAIQ,WAAW,KAAK,CAAC,EACjB,OAAO,MAAMd,MAAM,CAAC,CAAC,CAAC;EAC1B;EACA,IAAIY,KAAK,CAAC,CAAC,CAAC,GAAGA,KAAK,CAACE,WAAW,GAAG,CAAC,CAAC,EAAE;IACnCF,KAAK,GAAG,CAAC,GAAGA,KAAK,CAAC,CAACG,OAAO,CAAC,CAAC;IAC5Bf,MAAM,GAAG,CAAC,GAAGA,MAAM,CAAC,CAACe,OAAO,CAAC,CAAC;EAClC;EACA,MAAMZ,MAAM,GAAGJ,YAAY,CAACC,MAAM,EAAEC,IAAI,EAAEO,KAAK,CAAC;EAChD,MAAMH,SAAS,GAAGF,MAAM,CAACG,MAAM;EAC/B,MAAMU,YAAY,GAAIrB,CAAC,IAAK;IACxB,IAAIY,CAAC,GAAG,CAAC;IACT,IAAIF,SAAS,GAAG,CAAC,EAAE;MACf,OAAOE,CAAC,GAAGK,KAAK,CAACN,MAAM,GAAG,CAAC,EAAEC,CAAC,EAAE,EAAE;QAC9B,IAAIZ,CAAC,GAAGiB,KAAK,CAACL,CAAC,GAAG,CAAC,CAAC,EAChB;MACR;IACJ;IACA,MAAMU,eAAe,GAAG7B,QAAQ,CAACwB,KAAK,CAACL,CAAC,CAAC,EAAEK,KAAK,CAACL,CAAC,GAAG,CAAC,CAAC,EAAEZ,CAAC,CAAC;IAC3D,OAAOQ,MAAM,CAACI,CAAC,CAAC,CAACU,eAAe,CAAC;EACrC,CAAC;EACD,OAAOJ,OAAO,GACPlB,CAAC,IAAKqB,YAAY,CAACnC,KAAK,CAAC+B,KAAK,CAAC,CAAC,CAAC,EAAEA,KAAK,CAACE,WAAW,GAAG,CAAC,CAAC,EAAEnB,CAAC,CAAC,CAAC,GAC/DqB,YAAY;AACtB;AAEA,SAASL,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}