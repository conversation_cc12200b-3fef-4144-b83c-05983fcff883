import React, { useState } from 'react';
import './ImportResults.css';

const ImportResults = ({ results, onNewImport, onClose }) => {
  const [showErrors, setShowErrors] = useState(false);
  const [errorFilter, setErrorFilter] = useState('all');

  if (!results) {
    return (
      <div className="import-results">
        <div className="results-loading">
          <p>Loading results...</p>
        </div>
      </div>
    );
  }

  const isSuccess = results.status === 'Completed';
  const hasErrors = results.errors && results.errors.length > 0;
  const processingTime = results.summary?.processingTime || 0;

  const formatDuration = (duration) => {
    if (!duration) return 'N/A';
    
    // Parse ISO 8601 duration or milliseconds
    let totalSeconds;
    if (typeof duration === 'string' && duration.includes(':')) {
      const parts = duration.split(':');
      totalSeconds = parseInt(parts[0]) * 3600 + parseInt(parts[1]) * 60 + parseFloat(parts[2]);
    } else {
      totalSeconds = parseFloat(duration) / 1000;
    }
    
    if (totalSeconds < 60) return `${totalSeconds.toFixed(1)}s`;
    const minutes = Math.floor(totalSeconds / 60);
    const seconds = (totalSeconds % 60).toFixed(1);
    return `${minutes}m ${seconds}s`;
  };

  const downloadErrorReport = () => {
    if (!hasErrors) return;

    const csvContent = [
      ['Row', 'Field', 'Error', 'Value', 'Severity'].join(','),
      ...results.errors.map(error => [
        error.rowNumber,
        error.fieldName || '',
        `"${error.errorMessage || ''}"`,
        `"${error.fieldValue || ''}"`,
        error.severity || 'Error'
      ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `import_errors_${results.jobId}.csv`;
    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);
  };

  const getFilteredErrors = () => {
    if (!results.errors) return [];
    
    switch (errorFilter) {
      case 'errors':
        return results.errors.filter(e => e.severity === 'Error');
      case 'warnings':
        return results.errors.filter(e => e.severity === 'Warning');
      default:
        return results.errors;
    }
  };

  const groupErrorsByType = (errors) => {
    const groups = {};
    errors.forEach(error => {
      const key = error.errorCode || error.errorMessage || 'Unknown';
      if (!groups[key]) {
        groups[key] = {
          type: key,
          count: 0,
          examples: []
        };
      }
      groups[key].count++;
      if (groups[key].examples.length < 3) {
        groups[key].examples.push(error);
      }
    });
    return Object.values(groups).sort((a, b) => b.count - a.count);
  };

  const errorGroups = groupErrorsByType(getFilteredErrors());

  return (
    <div className="import-results">
      <div className="results-header">
        <div className={`status-indicator ${isSuccess ? 'success' : 'error'}`}>
          <div className="status-icon">
            {isSuccess ? '✅' : '❌'}
          </div>
          <div className="status-content">
            <h3>Import {results.status}</h3>
            <p>Job ID: {results.jobId}</p>
          </div>
        </div>
      </div>

      {/* Summary Statistics */}
      <div className="results-summary">
        <h4>Import Summary</h4>
        <div className="summary-grid">
          <div className="summary-card total">
            <div className="card-icon">📊</div>
            <div className="card-content">
              <div className="card-value">{results.totalRows?.toLocaleString() || 0}</div>
              <div className="card-label">Total Rows</div>
            </div>
          </div>

          <div className="summary-card success">
            <div className="card-icon">✅</div>
            <div className="card-content">
              <div className="card-value">{results.successfulRows?.toLocaleString() || 0}</div>
              <div className="card-label">Successful</div>
            </div>
          </div>

          <div className="summary-card error">
            <div className="card-icon">❌</div>
            <div className="card-content">
              <div className="card-value">{results.failedRows?.toLocaleString() || 0}</div>
              <div className="card-label">Failed</div>
            </div>
          </div>

          <div className="summary-card warning">
            <div className="card-icon">⏭️</div>
            <div className="card-content">
              <div className="card-value">{results.skippedRows?.toLocaleString() || 0}</div>
              <div className="card-label">Skipped</div>
            </div>
          </div>

          {results.updatedRows > 0 && (
            <div className="summary-card info">
              <div className="card-icon">🔄</div>
              <div className="card-content">
                <div className="card-value">{results.updatedRows?.toLocaleString() || 0}</div>
                <div className="card-label">Updated</div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Processing Details */}
      <div className="processing-details">
        <h4>Processing Details</h4>
        <div className="details-grid">
          <div className="detail-item">
            <span className="detail-label">Processing Time:</span>
            <span className="detail-value">{formatDuration(processingTime)}</span>
          </div>
          <div className="detail-item">
            <span className="detail-label">Started At:</span>
            <span className="detail-value">
              {results.startedAt ? new Date(results.startedAt).toLocaleString() : 'N/A'}
            </span>
          </div>
          <div className="detail-item">
            <span className="detail-label">Completed At:</span>
            <span className="detail-value">
              {results.completedAt ? new Date(results.completedAt).toLocaleString() : 'N/A'}
            </span>
          </div>
          <div className="detail-item">
            <span className="detail-label">Success Rate:</span>
            <span className="detail-value">
              {results.totalRows > 0 
                ? `${Math.round((results.successfulRows / results.totalRows) * 100)}%`
                : '0%'
              }
            </span>
          </div>
        </div>
      </div>

      {/* Detailed Summary */}
      {results.summary && (
        <div className="detailed-summary">
          <h4>Detailed Summary</h4>
          <div className="summary-details">
            <div className="summary-row">
              <span>New Persons Created:</span>
              <span>{results.summary.newPersonsCreated?.toLocaleString() || 0}</span>
            </div>
            <div className="summary-row">
              <span>Existing Persons Updated:</span>
              <span>{results.summary.existingPersonsUpdated?.toLocaleString() || 0}</span>
            </div>
            <div className="summary-row">
              <span>Duplicates Skipped:</span>
              <span>{results.summary.duplicatesSkipped?.toLocaleString() || 0}</span>
            </div>
            <div className="summary-row">
              <span>Validation Failures:</span>
              <span>{results.summary.validationFailures?.toLocaleString() || 0}</span>
            </div>
          </div>
        </div>
      )}

      {/* Error Analysis */}
      {hasErrors && (
        <div className="error-analysis">
          <div className="error-header">
            <h4>Error Analysis ({results.errors.length} errors)</h4>
            <div className="error-actions">
              <select
                value={errorFilter}
                onChange={(e) => setErrorFilter(e.target.value)}
                className="error-filter"
              >
                <option value="all">All Issues</option>
                <option value="errors">Errors Only</option>
                <option value="warnings">Warnings Only</option>
              </select>
              <button onClick={downloadErrorReport} className="btn btn-outline">
                📥 Download Error Report
              </button>
              <button 
                onClick={() => setShowErrors(!showErrors)}
                className="btn btn-outline"
              >
                {showErrors ? 'Hide Details' : 'Show Details'}
              </button>
            </div>
          </div>

          {/* Error Groups */}
          <div className="error-groups">
            {errorGroups.map((group, index) => (
              <div key={index} className="error-group">
                <div className="group-header">
                  <span className="group-type">{group.type}</span>
                  <span className="group-count">{group.count} occurrences</span>
                </div>
                {showErrors && (
                  <div className="group-examples">
                    {group.examples.map((error, idx) => (
                      <div key={idx} className="error-example">
                        <span className="error-row">Row {error.rowNumber}</span>
                        <span className="error-field">{error.fieldName}</span>
                        <span className="error-message">{error.errorMessage}</span>
                        {error.fieldValue && (
                          <span className="error-value">"{error.fieldValue}"</span>
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Recommendations */}
      {!isSuccess && (
        <div className="recommendations">
          <h4>💡 Recommendations</h4>
          <ul>
            {results.failedRows > 0 && (
              <li>Review the error report to identify common validation issues</li>
            )}
            {results.errors?.some(e => e.errorCode === 'INVALID_FORMAT') && (
              <li>Check data formats, especially for mobile numbers and email addresses</li>
            )}
            {results.errors?.some(e => e.errorCode === 'REQUIRED_FIELD') && (
              <li>Ensure all required fields (Division, Category, Name, Mobile) are provided</li>
            )}
            {results.errors?.some(e => e.errorCode === 'NOT_FOUND') && (
              <li>Verify that Division and Category names match existing records</li>
            )}
            <li>Consider using the "Validate Only" option to check data before importing</li>
          </ul>
        </div>
      )}

      {/* Actions */}
      <div className="results-actions">
        <button onClick={onNewImport} className="btn btn-primary">
          🔄 Import Another File
        </button>
        <button onClick={onClose} className="btn btn-outline">
          ✅ Done
        </button>
      </div>
    </div>
  );
};

export default ImportResults;
