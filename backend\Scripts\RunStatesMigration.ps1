# PowerShell script to run the States migration
# This script creates the States table and inserts all Indian states

Write-Host "Starting States Migration..." -ForegroundColor Green

# Change to the backend directory
$backendPath = Split-Path -Parent $PSScriptRoot
Set-Location $backendPath

Write-Host "Current directory: $(Get-Location)" -ForegroundColor Yellow

# Check if Entity Framework tools are installed
try {
    $efVersion = dotnet ef --version
    Write-Host "Entity Framework Core tools version: $efVersion" -ForegroundColor Green
} catch {
    Write-Host "Entity Framework Core tools not found. Installing..." -ForegroundColor Yellow
    dotnet tool install --global dotnet-ef
}

# Build the project first
Write-Host "Building the project..." -ForegroundColor Yellow
dotnet build

if ($LASTEXITCODE -ne 0) {
    Write-Host "Build failed. Please fix compilation errors first." -ForegroundColor Red
    exit 1
}

# Check current migrations
Write-Host "Checking current migrations..." -ForegroundColor Yellow
dotnet ef migrations list

# Add the migration if it doesn't exist
Write-Host "Adding States migration..." -ForegroundColor Yellow
dotnet ef migrations add CreateStatesTable --output-dir Migrations

# Update the database
Write-Host "Updating database with States migration..." -ForegroundColor Yellow
dotnet ef database update

if ($LASTEXITCODE -eq 0) {
    Write-Host "States migration completed successfully!" -ForegroundColor Green
    Write-Host "The following has been created:" -ForegroundColor Cyan
    Write-Host "- States table with proper schema" -ForegroundColor White
    Write-Host "- All 28 Indian states inserted" -ForegroundColor White
    Write-Host "- All 8 Union Territories inserted" -ForegroundColor White
    Write-Host "- Proper indexes for performance" -ForegroundColor White
    Write-Host "- Regional categorization included" -ForegroundColor White
} else {
    Write-Host "Migration failed. Please check the error messages above." -ForegroundColor Red
    exit 1
}

Write-Host "Migration script completed." -ForegroundColor Green
