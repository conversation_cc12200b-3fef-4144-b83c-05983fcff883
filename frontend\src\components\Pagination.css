/* Single Row Pagination Component Styles */
.pagination-wrapper-single {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 1rem 0;
  margin: 1rem 0;
}

.pagination-container {
  display: flex;
  align-items: center;
  gap: 1rem;
  background: #ffffff;
  padding: 0.75rem 1.5rem;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  border: 1px solid #e9ecef;
}

.pagination-info {
  display: flex;
  align-items: center;
}

.entries-text {
  font-size: 0.875rem;
  color: #6c757d;
  font-weight: 500;
  white-space: nowrap;
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.pagination-pages {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  margin: 0 0.5rem;
}

.pagination-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 36px;
  height: 36px;
  padding: 0.5rem;
  border: 1px solid #dee2e6;
  background: #ffffff;
  color: #495057;
  font-size: 0.875rem;
  font-weight: 500;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  user-select: none;
  text-decoration: none;
}

.pagination-btn:hover:not(.disabled) {
  background: #f8f9fa;
  border-color: #adb5bd;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.pagination-btn:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(13, 110, 253, 0.25);
}

.pagination-btn.active {
  background: linear-gradient(135deg, #0d6efd 0%, #0a58ca 100%);
  border-color: #0a58ca;
  color: #ffffff;
  font-weight: 600;
  box-shadow: 0 2px 6px rgba(13, 110, 253, 0.3);
  transform: translateY(-1px);
}

.pagination-btn.disabled {
  background: #f8f9fa;
  border-color: #e9ecef;
  color: #adb5bd;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.pagination-btn.dots {
  border: none;
  background: transparent;
  cursor: default;
  color: #adb5bd;
}

.pagination-btn.dots:hover {
  background: transparent;
  transform: none;
  box-shadow: none;
}

.pagination-prev,
.pagination-next {
  font-size: 1.25rem;
  font-weight: 600;
  min-width: 40px;
}

.pagination-prev {
  margin-right: 0.25rem;
}

.pagination-next {
  margin-left: 0.25rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .pagination-container {
    flex-direction: column;
    gap: 0.75rem;
    padding: 1rem;
  }
  
  .pagination-info {
    order: 2;
  }
  
  .pagination-controls {
    order: 1;
  }
  
  .entries-text {
    font-size: 0.75rem;
  }
  
  .pagination-btn {
    min-width: 32px;
    height: 32px;
    font-size: 0.75rem;
  }
  
  .pagination-prev,
  .pagination-next {
    font-size: 1rem;
    min-width: 36px;
  }
}

@media (max-width: 480px) {
  .pagination-wrapper-single {
    padding: 0.5rem 0;
    margin: 0.5rem 0;
  }
  
  .pagination-container {
    padding: 0.75rem;
    gap: 0.5rem;
  }
  
  .pagination-controls {
    gap: 0.125rem;
  }
  
  .pagination-pages {
    gap: 0.125rem;
    margin: 0 0.25rem;
  }
  
  .pagination-btn {
    min-width: 28px;
    height: 28px;
    font-size: 0.75rem;
  }
}

/* Animation */
.pagination-btn.active {
  animation: activePagePulse 0.3s ease-out;
}

@keyframes activePagePulse {
  0% {
    transform: translateY(-1px) scale(1);
  }
  50% {
    transform: translateY(-2px) scale(1.05);
  }
  100% {
    transform: translateY(-1px) scale(1);
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .pagination-container {
    background: #2d3748;
    border-color: #4a5568;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  }
  
  .entries-text {
    color: #a0aec0;
  }
  
  .pagination-btn {
    background: #4a5568;
    border-color: #718096;
    color: #e2e8f0;
  }
  
  .pagination-btn:hover:not(.disabled) {
    background: #718096;
    border-color: #a0aec0;
  }
  
  .pagination-btn.disabled {
    background: #2d3748;
    border-color: #4a5568;
    color: #718096;
  }
}