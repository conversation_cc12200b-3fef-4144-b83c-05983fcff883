# Troubleshooting: API 404 Error Fix

## Problem
When uploading an Excel file, you encounter:
```
POST http://localhost:3000/api/import-export/persons/import 404 (Not Found)
Import error: SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON
```

## Root Cause
The frontend was making API requests to the wrong URL. The requests were going to the frontend development server (`localhost:3000`) instead of the backend API server (`localhost:5000`).

## Solution Applied

### 1. Fixed API URL Configuration
- Updated all `fetch` calls in import components to use `apiService.baseURL`
- The `apiService` is configured to use `http://localhost:5000/api` as the base URL

### 2. Added Proxy Configuration
Added proxy configuration to `frontend/package.json`:
```json
{
  "proxy": "http://localhost:5000"
}
```

This tells the React development server to proxy API requests to the backend server.

### 3. Created Environment Configuration
Created `frontend/.env` file:
```
REACT_APP_API_URL=http://localhost:5000/api
```

## Files Modified

### Frontend Files:
1. **frontend/src/components/import/ImportPersons.js**
   - Fixed fetch URLs to use `apiService.baseURL`
   - Updated import, progress polling, and cancel endpoints

2. **frontend/src/components/import/FileUpload.js**
   - Added `apiService` import
   - Fixed template download endpoint

3. **frontend/package.json**
   - Added proxy configuration

4. **frontend/.env** (new file)
   - Added API URL configuration

## How to Test the Fix

### 1. Ensure Backend is Running
Make sure your backend server is running on `http://localhost:5000`:
```bash
cd backend
dotnet run
```

### 2. Restart Frontend Development Server
After adding the proxy configuration, restart the frontend:
```bash
cd frontend
npm start
```

### 3. Test the Import Process
1. Navigate to the import section
2. Select division and category
3. Upload a valid Excel file
4. Complete field mapping
5. Verify the import proceeds without 404 errors

## Expected Behavior After Fix

### 1. API Requests
- All API requests should now go to `http://localhost:5000/api/...`
- No more 404 errors from `localhost:3000`

### 2. Import Process
- Division/category selection works
- File upload processes correctly
- Field mapping validates properly
- Import progress tracking functions
- Results display correctly

## Additional Troubleshooting

### If Backend is Not Running
- Start the backend server: `dotnet run` in the backend directory
- Verify it's accessible at `http://localhost:5000/api`

### If Different Port is Used
- Update the proxy in `package.json`
- Update `REACT_APP_API_URL` in `.env`
- Restart the frontend development server

### If CORS Issues Occur
- Ensure backend CORS configuration allows `localhost:3000`
- Check backend startup logs for CORS configuration

### Network Tab Debugging
1. Open browser Developer Tools
2. Go to Network tab
3. Attempt the import
4. Verify requests go to `localhost:5000` not `localhost:3000`

## Prevention

### For Future Development
1. Always use the `apiService` for API calls instead of direct `fetch`
2. Configure environment variables for different environments
3. Use proxy configuration for development
4. Test API connectivity before implementing new features

### Code Review Checklist
- [ ] All API calls use `apiService` or proper base URL
- [ ] No hardcoded `localhost:3000` in API calls
- [ ] Environment variables used for configuration
- [ ] Proxy configuration present for development

This fix ensures that the Excel import functionality works correctly by routing API requests to the proper backend server.
