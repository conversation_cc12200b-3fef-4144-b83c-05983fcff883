import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { motion } from 'framer-motion';
import { FiHome, FiLayers, FiLogOut, FiUser, FiBarChart2, FiGrid, FiUsers, FiUpload, FiSettings } from 'react-icons/fi';
import { useAuth } from '../context/AuthContext';

const Navbar = () => {
  const location = useLocation();
  const { user, logout } = useAuth();

  const menuItems = [
    { path: '/', label: 'Dashboard', icon: FiBarChart2 },
    { path: '/divisions', label: 'Division Setup', icon: FiGrid },
    { path: '/categories', label: 'Categories', icon: FiLayers },
    { path: '/persons-view', label: 'View Persons', icon: FiUsers },
    { path: '/persons', label: 'Person Management', icon: FiUser },
    { path: '/import', label: 'Import Persons', icon: FiUpload },
    { path: '/form-builder', label: 'Form Builder', icon: FiSettings }
  ];

  const handleLogout = () => {
    logout();
  };

  return (
    <motion.div 
      className="admin-sidebar"
      initial={{ x: -250 }}
      animate={{ x: 0 }}
      transition={{ duration: 0.3 }}
    >
      <div className="sidebar-header">
        <div className="admin-profile">
          <div className="profile-avatar">
            <FiUser size={24} />
          </div>
          <div className="profile-info">
            <h3>Admin Panel</h3>
            <p>{user?.firstName} {user?.lastName}</p>
          </div>
        </div>
      </div>

      <nav className="sidebar-nav">
        {menuItems.map((item, index) => {
          const Icon = item.icon;
          const isActive = location.pathname === item.path;
          
          return (
            <motion.div
              key={item.path}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <Link 
                to={item.path} 
                className={`sidebar-link ${isActive ? 'active' : ''}`}
              >
                <Icon className="sidebar-icon" />
                <span>{item.label}</span>
              </Link>
            </motion.div>
          );
        })}
      </nav>

      <div className="sidebar-footer">
        <motion.button
          className="logout-btn"
          onClick={handleLogout}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          <FiLogOut className="sidebar-icon" />
          <span>Logout</span>
        </motion.button>
      </div>
    </motion.div>
  );
};

export default Navbar;