.division-category-selection {
  max-width: 800px;
  margin: 0 auto;
  padding: 24px;
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.selection-header {
  text-align: center;
  margin-bottom: 32px;
}

.selection-header h3 {
  color: #1a1a1a;
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 8px;
}

.selection-description {
  color: #666;
  font-size: 16px;
  line-height: 1.5;
  max-width: 600px;
  margin: 0 auto;
}

.error-message {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 8px;
  color: #dc2626;
  margin-bottom: 24px;
}

.error-icon {
  font-size: 18px;
}

.selection-form {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.selection-section {
  background: #f8fafc;
  padding: 24px;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.selection-section h4 {
  color: #1a1a1a;
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 16px;
}

.required-notice {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: #fef3c7;
  border: 1px solid #fbbf24;
  border-radius: 6px;
  margin-bottom: 24px;
  font-size: 14px;
  color: #92400e;
}

.required-icon {
  color: #dc2626;
  font-weight: bold;
  font-size: 16px;
}

.selection-info {
  background: #f0f9ff;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #bae6fd;
}

.selection-info h4 {
  color: #0369a1;
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 12px;
}

.selection-info ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.selection-info li {
  padding: 6px 0;
  color: #0369a1;
  position: relative;
  padding-left: 20px;
}

.selection-info li::before {
  content: "•";
  color: #0369a1;
  font-weight: bold;
  position: absolute;
  left: 0;
}

.selection-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 24px;
  border-top: 1px solid #e2e8f0;
}

.btn {
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-secondary {
  background: #f1f5f9;
  color: #475569;
  border: 1px solid #cbd5e1;
}

.btn-secondary:hover:not(:disabled) {
  background: #e2e8f0;
  border-color: #94a3b8;
}

.btn-primary {
  background: #3b82f6;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #2563eb;
}

.btn-primary:disabled {
  background: #94a3b8;
}

.selection-summary {
  margin-top: 24px;
  padding: 20px;
  background: #f0fdf4;
  border: 1px solid #bbf7d0;
  border-radius: 8px;
}

.selection-summary h4 {
  color: #166534;
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 12px;
}

.summary-item {
  padding: 4px 0;
  color: #166534;
}

.summary-item strong {
  font-weight: 600;
  margin-right: 8px;
}

/* Responsive design */
@media (max-width: 768px) {
  .division-category-selection {
    padding: 16px;
    margin: 16px;
  }
  
  .selection-header h3 {
    font-size: 20px;
  }
  
  .selection-description {
    font-size: 14px;
  }
  
  .selection-section {
    padding: 16px;
  }
  
  .selection-actions {
    flex-direction: column;
    gap: 12px;
  }
  
  .btn {
    width: 100%;
    justify-content: center;
  }
}

/* Animation for smooth transitions */
.selection-summary {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
