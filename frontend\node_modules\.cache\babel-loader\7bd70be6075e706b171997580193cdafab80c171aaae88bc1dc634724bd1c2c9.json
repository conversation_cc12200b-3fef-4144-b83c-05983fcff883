{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Advanced\\\\Advanced CRM filed all use\\\\frontend\\\\src\\\\components\\\\forms\\\\DynamicPersonForm.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { PersonNatureLabels, GenderLabels, WorkingProfileLabels, getDefaultPersonData } from '../../constants/personConstants';\nimport formConfigService from '../../services/formConfigService';\nimport apiService from '../../services/apiService';\nimport FormField from './FormField';\nimport './DynamicPersonForm.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DynamicPersonForm = ({\n  onSubmit,\n  onCancel,\n  initialData = null,\n  mode = 'create'\n}) => {\n  _s();\n  // Hierarchy state\n  const [divisions, setDivisions] = useState([]);\n  const [categories, setCategories] = useState([]);\n  const [subCategories, setSubCategories] = useState([]);\n\n  // Selection state\n  const [selectedDivision, setSelectedDivision] = useState('');\n  const [selectedCategory, setSelectedCategory] = useState('');\n  const [selectedSubCategory, setSelectedSubCategory] = useState('');\n\n  // Form state\n  const [formConfig, setFormConfig] = useState(null);\n  const [formData, setFormData] = useState(getDefaultPersonData());\n  const [errors, setErrors] = useState({});\n\n  // Loading states\n  const [loading, setLoading] = useState({\n    divisions: false,\n    categories: false,\n    subCategories: false,\n    form: false\n  });\n  const [submitting, setSubmitting] = useState(false);\n\n  // Form availability state\n  const [formAvailability, setFormAvailability] = useState({\n    categoryHasForm: false,\n    subCategoryHasForm: false,\n    showSubCategoryDropdown: false,\n    message: ''\n  });\n\n  // Load divisions on component mount\n  useEffect(() => {\n    loadDivisions();\n\n    // Test API connectivity\n    testApiConnectivity();\n\n    // Load initial data if provided\n    if (initialData) {\n      var _initialData$division, _initialData$category, _initialData$subCateg;\n      setFormData(initialData);\n      setSelectedDivision(((_initialData$division = initialData.divisionId) === null || _initialData$division === void 0 ? void 0 : _initialData$division.toString()) || '');\n      setSelectedCategory(((_initialData$category = initialData.categoryId) === null || _initialData$category === void 0 ? void 0 : _initialData$category.toString()) || '');\n      setSelectedSubCategory(((_initialData$subCateg = initialData.subCategoryId) === null || _initialData$subCateg === void 0 ? void 0 : _initialData$subCateg.toString()) || '');\n    } else {\n      // Load default form for new person creation\n      const defaultForm = formConfigService.getDefaultFormConfig();\n      // Ensure no duplicate fields in default form\n      if (defaultForm.fields) {\n        defaultForm.fields = deduplicateFields(defaultForm.fields);\n      }\n      setFormConfig(defaultForm);\n    }\n  }, [initialData]);\n\n  // Test API connectivity\n  const testApiConnectivity = async () => {\n    try {\n      console.log('Testing API connectivity...');\n\n      // Test basic connectivity\n      const baseUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';\n      console.log('API Base URL:', baseUrl);\n\n      // Test divisions endpoint\n      const response = await apiService.getDivisions();\n      console.log('API test successful:', response);\n\n      // Test a simple GET request to see if server is responding\n      const testResponse = await fetch(`${baseUrl}/divisions`);\n      console.log('Direct fetch test - Status:', testResponse.status);\n      console.log('Direct fetch test - OK:', testResponse.ok);\n    } catch (error) {\n      console.error('API connectivity test failed:', error);\n      console.error('Error details:', {\n        message: error.message,\n        status: error.status,\n        data: error.data\n      });\n    }\n  };\n\n  // Deduplicate fields based on field key\n  const deduplicateFields = fields => {\n    const seen = new Set();\n    const deduplicated = [];\n    fields.forEach(field => {\n      if (!seen.has(field.key)) {\n        seen.add(field.key);\n        deduplicated.push(field);\n      } else {\n        console.warn(`DynamicPersonForm: Removing duplicate field: ${field.key}`);\n      }\n    });\n    if (fields.length !== deduplicated.length) {\n      console.log(`DynamicPersonForm: Deduplicated ${fields.length} fields to ${deduplicated.length} unique fields`);\n    }\n    return deduplicated;\n  };\n\n  // Load categories when division changes\n  useEffect(() => {\n    if (selectedDivision) {\n      loadCategories(selectedDivision);\n      // Reset category and subcategory when division changes\n      setSelectedCategory('');\n      setSelectedSubCategory('');\n      setSubCategories([]);\n      setFormConfig(null);\n      setFormAvailability({\n        categoryHasForm: false,\n        subCategoryHasForm: false,\n        showSubCategoryDropdown: false,\n        message: ''\n      });\n    } else {\n      setCategories([]);\n      setSubCategories([]);\n      setSelectedCategory('');\n      setSelectedSubCategory('');\n      setFormConfig(null);\n    }\n  }, [selectedDivision]);\n\n  // Handle category selection and form loading\n  useEffect(() => {\n    if (selectedCategory) {\n      handleCategorySelection(selectedCategory);\n      // Reset subcategory when category changes\n      setSelectedSubCategory('');\n    } else {\n      setSubCategories([]);\n      setSelectedSubCategory('');\n      setFormConfig(null);\n      setFormAvailability({\n        categoryHasForm: false,\n        subCategoryHasForm: false,\n        showSubCategoryDropdown: false,\n        message: ''\n      });\n    }\n  }, [selectedCategory]);\n\n  // Handle subcategory selection and form loading\n  useEffect(() => {\n    if (selectedSubCategory) {\n      handleSubCategorySelection(selectedSubCategory);\n    }\n  }, [selectedSubCategory]);\n\n  // Load divisions from API\n  const loadDivisions = async () => {\n    setLoading(prev => ({\n      ...prev,\n      divisions: true\n    }));\n    try {\n      const response = await apiService.getDivisions();\n      setDivisions(response.data || []);\n    } catch (error) {\n      console.error('Error loading divisions:', error);\n      setErrors(prev => ({\n        ...prev,\n        divisions: 'Failed to load divisions'\n      }));\n    } finally {\n      setLoading(prev => ({\n        ...prev,\n        divisions: false\n      }));\n    }\n  };\n\n  // Load categories by division\n  const loadCategories = async divisionId => {\n    setLoading(prev => ({\n      ...prev,\n      categories: true\n    }));\n    try {\n      const response = await apiService.getCategoriesByDivision(divisionId);\n      setCategories(response.data || []);\n    } catch (error) {\n      console.error('Error loading categories:', error);\n      setErrors(prev => ({\n        ...prev,\n        categories: 'Failed to load categories'\n      }));\n      setCategories([]);\n    } finally {\n      setLoading(prev => ({\n        ...prev,\n        categories: false\n      }));\n    }\n  };\n\n  // Load subcategories by category\n  const loadSubCategories = async categoryId => {\n    setLoading(prev => ({\n      ...prev,\n      subCategories: true\n    }));\n    try {\n      const response = await apiService.getSubCategoriesByCategory(categoryId);\n      setSubCategories(response.data || []);\n    } catch (error) {\n      console.error('Error loading subcategories:', error);\n      setErrors(prev => ({\n        ...prev,\n        subCategories: 'Failed to load subcategories'\n      }));\n      setSubCategories([]);\n    } finally {\n      setLoading(prev => ({\n        ...prev,\n        subCategories: false\n      }));\n    }\n  };\n\n  // Handle category selection and form loading logic\n  const handleCategorySelection = async categoryId => {\n    setLoading(prev => ({\n      ...prev,\n      form: true\n    }));\n    try {\n      // Check if category has a form\n      const categoryHasForm = formConfigService.hasFormForCategory(parseInt(categoryId));\n      if (categoryHasForm) {\n        // Load category form and hide subcategory dropdown\n        const categoryForm = formConfigService.loadFormConfig('category', parseInt(categoryId));\n        // Deduplicate fields in loaded form\n        if (categoryForm && categoryForm.fields) {\n          categoryForm.fields = deduplicateFields(categoryForm.fields);\n        }\n        setFormConfig(categoryForm);\n        setFormAvailability({\n          categoryHasForm: true,\n          subCategoryHasForm: false,\n          showSubCategoryDropdown: false,\n          message: 'Using category-specific form'\n        });\n      } else {\n        // No category form, show subcategory dropdown and load subcategories\n        await loadSubCategories(categoryId);\n        // Load default form as fallback\n        const defaultForm = formConfigService.getDefaultFormConfig();\n        // Deduplicate fields in default form\n        if (defaultForm.fields) {\n          defaultForm.fields = deduplicateFields(defaultForm.fields);\n        }\n        setFormConfig(defaultForm);\n        setFormAvailability({\n          categoryHasForm: false,\n          subCategoryHasForm: false,\n          showSubCategoryDropdown: true,\n          message: 'Using default form. You can select a subcategory if available.'\n        });\n      }\n    } catch (error) {\n      console.error('Error handling category selection:', error);\n      setErrors(prev => ({\n        ...prev,\n        form: 'Error loading form configuration'\n      }));\n    } finally {\n      setLoading(prev => ({\n        ...prev,\n        form: false\n      }));\n    }\n  };\n\n  // Handle subcategory selection and form loading logic\n  const handleSubCategorySelection = async subCategoryId => {\n    setLoading(prev => ({\n      ...prev,\n      form: true\n    }));\n    try {\n      // Check if subcategory has a form\n      const subCategoryHasForm = formConfigService.hasFormForSubCategory(parseInt(subCategoryId));\n      if (subCategoryHasForm) {\n        // Load subcategory form\n        const subCategoryForm = formConfigService.loadFormConfig('subcategory', parseInt(subCategoryId));\n        // Deduplicate fields in loaded form\n        if (subCategoryForm && subCategoryForm.fields) {\n          subCategoryForm.fields = deduplicateFields(subCategoryForm.fields);\n        }\n        setFormConfig(subCategoryForm);\n        setFormAvailability({\n          categoryHasForm: false,\n          subCategoryHasForm: true,\n          showSubCategoryDropdown: true,\n          message: 'Using subcategory-specific form'\n        });\n      } else {\n        // No subcategory form available, use default form\n        const defaultForm = formConfigService.getDefaultFormConfig();\n        // Deduplicate fields in default form\n        if (defaultForm.fields) {\n          defaultForm.fields = deduplicateFields(defaultForm.fields);\n        }\n        setFormConfig(defaultForm);\n        setFormAvailability({\n          categoryHasForm: false,\n          subCategoryHasForm: false,\n          showSubCategoryDropdown: true,\n          message: 'No specific form found for this subcategory. Using default form.'\n        });\n      }\n    } catch (error) {\n      console.error('Error handling subcategory selection:', error);\n      setErrors(prev => ({\n        ...prev,\n        form: 'Error loading form configuration'\n      }));\n    } finally {\n      setLoading(prev => ({\n        ...prev,\n        form: false\n      }));\n    }\n  };\n\n  // Handle dropdown changes\n  const handleDivisionChange = e => {\n    const value = e.target.value;\n    setSelectedDivision(value);\n\n    // Update form data\n    setFormData(prev => ({\n      ...prev,\n      divisionId: value ? parseInt(value) : null,\n      categoryId: null,\n      subCategoryId: null\n    }));\n  };\n  const handleCategoryChange = e => {\n    const value = e.target.value;\n    setSelectedCategory(value);\n\n    // Update form data\n    setFormData(prev => ({\n      ...prev,\n      categoryId: value ? parseInt(value) : null,\n      subCategoryId: null\n    }));\n  };\n  const handleSubCategoryChange = e => {\n    const value = e.target.value;\n    setSelectedSubCategory(value);\n\n    // Update form data\n    setFormData(prev => ({\n      ...prev,\n      subCategoryId: value ? parseInt(value) : null\n    }));\n  };\n  const handleFieldChange = (fieldKey, value) => {\n    setFormData(prev => ({\n      ...prev,\n      [fieldKey]: value\n    }));\n\n    // Clear field error when user starts typing\n    if (errors[fieldKey]) {\n      setErrors(prev => ({\n        ...prev,\n        [fieldKey]: null\n      }));\n    }\n  };\n  const validateForm = () => {\n    const newErrors = {};\n\n    // Validate hierarchy selection\n    if (!selectedDivision) {\n      newErrors.division = 'Division is required';\n    }\n    if (!selectedCategory) {\n      newErrors.category = 'Category is required';\n    }\n\n    // Check if form is available (should always have default form as fallback)\n    if (!formConfig) {\n      newErrors.form = 'Form configuration not loaded. Please try again.';\n      return {\n        isValid: false,\n        errors: newErrors\n      };\n    }\n\n    // Validate form fields\n    formConfig.fields.forEach(field => {\n      const value = formData[field.key];\n\n      // Required field validation\n      if (field.required && (!value || typeof value === 'string' && value.trim() === '')) {\n        newErrors[field.key] = `${field.label} is required`;\n        return;\n      }\n\n      // Conditional field validation\n      if (field.conditional && shouldShowField(field)) {\n        if (field.required && (!value || typeof value === 'string' && value.trim() === '')) {\n          newErrors[field.key] = `${field.label} is required`;\n          return;\n        }\n      }\n\n      // Type-specific validation\n      if (value && typeof value === 'string' && value.trim() !== '') {\n        var _field$validation, _field$validation2, _field$validation3;\n        switch (field.type) {\n          case 'email':\n            const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n            if (!emailRegex.test(value)) {\n              newErrors[field.key] = 'Please enter a valid email address';\n            }\n            break;\n          case 'tel':\n            const phoneRegex = /^(\\+91[\\-\\s]?)?[0]?(91)?[789]\\d{9}$/;\n            if (!phoneRegex.test(value)) {\n              newErrors[field.key] = 'Please enter a valid mobile number';\n            }\n            break;\n          case 'url':\n            try {\n              new URL(value);\n            } catch {\n              newErrors[field.key] = 'Please enter a valid URL';\n            }\n            break;\n          case 'number':\n            if (field.validation) {\n              const numValue = parseFloat(value);\n              if (field.validation.min !== undefined && numValue < field.validation.min) {\n                newErrors[field.key] = `Value must be at least ${field.validation.min}`;\n              }\n              if (field.validation.max !== undefined && numValue > field.validation.max) {\n                newErrors[field.key] = `Value must be at most ${field.validation.max}`;\n              }\n            }\n            break;\n        }\n\n        // Pattern validation\n        if ((_field$validation = field.validation) !== null && _field$validation !== void 0 && _field$validation.pattern) {\n          const regex = new RegExp(field.validation.pattern);\n          if (!regex.test(value)) {\n            newErrors[field.key] = `${field.label} format is invalid`;\n          }\n        }\n\n        // Length validation\n        if ((_field$validation2 = field.validation) !== null && _field$validation2 !== void 0 && _field$validation2.minLength && value.length < field.validation.minLength) {\n          newErrors[field.key] = `${field.label} must be at least ${field.validation.minLength} characters`;\n        }\n        if ((_field$validation3 = field.validation) !== null && _field$validation3 !== void 0 && _field$validation3.maxLength && value.length > field.validation.maxLength) {\n          newErrors[field.key] = `${field.label} must be at most ${field.validation.maxLength} characters`;\n        }\n      }\n    });\n\n    // Business logic validation\n    if (formData.isMarried && formData.dateOfMarriage && formData.dateOfBirth) {\n      const birthDate = new Date(formData.dateOfBirth);\n      const marriageDate = new Date(formData.dateOfMarriage);\n      if (marriageDate <= birthDate) {\n        newErrors.dateOfMarriage = 'Marriage date must be after birth date';\n      }\n    }\n    return {\n      isValid: Object.keys(newErrors).length === 0,\n      errors: newErrors\n    };\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    const validation = validateForm();\n    if (!validation.isValid) {\n      console.log('Validation failed:', validation.errors);\n      setErrors(validation.errors);\n      return;\n    }\n    setSubmitting(true);\n    try {\n      // Prepare data for API - match backend model exactly\n      const submitData = {\n        // Required hierarchy fields\n        divisionId: parseInt(selectedDivision),\n        categoryId: parseInt(selectedCategory),\n        subCategoryId: selectedSubCategory ? parseInt(selectedSubCategory) : null,\n        // Required fields\n        name: formData.name || '',\n        mobileNumber: formData.mobileNumber || '',\n        nature: formData.nature ? parseInt(formData.nature) : null,\n        // Optional enum fields\n        gender: formData.gender ? parseInt(formData.gender) : null,\n        // Contact Information\n        alternateNumbers: Array.isArray(formData.alternateNumbers) ? formData.alternateNumbers : formData.alternateNumbers ? formData.alternateNumbers.split(',').map(s => s.trim()) : [],\n        alternateEmailIds: Array.isArray(formData.alternateEmailIds) ? formData.alternateEmailIds : formData.alternateEmailIds ? formData.alternateEmailIds.split(',').map(s => s.trim()) : [],\n        // Personal Information\n        dateOfBirth: formData.dateOfBirth || null,\n        isMarried: formData.isMarried || false,\n        dateOfMarriage: formData.dateOfMarriage || null,\n        // Location Information\n        workingState: formData.workingState || '',\n        domesticState: formData.domesticState || '',\n        district: formData.district || '',\n        address: formData.address || '',\n        workingArea: formData.workingArea || '',\n        // Associate Information\n        hasAssociate: formData.hasAssociate || false,\n        associateName: formData.associateName || '',\n        associateRelation: formData.associateRelation || '',\n        associateMobile: formData.associateMobile || '',\n        // Digital Presence\n        usingWebsite: formData.usingWebsite || false,\n        usingCRMApp: formData.usingCRMApp || false,\n        // Business Information\n        transactionValue: formData.transactionValue ? parseFloat(formData.transactionValue) : null,\n        reraRegistrationNumber: formData.reraRegistrationNumber || '',\n        workingProfiles: Array.isArray(formData.workingProfiles) ? formData.workingProfiles.map(wp => parseInt(wp)) : [],\n        starRating: formData.starRating ? parseInt(formData.starRating) : null,\n        source: formData.source || '',\n        remarks: formData.remarks || '',\n        // Company Information\n        firmName: formData.firmName || '',\n        numberOfOffices: formData.numberOfOffices ? parseInt(formData.numberOfOffices) : null,\n        numberOfBranches: formData.numberOfBranches ? parseInt(formData.numberOfBranches) : null,\n        totalEmployeeStrength: formData.totalEmployeeStrength ? parseInt(formData.totalEmployeeStrength) : null,\n        // Authorized Person\n        authorizedPersonName: formData.authorizedPersonName || '',\n        designation: formData.designation || '',\n        // Marketing Information\n        marketingContact: formData.marketingContact || '',\n        marketingDesignation: formData.marketingDesignation || '',\n        placeOfPosting: formData.placeOfPosting || '',\n        department: formData.department || ''\n      };\n\n      // Add optional fields only if they have valid values\n      // Debug: Check what values we have\n      console.log('Form data values for validation:');\n      console.log('primaryEmailId:', `\"${formData.primaryEmailId}\"`);\n      console.log('authorizedPersonEmail:', `\"${formData.authorizedPersonEmail}\"`);\n      console.log('website:', `\"${formData.website}\"`);\n      console.log('websiteLink:', `\"${formData.websiteLink}\"`);\n      console.log('crmAppLink:', `\"${formData.crmAppLink}\"`);\n\n      // Email fields - only add if not null/empty and not just whitespace\n      if (formData.primaryEmailId && formData.primaryEmailId !== null && typeof formData.primaryEmailId === 'string' && formData.primaryEmailId.trim() !== '') {\n        submitData['primaryEmailId'] = formData.primaryEmailId.trim();\n        console.log('Adding primaryEmailId:', formData.primaryEmailId.trim());\n      } else {\n        console.log('Skipping primaryEmailId - empty, null, or invalid');\n      }\n      if (formData.authorizedPersonEmail && formData.authorizedPersonEmail !== null && typeof formData.authorizedPersonEmail === 'string' && formData.authorizedPersonEmail.trim() !== '') {\n        submitData['authorizedPersonEmail'] = formData.authorizedPersonEmail.trim();\n        console.log('Adding authorizedPersonEmail:', formData.authorizedPersonEmail.trim());\n      } else {\n        console.log('Skipping authorizedPersonEmail - empty, null, or invalid');\n      }\n\n      // URL fields - only add if not null/empty and not just whitespace\n      if (formData.website && formData.website !== null && typeof formData.website === 'string' && formData.website.trim() !== '') {\n        submitData['website'] = formData.website.trim();\n        console.log('Adding website:', formData.website.trim());\n      } else {\n        console.log('Skipping website - empty, null, or invalid');\n      }\n      if (formData.websiteLink && formData.websiteLink !== null && typeof formData.websiteLink === 'string' && formData.websiteLink.trim() !== '') {\n        submitData['websiteLink'] = formData.websiteLink.trim();\n        console.log('Adding websiteLink:', formData.websiteLink.trim());\n      } else {\n        console.log('Skipping websiteLink - empty, null, or invalid');\n      }\n      if (formData.crmAppLink && formData.crmAppLink !== null && typeof formData.crmAppLink === 'string' && formData.crmAppLink.trim() !== '') {\n        submitData['crmAppLink'] = formData.crmAppLink.trim();\n        console.log('Adding crmAppLink:', formData.crmAppLink.trim());\n      } else {\n        console.log('Skipping crmAppLink - empty, null, or invalid');\n      }\n\n      // Debug logging\n      console.log('Final submitData object:', submitData);\n      console.log('Fields being sent to API:');\n      Object.keys(submitData).forEach(key => {\n        console.log(`  ${key}:`, typeof submitData[key], `\"${submitData[key]}\"`);\n      });\n      console.log('Selected Division:', selectedDivision);\n      console.log('Selected Category:', selectedCategory);\n      console.log('Selected SubCategory:', selectedSubCategory);\n\n      // Validate required fields before submission\n      const requiredFieldsCheck = {\n        divisionId: submitData.divisionId,\n        categoryId: submitData.categoryId,\n        name: submitData.name,\n        mobileNumber: submitData.mobileNumber,\n        nature: submitData.nature\n      };\n      console.log('Required fields check:', requiredFieldsCheck);\n\n      // Check for missing required fields\n      const missingFields = Object.entries(requiredFieldsCheck).filter(([, value]) => !value || value === '' || value === null).map(([key]) => key);\n      if (missingFields.length > 0) {\n        console.error('Missing required fields:', missingFields);\n        setErrors({\n          general: 'Missing required fields: ' + missingFields.join(', ')\n        });\n        return;\n      }\n      let result;\n      if (mode === 'create') {\n        result = await apiService.createPerson(submitData);\n      } else {\n        result = await apiService.updatePerson(initialData.id, submitData);\n      }\n      onSubmit(result);\n    } catch (error) {\n      var _error$data;\n      console.error('Error submitting form:', error);\n      console.error('Error data:', error.data);\n      console.error('Full error object:', error);\n      if ((_error$data = error.data) !== null && _error$data !== void 0 && _error$data.errors) {\n        // Handle validation errors from backend\n        const backendErrors = {};\n        Object.keys(error.data.errors).forEach(key => {\n          const errorMessages = error.data.errors[key];\n          backendErrors[key.toLowerCase()] = Array.isArray(errorMessages) ? errorMessages.join(', ') : errorMessages;\n        });\n        console.log('Backend validation errors:', backendErrors);\n        setErrors(backendErrors);\n      } else if (error.isValidationError && error.isValidationError()) {\n        setErrors(error.getValidationErrors());\n      } else {\n        var _error$data2, _error$data3;\n        const errorMessage = ((_error$data2 = error.data) === null || _error$data2 === void 0 ? void 0 : _error$data2.message) || ((_error$data3 = error.data) === null || _error$data3 === void 0 ? void 0 : _error$data3.title) || error.message || `HTTP ${error.status || 'Unknown'} - An error occurred while saving`;\n        console.log('General error message:', errorMessage);\n        setErrors({\n          general: errorMessage\n        });\n      }\n    } finally {\n      setSubmitting(false);\n    }\n  };\n  const shouldShowField = field => {\n    if (!field.conditional) return true;\n    const conditionValue = formData[field.conditional.field];\n    const expectedValue = field.conditional.value;\n\n    // Handle boolean conditions\n    if (typeof expectedValue === 'boolean' || expectedValue === 'true' || expectedValue === 'false') {\n      return conditionValue === (expectedValue === true || expectedValue === 'true');\n    }\n    return conditionValue === expectedValue;\n  };\n  const groupFieldsBySections = () => {\n    if (!formConfig || !formConfig.fields) return {};\n\n    // Debug: Check for duplicate fields\n    const fieldKeys = formConfig.fields.map(field => field.key);\n    const duplicateKeys = fieldKeys.filter((key, index) => fieldKeys.indexOf(key) !== index);\n    if (duplicateKeys.length > 0) {\n      console.warn('Duplicate field keys found:', duplicateKeys);\n      console.log('All fields:', formConfig.fields);\n    }\n    const sections = {};\n    formConfig.fields.forEach(field => {\n      const sectionKey = field.section || 'general';\n      if (!sections[sectionKey]) {\n        sections[sectionKey] = {\n          title: getSectionTitle(sectionKey),\n          fields: []\n        };\n      }\n      sections[sectionKey].fields.push(field);\n    });\n    return sections;\n  };\n  const getSectionTitle = sectionKey => {\n    const titles = {\n      personalInfo: 'Personal Information',\n      contactInfo: 'Contact Information',\n      locationInfo: 'Location Information',\n      businessInfo: 'Business Information',\n      associateInfo: 'Associate Information',\n      digitalPresence: 'Digital Presence',\n      companyInfo: 'Company Information',\n      authorizedPerson: 'Authorized Person',\n      marketingInfo: 'Marketing Information',\n      general: 'General Information'\n    };\n    return titles[sectionKey] || sectionKey;\n  };\n  const sections = formConfig ? groupFieldsBySections() : {};\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"dynamic-person-form\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"form-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: mode === 'create' ? 'Create New Person' : 'Edit Person'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 720,\n        columnNumber: 9\n      }, this), formConfig && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-info\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"form-name\",\n          children: formConfig.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 723,\n          columnNumber: 13\n        }, this), formConfig.description && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"form-description\",\n          children: formConfig.description\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 725,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 722,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 719,\n      columnNumber: 7\n    }, this), errors.general && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"alert alert-error\",\n      children: errors.general\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 732,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmit,\n      className: \"person-form\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Division & Category Selection\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 738,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"form-label\",\n            children: [\"Division \", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"required\",\n              children: \"*\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 743,\n              columnNumber: 24\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 742,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: selectedDivision,\n            onChange: handleDivisionChange,\n            disabled: loading.divisions,\n            className: `form-select ${errors.division ? 'error' : ''}`,\n            required: true,\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"\",\n              children: loading.divisions ? 'Loading divisions...' : 'Select Division'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 752,\n              columnNumber: 15\n            }, this), divisions.map(division => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: division.id,\n              children: division.name\n            }, division.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 756,\n              columnNumber: 17\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 745,\n            columnNumber: 13\n          }, this), errors.division && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"error-message\",\n            children: errors.division\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 762,\n            columnNumber: 15\n          }, this), errors.divisions && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"error-message\",\n            children: errors.divisions\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 765,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 741,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"form-label\",\n            children: [\"Category \", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"required\",\n              children: \"*\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 772,\n              columnNumber: 24\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 771,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: selectedCategory,\n            onChange: handleCategoryChange,\n            disabled: !selectedDivision || loading.categories,\n            className: `form-select ${errors.category ? 'error' : ''}`,\n            required: true,\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"\",\n              children: !selectedDivision ? 'Select Division first' : loading.categories ? 'Loading categories...' : 'Select Category'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 781,\n              columnNumber: 15\n            }, this), categories.map(category => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: category.id,\n              children: category.name\n            }, category.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 790,\n              columnNumber: 17\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 774,\n            columnNumber: 13\n          }, this), errors.category && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"error-message\",\n            children: errors.category\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 796,\n            columnNumber: 15\n          }, this), errors.categories && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"error-message\",\n            children: errors.categories\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 799,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 770,\n          columnNumber: 11\n        }, this), formAvailability.showSubCategoryDropdown && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"form-label\",\n            children: \"Subcategory (Optional)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 806,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: selectedSubCategory,\n            onChange: handleSubCategoryChange,\n            disabled: !selectedCategory || loading.subCategories,\n            className: \"form-select\",\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"\",\n              children: !selectedCategory ? 'Select Category first' : loading.subCategories ? 'Loading subcategories...' : 'Select Subcategory (Optional)'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 815,\n              columnNumber: 17\n            }, this), subCategories.map(subCategory => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: subCategory.id,\n              children: subCategory.name\n            }, subCategory.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 824,\n              columnNumber: 19\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 809,\n            columnNumber: 15\n          }, this), errors.subCategories && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"error-message\",\n            children: errors.subCategories\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 830,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 805,\n          columnNumber: 13\n        }, this), loading.form && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading-message\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Loading form configuration...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 838,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 837,\n          columnNumber: 13\n        }, this), formAvailability.message && !loading.form && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `status-message ${formAvailability.categoryHasForm || formAvailability.subCategoryHasForm ? 'success' : formAvailability.message.includes('No form') ? 'error' : 'info'}`,\n          children: formAvailability.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 843,\n          columnNumber: 13\n        }, this), errors.form && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"error-message\",\n          children: errors.form\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 855,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 737,\n        columnNumber: 9\n      }, this), formConfig && Object.entries(sections).map(([sectionKey, section]) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: section.title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 862,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-fields\",\n          children: section.fields.filter(field => shouldShowField(field)).map((field, fieldIndex) => /*#__PURE__*/_jsxDEV(FormField, {\n            field: field,\n            value: formData[field.key],\n            onChange: value => handleFieldChange(field.key, value),\n            error: errors[field.key]\n          }, `${sectionKey}-${field.key}-${fieldIndex}`, false, {\n            fileName: _jsxFileName,\n            lineNumber: 867,\n            columnNumber: 19\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 863,\n          columnNumber: 13\n        }, this)]\n      }, sectionKey, true, {\n        fileName: _jsxFileName,\n        lineNumber: 861,\n        columnNumber: 11\n      }, this)), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-actions\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          onClick: onCancel,\n          className: \"btn btn-outline\",\n          disabled: submitting,\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 883,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          className: \"btn btn-primary\",\n          disabled: submitting || !selectedDivision || !selectedCategory,\n          children: submitting ? mode === 'create' ? 'Creating...' : 'Updating...' : mode === 'create' ? 'Create Person' : 'Update Person'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 891,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 882,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 735,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 718,\n    columnNumber: 5\n  }, this);\n};\n_s(DynamicPersonForm, \"HQTZ+LW5pRnY6eFU2EO5G2IvcY0=\");\n_c = DynamicPersonForm;\nexport default DynamicPersonForm;\nvar _c;\n$RefreshReg$(_c, \"DynamicPersonForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "PersonNatureLabels", "<PERSON><PERSON><PERSON><PERSON>", "WorkingProfileLabels", "getDefaultPersonData", "formConfigService", "apiService", "FormField", "jsxDEV", "_jsxDEV", "DynamicPersonForm", "onSubmit", "onCancel", "initialData", "mode", "_s", "divisions", "setDivisions", "categories", "setCategories", "subCategories", "setSubCategories", "selectedDivision", "setSelectedDivision", "selectedCate<PERSON><PERSON>", "setSelectedCategory", "selectedSubCategory", "setSelectedSubCategory", "formConfig", "setFormConfig", "formData", "setFormData", "errors", "setErrors", "loading", "setLoading", "form", "submitting", "setSubmitting", "formAvailability", "setFormAvailability", "categoryHasForm", "subCategoryHasForm", "showSubCategoryDropdown", "message", "loadDivisions", "testApiConnectivity", "_initialData$division", "_initialData$category", "_initialData$subCateg", "divisionId", "toString", "categoryId", "subCategoryId", "defaultForm", "getDefaultFormConfig", "fields", "deduplicateFields", "console", "log", "baseUrl", "process", "env", "REACT_APP_API_URL", "response", "getDivisions", "testResponse", "fetch", "status", "ok", "error", "data", "seen", "Set", "deduplicated", "for<PERSON>ach", "field", "has", "key", "add", "push", "warn", "length", "loadCategories", "handleCategorySelection", "handleSubCategorySelection", "prev", "getCategoriesByDivision", "loadSubCategories", "getSubCategoriesByCategory", "hasFormForCategory", "parseInt", "categoryForm", "loadFormConfig", "hasFormForSubCategory", "subCategoryForm", "handleDivisionChange", "e", "value", "target", "handleCategoryChange", "handleSubCategoryChange", "handleFieldChange", "<PERSON><PERSON><PERSON>", "validateForm", "newErrors", "division", "category", "<PERSON><PERSON><PERSON><PERSON>", "required", "trim", "label", "conditional", "shouldShowField", "_field$validation", "_field$validation2", "_field$validation3", "type", "emailRegex", "test", "phoneRegex", "URL", "validation", "numValue", "parseFloat", "min", "undefined", "max", "pattern", "regex", "RegExp", "<PERSON><PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "dateOfMarriage", "dateOfBirth", "birthDate", "Date", "marriageDate", "Object", "keys", "handleSubmit", "preventDefault", "submitData", "name", "mobileNumber", "nature", "gender", "alternateNumbers", "Array", "isArray", "split", "map", "s", "alternateEmailIds", "workingState", "domesticState", "district", "address", "workingArea", "hasAssociate", "associate<PERSON><PERSON>", "associateRelation", "associate<PERSON><PERSON><PERSON>", "usingWebsite", "usingCRMApp", "transactionValue", "reraRegistrationNumber", "workingProfiles", "wp", "starRating", "source", "remarks", "firmName", "numberOfOffices", "numberOfBranches", "totalEmployeeStrength", "authorizedPersonName", "designation", "marketingContact", "marketingDesignation", "placeOfPosting", "department", "primaryEmailId", "authorizedPersonEmail", "website", "websiteLink", "crmAppLink", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "missingFields", "entries", "filter", "general", "join", "result", "create<PERSON>erson", "update<PERSON><PERSON>", "id", "_error$data", "backendErrors", "errorMessages", "toLowerCase", "isValidationError", "getValidationErrors", "_error$data2", "_error$data3", "errorMessage", "title", "conditionValue", "expectedValue", "groupFieldsBySections", "fieldKeys", "duplicateKeys", "index", "indexOf", "sections", "sectionKey", "section", "getSectionTitle", "titles", "personalInfo", "contactInfo", "locationInfo", "businessInfo", "associateInfo", "digitalPresence", "companyInfo", "<PERSON><PERSON><PERSON>", "marketingInfo", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "description", "onChange", "disabled", "subCategory", "includes", "fieldIndex", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/Advanced/Advanced CRM filed all use/frontend/src/components/forms/DynamicPersonForm.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { PersonNatureLabels, GenderLabels, WorkingProfileLabels, getDefaultPersonData } from '../../constants/personConstants';\nimport formConfigService from '../../services/formConfigService';\nimport apiService from '../../services/apiService';\nimport FormField from './FormField';\nimport './DynamicPersonForm.css';\n\nconst DynamicPersonForm = ({ onSubmit, onCancel, initialData = null, mode = 'create' }) => {\n  // Hierarchy state\n  const [divisions, setDivisions] = useState([]);\n  const [categories, setCategories] = useState([]);\n  const [subCategories, setSubCategories] = useState([]);\n\n  // Selection state\n  const [selectedDivision, setSelectedDivision] = useState('');\n  const [selectedCategory, setSelectedCategory] = useState('');\n  const [selectedSubCategory, setSelectedSubCategory] = useState('');\n\n  // Form state\n  const [formConfig, setFormConfig] = useState(null);\n  const [formData, setFormData] = useState(getDefaultPersonData());\n  const [errors, setErrors] = useState({});\n\n  // Loading states\n  const [loading, setLoading] = useState({\n    divisions: false,\n    categories: false,\n    subCategories: false,\n    form: false\n  });\n  const [submitting, setSubmitting] = useState(false);\n\n  // Form availability state\n  const [formAvailability, setFormAvailability] = useState({\n    categoryHasForm: false,\n    subCategoryHasForm: false,\n    showSubCategoryDropdown: false,\n    message: ''\n  });\n\n  // Load divisions on component mount\n  useEffect(() => {\n    loadDivisions();\n\n    // Test API connectivity\n    testApiConnectivity();\n\n    // Load initial data if provided\n    if (initialData) {\n      setFormData(initialData);\n      setSelectedDivision(initialData.divisionId?.toString() || '');\n      setSelectedCategory(initialData.categoryId?.toString() || '');\n      setSelectedSubCategory(initialData.subCategoryId?.toString() || '');\n    } else {\n      // Load default form for new person creation\n      const defaultForm = formConfigService.getDefaultFormConfig();\n      // Ensure no duplicate fields in default form\n      if (defaultForm.fields) {\n        defaultForm.fields = deduplicateFields(defaultForm.fields);\n      }\n      setFormConfig(defaultForm);\n    }\n  }, [initialData]);\n\n  // Test API connectivity\n  const testApiConnectivity = async () => {\n    try {\n      console.log('Testing API connectivity...');\n\n      // Test basic connectivity\n      const baseUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';\n      console.log('API Base URL:', baseUrl);\n\n      // Test divisions endpoint\n      const response = await apiService.getDivisions();\n      console.log('API test successful:', response);\n\n      // Test a simple GET request to see if server is responding\n      const testResponse = await fetch(`${baseUrl}/divisions`);\n      console.log('Direct fetch test - Status:', testResponse.status);\n      console.log('Direct fetch test - OK:', testResponse.ok);\n\n    } catch (error) {\n      console.error('API connectivity test failed:', error);\n      console.error('Error details:', {\n        message: error.message,\n        status: error.status,\n        data: error.data\n      });\n    }\n  };\n\n  // Deduplicate fields based on field key\n  const deduplicateFields = (fields) => {\n    const seen = new Set();\n    const deduplicated = [];\n\n    fields.forEach(field => {\n      if (!seen.has(field.key)) {\n        seen.add(field.key);\n        deduplicated.push(field);\n      } else {\n        console.warn(`DynamicPersonForm: Removing duplicate field: ${field.key}`);\n      }\n    });\n\n    if (fields.length !== deduplicated.length) {\n      console.log(`DynamicPersonForm: Deduplicated ${fields.length} fields to ${deduplicated.length} unique fields`);\n    }\n\n    return deduplicated;\n  };\n\n  // Load categories when division changes\n  useEffect(() => {\n    if (selectedDivision) {\n      loadCategories(selectedDivision);\n      // Reset category and subcategory when division changes\n      setSelectedCategory('');\n      setSelectedSubCategory('');\n      setSubCategories([]);\n      setFormConfig(null);\n      setFormAvailability({\n        categoryHasForm: false,\n        subCategoryHasForm: false,\n        showSubCategoryDropdown: false,\n        message: ''\n      });\n    } else {\n      setCategories([]);\n      setSubCategories([]);\n      setSelectedCategory('');\n      setSelectedSubCategory('');\n      setFormConfig(null);\n    }\n  }, [selectedDivision]);\n\n  // Handle category selection and form loading\n  useEffect(() => {\n    if (selectedCategory) {\n      handleCategorySelection(selectedCategory);\n      // Reset subcategory when category changes\n      setSelectedSubCategory('');\n    } else {\n      setSubCategories([]);\n      setSelectedSubCategory('');\n      setFormConfig(null);\n      setFormAvailability({\n        categoryHasForm: false,\n        subCategoryHasForm: false,\n        showSubCategoryDropdown: false,\n        message: ''\n      });\n    }\n  }, [selectedCategory]);\n\n  // Handle subcategory selection and form loading\n  useEffect(() => {\n    if (selectedSubCategory) {\n      handleSubCategorySelection(selectedSubCategory);\n    }\n  }, [selectedSubCategory]);\n\n  // Load divisions from API\n  const loadDivisions = async () => {\n    setLoading(prev => ({ ...prev, divisions: true }));\n    try {\n      const response = await apiService.getDivisions();\n      setDivisions(response.data || []);\n    } catch (error) {\n      console.error('Error loading divisions:', error);\n      setErrors(prev => ({ ...prev, divisions: 'Failed to load divisions' }));\n    } finally {\n      setLoading(prev => ({ ...prev, divisions: false }));\n    }\n  };\n\n  // Load categories by division\n  const loadCategories = async (divisionId) => {\n    setLoading(prev => ({ ...prev, categories: true }));\n    try {\n      const response = await apiService.getCategoriesByDivision(divisionId);\n      setCategories(response.data || []);\n    } catch (error) {\n      console.error('Error loading categories:', error);\n      setErrors(prev => ({ ...prev, categories: 'Failed to load categories' }));\n      setCategories([]);\n    } finally {\n      setLoading(prev => ({ ...prev, categories: false }));\n    }\n  };\n\n  // Load subcategories by category\n  const loadSubCategories = async (categoryId) => {\n    setLoading(prev => ({ ...prev, subCategories: true }));\n    try {\n      const response = await apiService.getSubCategoriesByCategory(categoryId);\n      setSubCategories(response.data || []);\n    } catch (error) {\n      console.error('Error loading subcategories:', error);\n      setErrors(prev => ({ ...prev, subCategories: 'Failed to load subcategories' }));\n      setSubCategories([]);\n    } finally {\n      setLoading(prev => ({ ...prev, subCategories: false }));\n    }\n  };\n\n  // Handle category selection and form loading logic\n  const handleCategorySelection = async (categoryId) => {\n    setLoading(prev => ({ ...prev, form: true }));\n\n    try {\n      // Check if category has a form\n      const categoryHasForm = formConfigService.hasFormForCategory(parseInt(categoryId));\n\n      if (categoryHasForm) {\n        // Load category form and hide subcategory dropdown\n        const categoryForm = formConfigService.loadFormConfig('category', parseInt(categoryId));\n        // Deduplicate fields in loaded form\n        if (categoryForm && categoryForm.fields) {\n          categoryForm.fields = deduplicateFields(categoryForm.fields);\n        }\n        setFormConfig(categoryForm);\n        setFormAvailability({\n          categoryHasForm: true,\n          subCategoryHasForm: false,\n          showSubCategoryDropdown: false,\n          message: 'Using category-specific form'\n        });\n      } else {\n        // No category form, show subcategory dropdown and load subcategories\n        await loadSubCategories(categoryId);\n        // Load default form as fallback\n        const defaultForm = formConfigService.getDefaultFormConfig();\n        // Deduplicate fields in default form\n        if (defaultForm.fields) {\n          defaultForm.fields = deduplicateFields(defaultForm.fields);\n        }\n        setFormConfig(defaultForm);\n        setFormAvailability({\n          categoryHasForm: false,\n          subCategoryHasForm: false,\n          showSubCategoryDropdown: true,\n          message: 'Using default form. You can select a subcategory if available.'\n        });\n      }\n    } catch (error) {\n      console.error('Error handling category selection:', error);\n      setErrors(prev => ({ ...prev, form: 'Error loading form configuration' }));\n    } finally {\n      setLoading(prev => ({ ...prev, form: false }));\n    }\n  };\n\n  // Handle subcategory selection and form loading logic\n  const handleSubCategorySelection = async (subCategoryId) => {\n    setLoading(prev => ({ ...prev, form: true }));\n\n    try {\n      // Check if subcategory has a form\n      const subCategoryHasForm = formConfigService.hasFormForSubCategory(parseInt(subCategoryId));\n\n      if (subCategoryHasForm) {\n        // Load subcategory form\n        const subCategoryForm = formConfigService.loadFormConfig('subcategory', parseInt(subCategoryId));\n        // Deduplicate fields in loaded form\n        if (subCategoryForm && subCategoryForm.fields) {\n          subCategoryForm.fields = deduplicateFields(subCategoryForm.fields);\n        }\n        setFormConfig(subCategoryForm);\n        setFormAvailability({\n          categoryHasForm: false,\n          subCategoryHasForm: true,\n          showSubCategoryDropdown: true,\n          message: 'Using subcategory-specific form'\n        });\n      } else {\n        // No subcategory form available, use default form\n        const defaultForm = formConfigService.getDefaultFormConfig();\n        // Deduplicate fields in default form\n        if (defaultForm.fields) {\n          defaultForm.fields = deduplicateFields(defaultForm.fields);\n        }\n        setFormConfig(defaultForm);\n        setFormAvailability({\n          categoryHasForm: false,\n          subCategoryHasForm: false,\n          showSubCategoryDropdown: true,\n          message: 'No specific form found for this subcategory. Using default form.'\n        });\n      }\n    } catch (error) {\n      console.error('Error handling subcategory selection:', error);\n      setErrors(prev => ({ ...prev, form: 'Error loading form configuration' }));\n    } finally {\n      setLoading(prev => ({ ...prev, form: false }));\n    }\n  };\n\n  // Handle dropdown changes\n  const handleDivisionChange = (e) => {\n    const value = e.target.value;\n    setSelectedDivision(value);\n\n    // Update form data\n    setFormData(prev => ({\n      ...prev,\n      divisionId: value ? parseInt(value) : null,\n      categoryId: null,\n      subCategoryId: null\n    }));\n  };\n\n  const handleCategoryChange = (e) => {\n    const value = e.target.value;\n    setSelectedCategory(value);\n\n    // Update form data\n    setFormData(prev => ({\n      ...prev,\n      categoryId: value ? parseInt(value) : null,\n      subCategoryId: null\n    }));\n  };\n\n  const handleSubCategoryChange = (e) => {\n    const value = e.target.value;\n    setSelectedSubCategory(value);\n\n    // Update form data\n    setFormData(prev => ({\n      ...prev,\n      subCategoryId: value ? parseInt(value) : null\n    }));\n  };\n\n  const handleFieldChange = (fieldKey, value) => {\n    setFormData(prev => ({\n      ...prev,\n      [fieldKey]: value\n    }));\n\n    // Clear field error when user starts typing\n    if (errors[fieldKey]) {\n      setErrors(prev => ({\n        ...prev,\n        [fieldKey]: null\n      }));\n    }\n  };\n\n  const validateForm = () => {\n    const newErrors = {};\n\n    // Validate hierarchy selection\n    if (!selectedDivision) {\n      newErrors.division = 'Division is required';\n    }\n    if (!selectedCategory) {\n      newErrors.category = 'Category is required';\n    }\n\n    // Check if form is available (should always have default form as fallback)\n    if (!formConfig) {\n      newErrors.form = 'Form configuration not loaded. Please try again.';\n      return { isValid: false, errors: newErrors };\n    }\n\n\n\n    // Validate form fields\n    formConfig.fields.forEach(field => {\n      const value = formData[field.key];\n      \n      // Required field validation\n      if (field.required && (!value || (typeof value === 'string' && value.trim() === ''))) {\n        newErrors[field.key] = `${field.label} is required`;\n        return;\n      }\n\n      // Conditional field validation\n      if (field.conditional && shouldShowField(field)) {\n        if (field.required && (!value || (typeof value === 'string' && value.trim() === ''))) {\n          newErrors[field.key] = `${field.label} is required`;\n          return;\n        }\n      }\n\n      // Type-specific validation\n      if (value && typeof value === 'string' && value.trim() !== '') {\n        switch (field.type) {\n          case 'email':\n            const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n            if (!emailRegex.test(value)) {\n              newErrors[field.key] = 'Please enter a valid email address';\n            }\n            break;\n          case 'tel':\n            const phoneRegex = /^(\\+91[\\-\\s]?)?[0]?(91)?[789]\\d{9}$/;\n            if (!phoneRegex.test(value)) {\n              newErrors[field.key] = 'Please enter a valid mobile number';\n            }\n            break;\n          case 'url':\n            try {\n              new URL(value);\n            } catch {\n              newErrors[field.key] = 'Please enter a valid URL';\n            }\n            break;\n          case 'number':\n            if (field.validation) {\n              const numValue = parseFloat(value);\n              if (field.validation.min !== undefined && numValue < field.validation.min) {\n                newErrors[field.key] = `Value must be at least ${field.validation.min}`;\n              }\n              if (field.validation.max !== undefined && numValue > field.validation.max) {\n                newErrors[field.key] = `Value must be at most ${field.validation.max}`;\n              }\n            }\n            break;\n        }\n\n        // Pattern validation\n        if (field.validation?.pattern) {\n          const regex = new RegExp(field.validation.pattern);\n          if (!regex.test(value)) {\n            newErrors[field.key] = `${field.label} format is invalid`;\n          }\n        }\n\n        // Length validation\n        if (field.validation?.minLength && value.length < field.validation.minLength) {\n          newErrors[field.key] = `${field.label} must be at least ${field.validation.minLength} characters`;\n        }\n        if (field.validation?.maxLength && value.length > field.validation.maxLength) {\n          newErrors[field.key] = `${field.label} must be at most ${field.validation.maxLength} characters`;\n        }\n      }\n    });\n\n    // Business logic validation\n    if (formData.isMarried && formData.dateOfMarriage && formData.dateOfBirth) {\n      const birthDate = new Date(formData.dateOfBirth);\n      const marriageDate = new Date(formData.dateOfMarriage);\n      if (marriageDate <= birthDate) {\n        newErrors.dateOfMarriage = 'Marriage date must be after birth date';\n      }\n    }\n\n    return {\n      isValid: Object.keys(newErrors).length === 0,\n      errors: newErrors\n    };\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    \n    const validation = validateForm();\n    if (!validation.isValid) {\n      console.log('Validation failed:', validation.errors);\n      setErrors(validation.errors);\n      return;\n    }\n\n    setSubmitting(true);\n    try {\n      // Prepare data for API - match backend model exactly\n      const submitData = {\n        // Required hierarchy fields\n        divisionId: parseInt(selectedDivision),\n        categoryId: parseInt(selectedCategory),\n        subCategoryId: selectedSubCategory ? parseInt(selectedSubCategory) : null,\n\n        // Required fields\n        name: formData.name || '',\n        mobileNumber: formData.mobileNumber || '',\n        nature: formData.nature ? parseInt(formData.nature) : null,\n\n        // Optional enum fields\n        gender: formData.gender ? parseInt(formData.gender) : null,\n\n        // Contact Information\n        alternateNumbers: Array.isArray(formData.alternateNumbers)\n          ? formData.alternateNumbers\n          : formData.alternateNumbers ? formData.alternateNumbers.split(',').map(s => s.trim()) : [],\n        alternateEmailIds: Array.isArray(formData.alternateEmailIds)\n          ? formData.alternateEmailIds\n          : formData.alternateEmailIds ? formData.alternateEmailIds.split(',').map(s => s.trim()) : [],\n\n        // Personal Information\n        dateOfBirth: formData.dateOfBirth || null,\n        isMarried: formData.isMarried || false,\n        dateOfMarriage: formData.dateOfMarriage || null,\n\n        // Location Information\n        workingState: formData.workingState || '',\n        domesticState: formData.domesticState || '',\n        district: formData.district || '',\n        address: formData.address || '',\n        workingArea: formData.workingArea || '',\n\n        // Associate Information\n        hasAssociate: formData.hasAssociate || false,\n        associateName: formData.associateName || '',\n        associateRelation: formData.associateRelation || '',\n        associateMobile: formData.associateMobile || '',\n\n        // Digital Presence\n        usingWebsite: formData.usingWebsite || false,\n        usingCRMApp: formData.usingCRMApp || false,\n\n        // Business Information\n        transactionValue: formData.transactionValue ? parseFloat(formData.transactionValue) : null,\n        reraRegistrationNumber: formData.reraRegistrationNumber || '',\n        workingProfiles: Array.isArray(formData.workingProfiles)\n          ? formData.workingProfiles.map(wp => parseInt(wp))\n          : [],\n        starRating: formData.starRating ? parseInt(formData.starRating) : null,\n        source: formData.source || '',\n        remarks: formData.remarks || '',\n\n        // Company Information\n        firmName: formData.firmName || '',\n        numberOfOffices: formData.numberOfOffices ? parseInt(formData.numberOfOffices) : null,\n        numberOfBranches: formData.numberOfBranches ? parseInt(formData.numberOfBranches) : null,\n        totalEmployeeStrength: formData.totalEmployeeStrength ? parseInt(formData.totalEmployeeStrength) : null,\n\n        // Authorized Person\n        authorizedPersonName: formData.authorizedPersonName || '',\n        designation: formData.designation || '',\n\n        // Marketing Information\n        marketingContact: formData.marketingContact || '',\n        marketingDesignation: formData.marketingDesignation || '',\n        placeOfPosting: formData.placeOfPosting || '',\n        department: formData.department || ''\n      };\n\n      // Add optional fields only if they have valid values\n      // Debug: Check what values we have\n      console.log('Form data values for validation:');\n      console.log('primaryEmailId:', `\"${formData.primaryEmailId}\"`);\n      console.log('authorizedPersonEmail:', `\"${formData.authorizedPersonEmail}\"`);\n      console.log('website:', `\"${formData.website}\"`);\n      console.log('websiteLink:', `\"${formData.websiteLink}\"`);\n      console.log('crmAppLink:', `\"${formData.crmAppLink}\"`);\n\n      // Email fields - only add if not null/empty and not just whitespace\n      if (formData.primaryEmailId && formData.primaryEmailId !== null && typeof formData.primaryEmailId === 'string' && formData.primaryEmailId.trim() !== '') {\n        submitData['primaryEmailId'] = formData.primaryEmailId.trim();\n        console.log('Adding primaryEmailId:', formData.primaryEmailId.trim());\n      } else {\n        console.log('Skipping primaryEmailId - empty, null, or invalid');\n      }\n\n      if (formData.authorizedPersonEmail && formData.authorizedPersonEmail !== null && typeof formData.authorizedPersonEmail === 'string' && formData.authorizedPersonEmail.trim() !== '') {\n        submitData['authorizedPersonEmail'] = formData.authorizedPersonEmail.trim();\n        console.log('Adding authorizedPersonEmail:', formData.authorizedPersonEmail.trim());\n      } else {\n        console.log('Skipping authorizedPersonEmail - empty, null, or invalid');\n      }\n\n      // URL fields - only add if not null/empty and not just whitespace\n      if (formData.website && formData.website !== null && typeof formData.website === 'string' && formData.website.trim() !== '') {\n        submitData['website'] = formData.website.trim();\n        console.log('Adding website:', formData.website.trim());\n      } else {\n        console.log('Skipping website - empty, null, or invalid');\n      }\n\n      if (formData.websiteLink && formData.websiteLink !== null && typeof formData.websiteLink === 'string' && formData.websiteLink.trim() !== '') {\n        submitData['websiteLink'] = formData.websiteLink.trim();\n        console.log('Adding websiteLink:', formData.websiteLink.trim());\n      } else {\n        console.log('Skipping websiteLink - empty, null, or invalid');\n      }\n\n      if (formData.crmAppLink && formData.crmAppLink !== null && typeof formData.crmAppLink === 'string' && formData.crmAppLink.trim() !== '') {\n        submitData['crmAppLink'] = formData.crmAppLink.trim();\n        console.log('Adding crmAppLink:', formData.crmAppLink.trim());\n      } else {\n        console.log('Skipping crmAppLink - empty, null, or invalid');\n      }\n\n      // Debug logging\n      console.log('Final submitData object:', submitData);\n      console.log('Fields being sent to API:');\n      Object.keys(submitData).forEach(key => {\n        console.log(`  ${key}:`, typeof submitData[key], `\"${submitData[key]}\"`);\n      });\n      console.log('Selected Division:', selectedDivision);\n      console.log('Selected Category:', selectedCategory);\n      console.log('Selected SubCategory:', selectedSubCategory);\n\n      // Validate required fields before submission\n      const requiredFieldsCheck = {\n        divisionId: submitData.divisionId,\n        categoryId: submitData.categoryId,\n        name: submitData.name,\n        mobileNumber: submitData.mobileNumber,\n        nature: submitData.nature\n      };\n      console.log('Required fields check:', requiredFieldsCheck);\n\n      // Check for missing required fields\n      const missingFields = Object.entries(requiredFieldsCheck)\n        .filter(([, value]) => !value || value === '' || value === null)\n        .map(([key]) => key);\n\n      if (missingFields.length > 0) {\n        console.error('Missing required fields:', missingFields);\n        setErrors({ general: 'Missing required fields: ' + missingFields.join(', ') });\n        return;\n      }\n\n      let result;\n      if (mode === 'create') {\n        result = await apiService.createPerson(submitData);\n      } else {\n        result = await apiService.updatePerson(initialData.id, submitData);\n      }\n\n      onSubmit(result);\n    } catch (error) {\n      console.error('Error submitting form:', error);\n      console.error('Error data:', error.data);\n      console.error('Full error object:', error);\n\n      if (error.data?.errors) {\n        // Handle validation errors from backend\n        const backendErrors = {};\n        Object.keys(error.data.errors).forEach(key => {\n          const errorMessages = error.data.errors[key];\n          backendErrors[key.toLowerCase()] = Array.isArray(errorMessages)\n            ? errorMessages.join(', ')\n            : errorMessages;\n        });\n        console.log('Backend validation errors:', backendErrors);\n        setErrors(backendErrors);\n      } else if (error.isValidationError && error.isValidationError()) {\n        setErrors(error.getValidationErrors());\n      } else {\n        const errorMessage = error.data?.message ||\n                           error.data?.title ||\n                           error.message ||\n                           `HTTP ${error.status || 'Unknown'} - An error occurred while saving`;\n        console.log('General error message:', errorMessage);\n        setErrors({ general: errorMessage });\n      }\n    } finally {\n      setSubmitting(false);\n    }\n  };\n\n  const shouldShowField = (field) => {\n    if (!field.conditional) return true;\n    \n    const conditionValue = formData[field.conditional.field];\n    const expectedValue = field.conditional.value;\n    \n    // Handle boolean conditions\n    if (typeof expectedValue === 'boolean' || expectedValue === 'true' || expectedValue === 'false') {\n      return conditionValue === (expectedValue === true || expectedValue === 'true');\n    }\n    \n    return conditionValue === expectedValue;\n  };\n\n  const groupFieldsBySections = () => {\n    if (!formConfig || !formConfig.fields) return {};\n\n    // Debug: Check for duplicate fields\n    const fieldKeys = formConfig.fields.map(field => field.key);\n    const duplicateKeys = fieldKeys.filter((key, index) => fieldKeys.indexOf(key) !== index);\n    if (duplicateKeys.length > 0) {\n      console.warn('Duplicate field keys found:', duplicateKeys);\n      console.log('All fields:', formConfig.fields);\n    }\n\n    const sections = {};\n    formConfig.fields.forEach((field) => {\n      const sectionKey = field.section || 'general';\n      if (!sections[sectionKey]) {\n        sections[sectionKey] = {\n          title: getSectionTitle(sectionKey),\n          fields: []\n        };\n      }\n      sections[sectionKey].fields.push(field);\n    });\n\n    return sections;\n  };\n\n  const getSectionTitle = (sectionKey) => {\n    const titles = {\n      personalInfo: 'Personal Information',\n      contactInfo: 'Contact Information',\n      locationInfo: 'Location Information',\n      businessInfo: 'Business Information',\n      associateInfo: 'Associate Information',\n      digitalPresence: 'Digital Presence',\n      companyInfo: 'Company Information',\n      authorizedPerson: 'Authorized Person',\n      marketingInfo: 'Marketing Information',\n      general: 'General Information'\n    };\n    return titles[sectionKey] || sectionKey;\n  };\n\n  const sections = formConfig ? groupFieldsBySections() : {};\n\n\n\n  return (\n    <div className=\"dynamic-person-form\">\n      <div className=\"form-header\">\n        <h2>{mode === 'create' ? 'Create New Person' : 'Edit Person'}</h2>\n        {formConfig && (\n          <div className=\"form-info\">\n            <span className=\"form-name\">{formConfig.name}</span>\n            {formConfig.description && (\n              <span className=\"form-description\">{formConfig.description}</span>\n            )}\n          </div>\n        )}\n      </div>\n\n      {errors.general && (\n        <div className=\"alert alert-error\">{errors.general}</div>\n      )}\n\n      <form onSubmit={handleSubmit} className=\"person-form\">\n        {/* Hierarchical Dropdowns */}\n        <div className=\"form-section\">\n          <h3>Division & Category Selection</h3>\n\n          {/* Division Dropdown */}\n          <div className=\"form-group\">\n            <label className=\"form-label\">\n              Division <span className=\"required\">*</span>\n            </label>\n            <select\n              value={selectedDivision}\n              onChange={handleDivisionChange}\n              disabled={loading.divisions}\n              className={`form-select ${errors.division ? 'error' : ''}`}\n              required\n            >\n              <option value=\"\">\n                {loading.divisions ? 'Loading divisions...' : 'Select Division'}\n              </option>\n              {divisions.map(division => (\n                <option key={division.id} value={division.id}>\n                  {division.name}\n                </option>\n              ))}\n            </select>\n            {errors.division && (\n              <div className=\"error-message\">{errors.division}</div>\n            )}\n            {errors.divisions && (\n              <div className=\"error-message\">{errors.divisions}</div>\n            )}\n          </div>\n\n          {/* Category Dropdown */}\n          <div className=\"form-group\">\n            <label className=\"form-label\">\n              Category <span className=\"required\">*</span>\n            </label>\n            <select\n              value={selectedCategory}\n              onChange={handleCategoryChange}\n              disabled={!selectedDivision || loading.categories}\n              className={`form-select ${errors.category ? 'error' : ''}`}\n              required\n            >\n              <option value=\"\">\n                {!selectedDivision\n                  ? 'Select Division first'\n                  : loading.categories\n                    ? 'Loading categories...'\n                    : 'Select Category'\n                }\n              </option>\n              {categories.map(category => (\n                <option key={category.id} value={category.id}>\n                  {category.name}\n                </option>\n              ))}\n            </select>\n            {errors.category && (\n              <div className=\"error-message\">{errors.category}</div>\n            )}\n            {errors.categories && (\n              <div className=\"error-message\">{errors.categories}</div>\n            )}\n          </div>\n\n          {/* Subcategory Dropdown - Only show if category doesn't have a form */}\n          {formAvailability.showSubCategoryDropdown && (\n            <div className=\"form-group\">\n              <label className=\"form-label\">\n                Subcategory (Optional)\n              </label>\n              <select\n                value={selectedSubCategory}\n                onChange={handleSubCategoryChange}\n                disabled={!selectedCategory || loading.subCategories}\n                className=\"form-select\"\n              >\n                <option value=\"\">\n                  {!selectedCategory\n                    ? 'Select Category first'\n                    : loading.subCategories\n                      ? 'Loading subcategories...'\n                      : 'Select Subcategory (Optional)'\n                  }\n                </option>\n                {subCategories.map(subCategory => (\n                  <option key={subCategory.id} value={subCategory.id}>\n                    {subCategory.name}\n                  </option>\n                ))}\n              </select>\n              {errors.subCategories && (\n                <div className=\"error-message\">{errors.subCategories}</div>\n              )}\n            </div>\n          )}\n\n          {/* Form Status Messages */}\n          {loading.form && (\n            <div className=\"loading-message\">\n              <span>Loading form configuration...</span>\n            </div>\n          )}\n\n          {formAvailability.message && !loading.form && (\n            <div className={`status-message ${\n              formAvailability.categoryHasForm || formAvailability.subCategoryHasForm\n                ? 'success'\n                : formAvailability.message.includes('No form')\n                  ? 'error'\n                  : 'info'\n            }`}>\n              {formAvailability.message}\n            </div>\n          )}\n\n          {errors.form && (\n            <div className=\"error-message\">{errors.form}</div>\n          )}\n        </div>\n\n        {/* Dynamic Form Fields - Only show when form is available */}\n        {formConfig && Object.entries(sections).map(([sectionKey, section]) => (\n          <div key={sectionKey} className=\"form-section\">\n            <h3>{section.title}</h3>\n            <div className=\"form-fields\">\n              {section.fields\n                .filter(field => shouldShowField(field))\n                .map((field, fieldIndex) => (\n                  <FormField\n                    key={`${sectionKey}-${field.key}-${fieldIndex}`}\n                    field={field}\n                    value={formData[field.key]}\n                    onChange={(value) => handleFieldChange(field.key, value)}\n                    error={errors[field.key]}\n                  />\n                ))}\n            </div>\n          </div>\n        ))}\n\n\n\n        {/* Form Actions */}\n        <div className=\"form-actions\">\n          <button\n            type=\"button\"\n            onClick={onCancel}\n            className=\"btn btn-outline\"\n            disabled={submitting}\n          >\n            Cancel\n          </button>\n          <button\n            type=\"submit\"\n            className=\"btn btn-primary\"\n            disabled={submitting || !selectedDivision || !selectedCategory}\n          >\n            {submitting\n              ? (mode === 'create' ? 'Creating...' : 'Updating...')\n              : (mode === 'create' ? 'Create Person' : 'Update Person')\n            }\n          </button>\n        </div>\n      </form>\n    </div>\n  );\n};\n\nexport default DynamicPersonForm;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,kBAAkB,EAAEC,YAAY,EAAEC,oBAAoB,EAAEC,oBAAoB,QAAQ,iCAAiC;AAC9H,OAAOC,iBAAiB,MAAM,kCAAkC;AAChE,OAAOC,UAAU,MAAM,2BAA2B;AAClD,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAO,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjC,MAAMC,iBAAiB,GAAGA,CAAC;EAAEC,QAAQ;EAAEC,QAAQ;EAAEC,WAAW,GAAG,IAAI;EAAEC,IAAI,GAAG;AAAS,CAAC,KAAK;EAAAC,EAAA;EACzF;EACA,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACmB,UAAU,EAAEC,aAAa,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACqB,aAAa,EAAEC,gBAAgB,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;;EAEtD;EACA,MAAM,CAACuB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACyB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAAC2B,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;;EAElE;EACA,MAAM,CAAC6B,UAAU,EAAEC,aAAa,CAAC,GAAG9B,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAAC+B,QAAQ,EAAEC,WAAW,CAAC,GAAGhC,QAAQ,CAACK,oBAAoB,CAAC,CAAC,CAAC;EAChE,MAAM,CAAC4B,MAAM,EAAEC,SAAS,CAAC,GAAGlC,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAExC;EACA,MAAM,CAACmC,OAAO,EAAEC,UAAU,CAAC,GAAGpC,QAAQ,CAAC;IACrCiB,SAAS,EAAE,KAAK;IAChBE,UAAU,EAAE,KAAK;IACjBE,aAAa,EAAE,KAAK;IACpBgB,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGvC,QAAQ,CAAC,KAAK,CAAC;;EAEnD;EACA,MAAM,CAACwC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGzC,QAAQ,CAAC;IACvD0C,eAAe,EAAE,KAAK;IACtBC,kBAAkB,EAAE,KAAK;IACzBC,uBAAuB,EAAE,KAAK;IAC9BC,OAAO,EAAE;EACX,CAAC,CAAC;;EAEF;EACA5C,SAAS,CAAC,MAAM;IACd6C,aAAa,CAAC,CAAC;;IAEf;IACAC,mBAAmB,CAAC,CAAC;;IAErB;IACA,IAAIjC,WAAW,EAAE;MAAA,IAAAkC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA;MACflB,WAAW,CAAClB,WAAW,CAAC;MACxBU,mBAAmB,CAAC,EAAAwB,qBAAA,GAAAlC,WAAW,CAACqC,UAAU,cAAAH,qBAAA,uBAAtBA,qBAAA,CAAwBI,QAAQ,CAAC,CAAC,KAAI,EAAE,CAAC;MAC7D1B,mBAAmB,CAAC,EAAAuB,qBAAA,GAAAnC,WAAW,CAACuC,UAAU,cAAAJ,qBAAA,uBAAtBA,qBAAA,CAAwBG,QAAQ,CAAC,CAAC,KAAI,EAAE,CAAC;MAC7DxB,sBAAsB,CAAC,EAAAsB,qBAAA,GAAApC,WAAW,CAACwC,aAAa,cAAAJ,qBAAA,uBAAzBA,qBAAA,CAA2BE,QAAQ,CAAC,CAAC,KAAI,EAAE,CAAC;IACrE,CAAC,MAAM;MACL;MACA,MAAMG,WAAW,GAAGjD,iBAAiB,CAACkD,oBAAoB,CAAC,CAAC;MAC5D;MACA,IAAID,WAAW,CAACE,MAAM,EAAE;QACtBF,WAAW,CAACE,MAAM,GAAGC,iBAAiB,CAACH,WAAW,CAACE,MAAM,CAAC;MAC5D;MACA3B,aAAa,CAACyB,WAAW,CAAC;IAC5B;EACF,CAAC,EAAE,CAACzC,WAAW,CAAC,CAAC;;EAEjB;EACA,MAAMiC,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACFY,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;;MAE1C;MACA,MAAMC,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,2BAA2B;MAC5EL,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEC,OAAO,CAAC;;MAErC;MACA,MAAMI,QAAQ,GAAG,MAAM1D,UAAU,CAAC2D,YAAY,CAAC,CAAC;MAChDP,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEK,QAAQ,CAAC;;MAE7C;MACA,MAAME,YAAY,GAAG,MAAMC,KAAK,CAAC,GAAGP,OAAO,YAAY,CAAC;MACxDF,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEO,YAAY,CAACE,MAAM,CAAC;MAC/DV,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEO,YAAY,CAACG,EAAE,CAAC;IAEzD,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdZ,OAAO,CAACY,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrDZ,OAAO,CAACY,KAAK,CAAC,gBAAgB,EAAE;QAC9B1B,OAAO,EAAE0B,KAAK,CAAC1B,OAAO;QACtBwB,MAAM,EAAEE,KAAK,CAACF,MAAM;QACpBG,IAAI,EAAED,KAAK,CAACC;MACd,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAMd,iBAAiB,GAAID,MAAM,IAAK;IACpC,MAAMgB,IAAI,GAAG,IAAIC,GAAG,CAAC,CAAC;IACtB,MAAMC,YAAY,GAAG,EAAE;IAEvBlB,MAAM,CAACmB,OAAO,CAACC,KAAK,IAAI;MACtB,IAAI,CAACJ,IAAI,CAACK,GAAG,CAACD,KAAK,CAACE,GAAG,CAAC,EAAE;QACxBN,IAAI,CAACO,GAAG,CAACH,KAAK,CAACE,GAAG,CAAC;QACnBJ,YAAY,CAACM,IAAI,CAACJ,KAAK,CAAC;MAC1B,CAAC,MAAM;QACLlB,OAAO,CAACuB,IAAI,CAAC,gDAAgDL,KAAK,CAACE,GAAG,EAAE,CAAC;MAC3E;IACF,CAAC,CAAC;IAEF,IAAItB,MAAM,CAAC0B,MAAM,KAAKR,YAAY,CAACQ,MAAM,EAAE;MACzCxB,OAAO,CAACC,GAAG,CAAC,mCAAmCH,MAAM,CAAC0B,MAAM,cAAcR,YAAY,CAACQ,MAAM,gBAAgB,CAAC;IAChH;IAEA,OAAOR,YAAY;EACrB,CAAC;;EAED;EACA1E,SAAS,CAAC,MAAM;IACd,IAAIsB,gBAAgB,EAAE;MACpB6D,cAAc,CAAC7D,gBAAgB,CAAC;MAChC;MACAG,mBAAmB,CAAC,EAAE,CAAC;MACvBE,sBAAsB,CAAC,EAAE,CAAC;MAC1BN,gBAAgB,CAAC,EAAE,CAAC;MACpBQ,aAAa,CAAC,IAAI,CAAC;MACnBW,mBAAmB,CAAC;QAClBC,eAAe,EAAE,KAAK;QACtBC,kBAAkB,EAAE,KAAK;QACzBC,uBAAuB,EAAE,KAAK;QAC9BC,OAAO,EAAE;MACX,CAAC,CAAC;IACJ,CAAC,MAAM;MACLzB,aAAa,CAAC,EAAE,CAAC;MACjBE,gBAAgB,CAAC,EAAE,CAAC;MACpBI,mBAAmB,CAAC,EAAE,CAAC;MACvBE,sBAAsB,CAAC,EAAE,CAAC;MAC1BE,aAAa,CAAC,IAAI,CAAC;IACrB;EACF,CAAC,EAAE,CAACP,gBAAgB,CAAC,CAAC;;EAEtB;EACAtB,SAAS,CAAC,MAAM;IACd,IAAIwB,gBAAgB,EAAE;MACpB4D,uBAAuB,CAAC5D,gBAAgB,CAAC;MACzC;MACAG,sBAAsB,CAAC,EAAE,CAAC;IAC5B,CAAC,MAAM;MACLN,gBAAgB,CAAC,EAAE,CAAC;MACpBM,sBAAsB,CAAC,EAAE,CAAC;MAC1BE,aAAa,CAAC,IAAI,CAAC;MACnBW,mBAAmB,CAAC;QAClBC,eAAe,EAAE,KAAK;QACtBC,kBAAkB,EAAE,KAAK;QACzBC,uBAAuB,EAAE,KAAK;QAC9BC,OAAO,EAAE;MACX,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACpB,gBAAgB,CAAC,CAAC;;EAEtB;EACAxB,SAAS,CAAC,MAAM;IACd,IAAI0B,mBAAmB,EAAE;MACvB2D,0BAA0B,CAAC3D,mBAAmB,CAAC;IACjD;EACF,CAAC,EAAE,CAACA,mBAAmB,CAAC,CAAC;;EAEzB;EACA,MAAMmB,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChCV,UAAU,CAACmD,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAEtE,SAAS,EAAE;IAAK,CAAC,CAAC,CAAC;IAClD,IAAI;MACF,MAAMgD,QAAQ,GAAG,MAAM1D,UAAU,CAAC2D,YAAY,CAAC,CAAC;MAChDhD,YAAY,CAAC+C,QAAQ,CAACO,IAAI,IAAI,EAAE,CAAC;IACnC,CAAC,CAAC,OAAOD,KAAK,EAAE;MACdZ,OAAO,CAACY,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChDrC,SAAS,CAACqD,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEtE,SAAS,EAAE;MAA2B,CAAC,CAAC,CAAC;IACzE,CAAC,SAAS;MACRmB,UAAU,CAACmD,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEtE,SAAS,EAAE;MAAM,CAAC,CAAC,CAAC;IACrD;EACF,CAAC;;EAED;EACA,MAAMmE,cAAc,GAAG,MAAOjC,UAAU,IAAK;IAC3Cf,UAAU,CAACmD,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAEpE,UAAU,EAAE;IAAK,CAAC,CAAC,CAAC;IACnD,IAAI;MACF,MAAM8C,QAAQ,GAAG,MAAM1D,UAAU,CAACiF,uBAAuB,CAACrC,UAAU,CAAC;MACrE/B,aAAa,CAAC6C,QAAQ,CAACO,IAAI,IAAI,EAAE,CAAC;IACpC,CAAC,CAAC,OAAOD,KAAK,EAAE;MACdZ,OAAO,CAACY,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjDrC,SAAS,CAACqD,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEpE,UAAU,EAAE;MAA4B,CAAC,CAAC,CAAC;MACzEC,aAAa,CAAC,EAAE,CAAC;IACnB,CAAC,SAAS;MACRgB,UAAU,CAACmD,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEpE,UAAU,EAAE;MAAM,CAAC,CAAC,CAAC;IACtD;EACF,CAAC;;EAED;EACA,MAAMsE,iBAAiB,GAAG,MAAOpC,UAAU,IAAK;IAC9CjB,UAAU,CAACmD,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAElE,aAAa,EAAE;IAAK,CAAC,CAAC,CAAC;IACtD,IAAI;MACF,MAAM4C,QAAQ,GAAG,MAAM1D,UAAU,CAACmF,0BAA0B,CAACrC,UAAU,CAAC;MACxE/B,gBAAgB,CAAC2C,QAAQ,CAACO,IAAI,IAAI,EAAE,CAAC;IACvC,CAAC,CAAC,OAAOD,KAAK,EAAE;MACdZ,OAAO,CAACY,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpDrC,SAAS,CAACqD,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAElE,aAAa,EAAE;MAA+B,CAAC,CAAC,CAAC;MAC/EC,gBAAgB,CAAC,EAAE,CAAC;IACtB,CAAC,SAAS;MACRc,UAAU,CAACmD,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAElE,aAAa,EAAE;MAAM,CAAC,CAAC,CAAC;IACzD;EACF,CAAC;;EAED;EACA,MAAMgE,uBAAuB,GAAG,MAAOhC,UAAU,IAAK;IACpDjB,UAAU,CAACmD,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAElD,IAAI,EAAE;IAAK,CAAC,CAAC,CAAC;IAE7C,IAAI;MACF;MACA,MAAMK,eAAe,GAAGpC,iBAAiB,CAACqF,kBAAkB,CAACC,QAAQ,CAACvC,UAAU,CAAC,CAAC;MAElF,IAAIX,eAAe,EAAE;QACnB;QACA,MAAMmD,YAAY,GAAGvF,iBAAiB,CAACwF,cAAc,CAAC,UAAU,EAAEF,QAAQ,CAACvC,UAAU,CAAC,CAAC;QACvF;QACA,IAAIwC,YAAY,IAAIA,YAAY,CAACpC,MAAM,EAAE;UACvCoC,YAAY,CAACpC,MAAM,GAAGC,iBAAiB,CAACmC,YAAY,CAACpC,MAAM,CAAC;QAC9D;QACA3B,aAAa,CAAC+D,YAAY,CAAC;QAC3BpD,mBAAmB,CAAC;UAClBC,eAAe,EAAE,IAAI;UACrBC,kBAAkB,EAAE,KAAK;UACzBC,uBAAuB,EAAE,KAAK;UAC9BC,OAAO,EAAE;QACX,CAAC,CAAC;MACJ,CAAC,MAAM;QACL;QACA,MAAM4C,iBAAiB,CAACpC,UAAU,CAAC;QACnC;QACA,MAAME,WAAW,GAAGjD,iBAAiB,CAACkD,oBAAoB,CAAC,CAAC;QAC5D;QACA,IAAID,WAAW,CAACE,MAAM,EAAE;UACtBF,WAAW,CAACE,MAAM,GAAGC,iBAAiB,CAACH,WAAW,CAACE,MAAM,CAAC;QAC5D;QACA3B,aAAa,CAACyB,WAAW,CAAC;QAC1Bd,mBAAmB,CAAC;UAClBC,eAAe,EAAE,KAAK;UACtBC,kBAAkB,EAAE,KAAK;UACzBC,uBAAuB,EAAE,IAAI;UAC7BC,OAAO,EAAE;QACX,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAO0B,KAAK,EAAE;MACdZ,OAAO,CAACY,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;MAC1DrC,SAAS,CAACqD,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAElD,IAAI,EAAE;MAAmC,CAAC,CAAC,CAAC;IAC5E,CAAC,SAAS;MACRD,UAAU,CAACmD,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAElD,IAAI,EAAE;MAAM,CAAC,CAAC,CAAC;IAChD;EACF,CAAC;;EAED;EACA,MAAMiD,0BAA0B,GAAG,MAAOhC,aAAa,IAAK;IAC1DlB,UAAU,CAACmD,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAElD,IAAI,EAAE;IAAK,CAAC,CAAC,CAAC;IAE7C,IAAI;MACF;MACA,MAAMM,kBAAkB,GAAGrC,iBAAiB,CAACyF,qBAAqB,CAACH,QAAQ,CAACtC,aAAa,CAAC,CAAC;MAE3F,IAAIX,kBAAkB,EAAE;QACtB;QACA,MAAMqD,eAAe,GAAG1F,iBAAiB,CAACwF,cAAc,CAAC,aAAa,EAAEF,QAAQ,CAACtC,aAAa,CAAC,CAAC;QAChG;QACA,IAAI0C,eAAe,IAAIA,eAAe,CAACvC,MAAM,EAAE;UAC7CuC,eAAe,CAACvC,MAAM,GAAGC,iBAAiB,CAACsC,eAAe,CAACvC,MAAM,CAAC;QACpE;QACA3B,aAAa,CAACkE,eAAe,CAAC;QAC9BvD,mBAAmB,CAAC;UAClBC,eAAe,EAAE,KAAK;UACtBC,kBAAkB,EAAE,IAAI;UACxBC,uBAAuB,EAAE,IAAI;UAC7BC,OAAO,EAAE;QACX,CAAC,CAAC;MACJ,CAAC,MAAM;QACL;QACA,MAAMU,WAAW,GAAGjD,iBAAiB,CAACkD,oBAAoB,CAAC,CAAC;QAC5D;QACA,IAAID,WAAW,CAACE,MAAM,EAAE;UACtBF,WAAW,CAACE,MAAM,GAAGC,iBAAiB,CAACH,WAAW,CAACE,MAAM,CAAC;QAC5D;QACA3B,aAAa,CAACyB,WAAW,CAAC;QAC1Bd,mBAAmB,CAAC;UAClBC,eAAe,EAAE,KAAK;UACtBC,kBAAkB,EAAE,KAAK;UACzBC,uBAAuB,EAAE,IAAI;UAC7BC,OAAO,EAAE;QACX,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAO0B,KAAK,EAAE;MACdZ,OAAO,CAACY,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;MAC7DrC,SAAS,CAACqD,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAElD,IAAI,EAAE;MAAmC,CAAC,CAAC,CAAC;IAC5E,CAAC,SAAS;MACRD,UAAU,CAACmD,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAElD,IAAI,EAAE;MAAM,CAAC,CAAC,CAAC;IAChD;EACF,CAAC;;EAED;EACA,MAAM4D,oBAAoB,GAAIC,CAAC,IAAK;IAClC,MAAMC,KAAK,GAAGD,CAAC,CAACE,MAAM,CAACD,KAAK;IAC5B3E,mBAAmB,CAAC2E,KAAK,CAAC;;IAE1B;IACAnE,WAAW,CAACuD,IAAI,KAAK;MACnB,GAAGA,IAAI;MACPpC,UAAU,EAAEgD,KAAK,GAAGP,QAAQ,CAACO,KAAK,CAAC,GAAG,IAAI;MAC1C9C,UAAU,EAAE,IAAI;MAChBC,aAAa,EAAE;IACjB,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAM+C,oBAAoB,GAAIH,CAAC,IAAK;IAClC,MAAMC,KAAK,GAAGD,CAAC,CAACE,MAAM,CAACD,KAAK;IAC5BzE,mBAAmB,CAACyE,KAAK,CAAC;;IAE1B;IACAnE,WAAW,CAACuD,IAAI,KAAK;MACnB,GAAGA,IAAI;MACPlC,UAAU,EAAE8C,KAAK,GAAGP,QAAQ,CAACO,KAAK,CAAC,GAAG,IAAI;MAC1C7C,aAAa,EAAE;IACjB,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMgD,uBAAuB,GAAIJ,CAAC,IAAK;IACrC,MAAMC,KAAK,GAAGD,CAAC,CAACE,MAAM,CAACD,KAAK;IAC5BvE,sBAAsB,CAACuE,KAAK,CAAC;;IAE7B;IACAnE,WAAW,CAACuD,IAAI,KAAK;MACnB,GAAGA,IAAI;MACPjC,aAAa,EAAE6C,KAAK,GAAGP,QAAQ,CAACO,KAAK,CAAC,GAAG;IAC3C,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMI,iBAAiB,GAAGA,CAACC,QAAQ,EAAEL,KAAK,KAAK;IAC7CnE,WAAW,CAACuD,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACiB,QAAQ,GAAGL;IACd,CAAC,CAAC,CAAC;;IAEH;IACA,IAAIlE,MAAM,CAACuE,QAAQ,CAAC,EAAE;MACpBtE,SAAS,CAACqD,IAAI,KAAK;QACjB,GAAGA,IAAI;QACP,CAACiB,QAAQ,GAAG;MACd,CAAC,CAAC,CAAC;IACL;EACF,CAAC;EAED,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,SAAS,GAAG,CAAC,CAAC;;IAEpB;IACA,IAAI,CAACnF,gBAAgB,EAAE;MACrBmF,SAAS,CAACC,QAAQ,GAAG,sBAAsB;IAC7C;IACA,IAAI,CAAClF,gBAAgB,EAAE;MACrBiF,SAAS,CAACE,QAAQ,GAAG,sBAAsB;IAC7C;;IAEA;IACA,IAAI,CAAC/E,UAAU,EAAE;MACf6E,SAAS,CAACrE,IAAI,GAAG,kDAAkD;MACnE,OAAO;QAAEwE,OAAO,EAAE,KAAK;QAAE5E,MAAM,EAAEyE;MAAU,CAAC;IAC9C;;IAIA;IACA7E,UAAU,CAAC4B,MAAM,CAACmB,OAAO,CAACC,KAAK,IAAI;MACjC,MAAMsB,KAAK,GAAGpE,QAAQ,CAAC8C,KAAK,CAACE,GAAG,CAAC;;MAEjC;MACA,IAAIF,KAAK,CAACiC,QAAQ,KAAK,CAACX,KAAK,IAAK,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACY,IAAI,CAAC,CAAC,KAAK,EAAG,CAAC,EAAE;QACpFL,SAAS,CAAC7B,KAAK,CAACE,GAAG,CAAC,GAAG,GAAGF,KAAK,CAACmC,KAAK,cAAc;QACnD;MACF;;MAEA;MACA,IAAInC,KAAK,CAACoC,WAAW,IAAIC,eAAe,CAACrC,KAAK,CAAC,EAAE;QAC/C,IAAIA,KAAK,CAACiC,QAAQ,KAAK,CAACX,KAAK,IAAK,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACY,IAAI,CAAC,CAAC,KAAK,EAAG,CAAC,EAAE;UACpFL,SAAS,CAAC7B,KAAK,CAACE,GAAG,CAAC,GAAG,GAAGF,KAAK,CAACmC,KAAK,cAAc;UACnD;QACF;MACF;;MAEA;MACA,IAAIb,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACY,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;QAAA,IAAAI,iBAAA,EAAAC,kBAAA,EAAAC,kBAAA;QAC7D,QAAQxC,KAAK,CAACyC,IAAI;UAChB,KAAK,OAAO;YACV,MAAMC,UAAU,GAAG,4BAA4B;YAC/C,IAAI,CAACA,UAAU,CAACC,IAAI,CAACrB,KAAK,CAAC,EAAE;cAC3BO,SAAS,CAAC7B,KAAK,CAACE,GAAG,CAAC,GAAG,oCAAoC;YAC7D;YACA;UACF,KAAK,KAAK;YACR,MAAM0C,UAAU,GAAG,qCAAqC;YACxD,IAAI,CAACA,UAAU,CAACD,IAAI,CAACrB,KAAK,CAAC,EAAE;cAC3BO,SAAS,CAAC7B,KAAK,CAACE,GAAG,CAAC,GAAG,oCAAoC;YAC7D;YACA;UACF,KAAK,KAAK;YACR,IAAI;cACF,IAAI2C,GAAG,CAACvB,KAAK,CAAC;YAChB,CAAC,CAAC,MAAM;cACNO,SAAS,CAAC7B,KAAK,CAACE,GAAG,CAAC,GAAG,0BAA0B;YACnD;YACA;UACF,KAAK,QAAQ;YACX,IAAIF,KAAK,CAAC8C,UAAU,EAAE;cACpB,MAAMC,QAAQ,GAAGC,UAAU,CAAC1B,KAAK,CAAC;cAClC,IAAItB,KAAK,CAAC8C,UAAU,CAACG,GAAG,KAAKC,SAAS,IAAIH,QAAQ,GAAG/C,KAAK,CAAC8C,UAAU,CAACG,GAAG,EAAE;gBACzEpB,SAAS,CAAC7B,KAAK,CAACE,GAAG,CAAC,GAAG,0BAA0BF,KAAK,CAAC8C,UAAU,CAACG,GAAG,EAAE;cACzE;cACA,IAAIjD,KAAK,CAAC8C,UAAU,CAACK,GAAG,KAAKD,SAAS,IAAIH,QAAQ,GAAG/C,KAAK,CAAC8C,UAAU,CAACK,GAAG,EAAE;gBACzEtB,SAAS,CAAC7B,KAAK,CAACE,GAAG,CAAC,GAAG,yBAAyBF,KAAK,CAAC8C,UAAU,CAACK,GAAG,EAAE;cACxE;YACF;YACA;QACJ;;QAEA;QACA,KAAAb,iBAAA,GAAItC,KAAK,CAAC8C,UAAU,cAAAR,iBAAA,eAAhBA,iBAAA,CAAkBc,OAAO,EAAE;UAC7B,MAAMC,KAAK,GAAG,IAAIC,MAAM,CAACtD,KAAK,CAAC8C,UAAU,CAACM,OAAO,CAAC;UAClD,IAAI,CAACC,KAAK,CAACV,IAAI,CAACrB,KAAK,CAAC,EAAE;YACtBO,SAAS,CAAC7B,KAAK,CAACE,GAAG,CAAC,GAAG,GAAGF,KAAK,CAACmC,KAAK,oBAAoB;UAC3D;QACF;;QAEA;QACA,IAAI,CAAAI,kBAAA,GAAAvC,KAAK,CAAC8C,UAAU,cAAAP,kBAAA,eAAhBA,kBAAA,CAAkBgB,SAAS,IAAIjC,KAAK,CAAChB,MAAM,GAAGN,KAAK,CAAC8C,UAAU,CAACS,SAAS,EAAE;UAC5E1B,SAAS,CAAC7B,KAAK,CAACE,GAAG,CAAC,GAAG,GAAGF,KAAK,CAACmC,KAAK,qBAAqBnC,KAAK,CAAC8C,UAAU,CAACS,SAAS,aAAa;QACnG;QACA,IAAI,CAAAf,kBAAA,GAAAxC,KAAK,CAAC8C,UAAU,cAAAN,kBAAA,eAAhBA,kBAAA,CAAkBgB,SAAS,IAAIlC,KAAK,CAAChB,MAAM,GAAGN,KAAK,CAAC8C,UAAU,CAACU,SAAS,EAAE;UAC5E3B,SAAS,CAAC7B,KAAK,CAACE,GAAG,CAAC,GAAG,GAAGF,KAAK,CAACmC,KAAK,oBAAoBnC,KAAK,CAAC8C,UAAU,CAACU,SAAS,aAAa;QAClG;MACF;IACF,CAAC,CAAC;;IAEF;IACA,IAAItG,QAAQ,CAACuG,SAAS,IAAIvG,QAAQ,CAACwG,cAAc,IAAIxG,QAAQ,CAACyG,WAAW,EAAE;MACzE,MAAMC,SAAS,GAAG,IAAIC,IAAI,CAAC3G,QAAQ,CAACyG,WAAW,CAAC;MAChD,MAAMG,YAAY,GAAG,IAAID,IAAI,CAAC3G,QAAQ,CAACwG,cAAc,CAAC;MACtD,IAAII,YAAY,IAAIF,SAAS,EAAE;QAC7B/B,SAAS,CAAC6B,cAAc,GAAG,wCAAwC;MACrE;IACF;IAEA,OAAO;MACL1B,OAAO,EAAE+B,MAAM,CAACC,IAAI,CAACnC,SAAS,CAAC,CAACvB,MAAM,KAAK,CAAC;MAC5ClD,MAAM,EAAEyE;IACV,CAAC;EACH,CAAC;EAED,MAAMoC,YAAY,GAAG,MAAO5C,CAAC,IAAK;IAChCA,CAAC,CAAC6C,cAAc,CAAC,CAAC;IAElB,MAAMpB,UAAU,GAAGlB,YAAY,CAAC,CAAC;IACjC,IAAI,CAACkB,UAAU,CAACd,OAAO,EAAE;MACvBlD,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE+D,UAAU,CAAC1F,MAAM,CAAC;MACpDC,SAAS,CAACyF,UAAU,CAAC1F,MAAM,CAAC;MAC5B;IACF;IAEAM,aAAa,CAAC,IAAI,CAAC;IACnB,IAAI;MACF;MACA,MAAMyG,UAAU,GAAG;QACjB;QACA7F,UAAU,EAAEyC,QAAQ,CAACrE,gBAAgB,CAAC;QACtC8B,UAAU,EAAEuC,QAAQ,CAACnE,gBAAgB,CAAC;QACtC6B,aAAa,EAAE3B,mBAAmB,GAAGiE,QAAQ,CAACjE,mBAAmB,CAAC,GAAG,IAAI;QAEzE;QACAsH,IAAI,EAAElH,QAAQ,CAACkH,IAAI,IAAI,EAAE;QACzBC,YAAY,EAAEnH,QAAQ,CAACmH,YAAY,IAAI,EAAE;QACzCC,MAAM,EAAEpH,QAAQ,CAACoH,MAAM,GAAGvD,QAAQ,CAAC7D,QAAQ,CAACoH,MAAM,CAAC,GAAG,IAAI;QAE1D;QACAC,MAAM,EAAErH,QAAQ,CAACqH,MAAM,GAAGxD,QAAQ,CAAC7D,QAAQ,CAACqH,MAAM,CAAC,GAAG,IAAI;QAE1D;QACAC,gBAAgB,EAAEC,KAAK,CAACC,OAAO,CAACxH,QAAQ,CAACsH,gBAAgB,CAAC,GACtDtH,QAAQ,CAACsH,gBAAgB,GACzBtH,QAAQ,CAACsH,gBAAgB,GAAGtH,QAAQ,CAACsH,gBAAgB,CAACG,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,CAAC,IAAIA,CAAC,CAAC3C,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE;QAC5F4C,iBAAiB,EAAEL,KAAK,CAACC,OAAO,CAACxH,QAAQ,CAAC4H,iBAAiB,CAAC,GACxD5H,QAAQ,CAAC4H,iBAAiB,GAC1B5H,QAAQ,CAAC4H,iBAAiB,GAAG5H,QAAQ,CAAC4H,iBAAiB,CAACH,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,CAAC,IAAIA,CAAC,CAAC3C,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE;QAE9F;QACAyB,WAAW,EAAEzG,QAAQ,CAACyG,WAAW,IAAI,IAAI;QACzCF,SAAS,EAAEvG,QAAQ,CAACuG,SAAS,IAAI,KAAK;QACtCC,cAAc,EAAExG,QAAQ,CAACwG,cAAc,IAAI,IAAI;QAE/C;QACAqB,YAAY,EAAE7H,QAAQ,CAAC6H,YAAY,IAAI,EAAE;QACzCC,aAAa,EAAE9H,QAAQ,CAAC8H,aAAa,IAAI,EAAE;QAC3CC,QAAQ,EAAE/H,QAAQ,CAAC+H,QAAQ,IAAI,EAAE;QACjCC,OAAO,EAAEhI,QAAQ,CAACgI,OAAO,IAAI,EAAE;QAC/BC,WAAW,EAAEjI,QAAQ,CAACiI,WAAW,IAAI,EAAE;QAEvC;QACAC,YAAY,EAAElI,QAAQ,CAACkI,YAAY,IAAI,KAAK;QAC5CC,aAAa,EAAEnI,QAAQ,CAACmI,aAAa,IAAI,EAAE;QAC3CC,iBAAiB,EAAEpI,QAAQ,CAACoI,iBAAiB,IAAI,EAAE;QACnDC,eAAe,EAAErI,QAAQ,CAACqI,eAAe,IAAI,EAAE;QAE/C;QACAC,YAAY,EAAEtI,QAAQ,CAACsI,YAAY,IAAI,KAAK;QAC5CC,WAAW,EAAEvI,QAAQ,CAACuI,WAAW,IAAI,KAAK;QAE1C;QACAC,gBAAgB,EAAExI,QAAQ,CAACwI,gBAAgB,GAAG1C,UAAU,CAAC9F,QAAQ,CAACwI,gBAAgB,CAAC,GAAG,IAAI;QAC1FC,sBAAsB,EAAEzI,QAAQ,CAACyI,sBAAsB,IAAI,EAAE;QAC7DC,eAAe,EAAEnB,KAAK,CAACC,OAAO,CAACxH,QAAQ,CAAC0I,eAAe,CAAC,GACpD1I,QAAQ,CAAC0I,eAAe,CAAChB,GAAG,CAACiB,EAAE,IAAI9E,QAAQ,CAAC8E,EAAE,CAAC,CAAC,GAChD,EAAE;QACNC,UAAU,EAAE5I,QAAQ,CAAC4I,UAAU,GAAG/E,QAAQ,CAAC7D,QAAQ,CAAC4I,UAAU,CAAC,GAAG,IAAI;QACtEC,MAAM,EAAE7I,QAAQ,CAAC6I,MAAM,IAAI,EAAE;QAC7BC,OAAO,EAAE9I,QAAQ,CAAC8I,OAAO,IAAI,EAAE;QAE/B;QACAC,QAAQ,EAAE/I,QAAQ,CAAC+I,QAAQ,IAAI,EAAE;QACjCC,eAAe,EAAEhJ,QAAQ,CAACgJ,eAAe,GAAGnF,QAAQ,CAAC7D,QAAQ,CAACgJ,eAAe,CAAC,GAAG,IAAI;QACrFC,gBAAgB,EAAEjJ,QAAQ,CAACiJ,gBAAgB,GAAGpF,QAAQ,CAAC7D,QAAQ,CAACiJ,gBAAgB,CAAC,GAAG,IAAI;QACxFC,qBAAqB,EAAElJ,QAAQ,CAACkJ,qBAAqB,GAAGrF,QAAQ,CAAC7D,QAAQ,CAACkJ,qBAAqB,CAAC,GAAG,IAAI;QAEvG;QACAC,oBAAoB,EAAEnJ,QAAQ,CAACmJ,oBAAoB,IAAI,EAAE;QACzDC,WAAW,EAAEpJ,QAAQ,CAACoJ,WAAW,IAAI,EAAE;QAEvC;QACAC,gBAAgB,EAAErJ,QAAQ,CAACqJ,gBAAgB,IAAI,EAAE;QACjDC,oBAAoB,EAAEtJ,QAAQ,CAACsJ,oBAAoB,IAAI,EAAE;QACzDC,cAAc,EAAEvJ,QAAQ,CAACuJ,cAAc,IAAI,EAAE;QAC7CC,UAAU,EAAExJ,QAAQ,CAACwJ,UAAU,IAAI;MACrC,CAAC;;MAED;MACA;MACA5H,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;MAC/CD,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE,IAAI7B,QAAQ,CAACyJ,cAAc,GAAG,CAAC;MAC9D7H,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE,IAAI7B,QAAQ,CAAC0J,qBAAqB,GAAG,CAAC;MAC5E9H,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE,IAAI7B,QAAQ,CAAC2J,OAAO,GAAG,CAAC;MAChD/H,OAAO,CAACC,GAAG,CAAC,cAAc,EAAE,IAAI7B,QAAQ,CAAC4J,WAAW,GAAG,CAAC;MACxDhI,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE,IAAI7B,QAAQ,CAAC6J,UAAU,GAAG,CAAC;;MAEtD;MACA,IAAI7J,QAAQ,CAACyJ,cAAc,IAAIzJ,QAAQ,CAACyJ,cAAc,KAAK,IAAI,IAAI,OAAOzJ,QAAQ,CAACyJ,cAAc,KAAK,QAAQ,IAAIzJ,QAAQ,CAACyJ,cAAc,CAACzE,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;QACvJiC,UAAU,CAAC,gBAAgB,CAAC,GAAGjH,QAAQ,CAACyJ,cAAc,CAACzE,IAAI,CAAC,CAAC;QAC7DpD,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE7B,QAAQ,CAACyJ,cAAc,CAACzE,IAAI,CAAC,CAAC,CAAC;MACvE,CAAC,MAAM;QACLpD,OAAO,CAACC,GAAG,CAAC,mDAAmD,CAAC;MAClE;MAEA,IAAI7B,QAAQ,CAAC0J,qBAAqB,IAAI1J,QAAQ,CAAC0J,qBAAqB,KAAK,IAAI,IAAI,OAAO1J,QAAQ,CAAC0J,qBAAqB,KAAK,QAAQ,IAAI1J,QAAQ,CAAC0J,qBAAqB,CAAC1E,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;QACnLiC,UAAU,CAAC,uBAAuB,CAAC,GAAGjH,QAAQ,CAAC0J,qBAAqB,CAAC1E,IAAI,CAAC,CAAC;QAC3EpD,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE7B,QAAQ,CAAC0J,qBAAqB,CAAC1E,IAAI,CAAC,CAAC,CAAC;MACrF,CAAC,MAAM;QACLpD,OAAO,CAACC,GAAG,CAAC,0DAA0D,CAAC;MACzE;;MAEA;MACA,IAAI7B,QAAQ,CAAC2J,OAAO,IAAI3J,QAAQ,CAAC2J,OAAO,KAAK,IAAI,IAAI,OAAO3J,QAAQ,CAAC2J,OAAO,KAAK,QAAQ,IAAI3J,QAAQ,CAAC2J,OAAO,CAAC3E,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;QAC3HiC,UAAU,CAAC,SAAS,CAAC,GAAGjH,QAAQ,CAAC2J,OAAO,CAAC3E,IAAI,CAAC,CAAC;QAC/CpD,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE7B,QAAQ,CAAC2J,OAAO,CAAC3E,IAAI,CAAC,CAAC,CAAC;MACzD,CAAC,MAAM;QACLpD,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;MAC3D;MAEA,IAAI7B,QAAQ,CAAC4J,WAAW,IAAI5J,QAAQ,CAAC4J,WAAW,KAAK,IAAI,IAAI,OAAO5J,QAAQ,CAAC4J,WAAW,KAAK,QAAQ,IAAI5J,QAAQ,CAAC4J,WAAW,CAAC5E,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;QAC3IiC,UAAU,CAAC,aAAa,CAAC,GAAGjH,QAAQ,CAAC4J,WAAW,CAAC5E,IAAI,CAAC,CAAC;QACvDpD,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE7B,QAAQ,CAAC4J,WAAW,CAAC5E,IAAI,CAAC,CAAC,CAAC;MACjE,CAAC,MAAM;QACLpD,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;MAC/D;MAEA,IAAI7B,QAAQ,CAAC6J,UAAU,IAAI7J,QAAQ,CAAC6J,UAAU,KAAK,IAAI,IAAI,OAAO7J,QAAQ,CAAC6J,UAAU,KAAK,QAAQ,IAAI7J,QAAQ,CAAC6J,UAAU,CAAC7E,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;QACvIiC,UAAU,CAAC,YAAY,CAAC,GAAGjH,QAAQ,CAAC6J,UAAU,CAAC7E,IAAI,CAAC,CAAC;QACrDpD,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE7B,QAAQ,CAAC6J,UAAU,CAAC7E,IAAI,CAAC,CAAC,CAAC;MAC/D,CAAC,MAAM;QACLpD,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;MAC9D;;MAEA;MACAD,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEoF,UAAU,CAAC;MACnDrF,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;MACxCgF,MAAM,CAACC,IAAI,CAACG,UAAU,CAAC,CAACpE,OAAO,CAACG,GAAG,IAAI;QACrCpB,OAAO,CAACC,GAAG,CAAC,KAAKmB,GAAG,GAAG,EAAE,OAAOiE,UAAU,CAACjE,GAAG,CAAC,EAAE,IAAIiE,UAAU,CAACjE,GAAG,CAAC,GAAG,CAAC;MAC1E,CAAC,CAAC;MACFpB,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAErC,gBAAgB,CAAC;MACnDoC,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEnC,gBAAgB,CAAC;MACnDkC,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEjC,mBAAmB,CAAC;;MAEzD;MACA,MAAMkK,mBAAmB,GAAG;QAC1B1I,UAAU,EAAE6F,UAAU,CAAC7F,UAAU;QACjCE,UAAU,EAAE2F,UAAU,CAAC3F,UAAU;QACjC4F,IAAI,EAAED,UAAU,CAACC,IAAI;QACrBC,YAAY,EAAEF,UAAU,CAACE,YAAY;QACrCC,MAAM,EAAEH,UAAU,CAACG;MACrB,CAAC;MACDxF,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEiI,mBAAmB,CAAC;;MAE1D;MACA,MAAMC,aAAa,GAAGlD,MAAM,CAACmD,OAAO,CAACF,mBAAmB,CAAC,CACtDG,MAAM,CAAC,CAAC,GAAG7F,KAAK,CAAC,KAAK,CAACA,KAAK,IAAIA,KAAK,KAAK,EAAE,IAAIA,KAAK,KAAK,IAAI,CAAC,CAC/DsD,GAAG,CAAC,CAAC,CAAC1E,GAAG,CAAC,KAAKA,GAAG,CAAC;MAEtB,IAAI+G,aAAa,CAAC3G,MAAM,GAAG,CAAC,EAAE;QAC5BxB,OAAO,CAACY,KAAK,CAAC,0BAA0B,EAAEuH,aAAa,CAAC;QACxD5J,SAAS,CAAC;UAAE+J,OAAO,EAAE,2BAA2B,GAAGH,aAAa,CAACI,IAAI,CAAC,IAAI;QAAE,CAAC,CAAC;QAC9E;MACF;MAEA,IAAIC,MAAM;MACV,IAAIpL,IAAI,KAAK,QAAQ,EAAE;QACrBoL,MAAM,GAAG,MAAM5L,UAAU,CAAC6L,YAAY,CAACpD,UAAU,CAAC;MACpD,CAAC,MAAM;QACLmD,MAAM,GAAG,MAAM5L,UAAU,CAAC8L,YAAY,CAACvL,WAAW,CAACwL,EAAE,EAAEtD,UAAU,CAAC;MACpE;MAEApI,QAAQ,CAACuL,MAAM,CAAC;IAClB,CAAC,CAAC,OAAO5H,KAAK,EAAE;MAAA,IAAAgI,WAAA;MACd5I,OAAO,CAACY,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9CZ,OAAO,CAACY,KAAK,CAAC,aAAa,EAAEA,KAAK,CAACC,IAAI,CAAC;MACxCb,OAAO,CAACY,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAE1C,KAAAgI,WAAA,GAAIhI,KAAK,CAACC,IAAI,cAAA+H,WAAA,eAAVA,WAAA,CAAYtK,MAAM,EAAE;QACtB;QACA,MAAMuK,aAAa,GAAG,CAAC,CAAC;QACxB5D,MAAM,CAACC,IAAI,CAACtE,KAAK,CAACC,IAAI,CAACvC,MAAM,CAAC,CAAC2C,OAAO,CAACG,GAAG,IAAI;UAC5C,MAAM0H,aAAa,GAAGlI,KAAK,CAACC,IAAI,CAACvC,MAAM,CAAC8C,GAAG,CAAC;UAC5CyH,aAAa,CAACzH,GAAG,CAAC2H,WAAW,CAAC,CAAC,CAAC,GAAGpD,KAAK,CAACC,OAAO,CAACkD,aAAa,CAAC,GAC3DA,aAAa,CAACP,IAAI,CAAC,IAAI,CAAC,GACxBO,aAAa;QACnB,CAAC,CAAC;QACF9I,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE4I,aAAa,CAAC;QACxDtK,SAAS,CAACsK,aAAa,CAAC;MAC1B,CAAC,MAAM,IAAIjI,KAAK,CAACoI,iBAAiB,IAAIpI,KAAK,CAACoI,iBAAiB,CAAC,CAAC,EAAE;QAC/DzK,SAAS,CAACqC,KAAK,CAACqI,mBAAmB,CAAC,CAAC,CAAC;MACxC,CAAC,MAAM;QAAA,IAAAC,YAAA,EAAAC,YAAA;QACL,MAAMC,YAAY,GAAG,EAAAF,YAAA,GAAAtI,KAAK,CAACC,IAAI,cAAAqI,YAAA,uBAAVA,YAAA,CAAYhK,OAAO,OAAAiK,YAAA,GACrBvI,KAAK,CAACC,IAAI,cAAAsI,YAAA,uBAAVA,YAAA,CAAYE,KAAK,KACjBzI,KAAK,CAAC1B,OAAO,IACb,QAAQ0B,KAAK,CAACF,MAAM,IAAI,SAAS,mCAAmC;QACvFV,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEmJ,YAAY,CAAC;QACnD7K,SAAS,CAAC;UAAE+J,OAAO,EAAEc;QAAa,CAAC,CAAC;MACtC;IACF,CAAC,SAAS;MACRxK,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;EAED,MAAM2E,eAAe,GAAIrC,KAAK,IAAK;IACjC,IAAI,CAACA,KAAK,CAACoC,WAAW,EAAE,OAAO,IAAI;IAEnC,MAAMgG,cAAc,GAAGlL,QAAQ,CAAC8C,KAAK,CAACoC,WAAW,CAACpC,KAAK,CAAC;IACxD,MAAMqI,aAAa,GAAGrI,KAAK,CAACoC,WAAW,CAACd,KAAK;;IAE7C;IACA,IAAI,OAAO+G,aAAa,KAAK,SAAS,IAAIA,aAAa,KAAK,MAAM,IAAIA,aAAa,KAAK,OAAO,EAAE;MAC/F,OAAOD,cAAc,MAAMC,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,MAAM,CAAC;IAChF;IAEA,OAAOD,cAAc,KAAKC,aAAa;EACzC,CAAC;EAED,MAAMC,qBAAqB,GAAGA,CAAA,KAAM;IAClC,IAAI,CAACtL,UAAU,IAAI,CAACA,UAAU,CAAC4B,MAAM,EAAE,OAAO,CAAC,CAAC;;IAEhD;IACA,MAAM2J,SAAS,GAAGvL,UAAU,CAAC4B,MAAM,CAACgG,GAAG,CAAC5E,KAAK,IAAIA,KAAK,CAACE,GAAG,CAAC;IAC3D,MAAMsI,aAAa,GAAGD,SAAS,CAACpB,MAAM,CAAC,CAACjH,GAAG,EAAEuI,KAAK,KAAKF,SAAS,CAACG,OAAO,CAACxI,GAAG,CAAC,KAAKuI,KAAK,CAAC;IACxF,IAAID,aAAa,CAAClI,MAAM,GAAG,CAAC,EAAE;MAC5BxB,OAAO,CAACuB,IAAI,CAAC,6BAA6B,EAAEmI,aAAa,CAAC;MAC1D1J,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE/B,UAAU,CAAC4B,MAAM,CAAC;IAC/C;IAEA,MAAM+J,QAAQ,GAAG,CAAC,CAAC;IACnB3L,UAAU,CAAC4B,MAAM,CAACmB,OAAO,CAAEC,KAAK,IAAK;MACnC,MAAM4I,UAAU,GAAG5I,KAAK,CAAC6I,OAAO,IAAI,SAAS;MAC7C,IAAI,CAACF,QAAQ,CAACC,UAAU,CAAC,EAAE;QACzBD,QAAQ,CAACC,UAAU,CAAC,GAAG;UACrBT,KAAK,EAAEW,eAAe,CAACF,UAAU,CAAC;UAClChK,MAAM,EAAE;QACV,CAAC;MACH;MACA+J,QAAQ,CAACC,UAAU,CAAC,CAAChK,MAAM,CAACwB,IAAI,CAACJ,KAAK,CAAC;IACzC,CAAC,CAAC;IAEF,OAAO2I,QAAQ;EACjB,CAAC;EAED,MAAMG,eAAe,GAAIF,UAAU,IAAK;IACtC,MAAMG,MAAM,GAAG;MACbC,YAAY,EAAE,sBAAsB;MACpCC,WAAW,EAAE,qBAAqB;MAClCC,YAAY,EAAE,sBAAsB;MACpCC,YAAY,EAAE,sBAAsB;MACpCC,aAAa,EAAE,uBAAuB;MACtCC,eAAe,EAAE,kBAAkB;MACnCC,WAAW,EAAE,qBAAqB;MAClCC,gBAAgB,EAAE,mBAAmB;MACrCC,aAAa,EAAE,uBAAuB;MACtCpC,OAAO,EAAE;IACX,CAAC;IACD,OAAO2B,MAAM,CAACH,UAAU,CAAC,IAAIA,UAAU;EACzC,CAAC;EAED,MAAMD,QAAQ,GAAG3L,UAAU,GAAGsL,qBAAqB,CAAC,CAAC,GAAG,CAAC,CAAC;EAI1D,oBACEzM,OAAA;IAAK4N,SAAS,EAAC,qBAAqB;IAAAC,QAAA,gBAClC7N,OAAA;MAAK4N,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1B7N,OAAA;QAAA6N,QAAA,EAAKxN,IAAI,KAAK,QAAQ,GAAG,mBAAmB,GAAG;MAAa;QAAAyN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,EACjE9M,UAAU,iBACTnB,OAAA;QAAK4N,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxB7N,OAAA;UAAM4N,SAAS,EAAC,WAAW;UAAAC,QAAA,EAAE1M,UAAU,CAACoH;QAAI;UAAAuF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,EACnD9M,UAAU,CAAC+M,WAAW,iBACrBlO,OAAA;UAAM4N,SAAS,EAAC,kBAAkB;UAAAC,QAAA,EAAE1M,UAAU,CAAC+M;QAAW;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAClE;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAEL1M,MAAM,CAACgK,OAAO,iBACbvL,OAAA;MAAK4N,SAAS,EAAC,mBAAmB;MAAAC,QAAA,EAAEtM,MAAM,CAACgK;IAAO;MAAAuC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CACzD,eAEDjO,OAAA;MAAME,QAAQ,EAAEkI,YAAa;MAACwF,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAEnD7N,OAAA;QAAK4N,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3B7N,OAAA;UAAA6N,QAAA,EAAI;QAA6B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAGtCjO,OAAA;UAAK4N,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzB7N,OAAA;YAAO4N,SAAS,EAAC,YAAY;YAAAC,QAAA,GAAC,WACnB,eAAA7N,OAAA;cAAM4N,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC,eACRjO,OAAA;YACEyF,KAAK,EAAE5E,gBAAiB;YACxBsN,QAAQ,EAAE5I,oBAAqB;YAC/B6I,QAAQ,EAAE3M,OAAO,CAAClB,SAAU;YAC5BqN,SAAS,EAAE,eAAerM,MAAM,CAAC0E,QAAQ,GAAG,OAAO,GAAG,EAAE,EAAG;YAC3DG,QAAQ;YAAAyH,QAAA,gBAER7N,OAAA;cAAQyF,KAAK,EAAC,EAAE;cAAAoI,QAAA,EACbpM,OAAO,CAAClB,SAAS,GAAG,sBAAsB,GAAG;YAAiB;cAAAuN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzD,CAAC,EACR1N,SAAS,CAACwI,GAAG,CAAC9C,QAAQ,iBACrBjG,OAAA;cAA0ByF,KAAK,EAAEQ,QAAQ,CAAC2F,EAAG;cAAAiC,QAAA,EAC1C5H,QAAQ,CAACsC;YAAI,GADHtC,QAAQ,CAAC2F,EAAE;cAAAkC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEhB,CACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,EACR1M,MAAM,CAAC0E,QAAQ,iBACdjG,OAAA;YAAK4N,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAEtM,MAAM,CAAC0E;UAAQ;YAAA6H,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACtD,EACA1M,MAAM,CAAChB,SAAS,iBACfP,OAAA;YAAK4N,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAEtM,MAAM,CAAChB;UAAS;YAAAuN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACvD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGNjO,OAAA;UAAK4N,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzB7N,OAAA;YAAO4N,SAAS,EAAC,YAAY;YAAAC,QAAA,GAAC,WACnB,eAAA7N,OAAA;cAAM4N,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC,eACRjO,OAAA;YACEyF,KAAK,EAAE1E,gBAAiB;YACxBoN,QAAQ,EAAExI,oBAAqB;YAC/ByI,QAAQ,EAAE,CAACvN,gBAAgB,IAAIY,OAAO,CAAChB,UAAW;YAClDmN,SAAS,EAAE,eAAerM,MAAM,CAAC2E,QAAQ,GAAG,OAAO,GAAG,EAAE,EAAG;YAC3DE,QAAQ;YAAAyH,QAAA,gBAER7N,OAAA;cAAQyF,KAAK,EAAC,EAAE;cAAAoI,QAAA,EACb,CAAChN,gBAAgB,GACd,uBAAuB,GACvBY,OAAO,CAAChB,UAAU,GAChB,uBAAuB,GACvB;YAAiB;cAAAqN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEjB,CAAC,EACRxN,UAAU,CAACsI,GAAG,CAAC7C,QAAQ,iBACtBlG,OAAA;cAA0ByF,KAAK,EAAES,QAAQ,CAAC0F,EAAG;cAAAiC,QAAA,EAC1C3H,QAAQ,CAACqC;YAAI,GADHrC,QAAQ,CAAC0F,EAAE;cAAAkC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEhB,CACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,EACR1M,MAAM,CAAC2E,QAAQ,iBACdlG,OAAA;YAAK4N,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAEtM,MAAM,CAAC2E;UAAQ;YAAA4H,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACtD,EACA1M,MAAM,CAACd,UAAU,iBAChBT,OAAA;YAAK4N,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAEtM,MAAM,CAACd;UAAU;YAAAqN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACxD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,EAGLnM,gBAAgB,CAACI,uBAAuB,iBACvClC,OAAA;UAAK4N,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzB7N,OAAA;YAAO4N,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAE9B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRjO,OAAA;YACEyF,KAAK,EAAExE,mBAAoB;YAC3BkN,QAAQ,EAAEvI,uBAAwB;YAClCwI,QAAQ,EAAE,CAACrN,gBAAgB,IAAIU,OAAO,CAACd,aAAc;YACrDiN,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAEvB7N,OAAA;cAAQyF,KAAK,EAAC,EAAE;cAAAoI,QAAA,EACb,CAAC9M,gBAAgB,GACd,uBAAuB,GACvBU,OAAO,CAACd,aAAa,GACnB,0BAA0B,GAC1B;YAA+B;cAAAmN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAE/B,CAAC,EACRtN,aAAa,CAACoI,GAAG,CAACsF,WAAW,iBAC5BrO,OAAA;cAA6ByF,KAAK,EAAE4I,WAAW,CAACzC,EAAG;cAAAiC,QAAA,EAChDQ,WAAW,CAAC9F;YAAI,GADN8F,WAAW,CAACzC,EAAE;cAAAkC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEnB,CACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,EACR1M,MAAM,CAACZ,aAAa,iBACnBX,OAAA;YAAK4N,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAEtM,MAAM,CAACZ;UAAa;YAAAmN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAC3D;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN,EAGAxM,OAAO,CAACE,IAAI,iBACX3B,OAAA;UAAK4N,SAAS,EAAC,iBAAiB;UAAAC,QAAA,eAC9B7N,OAAA;YAAA6N,QAAA,EAAM;UAA6B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CACN,EAEAnM,gBAAgB,CAACK,OAAO,IAAI,CAACV,OAAO,CAACE,IAAI,iBACxC3B,OAAA;UAAK4N,SAAS,EAAE,kBACd9L,gBAAgB,CAACE,eAAe,IAAIF,gBAAgB,CAACG,kBAAkB,GACnE,SAAS,GACTH,gBAAgB,CAACK,OAAO,CAACmM,QAAQ,CAAC,SAAS,CAAC,GAC1C,OAAO,GACP,MAAM,EACX;UAAAT,QAAA,EACA/L,gBAAgB,CAACK;QAAO;UAAA2L,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CACN,EAEA1M,MAAM,CAACI,IAAI,iBACV3B,OAAA;UAAK4N,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAEtM,MAAM,CAACI;QAAI;UAAAmM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAClD;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,EAGL9M,UAAU,IAAI+G,MAAM,CAACmD,OAAO,CAACyB,QAAQ,CAAC,CAAC/D,GAAG,CAAC,CAAC,CAACgE,UAAU,EAAEC,OAAO,CAAC,kBAChEhN,OAAA;QAAsB4N,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC5C7N,OAAA;UAAA6N,QAAA,EAAKb,OAAO,CAACV;QAAK;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACxBjO,OAAA;UAAK4N,SAAS,EAAC,aAAa;UAAAC,QAAA,EACzBb,OAAO,CAACjK,MAAM,CACZuI,MAAM,CAACnH,KAAK,IAAIqC,eAAe,CAACrC,KAAK,CAAC,CAAC,CACvC4E,GAAG,CAAC,CAAC5E,KAAK,EAAEoK,UAAU,kBACrBvO,OAAA,CAACF,SAAS;YAERqE,KAAK,EAAEA,KAAM;YACbsB,KAAK,EAAEpE,QAAQ,CAAC8C,KAAK,CAACE,GAAG,CAAE;YAC3B8J,QAAQ,EAAG1I,KAAK,IAAKI,iBAAiB,CAAC1B,KAAK,CAACE,GAAG,EAAEoB,KAAK,CAAE;YACzD5B,KAAK,EAAEtC,MAAM,CAAC4C,KAAK,CAACE,GAAG;UAAE,GAJpB,GAAG0I,UAAU,IAAI5I,KAAK,CAACE,GAAG,IAAIkK,UAAU,EAAE;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAKhD,CACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA,GAdElB,UAAU;QAAAe,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAef,CACN,CAAC,eAKFjO,OAAA;QAAK4N,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3B7N,OAAA;UACE4G,IAAI,EAAC,QAAQ;UACb4H,OAAO,EAAErO,QAAS;UAClByN,SAAS,EAAC,iBAAiB;UAC3BQ,QAAQ,EAAExM,UAAW;UAAAiM,QAAA,EACtB;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTjO,OAAA;UACE4G,IAAI,EAAC,QAAQ;UACbgH,SAAS,EAAC,iBAAiB;UAC3BQ,QAAQ,EAAExM,UAAU,IAAI,CAACf,gBAAgB,IAAI,CAACE,gBAAiB;UAAA8M,QAAA,EAE9DjM,UAAU,GACNvB,IAAI,KAAK,QAAQ,GAAG,aAAa,GAAG,aAAa,GACjDA,IAAI,KAAK,QAAQ,GAAG,eAAe,GAAG;QAAgB;UAAAyN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAErD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAAC3N,EAAA,CAj4BIL,iBAAiB;AAAAwO,EAAA,GAAjBxO,iBAAiB;AAm4BvB,eAAeA,iBAAiB;AAAC,IAAAwO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}