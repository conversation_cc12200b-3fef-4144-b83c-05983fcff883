import React, { useState, useEffect } from 'react';
import axios from 'axios';
import Pagination from './Pagination';

const API_BASE_URL = 'http://localhost:5000/api';

const CategoryManagement = () => {
  const [divisions, setDivisions] = useState([]);
  const [categories, setCategories] = useState([]);
  const [allCategories, setAllCategories] = useState([]);
  const [subCategories, setSubCategories] = useState([]);
  const [selectedDivision, setSelectedDivision] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [newCategory, setNewCategory] = useState('');
  const [newSubCategory, setNewSubCategory] = useState('');
  const [loading, setLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);

  useEffect(() => {
    fetchDivisions();
    fetchAllCategories();
  }, []);

  useEffect(() => {
    if (selectedDivision) {
      fetchCategories(selectedDivision);
      setSelectedCategory(''); // Reset category selection when division changes
    } else {
      setCategories([]);
      setSelectedCategory('');
    }
  }, [selectedDivision]);

  useEffect(() => {
    if (selectedCategory) {
      fetchSubCategories(selectedCategory);
    }
  }, [selectedCategory]);

  const fetchDivisions = async () => {
    try {
      const response = await axios.get(`${API_BASE_URL}/divisions`);
      setDivisions(response.data);
    } catch (error) {
      console.error('Error fetching divisions:', error);
    }
  };

  const fetchCategories = async (divisionId) => {
    try {
      const response = await axios.get(`${API_BASE_URL}/categories/division/${divisionId}`);
      setCategories(response.data);
    } catch (error) {
      console.error('Error fetching categories:', error);
    }
  };

  const fetchAllCategories = async () => {
    try {
      const response = await axios.get(`${API_BASE_URL}/categories`);
      setAllCategories(response.data);
    } catch (error) {
      console.error('Error fetching all categories:', error);
    }
  };

  const fetchSubCategories = async (categoryId) => {
    try {
      const response = await axios.get(`${API_BASE_URL}/categories/${categoryId}`);
      setSubCategories(response.data.subCategories || []);
    } catch (error) {
      console.error('Error fetching sub-categories:', error);
    }
  };

  const handleCategorySubmit = async (e) => {
    e.preventDefault();
    if (!newCategory.trim() || !selectedDivision) return;

    setLoading(true);
    try {
      await axios.post(`${API_BASE_URL}/categories`, {
        Name: newCategory.trim(),
        DivisionId: parseInt(selectedDivision)
      });
      setNewCategory('');
      fetchCategories(selectedDivision);
      fetchAllCategories(); // Refresh the overview
      alert('Category created successfully!');
    } catch (error) {
      console.error('Error creating category:', error);
      if (error.response?.data?.errors) {
        const errorMessages = Object.values(error.response.data.errors).flat().join(', ');
        alert(`Error creating category: ${errorMessages}`);
      } else if (error.response?.data?.message) {
        alert(`Error creating category: ${error.response.data.message}`);
      } else {
        alert('Error creating category. Please try again.');
      }
    } finally {
      setLoading(false);
    }
  };

  const handleSubCategorySubmit = async (e) => {
    e.preventDefault();
    if (!newSubCategory.trim() || !selectedCategory) return;

    setLoading(true);
    try {
      await axios.post(`${API_BASE_URL}/subcategories`, {
        Name: newSubCategory.trim(),
        CategoryId: parseInt(selectedCategory)
      });
      setNewSubCategory('');
      fetchSubCategories(selectedCategory);
      fetchAllCategories(); // Refresh the overview
      alert('Sub-category created successfully!');
    } catch (error) {
      console.error('Error creating sub-category:', error);
      if (error.response?.data?.errors) {
        const errorMessages = Object.values(error.response.data.errors).flat().join(', ');
        alert(`Error creating sub-category: ${errorMessages}`);
      } else if (error.response?.data?.message) {
        alert(`Error creating sub-category: ${error.response.data.message}`);
      } else {
        alert('Error creating sub-category. Please try again.');
      }
    } finally {
      setLoading(false);
    }
  };

  // Calculate pagination for categories overview
  const totalItems = allCategories.length;
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentItems = allCategories.slice(startIndex, endIndex);

  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  return (
    <div>
      <h1>Category Management</h1>

      <div className="row">
        <div className="col">
          <div className="card">
            <h3>Add Category</h3>
            <div className="form-group">
              <label htmlFor="divisionSelect">Select Division</label>
              <select
                id="divisionSelect"
                className="form-control"
                value={selectedDivision}
                onChange={(e) => setSelectedDivision(e.target.value)}
              >
                <option value="">Select Division</option>
                {divisions.map((division) => (
                  <option key={division.id} value={division.id}>
                    {division.name}
                  </option>
                ))}
              </select>
            </div>

            <form onSubmit={handleCategorySubmit}>
              <div className="form-group">
                <label htmlFor="categoryName">Category Name</label>
                <input
                  type="text"
                  id="categoryName"
                  className="form-control"
                  value={newCategory}
                  onChange={(e) => setNewCategory(e.target.value)}
                  placeholder="Enter category name"
                  required
                />
              </div>
              <button
                type="submit"
                className="btn btn-primary"
                disabled={loading || !selectedDivision}
              >
                {loading ? 'Adding...' : 'Add Category'}
              </button>
            </form>
          </div>
        </div>

        <div className="col">
          <div className="card">
            <h3>Add Sub-Category</h3>
            <div className="form-group">
              <label htmlFor="categorySelect">Select Category</label>
              <select
                id="categorySelect"
                className="form-control"
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
              >
                <option value="">Select Category</option>
                {categories.map((category) => (
                  <option key={category.id} value={category.id}>
                    {category.name}
                  </option>
                ))}
              </select>
            </div>

            <form onSubmit={handleSubCategorySubmit}>
              <div className="form-group">
                <label htmlFor="subCategoryName">Sub-Category Name</label>
                <input
                  type="text"
                  id="subCategoryName"
                  className="form-control"
                  value={newSubCategory}
                  onChange={(e) => setNewSubCategory(e.target.value)}
                  placeholder="Enter sub-category name"
                  required
                />
              </div>
              <button
                type="submit"
                className="btn btn-primary"
                disabled={loading || !selectedCategory}
              >
                {loading ? 'Adding...' : 'Add Sub-Category'}
              </button>
            </form>
          </div>
        </div>
      </div>

      <div className="card">
        <h3>Categories Overview</h3>
        {allCategories.length === 0 ? (
          <p>No categories found. Add some categories to see them here.</p>
        ) : (
          <>
            <table className="table">
              <thead>
                <tr>
                  <th>Division</th>
                  <th>Category</th>
                  <th>Sub-Categories</th>
                </tr>
              </thead>
              <tbody>
                {currentItems.map((category) => (
                  <tr key={category.id}>
                    <td>{category.division?.name || 'Unknown Division'}</td>
                    <td>{category.name}</td>
                    <td>
                      {category.subCategories && category.subCategories.length > 0
                        ? category.subCategories.map(sub => sub.name).join(', ')
                        : 'No sub-categories'
                      }
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
            <Pagination
              currentPage={currentPage}
              totalItems={totalItems}
              itemsPerPage={itemsPerPage}
              onPageChange={handlePageChange}
            />
          </>
        )}
      </div>
    </div>
  );
};

export default CategoryManagement;