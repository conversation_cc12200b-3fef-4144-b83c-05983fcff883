# Person Management System

## Overview
A comprehensive Person management system implemented following the established Repository pattern architecture with proper error handling, validation, and database optimization.

## Database Schema

### Person Entity
The `Person` entity includes the following relationships and fields:

#### Foreign Key Relationships
- `DivisionId` (required) - Foreign key to Division table
- `CategoryId` (required) - Foreign key to Category table  
- `SubCategoryId` (optional) - Foreign key to SubCategory table

#### Required Fields
- `DivisionId` (int, required)
- `CategoryId` (int, required)
- `Name` (string, required, max 255 characters)
- `MobileNumber` (string, required, phone number format validation)
- `Nature` (enum: Business, Corporate, Agriculture, Individual)

#### Complete Field List
- **Personal Information**: Name, Gender, DateOfBirth, IsMarried, DateOfMarriage
- **Contact Information**: MobileNumber, AlternateNumbers, PrimaryEmailId, AlternateEmailIds, Website
- **Location Information**: WorkingState, DomesticState, District, Address, WorkingArea
- **Associate Information**: HasAssociate, AssociateName, AssociateRelation, AssociateMobile
- **Digital Presence**: UsingWebsite, WebsiteLink, UsingCRMApp, CRMAppLink
- **Business Information**: TransactionValue, RERARegistrationNumber, WorkingProfiles, StarRating, Source, Remarks
- **Company Information**: FirmName, NumberOfOffices, NumberOfBranches, TotalEmployeeStrength
- **Authorized Person**: AuthorizedPersonName, AuthorizedPersonEmail, Designation
- **Marketing Information**: MarketingContact, MarketingDesignation, PlaceOfPosting, Department
- **Audit Fields**: CreatedAt, UpdatedAt, IsDeleted, DeletedAt

## Implementation Structure

### Models (`backend/Models/Person/`)
- `Person.cs` - Main entity model
- `PersonEnums.cs` - Enums for PersonNature, Gender, WorkingProfile
- `CreatePersonRequest.cs` - DTO for creating persons with comprehensive validation
- `UpdatePersonRequest.cs` - DTO for updating persons with optional fields
- `PersonResponse.cs` - DTO for API responses with display properties
- `PersonSearchRequest.cs` - DTO for search and filtering with pagination
- `PersonSearchResponse.cs` - DTO for paginated search results

### Repository Layer (`backend/Repositories/Person/`)
- `IPersonRepository.cs` - Repository interface with comprehensive CRUD operations
- `PersonRepository.cs` - Repository implementation with:
  - Basic CRUD operations with soft delete support
  - Advanced search and filtering capabilities
  - Validation operations (mobile number, email uniqueness)
  - Statistics operations (counts, averages, totals)
  - Bulk operations for efficiency

### Service Layer (`backend/Services/Person/`)
- `IPersonService.cs` - Service interface with business logic operations
- `PersonService.cs` - Service implementation with:
  - Business rule validation
  - Conditional field validation
  - Data transformation between DTOs and entities
  - Statistics calculation
  - Bulk operations with validation

### Controller (`backend/Controllers/PersonsController.cs`)
- Full CRUD operations
- Advanced search with filters and pagination
- Bulk operations (create, soft delete, restore)
- Statistics endpoints
- Enum endpoints for dropdown data

## Database Optimizations

### Indexes
- **Single Column Indexes**: DivisionId, CategoryId, SubCategoryId, MobileNumber, PrimaryEmailId, Name, Nature, WorkingState, District, CreatedAt, IsDeleted, StarRating
- **Composite Indexes**: 
  - Division + Category
  - Division + Category + MobileNumber
  - IsDeleted + CreatedAt
  - Nature + IsDeleted
- **Unique Constraint**: MobileNumber + DivisionId + CategoryId + IsDeleted (filtered for non-deleted records)

### Performance Features
- Soft delete implementation with filtered indexes
- Efficient pagination in search operations
- Optimized queries with proper includes for related data
- Bulk operations to reduce database round trips

## Validation Features

### Data Validation
- **Phone Number**: Indian mobile number format validation
- **Email**: Standard email format validation for primary and alternate emails
- **URL**: Website and CRM app link validation
- **Date Logic**: Birth date and marriage date validation
- **Conditional Required Fields**: Associate details, website links, CRM app links

### Business Rule Validation
- **Unique Mobile Numbers**: Within same division and category
- **Conditional Requirements**: Marriage date when married, associate details when has associate
- **Date Consistency**: Marriage date after birth date, no future dates
- **Array Validation**: Alternate numbers and emails format validation

## API Endpoints

### Basic CRUD
- `GET /api/persons` - Get all persons
- `GET /api/persons/{id}` - Get person by ID
- `POST /api/persons` - Create new person
- `PUT /api/persons/{id}` - Update person
- `DELETE /api/persons/{id}` - Delete person permanently
- `POST /api/persons/{id}/soft-delete` - Soft delete person
- `POST /api/persons/{id}/restore` - Restore soft-deleted person

### Search and Filter
- `POST /api/persons/search` - Advanced search with filters and pagination
- `GET /api/persons/division/{divisionId}` - Get persons by division
- `GET /api/persons/category/{categoryId}` - Get persons by category
- `GET /api/persons/subcategory/{subCategoryId}` - Get persons by subcategory
- `GET /api/persons/mobile/{mobileNumber}` - Search by mobile number

### Statistics
- `GET /api/persons/statistics` - Overall person statistics
- `GET /api/persons/statistics/division/{divisionId}` - Division-specific statistics
- `GET /api/persons/statistics/category/{categoryId}` - Category-specific statistics

### Bulk Operations
- `POST /api/persons/bulk` - Create multiple persons
- `POST /api/persons/bulk/soft-delete` - Soft delete multiple persons
- `POST /api/persons/bulk/restore` - Restore multiple persons

### Utility
- `GET /api/persons/enums` - Get enum values for dropdowns

## Error Handling
- Uses existing exception handling system (BusinessException, ValidationException, NotFoundException)
- Comprehensive validation with detailed error messages
- Proper HTTP status codes for different scenarios
- Global exception middleware integration

## Features
- **Soft Delete**: Persons can be soft deleted and restored
- **Audit Trail**: CreatedAt, UpdatedAt, DeletedAt timestamps
- **Search & Filter**: Comprehensive search with multiple criteria
- **Pagination**: Efficient pagination for large datasets
- **Statistics**: Detailed statistics and analytics
- **Bulk Operations**: Efficient bulk create, delete, and restore
- **Validation**: Comprehensive data and business rule validation
- **Performance**: Optimized with proper indexing and query optimization

## Usage Examples

### Create Person
```json
POST /api/persons
{
  "divisionId": 1,
  "categoryId": 2,
  "name": "John Doe",
  "mobileNumber": "9876543210",
  "nature": 1,
  "primaryEmailId": "<EMAIL>"
}
```

### Search Persons
```json
POST /api/persons/search
{
  "page": 1,
  "pageSize": 10,
  "name": "John",
  "divisionId": 1,
  "nature": 1,
  "sortBy": "createdAt",
  "sortDirection": "desc"
}
```

### Get Statistics
```json
GET /api/persons/statistics
{
  "totalPersons": 150,
  "activePersons": 140,
  "deletedPersons": 10,
  "personsByNature": {
    "Business": 50,
    "Corporate": 60,
    "Individual": 30
  },
  "averageStarRating": 4.2,
  "totalTransactionValue": 1500000.00
}
```
