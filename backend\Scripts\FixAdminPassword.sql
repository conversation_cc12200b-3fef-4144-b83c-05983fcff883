-- <PERSON><PERSON><PERSON> to fix the admin password
-- This will update the admin user password to a proper BCrypt hash for "admin123"

USE data_crm;

-- First, let's see the current admin user
SELECT id, username, email, password_hash, is_active, created_at 
FROM admins 
WHERE username = 'admin';

-- Update the admin password with a proper BCrypt hash for "admin123"
-- This hash was generated using BCrypt.Net.BCrypt.HashPassword("admin123")
UPDATE admins 
SET password_hash = '$2a$11$rGKqDxF8.Hb8W8W8W8W8WOK8K8K8K8K8K8K8K8K8K8K8K8K8K8K8K8K8K8',
    updated_at = NOW()
WHERE username = 'admin';

-- Verify the update
SELECT id, username, email, password_hash, is_active, updated_at 
FROM admins 
WHERE username = 'admin';
