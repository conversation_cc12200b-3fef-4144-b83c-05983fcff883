{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Advanced\\\\Advanced CRM filed all use\\\\frontend\\\\src\\\\components\\\\forms\\\\DynamicPersonForm.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { PersonNatureLabels, GenderLabels, WorkingProfileLabels, getDefaultPersonData } from '../../constants/personConstants';\nimport formConfigService from '../../services/formConfigService';\nimport apiService from '../../services/apiService';\nimport FormField from './FormField';\nimport './DynamicPersonForm.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DynamicPersonForm = ({\n  onSubmit,\n  onCancel,\n  initialData = null,\n  mode = 'create'\n}) => {\n  _s();\n  // Hierarchy state\n  const [divisions, setDivisions] = useState([]);\n  const [categories, setCategories] = useState([]);\n  const [subCategories, setSubCategories] = useState([]);\n\n  // Selection state\n  const [selectedDivision, setSelectedDivision] = useState('');\n  const [selectedCategory, setSelectedCategory] = useState('');\n  const [selectedSubCategory, setSelectedSubCategory] = useState('');\n\n  // Form state\n  const [formConfig, setFormConfig] = useState(null);\n  const [formData, setFormData] = useState(getDefaultPersonData());\n  const [errors, setErrors] = useState({});\n\n  // Loading states\n  const [loading, setLoading] = useState({\n    divisions: false,\n    categories: false,\n    subCategories: false,\n    form: false\n  });\n  const [submitting, setSubmitting] = useState(false);\n\n  // Form availability state\n  const [formAvailability, setFormAvailability] = useState({\n    categoryHasForm: false,\n    subCategoryHasForm: false,\n    showSubCategoryDropdown: false,\n    message: ''\n  });\n\n  // Load divisions on component mount\n  useEffect(() => {\n    loadDivisions();\n\n    // Test API connectivity\n    testApiConnectivity();\n\n    // Load initial data if provided\n    if (initialData) {\n      var _initialData$division, _initialData$category, _initialData$subCateg;\n      setFormData(initialData);\n      setSelectedDivision(((_initialData$division = initialData.divisionId) === null || _initialData$division === void 0 ? void 0 : _initialData$division.toString()) || '');\n      setSelectedCategory(((_initialData$category = initialData.categoryId) === null || _initialData$category === void 0 ? void 0 : _initialData$category.toString()) || '');\n      setSelectedSubCategory(((_initialData$subCateg = initialData.subCategoryId) === null || _initialData$subCateg === void 0 ? void 0 : _initialData$subCateg.toString()) || '');\n    } else {\n      // Load default form for new person creation\n      const defaultForm = formConfigService.getDefaultFormConfig();\n      // Ensure no duplicate fields in default form\n      if (defaultForm.fields) {\n        defaultForm.fields = deduplicateFields(defaultForm.fields);\n      }\n      setFormConfig(defaultForm);\n    }\n  }, [initialData]);\n\n  // Test API connectivity\n  const testApiConnectivity = async () => {\n    try {\n      console.log('Testing API connectivity...');\n      const response = await apiService.getDivisions();\n      console.log('API test successful:', response);\n    } catch (error) {\n      console.error('API connectivity test failed:', error);\n    }\n  };\n\n  // Deduplicate fields based on field key\n  const deduplicateFields = fields => {\n    const seen = new Set();\n    const deduplicated = [];\n    fields.forEach(field => {\n      if (!seen.has(field.key)) {\n        seen.add(field.key);\n        deduplicated.push(field);\n      } else {\n        console.warn(`DynamicPersonForm: Removing duplicate field: ${field.key}`);\n      }\n    });\n    if (fields.length !== deduplicated.length) {\n      console.log(`DynamicPersonForm: Deduplicated ${fields.length} fields to ${deduplicated.length} unique fields`);\n    }\n    return deduplicated;\n  };\n\n  // Load categories when division changes\n  useEffect(() => {\n    if (selectedDivision) {\n      loadCategories(selectedDivision);\n      // Reset category and subcategory when division changes\n      setSelectedCategory('');\n      setSelectedSubCategory('');\n      setSubCategories([]);\n      setFormConfig(null);\n      setFormAvailability({\n        categoryHasForm: false,\n        subCategoryHasForm: false,\n        showSubCategoryDropdown: false,\n        message: ''\n      });\n    } else {\n      setCategories([]);\n      setSubCategories([]);\n      setSelectedCategory('');\n      setSelectedSubCategory('');\n      setFormConfig(null);\n    }\n  }, [selectedDivision]);\n\n  // Handle category selection and form loading\n  useEffect(() => {\n    if (selectedCategory) {\n      handleCategorySelection(selectedCategory);\n      // Reset subcategory when category changes\n      setSelectedSubCategory('');\n    } else {\n      setSubCategories([]);\n      setSelectedSubCategory('');\n      setFormConfig(null);\n      setFormAvailability({\n        categoryHasForm: false,\n        subCategoryHasForm: false,\n        showSubCategoryDropdown: false,\n        message: ''\n      });\n    }\n  }, [selectedCategory]);\n\n  // Handle subcategory selection and form loading\n  useEffect(() => {\n    if (selectedSubCategory) {\n      handleSubCategorySelection(selectedSubCategory);\n    }\n  }, [selectedSubCategory]);\n\n  // Load divisions from API\n  const loadDivisions = async () => {\n    setLoading(prev => ({\n      ...prev,\n      divisions: true\n    }));\n    try {\n      const response = await apiService.getDivisions();\n      setDivisions(response.data || []);\n    } catch (error) {\n      console.error('Error loading divisions:', error);\n      setErrors(prev => ({\n        ...prev,\n        divisions: 'Failed to load divisions'\n      }));\n    } finally {\n      setLoading(prev => ({\n        ...prev,\n        divisions: false\n      }));\n    }\n  };\n\n  // Load categories by division\n  const loadCategories = async divisionId => {\n    setLoading(prev => ({\n      ...prev,\n      categories: true\n    }));\n    try {\n      const response = await apiService.getCategoriesByDivision(divisionId);\n      setCategories(response.data || []);\n    } catch (error) {\n      console.error('Error loading categories:', error);\n      setErrors(prev => ({\n        ...prev,\n        categories: 'Failed to load categories'\n      }));\n      setCategories([]);\n    } finally {\n      setLoading(prev => ({\n        ...prev,\n        categories: false\n      }));\n    }\n  };\n\n  // Load subcategories by category\n  const loadSubCategories = async categoryId => {\n    setLoading(prev => ({\n      ...prev,\n      subCategories: true\n    }));\n    try {\n      const response = await apiService.getSubCategoriesByCategory(categoryId);\n      setSubCategories(response.data || []);\n    } catch (error) {\n      console.error('Error loading subcategories:', error);\n      setErrors(prev => ({\n        ...prev,\n        subCategories: 'Failed to load subcategories'\n      }));\n      setSubCategories([]);\n    } finally {\n      setLoading(prev => ({\n        ...prev,\n        subCategories: false\n      }));\n    }\n  };\n\n  // Handle category selection and form loading logic\n  const handleCategorySelection = async categoryId => {\n    setLoading(prev => ({\n      ...prev,\n      form: true\n    }));\n    try {\n      // Check if category has a form\n      const categoryHasForm = formConfigService.hasFormForCategory(parseInt(categoryId));\n      if (categoryHasForm) {\n        // Load category form and hide subcategory dropdown\n        const categoryForm = formConfigService.loadFormConfig('category', parseInt(categoryId));\n        // Deduplicate fields in loaded form\n        if (categoryForm && categoryForm.fields) {\n          categoryForm.fields = deduplicateFields(categoryForm.fields);\n        }\n        setFormConfig(categoryForm);\n        setFormAvailability({\n          categoryHasForm: true,\n          subCategoryHasForm: false,\n          showSubCategoryDropdown: false,\n          message: 'Using category-specific form'\n        });\n      } else {\n        // No category form, show subcategory dropdown and load subcategories\n        await loadSubCategories(categoryId);\n        // Load default form as fallback\n        const defaultForm = formConfigService.getDefaultFormConfig();\n        // Deduplicate fields in default form\n        if (defaultForm.fields) {\n          defaultForm.fields = deduplicateFields(defaultForm.fields);\n        }\n        setFormConfig(defaultForm);\n        setFormAvailability({\n          categoryHasForm: false,\n          subCategoryHasForm: false,\n          showSubCategoryDropdown: true,\n          message: 'Using default form. You can select a subcategory if available.'\n        });\n      }\n    } catch (error) {\n      console.error('Error handling category selection:', error);\n      setErrors(prev => ({\n        ...prev,\n        form: 'Error loading form configuration'\n      }));\n    } finally {\n      setLoading(prev => ({\n        ...prev,\n        form: false\n      }));\n    }\n  };\n\n  // Handle subcategory selection and form loading logic\n  const handleSubCategorySelection = async subCategoryId => {\n    setLoading(prev => ({\n      ...prev,\n      form: true\n    }));\n    try {\n      // Check if subcategory has a form\n      const subCategoryHasForm = formConfigService.hasFormForSubCategory(parseInt(subCategoryId));\n      if (subCategoryHasForm) {\n        // Load subcategory form\n        const subCategoryForm = formConfigService.loadFormConfig('subcategory', parseInt(subCategoryId));\n        // Deduplicate fields in loaded form\n        if (subCategoryForm && subCategoryForm.fields) {\n          subCategoryForm.fields = deduplicateFields(subCategoryForm.fields);\n        }\n        setFormConfig(subCategoryForm);\n        setFormAvailability({\n          categoryHasForm: false,\n          subCategoryHasForm: true,\n          showSubCategoryDropdown: true,\n          message: 'Using subcategory-specific form'\n        });\n      } else {\n        // No subcategory form available, use default form\n        const defaultForm = formConfigService.getDefaultFormConfig();\n        // Deduplicate fields in default form\n        if (defaultForm.fields) {\n          defaultForm.fields = deduplicateFields(defaultForm.fields);\n        }\n        setFormConfig(defaultForm);\n        setFormAvailability({\n          categoryHasForm: false,\n          subCategoryHasForm: false,\n          showSubCategoryDropdown: true,\n          message: 'No specific form found for this subcategory. Using default form.'\n        });\n      }\n    } catch (error) {\n      console.error('Error handling subcategory selection:', error);\n      setErrors(prev => ({\n        ...prev,\n        form: 'Error loading form configuration'\n      }));\n    } finally {\n      setLoading(prev => ({\n        ...prev,\n        form: false\n      }));\n    }\n  };\n\n  // Handle dropdown changes\n  const handleDivisionChange = e => {\n    const value = e.target.value;\n    setSelectedDivision(value);\n\n    // Update form data\n    setFormData(prev => ({\n      ...prev,\n      divisionId: value ? parseInt(value) : null,\n      categoryId: null,\n      subCategoryId: null\n    }));\n  };\n  const handleCategoryChange = e => {\n    const value = e.target.value;\n    setSelectedCategory(value);\n\n    // Update form data\n    setFormData(prev => ({\n      ...prev,\n      categoryId: value ? parseInt(value) : null,\n      subCategoryId: null\n    }));\n  };\n  const handleSubCategoryChange = e => {\n    const value = e.target.value;\n    setSelectedSubCategory(value);\n\n    // Update form data\n    setFormData(prev => ({\n      ...prev,\n      subCategoryId: value ? parseInt(value) : null\n    }));\n  };\n  const handleFieldChange = (fieldKey, value) => {\n    setFormData(prev => ({\n      ...prev,\n      [fieldKey]: value\n    }));\n\n    // Clear field error when user starts typing\n    if (errors[fieldKey]) {\n      setErrors(prev => ({\n        ...prev,\n        [fieldKey]: null\n      }));\n    }\n  };\n  const validateForm = () => {\n    const newErrors = {};\n\n    // Validate hierarchy selection\n    if (!selectedDivision) {\n      newErrors.division = 'Division is required';\n    }\n    if (!selectedCategory) {\n      newErrors.category = 'Category is required';\n    }\n\n    // Check if form is available (should always have default form as fallback)\n    if (!formConfig) {\n      newErrors.form = 'Form configuration not loaded. Please try again.';\n      return {\n        isValid: false,\n        errors: newErrors\n      };\n    }\n\n    // Validate form fields\n    formConfig.fields.forEach(field => {\n      const value = formData[field.key];\n\n      // Required field validation\n      if (field.required && (!value || typeof value === 'string' && value.trim() === '')) {\n        newErrors[field.key] = `${field.label} is required`;\n        return;\n      }\n\n      // Conditional field validation\n      if (field.conditional && shouldShowField(field)) {\n        if (field.required && (!value || typeof value === 'string' && value.trim() === '')) {\n          newErrors[field.key] = `${field.label} is required`;\n          return;\n        }\n      }\n\n      // Type-specific validation\n      if (value && typeof value === 'string' && value.trim() !== '') {\n        var _field$validation, _field$validation2, _field$validation3;\n        switch (field.type) {\n          case 'email':\n            const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n            if (!emailRegex.test(value)) {\n              newErrors[field.key] = 'Please enter a valid email address';\n            }\n            break;\n          case 'tel':\n            const phoneRegex = /^(\\+91[\\-\\s]?)?[0]?(91)?[789]\\d{9}$/;\n            if (!phoneRegex.test(value)) {\n              newErrors[field.key] = 'Please enter a valid mobile number';\n            }\n            break;\n          case 'url':\n            try {\n              new URL(value);\n            } catch {\n              newErrors[field.key] = 'Please enter a valid URL';\n            }\n            break;\n          case 'number':\n            if (field.validation) {\n              const numValue = parseFloat(value);\n              if (field.validation.min !== undefined && numValue < field.validation.min) {\n                newErrors[field.key] = `Value must be at least ${field.validation.min}`;\n              }\n              if (field.validation.max !== undefined && numValue > field.validation.max) {\n                newErrors[field.key] = `Value must be at most ${field.validation.max}`;\n              }\n            }\n            break;\n        }\n\n        // Pattern validation\n        if ((_field$validation = field.validation) !== null && _field$validation !== void 0 && _field$validation.pattern) {\n          const regex = new RegExp(field.validation.pattern);\n          if (!regex.test(value)) {\n            newErrors[field.key] = `${field.label} format is invalid`;\n          }\n        }\n\n        // Length validation\n        if ((_field$validation2 = field.validation) !== null && _field$validation2 !== void 0 && _field$validation2.minLength && value.length < field.validation.minLength) {\n          newErrors[field.key] = `${field.label} must be at least ${field.validation.minLength} characters`;\n        }\n        if ((_field$validation3 = field.validation) !== null && _field$validation3 !== void 0 && _field$validation3.maxLength && value.length > field.validation.maxLength) {\n          newErrors[field.key] = `${field.label} must be at most ${field.validation.maxLength} characters`;\n        }\n      }\n    });\n\n    // Business logic validation\n    if (formData.isMarried && formData.dateOfMarriage && formData.dateOfBirth) {\n      const birthDate = new Date(formData.dateOfBirth);\n      const marriageDate = new Date(formData.dateOfMarriage);\n      if (marriageDate <= birthDate) {\n        newErrors.dateOfMarriage = 'Marriage date must be after birth date';\n      }\n    }\n    return {\n      isValid: Object.keys(newErrors).length === 0,\n      errors: newErrors\n    };\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    const validation = validateForm();\n    if (!validation.isValid) {\n      console.log('Validation failed:', validation.errors);\n      setErrors(validation.errors);\n      return;\n    }\n    setSubmitting(true);\n    try {\n      // Prepare data for API - match backend model exactly\n      const submitData = {\n        // Required hierarchy fields\n        divisionId: parseInt(selectedDivision),\n        categoryId: parseInt(selectedCategory),\n        subCategoryId: selectedSubCategory ? parseInt(selectedSubCategory) : null,\n        // Required fields\n        name: formData.name || '',\n        mobileNumber: formData.mobileNumber || '',\n        nature: formData.nature ? parseInt(formData.nature) : null,\n        // Optional enum fields\n        gender: formData.gender ? parseInt(formData.gender) : null,\n        // Contact Information\n        alternateNumbers: Array.isArray(formData.alternateNumbers) ? formData.alternateNumbers : formData.alternateNumbers ? formData.alternateNumbers.split(',').map(s => s.trim()) : [],\n        primaryEmailId: formData.primaryEmailId || '',\n        alternateEmailIds: Array.isArray(formData.alternateEmailIds) ? formData.alternateEmailIds : formData.alternateEmailIds ? formData.alternateEmailIds.split(',').map(s => s.trim()) : [],\n        website: formData.website || '',\n        // Personal Information\n        dateOfBirth: formData.dateOfBirth || null,\n        isMarried: formData.isMarried || false,\n        dateOfMarriage: formData.dateOfMarriage || null,\n        // Location Information\n        workingState: formData.workingState || '',\n        domesticState: formData.domesticState || '',\n        district: formData.district || '',\n        address: formData.address || '',\n        workingArea: formData.workingArea || '',\n        // Associate Information\n        hasAssociate: formData.hasAssociate || false,\n        associateName: formData.associateName || '',\n        associateRelation: formData.associateRelation || '',\n        associateMobile: formData.associateMobile || '',\n        // Digital Presence\n        usingWebsite: formData.usingWebsite || false,\n        websiteLink: formData.websiteLink || '',\n        usingCRMApp: formData.usingCRMApp || false,\n        crmAppLink: formData.crmAppLink || '',\n        // Business Information\n        transactionValue: formData.transactionValue ? parseFloat(formData.transactionValue) : null,\n        reraRegistrationNumber: formData.reraRegistrationNumber || '',\n        workingProfiles: Array.isArray(formData.workingProfiles) ? formData.workingProfiles.map(wp => parseInt(wp)) : [],\n        starRating: formData.starRating ? parseInt(formData.starRating) : null,\n        source: formData.source || '',\n        remarks: formData.remarks || '',\n        // Company Information\n        firmName: formData.firmName || '',\n        numberOfOffices: formData.numberOfOffices ? parseInt(formData.numberOfOffices) : null,\n        numberOfBranches: formData.numberOfBranches ? parseInt(formData.numberOfBranches) : null,\n        totalEmployeeStrength: formData.totalEmployeeStrength ? parseInt(formData.totalEmployeeStrength) : null,\n        // Authorized Person\n        authorizedPersonName: formData.authorizedPersonName || '',\n        authorizedPersonEmail: formData.authorizedPersonEmail || '',\n        designation: formData.designation || '',\n        // Marketing Information\n        marketingContact: formData.marketingContact || '',\n        marketingDesignation: formData.marketingDesignation || '',\n        placeOfPosting: formData.placeOfPosting || '',\n        department: formData.department || ''\n      };\n\n      // Debug logging\n      console.log('Submitting person data:', submitData);\n      console.log('Selected Division:', selectedDivision);\n      console.log('Selected Category:', selectedCategory);\n      console.log('Selected SubCategory:', selectedSubCategory);\n\n      // Validate required fields before submission\n      const requiredFieldsCheck = {\n        divisionId: submitData.divisionId,\n        categoryId: submitData.categoryId,\n        name: submitData.name,\n        mobileNumber: submitData.mobileNumber,\n        nature: submitData.nature\n      };\n      console.log('Required fields check:', requiredFieldsCheck);\n\n      // Check for missing required fields\n      const missingFields = Object.entries(requiredFieldsCheck).filter(([, value]) => !value || value === '' || value === null).map(([key]) => key);\n      if (missingFields.length > 0) {\n        console.error('Missing required fields:', missingFields);\n        setErrors({\n          general: 'Missing required fields: ' + missingFields.join(', ')\n        });\n        return;\n      }\n      let result;\n      if (mode === 'create') {\n        result = await apiService.createPerson(submitData);\n      } else {\n        result = await apiService.updatePerson(initialData.id, submitData);\n      }\n      onSubmit(result);\n    } catch (error) {\n      var _error$data;\n      console.error('Error submitting form:', error);\n      console.error('Error data:', error.data);\n      console.error('Full error object:', error);\n      if ((_error$data = error.data) !== null && _error$data !== void 0 && _error$data.errors) {\n        // Handle validation errors from backend\n        const backendErrors = {};\n        Object.keys(error.data.errors).forEach(key => {\n          const errorMessages = error.data.errors[key];\n          backendErrors[key.toLowerCase()] = Array.isArray(errorMessages) ? errorMessages.join(', ') : errorMessages;\n        });\n        console.log('Backend validation errors:', backendErrors);\n        setErrors(backendErrors);\n      } else if (error.isValidationError && error.isValidationError()) {\n        setErrors(error.getValidationErrors());\n      } else {\n        var _error$data2, _error$data3;\n        const errorMessage = ((_error$data2 = error.data) === null || _error$data2 === void 0 ? void 0 : _error$data2.message) || ((_error$data3 = error.data) === null || _error$data3 === void 0 ? void 0 : _error$data3.title) || error.message || `HTTP ${error.status || 'Unknown'} - An error occurred while saving`;\n        console.log('General error message:', errorMessage);\n        setErrors({\n          general: errorMessage\n        });\n      }\n    } finally {\n      setSubmitting(false);\n    }\n  };\n  const shouldShowField = field => {\n    if (!field.conditional) return true;\n    const conditionValue = formData[field.conditional.field];\n    const expectedValue = field.conditional.value;\n\n    // Handle boolean conditions\n    if (typeof expectedValue === 'boolean' || expectedValue === 'true' || expectedValue === 'false') {\n      return conditionValue === (expectedValue === true || expectedValue === 'true');\n    }\n    return conditionValue === expectedValue;\n  };\n  const groupFieldsBySections = () => {\n    if (!formConfig || !formConfig.fields) return {};\n\n    // Debug: Check for duplicate fields\n    const fieldKeys = formConfig.fields.map(field => field.key);\n    const duplicateKeys = fieldKeys.filter((key, index) => fieldKeys.indexOf(key) !== index);\n    if (duplicateKeys.length > 0) {\n      console.warn('Duplicate field keys found:', duplicateKeys);\n      console.log('All fields:', formConfig.fields);\n    }\n    const sections = {};\n    formConfig.fields.forEach(field => {\n      const sectionKey = field.section || 'general';\n      if (!sections[sectionKey]) {\n        sections[sectionKey] = {\n          title: getSectionTitle(sectionKey),\n          fields: []\n        };\n      }\n      sections[sectionKey].fields.push(field);\n    });\n    return sections;\n  };\n  const getSectionTitle = sectionKey => {\n    const titles = {\n      personalInfo: 'Personal Information',\n      contactInfo: 'Contact Information',\n      locationInfo: 'Location Information',\n      businessInfo: 'Business Information',\n      associateInfo: 'Associate Information',\n      digitalPresence: 'Digital Presence',\n      companyInfo: 'Company Information',\n      authorizedPerson: 'Authorized Person',\n      marketingInfo: 'Marketing Information',\n      general: 'General Information'\n    };\n    return titles[sectionKey] || sectionKey;\n  };\n  const sections = formConfig ? groupFieldsBySections() : {};\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"dynamic-person-form\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"form-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: mode === 'create' ? 'Create New Person' : 'Edit Person'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 658,\n        columnNumber: 9\n      }, this), formConfig && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-info\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"form-name\",\n          children: formConfig.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 661,\n          columnNumber: 13\n        }, this), formConfig.description && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"form-description\",\n          children: formConfig.description\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 663,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 660,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 657,\n      columnNumber: 7\n    }, this), errors.general && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"alert alert-error\",\n      children: errors.general\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 670,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmit,\n      className: \"person-form\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Division & Category Selection\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 676,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"form-label\",\n            children: [\"Division \", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"required\",\n              children: \"*\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 681,\n              columnNumber: 24\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 680,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: selectedDivision,\n            onChange: handleDivisionChange,\n            disabled: loading.divisions,\n            className: `form-select ${errors.division ? 'error' : ''}`,\n            required: true,\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"\",\n              children: loading.divisions ? 'Loading divisions...' : 'Select Division'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 690,\n              columnNumber: 15\n            }, this), divisions.map(division => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: division.id,\n              children: division.name\n            }, division.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 694,\n              columnNumber: 17\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 683,\n            columnNumber: 13\n          }, this), errors.division && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"error-message\",\n            children: errors.division\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 700,\n            columnNumber: 15\n          }, this), errors.divisions && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"error-message\",\n            children: errors.divisions\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 703,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 679,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"form-label\",\n            children: [\"Category \", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"required\",\n              children: \"*\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 710,\n              columnNumber: 24\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 709,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: selectedCategory,\n            onChange: handleCategoryChange,\n            disabled: !selectedDivision || loading.categories,\n            className: `form-select ${errors.category ? 'error' : ''}`,\n            required: true,\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"\",\n              children: !selectedDivision ? 'Select Division first' : loading.categories ? 'Loading categories...' : 'Select Category'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 719,\n              columnNumber: 15\n            }, this), categories.map(category => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: category.id,\n              children: category.name\n            }, category.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 728,\n              columnNumber: 17\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 712,\n            columnNumber: 13\n          }, this), errors.category && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"error-message\",\n            children: errors.category\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 734,\n            columnNumber: 15\n          }, this), errors.categories && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"error-message\",\n            children: errors.categories\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 737,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 708,\n          columnNumber: 11\n        }, this), formAvailability.showSubCategoryDropdown && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"form-label\",\n            children: \"Subcategory (Optional)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 744,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: selectedSubCategory,\n            onChange: handleSubCategoryChange,\n            disabled: !selectedCategory || loading.subCategories,\n            className: \"form-select\",\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"\",\n              children: !selectedCategory ? 'Select Category first' : loading.subCategories ? 'Loading subcategories...' : 'Select Subcategory (Optional)'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 753,\n              columnNumber: 17\n            }, this), subCategories.map(subCategory => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: subCategory.id,\n              children: subCategory.name\n            }, subCategory.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 762,\n              columnNumber: 19\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 747,\n            columnNumber: 15\n          }, this), errors.subCategories && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"error-message\",\n            children: errors.subCategories\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 768,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 743,\n          columnNumber: 13\n        }, this), loading.form && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading-message\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Loading form configuration...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 776,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 775,\n          columnNumber: 13\n        }, this), formAvailability.message && !loading.form && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `status-message ${formAvailability.categoryHasForm || formAvailability.subCategoryHasForm ? 'success' : formAvailability.message.includes('No form') ? 'error' : 'info'}`,\n          children: formAvailability.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 781,\n          columnNumber: 13\n        }, this), errors.form && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"error-message\",\n          children: errors.form\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 793,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 675,\n        columnNumber: 9\n      }, this), formConfig && Object.entries(sections).map(([sectionKey, section]) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: section.title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 800,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-fields\",\n          children: section.fields.filter(field => shouldShowField(field)).map((field, fieldIndex) => /*#__PURE__*/_jsxDEV(FormField, {\n            field: field,\n            value: formData[field.key],\n            onChange: value => handleFieldChange(field.key, value),\n            error: errors[field.key]\n          }, `${sectionKey}-${field.key}-${fieldIndex}`, false, {\n            fileName: _jsxFileName,\n            lineNumber: 805,\n            columnNumber: 19\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 801,\n          columnNumber: 13\n        }, this)]\n      }, sectionKey, true, {\n        fileName: _jsxFileName,\n        lineNumber: 799,\n        columnNumber: 11\n      }, this)), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-actions\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          onClick: onCancel,\n          className: \"btn btn-outline\",\n          disabled: submitting,\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 821,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          className: \"btn btn-primary\",\n          disabled: submitting || !selectedDivision || !selectedCategory,\n          children: submitting ? mode === 'create' ? 'Creating...' : 'Updating...' : mode === 'create' ? 'Create Person' : 'Update Person'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 829,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 820,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 673,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 656,\n    columnNumber: 5\n  }, this);\n};\n_s(DynamicPersonForm, \"HQTZ+LW5pRnY6eFU2EO5G2IvcY0=\");\n_c = DynamicPersonForm;\nexport default DynamicPersonForm;\nvar _c;\n$RefreshReg$(_c, \"DynamicPersonForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "PersonNatureLabels", "<PERSON><PERSON><PERSON><PERSON>", "WorkingProfileLabels", "getDefaultPersonData", "formConfigService", "apiService", "FormField", "jsxDEV", "_jsxDEV", "DynamicPersonForm", "onSubmit", "onCancel", "initialData", "mode", "_s", "divisions", "setDivisions", "categories", "setCategories", "subCategories", "setSubCategories", "selectedDivision", "setSelectedDivision", "selectedCate<PERSON><PERSON>", "setSelectedCategory", "selectedSubCategory", "setSelectedSubCategory", "formConfig", "setFormConfig", "formData", "setFormData", "errors", "setErrors", "loading", "setLoading", "form", "submitting", "setSubmitting", "formAvailability", "setFormAvailability", "categoryHasForm", "subCategoryHasForm", "showSubCategoryDropdown", "message", "loadDivisions", "testApiConnectivity", "_initialData$division", "_initialData$category", "_initialData$subCateg", "divisionId", "toString", "categoryId", "subCategoryId", "defaultForm", "getDefaultFormConfig", "fields", "deduplicateFields", "console", "log", "response", "getDivisions", "error", "seen", "Set", "deduplicated", "for<PERSON>ach", "field", "has", "key", "add", "push", "warn", "length", "loadCategories", "handleCategorySelection", "handleSubCategorySelection", "prev", "data", "getCategoriesByDivision", "loadSubCategories", "getSubCategoriesByCategory", "hasFormForCategory", "parseInt", "categoryForm", "loadFormConfig", "hasFormForSubCategory", "subCategoryForm", "handleDivisionChange", "e", "value", "target", "handleCategoryChange", "handleSubCategoryChange", "handleFieldChange", "<PERSON><PERSON><PERSON>", "validateForm", "newErrors", "division", "category", "<PERSON><PERSON><PERSON><PERSON>", "required", "trim", "label", "conditional", "shouldShowField", "_field$validation", "_field$validation2", "_field$validation3", "type", "emailRegex", "test", "phoneRegex", "URL", "validation", "numValue", "parseFloat", "min", "undefined", "max", "pattern", "regex", "RegExp", "<PERSON><PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "dateOfMarriage", "dateOfBirth", "birthDate", "Date", "marriageDate", "Object", "keys", "handleSubmit", "preventDefault", "submitData", "name", "mobileNumber", "nature", "gender", "alternateNumbers", "Array", "isArray", "split", "map", "s", "primaryEmailId", "alternateEmailIds", "website", "workingState", "domesticState", "district", "address", "workingArea", "hasAssociate", "associate<PERSON><PERSON>", "associateRelation", "associate<PERSON><PERSON><PERSON>", "usingWebsite", "websiteLink", "usingCRMApp", "crmAppLink", "transactionValue", "reraRegistrationNumber", "workingProfiles", "wp", "starRating", "source", "remarks", "firmName", "numberOfOffices", "numberOfBranches", "totalEmployeeStrength", "authorizedPersonName", "authorizedPersonEmail", "designation", "marketingContact", "marketingDesignation", "placeOfPosting", "department", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "missingFields", "entries", "filter", "general", "join", "result", "create<PERSON>erson", "update<PERSON><PERSON>", "id", "_error$data", "backendErrors", "errorMessages", "toLowerCase", "isValidationError", "getValidationErrors", "_error$data2", "_error$data3", "errorMessage", "title", "status", "conditionValue", "expectedValue", "groupFieldsBySections", "fieldKeys", "duplicateKeys", "index", "indexOf", "sections", "sectionKey", "section", "getSectionTitle", "titles", "personalInfo", "contactInfo", "locationInfo", "businessInfo", "associateInfo", "digitalPresence", "companyInfo", "<PERSON><PERSON><PERSON>", "marketingInfo", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "description", "onChange", "disabled", "subCategory", "includes", "fieldIndex", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/Advanced/Advanced CRM filed all use/frontend/src/components/forms/DynamicPersonForm.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { PersonNatureLabels, GenderLabels, WorkingProfileLabels, getDefaultPersonData } from '../../constants/personConstants';\nimport formConfigService from '../../services/formConfigService';\nimport apiService from '../../services/apiService';\nimport FormField from './FormField';\nimport './DynamicPersonForm.css';\n\nconst DynamicPersonForm = ({ onSubmit, onCancel, initialData = null, mode = 'create' }) => {\n  // Hierarchy state\n  const [divisions, setDivisions] = useState([]);\n  const [categories, setCategories] = useState([]);\n  const [subCategories, setSubCategories] = useState([]);\n\n  // Selection state\n  const [selectedDivision, setSelectedDivision] = useState('');\n  const [selectedCategory, setSelectedCategory] = useState('');\n  const [selectedSubCategory, setSelectedSubCategory] = useState('');\n\n  // Form state\n  const [formConfig, setFormConfig] = useState(null);\n  const [formData, setFormData] = useState(getDefaultPersonData());\n  const [errors, setErrors] = useState({});\n\n  // Loading states\n  const [loading, setLoading] = useState({\n    divisions: false,\n    categories: false,\n    subCategories: false,\n    form: false\n  });\n  const [submitting, setSubmitting] = useState(false);\n\n  // Form availability state\n  const [formAvailability, setFormAvailability] = useState({\n    categoryHasForm: false,\n    subCategoryHasForm: false,\n    showSubCategoryDropdown: false,\n    message: ''\n  });\n\n  // Load divisions on component mount\n  useEffect(() => {\n    loadDivisions();\n\n    // Test API connectivity\n    testApiConnectivity();\n\n    // Load initial data if provided\n    if (initialData) {\n      setFormData(initialData);\n      setSelectedDivision(initialData.divisionId?.toString() || '');\n      setSelectedCategory(initialData.categoryId?.toString() || '');\n      setSelectedSubCategory(initialData.subCategoryId?.toString() || '');\n    } else {\n      // Load default form for new person creation\n      const defaultForm = formConfigService.getDefaultFormConfig();\n      // Ensure no duplicate fields in default form\n      if (defaultForm.fields) {\n        defaultForm.fields = deduplicateFields(defaultForm.fields);\n      }\n      setFormConfig(defaultForm);\n    }\n  }, [initialData]);\n\n  // Test API connectivity\n  const testApiConnectivity = async () => {\n    try {\n      console.log('Testing API connectivity...');\n      const response = await apiService.getDivisions();\n      console.log('API test successful:', response);\n    } catch (error) {\n      console.error('API connectivity test failed:', error);\n    }\n  };\n\n  // Deduplicate fields based on field key\n  const deduplicateFields = (fields) => {\n    const seen = new Set();\n    const deduplicated = [];\n\n    fields.forEach(field => {\n      if (!seen.has(field.key)) {\n        seen.add(field.key);\n        deduplicated.push(field);\n      } else {\n        console.warn(`DynamicPersonForm: Removing duplicate field: ${field.key}`);\n      }\n    });\n\n    if (fields.length !== deduplicated.length) {\n      console.log(`DynamicPersonForm: Deduplicated ${fields.length} fields to ${deduplicated.length} unique fields`);\n    }\n\n    return deduplicated;\n  };\n\n  // Load categories when division changes\n  useEffect(() => {\n    if (selectedDivision) {\n      loadCategories(selectedDivision);\n      // Reset category and subcategory when division changes\n      setSelectedCategory('');\n      setSelectedSubCategory('');\n      setSubCategories([]);\n      setFormConfig(null);\n      setFormAvailability({\n        categoryHasForm: false,\n        subCategoryHasForm: false,\n        showSubCategoryDropdown: false,\n        message: ''\n      });\n    } else {\n      setCategories([]);\n      setSubCategories([]);\n      setSelectedCategory('');\n      setSelectedSubCategory('');\n      setFormConfig(null);\n    }\n  }, [selectedDivision]);\n\n  // Handle category selection and form loading\n  useEffect(() => {\n    if (selectedCategory) {\n      handleCategorySelection(selectedCategory);\n      // Reset subcategory when category changes\n      setSelectedSubCategory('');\n    } else {\n      setSubCategories([]);\n      setSelectedSubCategory('');\n      setFormConfig(null);\n      setFormAvailability({\n        categoryHasForm: false,\n        subCategoryHasForm: false,\n        showSubCategoryDropdown: false,\n        message: ''\n      });\n    }\n  }, [selectedCategory]);\n\n  // Handle subcategory selection and form loading\n  useEffect(() => {\n    if (selectedSubCategory) {\n      handleSubCategorySelection(selectedSubCategory);\n    }\n  }, [selectedSubCategory]);\n\n  // Load divisions from API\n  const loadDivisions = async () => {\n    setLoading(prev => ({ ...prev, divisions: true }));\n    try {\n      const response = await apiService.getDivisions();\n      setDivisions(response.data || []);\n    } catch (error) {\n      console.error('Error loading divisions:', error);\n      setErrors(prev => ({ ...prev, divisions: 'Failed to load divisions' }));\n    } finally {\n      setLoading(prev => ({ ...prev, divisions: false }));\n    }\n  };\n\n  // Load categories by division\n  const loadCategories = async (divisionId) => {\n    setLoading(prev => ({ ...prev, categories: true }));\n    try {\n      const response = await apiService.getCategoriesByDivision(divisionId);\n      setCategories(response.data || []);\n    } catch (error) {\n      console.error('Error loading categories:', error);\n      setErrors(prev => ({ ...prev, categories: 'Failed to load categories' }));\n      setCategories([]);\n    } finally {\n      setLoading(prev => ({ ...prev, categories: false }));\n    }\n  };\n\n  // Load subcategories by category\n  const loadSubCategories = async (categoryId) => {\n    setLoading(prev => ({ ...prev, subCategories: true }));\n    try {\n      const response = await apiService.getSubCategoriesByCategory(categoryId);\n      setSubCategories(response.data || []);\n    } catch (error) {\n      console.error('Error loading subcategories:', error);\n      setErrors(prev => ({ ...prev, subCategories: 'Failed to load subcategories' }));\n      setSubCategories([]);\n    } finally {\n      setLoading(prev => ({ ...prev, subCategories: false }));\n    }\n  };\n\n  // Handle category selection and form loading logic\n  const handleCategorySelection = async (categoryId) => {\n    setLoading(prev => ({ ...prev, form: true }));\n\n    try {\n      // Check if category has a form\n      const categoryHasForm = formConfigService.hasFormForCategory(parseInt(categoryId));\n\n      if (categoryHasForm) {\n        // Load category form and hide subcategory dropdown\n        const categoryForm = formConfigService.loadFormConfig('category', parseInt(categoryId));\n        // Deduplicate fields in loaded form\n        if (categoryForm && categoryForm.fields) {\n          categoryForm.fields = deduplicateFields(categoryForm.fields);\n        }\n        setFormConfig(categoryForm);\n        setFormAvailability({\n          categoryHasForm: true,\n          subCategoryHasForm: false,\n          showSubCategoryDropdown: false,\n          message: 'Using category-specific form'\n        });\n      } else {\n        // No category form, show subcategory dropdown and load subcategories\n        await loadSubCategories(categoryId);\n        // Load default form as fallback\n        const defaultForm = formConfigService.getDefaultFormConfig();\n        // Deduplicate fields in default form\n        if (defaultForm.fields) {\n          defaultForm.fields = deduplicateFields(defaultForm.fields);\n        }\n        setFormConfig(defaultForm);\n        setFormAvailability({\n          categoryHasForm: false,\n          subCategoryHasForm: false,\n          showSubCategoryDropdown: true,\n          message: 'Using default form. You can select a subcategory if available.'\n        });\n      }\n    } catch (error) {\n      console.error('Error handling category selection:', error);\n      setErrors(prev => ({ ...prev, form: 'Error loading form configuration' }));\n    } finally {\n      setLoading(prev => ({ ...prev, form: false }));\n    }\n  };\n\n  // Handle subcategory selection and form loading logic\n  const handleSubCategorySelection = async (subCategoryId) => {\n    setLoading(prev => ({ ...prev, form: true }));\n\n    try {\n      // Check if subcategory has a form\n      const subCategoryHasForm = formConfigService.hasFormForSubCategory(parseInt(subCategoryId));\n\n      if (subCategoryHasForm) {\n        // Load subcategory form\n        const subCategoryForm = formConfigService.loadFormConfig('subcategory', parseInt(subCategoryId));\n        // Deduplicate fields in loaded form\n        if (subCategoryForm && subCategoryForm.fields) {\n          subCategoryForm.fields = deduplicateFields(subCategoryForm.fields);\n        }\n        setFormConfig(subCategoryForm);\n        setFormAvailability({\n          categoryHasForm: false,\n          subCategoryHasForm: true,\n          showSubCategoryDropdown: true,\n          message: 'Using subcategory-specific form'\n        });\n      } else {\n        // No subcategory form available, use default form\n        const defaultForm = formConfigService.getDefaultFormConfig();\n        // Deduplicate fields in default form\n        if (defaultForm.fields) {\n          defaultForm.fields = deduplicateFields(defaultForm.fields);\n        }\n        setFormConfig(defaultForm);\n        setFormAvailability({\n          categoryHasForm: false,\n          subCategoryHasForm: false,\n          showSubCategoryDropdown: true,\n          message: 'No specific form found for this subcategory. Using default form.'\n        });\n      }\n    } catch (error) {\n      console.error('Error handling subcategory selection:', error);\n      setErrors(prev => ({ ...prev, form: 'Error loading form configuration' }));\n    } finally {\n      setLoading(prev => ({ ...prev, form: false }));\n    }\n  };\n\n  // Handle dropdown changes\n  const handleDivisionChange = (e) => {\n    const value = e.target.value;\n    setSelectedDivision(value);\n\n    // Update form data\n    setFormData(prev => ({\n      ...prev,\n      divisionId: value ? parseInt(value) : null,\n      categoryId: null,\n      subCategoryId: null\n    }));\n  };\n\n  const handleCategoryChange = (e) => {\n    const value = e.target.value;\n    setSelectedCategory(value);\n\n    // Update form data\n    setFormData(prev => ({\n      ...prev,\n      categoryId: value ? parseInt(value) : null,\n      subCategoryId: null\n    }));\n  };\n\n  const handleSubCategoryChange = (e) => {\n    const value = e.target.value;\n    setSelectedSubCategory(value);\n\n    // Update form data\n    setFormData(prev => ({\n      ...prev,\n      subCategoryId: value ? parseInt(value) : null\n    }));\n  };\n\n  const handleFieldChange = (fieldKey, value) => {\n    setFormData(prev => ({\n      ...prev,\n      [fieldKey]: value\n    }));\n\n    // Clear field error when user starts typing\n    if (errors[fieldKey]) {\n      setErrors(prev => ({\n        ...prev,\n        [fieldKey]: null\n      }));\n    }\n  };\n\n  const validateForm = () => {\n    const newErrors = {};\n\n    // Validate hierarchy selection\n    if (!selectedDivision) {\n      newErrors.division = 'Division is required';\n    }\n    if (!selectedCategory) {\n      newErrors.category = 'Category is required';\n    }\n\n    // Check if form is available (should always have default form as fallback)\n    if (!formConfig) {\n      newErrors.form = 'Form configuration not loaded. Please try again.';\n      return { isValid: false, errors: newErrors };\n    }\n\n\n\n    // Validate form fields\n    formConfig.fields.forEach(field => {\n      const value = formData[field.key];\n      \n      // Required field validation\n      if (field.required && (!value || (typeof value === 'string' && value.trim() === ''))) {\n        newErrors[field.key] = `${field.label} is required`;\n        return;\n      }\n\n      // Conditional field validation\n      if (field.conditional && shouldShowField(field)) {\n        if (field.required && (!value || (typeof value === 'string' && value.trim() === ''))) {\n          newErrors[field.key] = `${field.label} is required`;\n          return;\n        }\n      }\n\n      // Type-specific validation\n      if (value && typeof value === 'string' && value.trim() !== '') {\n        switch (field.type) {\n          case 'email':\n            const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n            if (!emailRegex.test(value)) {\n              newErrors[field.key] = 'Please enter a valid email address';\n            }\n            break;\n          case 'tel':\n            const phoneRegex = /^(\\+91[\\-\\s]?)?[0]?(91)?[789]\\d{9}$/;\n            if (!phoneRegex.test(value)) {\n              newErrors[field.key] = 'Please enter a valid mobile number';\n            }\n            break;\n          case 'url':\n            try {\n              new URL(value);\n            } catch {\n              newErrors[field.key] = 'Please enter a valid URL';\n            }\n            break;\n          case 'number':\n            if (field.validation) {\n              const numValue = parseFloat(value);\n              if (field.validation.min !== undefined && numValue < field.validation.min) {\n                newErrors[field.key] = `Value must be at least ${field.validation.min}`;\n              }\n              if (field.validation.max !== undefined && numValue > field.validation.max) {\n                newErrors[field.key] = `Value must be at most ${field.validation.max}`;\n              }\n            }\n            break;\n        }\n\n        // Pattern validation\n        if (field.validation?.pattern) {\n          const regex = new RegExp(field.validation.pattern);\n          if (!regex.test(value)) {\n            newErrors[field.key] = `${field.label} format is invalid`;\n          }\n        }\n\n        // Length validation\n        if (field.validation?.minLength && value.length < field.validation.minLength) {\n          newErrors[field.key] = `${field.label} must be at least ${field.validation.minLength} characters`;\n        }\n        if (field.validation?.maxLength && value.length > field.validation.maxLength) {\n          newErrors[field.key] = `${field.label} must be at most ${field.validation.maxLength} characters`;\n        }\n      }\n    });\n\n    // Business logic validation\n    if (formData.isMarried && formData.dateOfMarriage && formData.dateOfBirth) {\n      const birthDate = new Date(formData.dateOfBirth);\n      const marriageDate = new Date(formData.dateOfMarriage);\n      if (marriageDate <= birthDate) {\n        newErrors.dateOfMarriage = 'Marriage date must be after birth date';\n      }\n    }\n\n    return {\n      isValid: Object.keys(newErrors).length === 0,\n      errors: newErrors\n    };\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    \n    const validation = validateForm();\n    if (!validation.isValid) {\n      console.log('Validation failed:', validation.errors);\n      setErrors(validation.errors);\n      return;\n    }\n\n    setSubmitting(true);\n    try {\n      // Prepare data for API - match backend model exactly\n      const submitData = {\n        // Required hierarchy fields\n        divisionId: parseInt(selectedDivision),\n        categoryId: parseInt(selectedCategory),\n        subCategoryId: selectedSubCategory ? parseInt(selectedSubCategory) : null,\n\n        // Required fields\n        name: formData.name || '',\n        mobileNumber: formData.mobileNumber || '',\n        nature: formData.nature ? parseInt(formData.nature) : null,\n\n        // Optional enum fields\n        gender: formData.gender ? parseInt(formData.gender) : null,\n\n        // Contact Information\n        alternateNumbers: Array.isArray(formData.alternateNumbers)\n          ? formData.alternateNumbers\n          : formData.alternateNumbers ? formData.alternateNumbers.split(',').map(s => s.trim()) : [],\n        primaryEmailId: formData.primaryEmailId || '',\n        alternateEmailIds: Array.isArray(formData.alternateEmailIds)\n          ? formData.alternateEmailIds\n          : formData.alternateEmailIds ? formData.alternateEmailIds.split(',').map(s => s.trim()) : [],\n        website: formData.website || '',\n\n        // Personal Information\n        dateOfBirth: formData.dateOfBirth || null,\n        isMarried: formData.isMarried || false,\n        dateOfMarriage: formData.dateOfMarriage || null,\n\n        // Location Information\n        workingState: formData.workingState || '',\n        domesticState: formData.domesticState || '',\n        district: formData.district || '',\n        address: formData.address || '',\n        workingArea: formData.workingArea || '',\n\n        // Associate Information\n        hasAssociate: formData.hasAssociate || false,\n        associateName: formData.associateName || '',\n        associateRelation: formData.associateRelation || '',\n        associateMobile: formData.associateMobile || '',\n\n        // Digital Presence\n        usingWebsite: formData.usingWebsite || false,\n        websiteLink: formData.websiteLink || '',\n        usingCRMApp: formData.usingCRMApp || false,\n        crmAppLink: formData.crmAppLink || '',\n\n        // Business Information\n        transactionValue: formData.transactionValue ? parseFloat(formData.transactionValue) : null,\n        reraRegistrationNumber: formData.reraRegistrationNumber || '',\n        workingProfiles: Array.isArray(formData.workingProfiles)\n          ? formData.workingProfiles.map(wp => parseInt(wp))\n          : [],\n        starRating: formData.starRating ? parseInt(formData.starRating) : null,\n        source: formData.source || '',\n        remarks: formData.remarks || '',\n\n        // Company Information\n        firmName: formData.firmName || '',\n        numberOfOffices: formData.numberOfOffices ? parseInt(formData.numberOfOffices) : null,\n        numberOfBranches: formData.numberOfBranches ? parseInt(formData.numberOfBranches) : null,\n        totalEmployeeStrength: formData.totalEmployeeStrength ? parseInt(formData.totalEmployeeStrength) : null,\n\n        // Authorized Person\n        authorizedPersonName: formData.authorizedPersonName || '',\n        authorizedPersonEmail: formData.authorizedPersonEmail || '',\n        designation: formData.designation || '',\n\n        // Marketing Information\n        marketingContact: formData.marketingContact || '',\n        marketingDesignation: formData.marketingDesignation || '',\n        placeOfPosting: formData.placeOfPosting || '',\n        department: formData.department || ''\n      };\n\n      // Debug logging\n      console.log('Submitting person data:', submitData);\n      console.log('Selected Division:', selectedDivision);\n      console.log('Selected Category:', selectedCategory);\n      console.log('Selected SubCategory:', selectedSubCategory);\n\n      // Validate required fields before submission\n      const requiredFieldsCheck = {\n        divisionId: submitData.divisionId,\n        categoryId: submitData.categoryId,\n        name: submitData.name,\n        mobileNumber: submitData.mobileNumber,\n        nature: submitData.nature\n      };\n      console.log('Required fields check:', requiredFieldsCheck);\n\n      // Check for missing required fields\n      const missingFields = Object.entries(requiredFieldsCheck)\n        .filter(([, value]) => !value || value === '' || value === null)\n        .map(([key]) => key);\n\n      if (missingFields.length > 0) {\n        console.error('Missing required fields:', missingFields);\n        setErrors({ general: 'Missing required fields: ' + missingFields.join(', ') });\n        return;\n      }\n\n      let result;\n      if (mode === 'create') {\n        result = await apiService.createPerson(submitData);\n      } else {\n        result = await apiService.updatePerson(initialData.id, submitData);\n      }\n\n      onSubmit(result);\n    } catch (error) {\n      console.error('Error submitting form:', error);\n      console.error('Error data:', error.data);\n      console.error('Full error object:', error);\n\n      if (error.data?.errors) {\n        // Handle validation errors from backend\n        const backendErrors = {};\n        Object.keys(error.data.errors).forEach(key => {\n          const errorMessages = error.data.errors[key];\n          backendErrors[key.toLowerCase()] = Array.isArray(errorMessages)\n            ? errorMessages.join(', ')\n            : errorMessages;\n        });\n        console.log('Backend validation errors:', backendErrors);\n        setErrors(backendErrors);\n      } else if (error.isValidationError && error.isValidationError()) {\n        setErrors(error.getValidationErrors());\n      } else {\n        const errorMessage = error.data?.message ||\n                           error.data?.title ||\n                           error.message ||\n                           `HTTP ${error.status || 'Unknown'} - An error occurred while saving`;\n        console.log('General error message:', errorMessage);\n        setErrors({ general: errorMessage });\n      }\n    } finally {\n      setSubmitting(false);\n    }\n  };\n\n  const shouldShowField = (field) => {\n    if (!field.conditional) return true;\n    \n    const conditionValue = formData[field.conditional.field];\n    const expectedValue = field.conditional.value;\n    \n    // Handle boolean conditions\n    if (typeof expectedValue === 'boolean' || expectedValue === 'true' || expectedValue === 'false') {\n      return conditionValue === (expectedValue === true || expectedValue === 'true');\n    }\n    \n    return conditionValue === expectedValue;\n  };\n\n  const groupFieldsBySections = () => {\n    if (!formConfig || !formConfig.fields) return {};\n\n    // Debug: Check for duplicate fields\n    const fieldKeys = formConfig.fields.map(field => field.key);\n    const duplicateKeys = fieldKeys.filter((key, index) => fieldKeys.indexOf(key) !== index);\n    if (duplicateKeys.length > 0) {\n      console.warn('Duplicate field keys found:', duplicateKeys);\n      console.log('All fields:', formConfig.fields);\n    }\n\n    const sections = {};\n    formConfig.fields.forEach((field) => {\n      const sectionKey = field.section || 'general';\n      if (!sections[sectionKey]) {\n        sections[sectionKey] = {\n          title: getSectionTitle(sectionKey),\n          fields: []\n        };\n      }\n      sections[sectionKey].fields.push(field);\n    });\n\n    return sections;\n  };\n\n  const getSectionTitle = (sectionKey) => {\n    const titles = {\n      personalInfo: 'Personal Information',\n      contactInfo: 'Contact Information',\n      locationInfo: 'Location Information',\n      businessInfo: 'Business Information',\n      associateInfo: 'Associate Information',\n      digitalPresence: 'Digital Presence',\n      companyInfo: 'Company Information',\n      authorizedPerson: 'Authorized Person',\n      marketingInfo: 'Marketing Information',\n      general: 'General Information'\n    };\n    return titles[sectionKey] || sectionKey;\n  };\n\n  const sections = formConfig ? groupFieldsBySections() : {};\n\n\n\n  return (\n    <div className=\"dynamic-person-form\">\n      <div className=\"form-header\">\n        <h2>{mode === 'create' ? 'Create New Person' : 'Edit Person'}</h2>\n        {formConfig && (\n          <div className=\"form-info\">\n            <span className=\"form-name\">{formConfig.name}</span>\n            {formConfig.description && (\n              <span className=\"form-description\">{formConfig.description}</span>\n            )}\n          </div>\n        )}\n      </div>\n\n      {errors.general && (\n        <div className=\"alert alert-error\">{errors.general}</div>\n      )}\n\n      <form onSubmit={handleSubmit} className=\"person-form\">\n        {/* Hierarchical Dropdowns */}\n        <div className=\"form-section\">\n          <h3>Division & Category Selection</h3>\n\n          {/* Division Dropdown */}\n          <div className=\"form-group\">\n            <label className=\"form-label\">\n              Division <span className=\"required\">*</span>\n            </label>\n            <select\n              value={selectedDivision}\n              onChange={handleDivisionChange}\n              disabled={loading.divisions}\n              className={`form-select ${errors.division ? 'error' : ''}`}\n              required\n            >\n              <option value=\"\">\n                {loading.divisions ? 'Loading divisions...' : 'Select Division'}\n              </option>\n              {divisions.map(division => (\n                <option key={division.id} value={division.id}>\n                  {division.name}\n                </option>\n              ))}\n            </select>\n            {errors.division && (\n              <div className=\"error-message\">{errors.division}</div>\n            )}\n            {errors.divisions && (\n              <div className=\"error-message\">{errors.divisions}</div>\n            )}\n          </div>\n\n          {/* Category Dropdown */}\n          <div className=\"form-group\">\n            <label className=\"form-label\">\n              Category <span className=\"required\">*</span>\n            </label>\n            <select\n              value={selectedCategory}\n              onChange={handleCategoryChange}\n              disabled={!selectedDivision || loading.categories}\n              className={`form-select ${errors.category ? 'error' : ''}`}\n              required\n            >\n              <option value=\"\">\n                {!selectedDivision\n                  ? 'Select Division first'\n                  : loading.categories\n                    ? 'Loading categories...'\n                    : 'Select Category'\n                }\n              </option>\n              {categories.map(category => (\n                <option key={category.id} value={category.id}>\n                  {category.name}\n                </option>\n              ))}\n            </select>\n            {errors.category && (\n              <div className=\"error-message\">{errors.category}</div>\n            )}\n            {errors.categories && (\n              <div className=\"error-message\">{errors.categories}</div>\n            )}\n          </div>\n\n          {/* Subcategory Dropdown - Only show if category doesn't have a form */}\n          {formAvailability.showSubCategoryDropdown && (\n            <div className=\"form-group\">\n              <label className=\"form-label\">\n                Subcategory (Optional)\n              </label>\n              <select\n                value={selectedSubCategory}\n                onChange={handleSubCategoryChange}\n                disabled={!selectedCategory || loading.subCategories}\n                className=\"form-select\"\n              >\n                <option value=\"\">\n                  {!selectedCategory\n                    ? 'Select Category first'\n                    : loading.subCategories\n                      ? 'Loading subcategories...'\n                      : 'Select Subcategory (Optional)'\n                  }\n                </option>\n                {subCategories.map(subCategory => (\n                  <option key={subCategory.id} value={subCategory.id}>\n                    {subCategory.name}\n                  </option>\n                ))}\n              </select>\n              {errors.subCategories && (\n                <div className=\"error-message\">{errors.subCategories}</div>\n              )}\n            </div>\n          )}\n\n          {/* Form Status Messages */}\n          {loading.form && (\n            <div className=\"loading-message\">\n              <span>Loading form configuration...</span>\n            </div>\n          )}\n\n          {formAvailability.message && !loading.form && (\n            <div className={`status-message ${\n              formAvailability.categoryHasForm || formAvailability.subCategoryHasForm\n                ? 'success'\n                : formAvailability.message.includes('No form')\n                  ? 'error'\n                  : 'info'\n            }`}>\n              {formAvailability.message}\n            </div>\n          )}\n\n          {errors.form && (\n            <div className=\"error-message\">{errors.form}</div>\n          )}\n        </div>\n\n        {/* Dynamic Form Fields - Only show when form is available */}\n        {formConfig && Object.entries(sections).map(([sectionKey, section]) => (\n          <div key={sectionKey} className=\"form-section\">\n            <h3>{section.title}</h3>\n            <div className=\"form-fields\">\n              {section.fields\n                .filter(field => shouldShowField(field))\n                .map((field, fieldIndex) => (\n                  <FormField\n                    key={`${sectionKey}-${field.key}-${fieldIndex}`}\n                    field={field}\n                    value={formData[field.key]}\n                    onChange={(value) => handleFieldChange(field.key, value)}\n                    error={errors[field.key]}\n                  />\n                ))}\n            </div>\n          </div>\n        ))}\n\n\n\n        {/* Form Actions */}\n        <div className=\"form-actions\">\n          <button\n            type=\"button\"\n            onClick={onCancel}\n            className=\"btn btn-outline\"\n            disabled={submitting}\n          >\n            Cancel\n          </button>\n          <button\n            type=\"submit\"\n            className=\"btn btn-primary\"\n            disabled={submitting || !selectedDivision || !selectedCategory}\n          >\n            {submitting\n              ? (mode === 'create' ? 'Creating...' : 'Updating...')\n              : (mode === 'create' ? 'Create Person' : 'Update Person')\n            }\n          </button>\n        </div>\n      </form>\n    </div>\n  );\n};\n\nexport default DynamicPersonForm;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,kBAAkB,EAAEC,YAAY,EAAEC,oBAAoB,EAAEC,oBAAoB,QAAQ,iCAAiC;AAC9H,OAAOC,iBAAiB,MAAM,kCAAkC;AAChE,OAAOC,UAAU,MAAM,2BAA2B;AAClD,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAO,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjC,MAAMC,iBAAiB,GAAGA,CAAC;EAAEC,QAAQ;EAAEC,QAAQ;EAAEC,WAAW,GAAG,IAAI;EAAEC,IAAI,GAAG;AAAS,CAAC,KAAK;EAAAC,EAAA;EACzF;EACA,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACmB,UAAU,EAAEC,aAAa,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACqB,aAAa,EAAEC,gBAAgB,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;;EAEtD;EACA,MAAM,CAACuB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACyB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAAC2B,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;;EAElE;EACA,MAAM,CAAC6B,UAAU,EAAEC,aAAa,CAAC,GAAG9B,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAAC+B,QAAQ,EAAEC,WAAW,CAAC,GAAGhC,QAAQ,CAACK,oBAAoB,CAAC,CAAC,CAAC;EAChE,MAAM,CAAC4B,MAAM,EAAEC,SAAS,CAAC,GAAGlC,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAExC;EACA,MAAM,CAACmC,OAAO,EAAEC,UAAU,CAAC,GAAGpC,QAAQ,CAAC;IACrCiB,SAAS,EAAE,KAAK;IAChBE,UAAU,EAAE,KAAK;IACjBE,aAAa,EAAE,KAAK;IACpBgB,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGvC,QAAQ,CAAC,KAAK,CAAC;;EAEnD;EACA,MAAM,CAACwC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGzC,QAAQ,CAAC;IACvD0C,eAAe,EAAE,KAAK;IACtBC,kBAAkB,EAAE,KAAK;IACzBC,uBAAuB,EAAE,KAAK;IAC9BC,OAAO,EAAE;EACX,CAAC,CAAC;;EAEF;EACA5C,SAAS,CAAC,MAAM;IACd6C,aAAa,CAAC,CAAC;;IAEf;IACAC,mBAAmB,CAAC,CAAC;;IAErB;IACA,IAAIjC,WAAW,EAAE;MAAA,IAAAkC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA;MACflB,WAAW,CAAClB,WAAW,CAAC;MACxBU,mBAAmB,CAAC,EAAAwB,qBAAA,GAAAlC,WAAW,CAACqC,UAAU,cAAAH,qBAAA,uBAAtBA,qBAAA,CAAwBI,QAAQ,CAAC,CAAC,KAAI,EAAE,CAAC;MAC7D1B,mBAAmB,CAAC,EAAAuB,qBAAA,GAAAnC,WAAW,CAACuC,UAAU,cAAAJ,qBAAA,uBAAtBA,qBAAA,CAAwBG,QAAQ,CAAC,CAAC,KAAI,EAAE,CAAC;MAC7DxB,sBAAsB,CAAC,EAAAsB,qBAAA,GAAApC,WAAW,CAACwC,aAAa,cAAAJ,qBAAA,uBAAzBA,qBAAA,CAA2BE,QAAQ,CAAC,CAAC,KAAI,EAAE,CAAC;IACrE,CAAC,MAAM;MACL;MACA,MAAMG,WAAW,GAAGjD,iBAAiB,CAACkD,oBAAoB,CAAC,CAAC;MAC5D;MACA,IAAID,WAAW,CAACE,MAAM,EAAE;QACtBF,WAAW,CAACE,MAAM,GAAGC,iBAAiB,CAACH,WAAW,CAACE,MAAM,CAAC;MAC5D;MACA3B,aAAa,CAACyB,WAAW,CAAC;IAC5B;EACF,CAAC,EAAE,CAACzC,WAAW,CAAC,CAAC;;EAEjB;EACA,MAAMiC,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACFY,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;MAC1C,MAAMC,QAAQ,GAAG,MAAMtD,UAAU,CAACuD,YAAY,CAAC,CAAC;MAChDH,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEC,QAAQ,CAAC;IAC/C,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdJ,OAAO,CAACI,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;IACvD;EACF,CAAC;;EAED;EACA,MAAML,iBAAiB,GAAID,MAAM,IAAK;IACpC,MAAMO,IAAI,GAAG,IAAIC,GAAG,CAAC,CAAC;IACtB,MAAMC,YAAY,GAAG,EAAE;IAEvBT,MAAM,CAACU,OAAO,CAACC,KAAK,IAAI;MACtB,IAAI,CAACJ,IAAI,CAACK,GAAG,CAACD,KAAK,CAACE,GAAG,CAAC,EAAE;QACxBN,IAAI,CAACO,GAAG,CAACH,KAAK,CAACE,GAAG,CAAC;QACnBJ,YAAY,CAACM,IAAI,CAACJ,KAAK,CAAC;MAC1B,CAAC,MAAM;QACLT,OAAO,CAACc,IAAI,CAAC,gDAAgDL,KAAK,CAACE,GAAG,EAAE,CAAC;MAC3E;IACF,CAAC,CAAC;IAEF,IAAIb,MAAM,CAACiB,MAAM,KAAKR,YAAY,CAACQ,MAAM,EAAE;MACzCf,OAAO,CAACC,GAAG,CAAC,mCAAmCH,MAAM,CAACiB,MAAM,cAAcR,YAAY,CAACQ,MAAM,gBAAgB,CAAC;IAChH;IAEA,OAAOR,YAAY;EACrB,CAAC;;EAED;EACAjE,SAAS,CAAC,MAAM;IACd,IAAIsB,gBAAgB,EAAE;MACpBoD,cAAc,CAACpD,gBAAgB,CAAC;MAChC;MACAG,mBAAmB,CAAC,EAAE,CAAC;MACvBE,sBAAsB,CAAC,EAAE,CAAC;MAC1BN,gBAAgB,CAAC,EAAE,CAAC;MACpBQ,aAAa,CAAC,IAAI,CAAC;MACnBW,mBAAmB,CAAC;QAClBC,eAAe,EAAE,KAAK;QACtBC,kBAAkB,EAAE,KAAK;QACzBC,uBAAuB,EAAE,KAAK;QAC9BC,OAAO,EAAE;MACX,CAAC,CAAC;IACJ,CAAC,MAAM;MACLzB,aAAa,CAAC,EAAE,CAAC;MACjBE,gBAAgB,CAAC,EAAE,CAAC;MACpBI,mBAAmB,CAAC,EAAE,CAAC;MACvBE,sBAAsB,CAAC,EAAE,CAAC;MAC1BE,aAAa,CAAC,IAAI,CAAC;IACrB;EACF,CAAC,EAAE,CAACP,gBAAgB,CAAC,CAAC;;EAEtB;EACAtB,SAAS,CAAC,MAAM;IACd,IAAIwB,gBAAgB,EAAE;MACpBmD,uBAAuB,CAACnD,gBAAgB,CAAC;MACzC;MACAG,sBAAsB,CAAC,EAAE,CAAC;IAC5B,CAAC,MAAM;MACLN,gBAAgB,CAAC,EAAE,CAAC;MACpBM,sBAAsB,CAAC,EAAE,CAAC;MAC1BE,aAAa,CAAC,IAAI,CAAC;MACnBW,mBAAmB,CAAC;QAClBC,eAAe,EAAE,KAAK;QACtBC,kBAAkB,EAAE,KAAK;QACzBC,uBAAuB,EAAE,KAAK;QAC9BC,OAAO,EAAE;MACX,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACpB,gBAAgB,CAAC,CAAC;;EAEtB;EACAxB,SAAS,CAAC,MAAM;IACd,IAAI0B,mBAAmB,EAAE;MACvBkD,0BAA0B,CAAClD,mBAAmB,CAAC;IACjD;EACF,CAAC,EAAE,CAACA,mBAAmB,CAAC,CAAC;;EAEzB;EACA,MAAMmB,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChCV,UAAU,CAAC0C,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE7D,SAAS,EAAE;IAAK,CAAC,CAAC,CAAC;IAClD,IAAI;MACF,MAAM4C,QAAQ,GAAG,MAAMtD,UAAU,CAACuD,YAAY,CAAC,CAAC;MAChD5C,YAAY,CAAC2C,QAAQ,CAACkB,IAAI,IAAI,EAAE,CAAC;IACnC,CAAC,CAAC,OAAOhB,KAAK,EAAE;MACdJ,OAAO,CAACI,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChD7B,SAAS,CAAC4C,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE7D,SAAS,EAAE;MAA2B,CAAC,CAAC,CAAC;IACzE,CAAC,SAAS;MACRmB,UAAU,CAAC0C,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE7D,SAAS,EAAE;MAAM,CAAC,CAAC,CAAC;IACrD;EACF,CAAC;;EAED;EACA,MAAM0D,cAAc,GAAG,MAAOxB,UAAU,IAAK;IAC3Cf,UAAU,CAAC0C,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE3D,UAAU,EAAE;IAAK,CAAC,CAAC,CAAC;IACnD,IAAI;MACF,MAAM0C,QAAQ,GAAG,MAAMtD,UAAU,CAACyE,uBAAuB,CAAC7B,UAAU,CAAC;MACrE/B,aAAa,CAACyC,QAAQ,CAACkB,IAAI,IAAI,EAAE,CAAC;IACpC,CAAC,CAAC,OAAOhB,KAAK,EAAE;MACdJ,OAAO,CAACI,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD7B,SAAS,CAAC4C,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE3D,UAAU,EAAE;MAA4B,CAAC,CAAC,CAAC;MACzEC,aAAa,CAAC,EAAE,CAAC;IACnB,CAAC,SAAS;MACRgB,UAAU,CAAC0C,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE3D,UAAU,EAAE;MAAM,CAAC,CAAC,CAAC;IACtD;EACF,CAAC;;EAED;EACA,MAAM8D,iBAAiB,GAAG,MAAO5B,UAAU,IAAK;IAC9CjB,UAAU,CAAC0C,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAEzD,aAAa,EAAE;IAAK,CAAC,CAAC,CAAC;IACtD,IAAI;MACF,MAAMwC,QAAQ,GAAG,MAAMtD,UAAU,CAAC2E,0BAA0B,CAAC7B,UAAU,CAAC;MACxE/B,gBAAgB,CAACuC,QAAQ,CAACkB,IAAI,IAAI,EAAE,CAAC;IACvC,CAAC,CAAC,OAAOhB,KAAK,EAAE;MACdJ,OAAO,CAACI,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD7B,SAAS,CAAC4C,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEzD,aAAa,EAAE;MAA+B,CAAC,CAAC,CAAC;MAC/EC,gBAAgB,CAAC,EAAE,CAAC;IACtB,CAAC,SAAS;MACRc,UAAU,CAAC0C,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEzD,aAAa,EAAE;MAAM,CAAC,CAAC,CAAC;IACzD;EACF,CAAC;;EAED;EACA,MAAMuD,uBAAuB,GAAG,MAAOvB,UAAU,IAAK;IACpDjB,UAAU,CAAC0C,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAEzC,IAAI,EAAE;IAAK,CAAC,CAAC,CAAC;IAE7C,IAAI;MACF;MACA,MAAMK,eAAe,GAAGpC,iBAAiB,CAAC6E,kBAAkB,CAACC,QAAQ,CAAC/B,UAAU,CAAC,CAAC;MAElF,IAAIX,eAAe,EAAE;QACnB;QACA,MAAM2C,YAAY,GAAG/E,iBAAiB,CAACgF,cAAc,CAAC,UAAU,EAAEF,QAAQ,CAAC/B,UAAU,CAAC,CAAC;QACvF;QACA,IAAIgC,YAAY,IAAIA,YAAY,CAAC5B,MAAM,EAAE;UACvC4B,YAAY,CAAC5B,MAAM,GAAGC,iBAAiB,CAAC2B,YAAY,CAAC5B,MAAM,CAAC;QAC9D;QACA3B,aAAa,CAACuD,YAAY,CAAC;QAC3B5C,mBAAmB,CAAC;UAClBC,eAAe,EAAE,IAAI;UACrBC,kBAAkB,EAAE,KAAK;UACzBC,uBAAuB,EAAE,KAAK;UAC9BC,OAAO,EAAE;QACX,CAAC,CAAC;MACJ,CAAC,MAAM;QACL;QACA,MAAMoC,iBAAiB,CAAC5B,UAAU,CAAC;QACnC;QACA,MAAME,WAAW,GAAGjD,iBAAiB,CAACkD,oBAAoB,CAAC,CAAC;QAC5D;QACA,IAAID,WAAW,CAACE,MAAM,EAAE;UACtBF,WAAW,CAACE,MAAM,GAAGC,iBAAiB,CAACH,WAAW,CAACE,MAAM,CAAC;QAC5D;QACA3B,aAAa,CAACyB,WAAW,CAAC;QAC1Bd,mBAAmB,CAAC;UAClBC,eAAe,EAAE,KAAK;UACtBC,kBAAkB,EAAE,KAAK;UACzBC,uBAAuB,EAAE,IAAI;UAC7BC,OAAO,EAAE;QACX,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAOkB,KAAK,EAAE;MACdJ,OAAO,CAACI,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;MAC1D7B,SAAS,CAAC4C,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEzC,IAAI,EAAE;MAAmC,CAAC,CAAC,CAAC;IAC5E,CAAC,SAAS;MACRD,UAAU,CAAC0C,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEzC,IAAI,EAAE;MAAM,CAAC,CAAC,CAAC;IAChD;EACF,CAAC;;EAED;EACA,MAAMwC,0BAA0B,GAAG,MAAOvB,aAAa,IAAK;IAC1DlB,UAAU,CAAC0C,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAEzC,IAAI,EAAE;IAAK,CAAC,CAAC,CAAC;IAE7C,IAAI;MACF;MACA,MAAMM,kBAAkB,GAAGrC,iBAAiB,CAACiF,qBAAqB,CAACH,QAAQ,CAAC9B,aAAa,CAAC,CAAC;MAE3F,IAAIX,kBAAkB,EAAE;QACtB;QACA,MAAM6C,eAAe,GAAGlF,iBAAiB,CAACgF,cAAc,CAAC,aAAa,EAAEF,QAAQ,CAAC9B,aAAa,CAAC,CAAC;QAChG;QACA,IAAIkC,eAAe,IAAIA,eAAe,CAAC/B,MAAM,EAAE;UAC7C+B,eAAe,CAAC/B,MAAM,GAAGC,iBAAiB,CAAC8B,eAAe,CAAC/B,MAAM,CAAC;QACpE;QACA3B,aAAa,CAAC0D,eAAe,CAAC;QAC9B/C,mBAAmB,CAAC;UAClBC,eAAe,EAAE,KAAK;UACtBC,kBAAkB,EAAE,IAAI;UACxBC,uBAAuB,EAAE,IAAI;UAC7BC,OAAO,EAAE;QACX,CAAC,CAAC;MACJ,CAAC,MAAM;QACL;QACA,MAAMU,WAAW,GAAGjD,iBAAiB,CAACkD,oBAAoB,CAAC,CAAC;QAC5D;QACA,IAAID,WAAW,CAACE,MAAM,EAAE;UACtBF,WAAW,CAACE,MAAM,GAAGC,iBAAiB,CAACH,WAAW,CAACE,MAAM,CAAC;QAC5D;QACA3B,aAAa,CAACyB,WAAW,CAAC;QAC1Bd,mBAAmB,CAAC;UAClBC,eAAe,EAAE,KAAK;UACtBC,kBAAkB,EAAE,KAAK;UACzBC,uBAAuB,EAAE,IAAI;UAC7BC,OAAO,EAAE;QACX,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAOkB,KAAK,EAAE;MACdJ,OAAO,CAACI,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;MAC7D7B,SAAS,CAAC4C,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEzC,IAAI,EAAE;MAAmC,CAAC,CAAC,CAAC;IAC5E,CAAC,SAAS;MACRD,UAAU,CAAC0C,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEzC,IAAI,EAAE;MAAM,CAAC,CAAC,CAAC;IAChD;EACF,CAAC;;EAED;EACA,MAAMoD,oBAAoB,GAAIC,CAAC,IAAK;IAClC,MAAMC,KAAK,GAAGD,CAAC,CAACE,MAAM,CAACD,KAAK;IAC5BnE,mBAAmB,CAACmE,KAAK,CAAC;;IAE1B;IACA3D,WAAW,CAAC8C,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP3B,UAAU,EAAEwC,KAAK,GAAGP,QAAQ,CAACO,KAAK,CAAC,GAAG,IAAI;MAC1CtC,UAAU,EAAE,IAAI;MAChBC,aAAa,EAAE;IACjB,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMuC,oBAAoB,GAAIH,CAAC,IAAK;IAClC,MAAMC,KAAK,GAAGD,CAAC,CAACE,MAAM,CAACD,KAAK;IAC5BjE,mBAAmB,CAACiE,KAAK,CAAC;;IAE1B;IACA3D,WAAW,CAAC8C,IAAI,KAAK;MACnB,GAAGA,IAAI;MACPzB,UAAU,EAAEsC,KAAK,GAAGP,QAAQ,CAACO,KAAK,CAAC,GAAG,IAAI;MAC1CrC,aAAa,EAAE;IACjB,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMwC,uBAAuB,GAAIJ,CAAC,IAAK;IACrC,MAAMC,KAAK,GAAGD,CAAC,CAACE,MAAM,CAACD,KAAK;IAC5B/D,sBAAsB,CAAC+D,KAAK,CAAC;;IAE7B;IACA3D,WAAW,CAAC8C,IAAI,KAAK;MACnB,GAAGA,IAAI;MACPxB,aAAa,EAAEqC,KAAK,GAAGP,QAAQ,CAACO,KAAK,CAAC,GAAG;IAC3C,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMI,iBAAiB,GAAGA,CAACC,QAAQ,EAAEL,KAAK,KAAK;IAC7C3D,WAAW,CAAC8C,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACkB,QAAQ,GAAGL;IACd,CAAC,CAAC,CAAC;;IAEH;IACA,IAAI1D,MAAM,CAAC+D,QAAQ,CAAC,EAAE;MACpB9D,SAAS,CAAC4C,IAAI,KAAK;QACjB,GAAGA,IAAI;QACP,CAACkB,QAAQ,GAAG;MACd,CAAC,CAAC,CAAC;IACL;EACF,CAAC;EAED,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,SAAS,GAAG,CAAC,CAAC;;IAEpB;IACA,IAAI,CAAC3E,gBAAgB,EAAE;MACrB2E,SAAS,CAACC,QAAQ,GAAG,sBAAsB;IAC7C;IACA,IAAI,CAAC1E,gBAAgB,EAAE;MACrByE,SAAS,CAACE,QAAQ,GAAG,sBAAsB;IAC7C;;IAEA;IACA,IAAI,CAACvE,UAAU,EAAE;MACfqE,SAAS,CAAC7D,IAAI,GAAG,kDAAkD;MACnE,OAAO;QAAEgE,OAAO,EAAE,KAAK;QAAEpE,MAAM,EAAEiE;MAAU,CAAC;IAC9C;;IAIA;IACArE,UAAU,CAAC4B,MAAM,CAACU,OAAO,CAACC,KAAK,IAAI;MACjC,MAAMuB,KAAK,GAAG5D,QAAQ,CAACqC,KAAK,CAACE,GAAG,CAAC;;MAEjC;MACA,IAAIF,KAAK,CAACkC,QAAQ,KAAK,CAACX,KAAK,IAAK,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACY,IAAI,CAAC,CAAC,KAAK,EAAG,CAAC,EAAE;QACpFL,SAAS,CAAC9B,KAAK,CAACE,GAAG,CAAC,GAAG,GAAGF,KAAK,CAACoC,KAAK,cAAc;QACnD;MACF;;MAEA;MACA,IAAIpC,KAAK,CAACqC,WAAW,IAAIC,eAAe,CAACtC,KAAK,CAAC,EAAE;QAC/C,IAAIA,KAAK,CAACkC,QAAQ,KAAK,CAACX,KAAK,IAAK,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACY,IAAI,CAAC,CAAC,KAAK,EAAG,CAAC,EAAE;UACpFL,SAAS,CAAC9B,KAAK,CAACE,GAAG,CAAC,GAAG,GAAGF,KAAK,CAACoC,KAAK,cAAc;UACnD;QACF;MACF;;MAEA;MACA,IAAIb,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACY,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;QAAA,IAAAI,iBAAA,EAAAC,kBAAA,EAAAC,kBAAA;QAC7D,QAAQzC,KAAK,CAAC0C,IAAI;UAChB,KAAK,OAAO;YACV,MAAMC,UAAU,GAAG,4BAA4B;YAC/C,IAAI,CAACA,UAAU,CAACC,IAAI,CAACrB,KAAK,CAAC,EAAE;cAC3BO,SAAS,CAAC9B,KAAK,CAACE,GAAG,CAAC,GAAG,oCAAoC;YAC7D;YACA;UACF,KAAK,KAAK;YACR,MAAM2C,UAAU,GAAG,qCAAqC;YACxD,IAAI,CAACA,UAAU,CAACD,IAAI,CAACrB,KAAK,CAAC,EAAE;cAC3BO,SAAS,CAAC9B,KAAK,CAACE,GAAG,CAAC,GAAG,oCAAoC;YAC7D;YACA;UACF,KAAK,KAAK;YACR,IAAI;cACF,IAAI4C,GAAG,CAACvB,KAAK,CAAC;YAChB,CAAC,CAAC,MAAM;cACNO,SAAS,CAAC9B,KAAK,CAACE,GAAG,CAAC,GAAG,0BAA0B;YACnD;YACA;UACF,KAAK,QAAQ;YACX,IAAIF,KAAK,CAAC+C,UAAU,EAAE;cACpB,MAAMC,QAAQ,GAAGC,UAAU,CAAC1B,KAAK,CAAC;cAClC,IAAIvB,KAAK,CAAC+C,UAAU,CAACG,GAAG,KAAKC,SAAS,IAAIH,QAAQ,GAAGhD,KAAK,CAAC+C,UAAU,CAACG,GAAG,EAAE;gBACzEpB,SAAS,CAAC9B,KAAK,CAACE,GAAG,CAAC,GAAG,0BAA0BF,KAAK,CAAC+C,UAAU,CAACG,GAAG,EAAE;cACzE;cACA,IAAIlD,KAAK,CAAC+C,UAAU,CAACK,GAAG,KAAKD,SAAS,IAAIH,QAAQ,GAAGhD,KAAK,CAAC+C,UAAU,CAACK,GAAG,EAAE;gBACzEtB,SAAS,CAAC9B,KAAK,CAACE,GAAG,CAAC,GAAG,yBAAyBF,KAAK,CAAC+C,UAAU,CAACK,GAAG,EAAE;cACxE;YACF;YACA;QACJ;;QAEA;QACA,KAAAb,iBAAA,GAAIvC,KAAK,CAAC+C,UAAU,cAAAR,iBAAA,eAAhBA,iBAAA,CAAkBc,OAAO,EAAE;UAC7B,MAAMC,KAAK,GAAG,IAAIC,MAAM,CAACvD,KAAK,CAAC+C,UAAU,CAACM,OAAO,CAAC;UAClD,IAAI,CAACC,KAAK,CAACV,IAAI,CAACrB,KAAK,CAAC,EAAE;YACtBO,SAAS,CAAC9B,KAAK,CAACE,GAAG,CAAC,GAAG,GAAGF,KAAK,CAACoC,KAAK,oBAAoB;UAC3D;QACF;;QAEA;QACA,IAAI,CAAAI,kBAAA,GAAAxC,KAAK,CAAC+C,UAAU,cAAAP,kBAAA,eAAhBA,kBAAA,CAAkBgB,SAAS,IAAIjC,KAAK,CAACjB,MAAM,GAAGN,KAAK,CAAC+C,UAAU,CAACS,SAAS,EAAE;UAC5E1B,SAAS,CAAC9B,KAAK,CAACE,GAAG,CAAC,GAAG,GAAGF,KAAK,CAACoC,KAAK,qBAAqBpC,KAAK,CAAC+C,UAAU,CAACS,SAAS,aAAa;QACnG;QACA,IAAI,CAAAf,kBAAA,GAAAzC,KAAK,CAAC+C,UAAU,cAAAN,kBAAA,eAAhBA,kBAAA,CAAkBgB,SAAS,IAAIlC,KAAK,CAACjB,MAAM,GAAGN,KAAK,CAAC+C,UAAU,CAACU,SAAS,EAAE;UAC5E3B,SAAS,CAAC9B,KAAK,CAACE,GAAG,CAAC,GAAG,GAAGF,KAAK,CAACoC,KAAK,oBAAoBpC,KAAK,CAAC+C,UAAU,CAACU,SAAS,aAAa;QAClG;MACF;IACF,CAAC,CAAC;;IAEF;IACA,IAAI9F,QAAQ,CAAC+F,SAAS,IAAI/F,QAAQ,CAACgG,cAAc,IAAIhG,QAAQ,CAACiG,WAAW,EAAE;MACzE,MAAMC,SAAS,GAAG,IAAIC,IAAI,CAACnG,QAAQ,CAACiG,WAAW,CAAC;MAChD,MAAMG,YAAY,GAAG,IAAID,IAAI,CAACnG,QAAQ,CAACgG,cAAc,CAAC;MACtD,IAAII,YAAY,IAAIF,SAAS,EAAE;QAC7B/B,SAAS,CAAC6B,cAAc,GAAG,wCAAwC;MACrE;IACF;IAEA,OAAO;MACL1B,OAAO,EAAE+B,MAAM,CAACC,IAAI,CAACnC,SAAS,CAAC,CAACxB,MAAM,KAAK,CAAC;MAC5CzC,MAAM,EAAEiE;IACV,CAAC;EACH,CAAC;EAED,MAAMoC,YAAY,GAAG,MAAO5C,CAAC,IAAK;IAChCA,CAAC,CAAC6C,cAAc,CAAC,CAAC;IAElB,MAAMpB,UAAU,GAAGlB,YAAY,CAAC,CAAC;IACjC,IAAI,CAACkB,UAAU,CAACd,OAAO,EAAE;MACvB1C,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEuD,UAAU,CAAClF,MAAM,CAAC;MACpDC,SAAS,CAACiF,UAAU,CAAClF,MAAM,CAAC;MAC5B;IACF;IAEAM,aAAa,CAAC,IAAI,CAAC;IACnB,IAAI;MACF;MACA,MAAMiG,UAAU,GAAG;QACjB;QACArF,UAAU,EAAEiC,QAAQ,CAAC7D,gBAAgB,CAAC;QACtC8B,UAAU,EAAE+B,QAAQ,CAAC3D,gBAAgB,CAAC;QACtC6B,aAAa,EAAE3B,mBAAmB,GAAGyD,QAAQ,CAACzD,mBAAmB,CAAC,GAAG,IAAI;QAEzE;QACA8G,IAAI,EAAE1G,QAAQ,CAAC0G,IAAI,IAAI,EAAE;QACzBC,YAAY,EAAE3G,QAAQ,CAAC2G,YAAY,IAAI,EAAE;QACzCC,MAAM,EAAE5G,QAAQ,CAAC4G,MAAM,GAAGvD,QAAQ,CAACrD,QAAQ,CAAC4G,MAAM,CAAC,GAAG,IAAI;QAE1D;QACAC,MAAM,EAAE7G,QAAQ,CAAC6G,MAAM,GAAGxD,QAAQ,CAACrD,QAAQ,CAAC6G,MAAM,CAAC,GAAG,IAAI;QAE1D;QACAC,gBAAgB,EAAEC,KAAK,CAACC,OAAO,CAAChH,QAAQ,CAAC8G,gBAAgB,CAAC,GACtD9G,QAAQ,CAAC8G,gBAAgB,GACzB9G,QAAQ,CAAC8G,gBAAgB,GAAG9G,QAAQ,CAAC8G,gBAAgB,CAACG,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,CAAC,IAAIA,CAAC,CAAC3C,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE;QAC5F4C,cAAc,EAAEpH,QAAQ,CAACoH,cAAc,IAAI,EAAE;QAC7CC,iBAAiB,EAAEN,KAAK,CAACC,OAAO,CAAChH,QAAQ,CAACqH,iBAAiB,CAAC,GACxDrH,QAAQ,CAACqH,iBAAiB,GAC1BrH,QAAQ,CAACqH,iBAAiB,GAAGrH,QAAQ,CAACqH,iBAAiB,CAACJ,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,CAAC,IAAIA,CAAC,CAAC3C,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE;QAC9F8C,OAAO,EAAEtH,QAAQ,CAACsH,OAAO,IAAI,EAAE;QAE/B;QACArB,WAAW,EAAEjG,QAAQ,CAACiG,WAAW,IAAI,IAAI;QACzCF,SAAS,EAAE/F,QAAQ,CAAC+F,SAAS,IAAI,KAAK;QACtCC,cAAc,EAAEhG,QAAQ,CAACgG,cAAc,IAAI,IAAI;QAE/C;QACAuB,YAAY,EAAEvH,QAAQ,CAACuH,YAAY,IAAI,EAAE;QACzCC,aAAa,EAAExH,QAAQ,CAACwH,aAAa,IAAI,EAAE;QAC3CC,QAAQ,EAAEzH,QAAQ,CAACyH,QAAQ,IAAI,EAAE;QACjCC,OAAO,EAAE1H,QAAQ,CAAC0H,OAAO,IAAI,EAAE;QAC/BC,WAAW,EAAE3H,QAAQ,CAAC2H,WAAW,IAAI,EAAE;QAEvC;QACAC,YAAY,EAAE5H,QAAQ,CAAC4H,YAAY,IAAI,KAAK;QAC5CC,aAAa,EAAE7H,QAAQ,CAAC6H,aAAa,IAAI,EAAE;QAC3CC,iBAAiB,EAAE9H,QAAQ,CAAC8H,iBAAiB,IAAI,EAAE;QACnDC,eAAe,EAAE/H,QAAQ,CAAC+H,eAAe,IAAI,EAAE;QAE/C;QACAC,YAAY,EAAEhI,QAAQ,CAACgI,YAAY,IAAI,KAAK;QAC5CC,WAAW,EAAEjI,QAAQ,CAACiI,WAAW,IAAI,EAAE;QACvCC,WAAW,EAAElI,QAAQ,CAACkI,WAAW,IAAI,KAAK;QAC1CC,UAAU,EAAEnI,QAAQ,CAACmI,UAAU,IAAI,EAAE;QAErC;QACAC,gBAAgB,EAAEpI,QAAQ,CAACoI,gBAAgB,GAAG9C,UAAU,CAACtF,QAAQ,CAACoI,gBAAgB,CAAC,GAAG,IAAI;QAC1FC,sBAAsB,EAAErI,QAAQ,CAACqI,sBAAsB,IAAI,EAAE;QAC7DC,eAAe,EAAEvB,KAAK,CAACC,OAAO,CAAChH,QAAQ,CAACsI,eAAe,CAAC,GACpDtI,QAAQ,CAACsI,eAAe,CAACpB,GAAG,CAACqB,EAAE,IAAIlF,QAAQ,CAACkF,EAAE,CAAC,CAAC,GAChD,EAAE;QACNC,UAAU,EAAExI,QAAQ,CAACwI,UAAU,GAAGnF,QAAQ,CAACrD,QAAQ,CAACwI,UAAU,CAAC,GAAG,IAAI;QACtEC,MAAM,EAAEzI,QAAQ,CAACyI,MAAM,IAAI,EAAE;QAC7BC,OAAO,EAAE1I,QAAQ,CAAC0I,OAAO,IAAI,EAAE;QAE/B;QACAC,QAAQ,EAAE3I,QAAQ,CAAC2I,QAAQ,IAAI,EAAE;QACjCC,eAAe,EAAE5I,QAAQ,CAAC4I,eAAe,GAAGvF,QAAQ,CAACrD,QAAQ,CAAC4I,eAAe,CAAC,GAAG,IAAI;QACrFC,gBAAgB,EAAE7I,QAAQ,CAAC6I,gBAAgB,GAAGxF,QAAQ,CAACrD,QAAQ,CAAC6I,gBAAgB,CAAC,GAAG,IAAI;QACxFC,qBAAqB,EAAE9I,QAAQ,CAAC8I,qBAAqB,GAAGzF,QAAQ,CAACrD,QAAQ,CAAC8I,qBAAqB,CAAC,GAAG,IAAI;QAEvG;QACAC,oBAAoB,EAAE/I,QAAQ,CAAC+I,oBAAoB,IAAI,EAAE;QACzDC,qBAAqB,EAAEhJ,QAAQ,CAACgJ,qBAAqB,IAAI,EAAE;QAC3DC,WAAW,EAAEjJ,QAAQ,CAACiJ,WAAW,IAAI,EAAE;QAEvC;QACAC,gBAAgB,EAAElJ,QAAQ,CAACkJ,gBAAgB,IAAI,EAAE;QACjDC,oBAAoB,EAAEnJ,QAAQ,CAACmJ,oBAAoB,IAAI,EAAE;QACzDC,cAAc,EAAEpJ,QAAQ,CAACoJ,cAAc,IAAI,EAAE;QAC7CC,UAAU,EAAErJ,QAAQ,CAACqJ,UAAU,IAAI;MACrC,CAAC;;MAED;MACAzH,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE4E,UAAU,CAAC;MAClD7E,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAErC,gBAAgB,CAAC;MACnDoC,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEnC,gBAAgB,CAAC;MACnDkC,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEjC,mBAAmB,CAAC;;MAEzD;MACA,MAAM0J,mBAAmB,GAAG;QAC1BlI,UAAU,EAAEqF,UAAU,CAACrF,UAAU;QACjCE,UAAU,EAAEmF,UAAU,CAACnF,UAAU;QACjCoF,IAAI,EAAED,UAAU,CAACC,IAAI;QACrBC,YAAY,EAAEF,UAAU,CAACE,YAAY;QACrCC,MAAM,EAAEH,UAAU,CAACG;MACrB,CAAC;MACDhF,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEyH,mBAAmB,CAAC;;MAE1D;MACA,MAAMC,aAAa,GAAGlD,MAAM,CAACmD,OAAO,CAACF,mBAAmB,CAAC,CACtDG,MAAM,CAAC,CAAC,GAAG7F,KAAK,CAAC,KAAK,CAACA,KAAK,IAAIA,KAAK,KAAK,EAAE,IAAIA,KAAK,KAAK,IAAI,CAAC,CAC/DsD,GAAG,CAAC,CAAC,CAAC3E,GAAG,CAAC,KAAKA,GAAG,CAAC;MAEtB,IAAIgH,aAAa,CAAC5G,MAAM,GAAG,CAAC,EAAE;QAC5Bf,OAAO,CAACI,KAAK,CAAC,0BAA0B,EAAEuH,aAAa,CAAC;QACxDpJ,SAAS,CAAC;UAAEuJ,OAAO,EAAE,2BAA2B,GAAGH,aAAa,CAACI,IAAI,CAAC,IAAI;QAAE,CAAC,CAAC;QAC9E;MACF;MAEA,IAAIC,MAAM;MACV,IAAI5K,IAAI,KAAK,QAAQ,EAAE;QACrB4K,MAAM,GAAG,MAAMpL,UAAU,CAACqL,YAAY,CAACpD,UAAU,CAAC;MACpD,CAAC,MAAM;QACLmD,MAAM,GAAG,MAAMpL,UAAU,CAACsL,YAAY,CAAC/K,WAAW,CAACgL,EAAE,EAAEtD,UAAU,CAAC;MACpE;MAEA5H,QAAQ,CAAC+K,MAAM,CAAC;IAClB,CAAC,CAAC,OAAO5H,KAAK,EAAE;MAAA,IAAAgI,WAAA;MACdpI,OAAO,CAACI,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9CJ,OAAO,CAACI,KAAK,CAAC,aAAa,EAAEA,KAAK,CAACgB,IAAI,CAAC;MACxCpB,OAAO,CAACI,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAE1C,KAAAgI,WAAA,GAAIhI,KAAK,CAACgB,IAAI,cAAAgH,WAAA,eAAVA,WAAA,CAAY9J,MAAM,EAAE;QACtB;QACA,MAAM+J,aAAa,GAAG,CAAC,CAAC;QACxB5D,MAAM,CAACC,IAAI,CAACtE,KAAK,CAACgB,IAAI,CAAC9C,MAAM,CAAC,CAACkC,OAAO,CAACG,GAAG,IAAI;UAC5C,MAAM2H,aAAa,GAAGlI,KAAK,CAACgB,IAAI,CAAC9C,MAAM,CAACqC,GAAG,CAAC;UAC5C0H,aAAa,CAAC1H,GAAG,CAAC4H,WAAW,CAAC,CAAC,CAAC,GAAGpD,KAAK,CAACC,OAAO,CAACkD,aAAa,CAAC,GAC3DA,aAAa,CAACP,IAAI,CAAC,IAAI,CAAC,GACxBO,aAAa;QACnB,CAAC,CAAC;QACFtI,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEoI,aAAa,CAAC;QACxD9J,SAAS,CAAC8J,aAAa,CAAC;MAC1B,CAAC,MAAM,IAAIjI,KAAK,CAACoI,iBAAiB,IAAIpI,KAAK,CAACoI,iBAAiB,CAAC,CAAC,EAAE;QAC/DjK,SAAS,CAAC6B,KAAK,CAACqI,mBAAmB,CAAC,CAAC,CAAC;MACxC,CAAC,MAAM;QAAA,IAAAC,YAAA,EAAAC,YAAA;QACL,MAAMC,YAAY,GAAG,EAAAF,YAAA,GAAAtI,KAAK,CAACgB,IAAI,cAAAsH,YAAA,uBAAVA,YAAA,CAAYxJ,OAAO,OAAAyJ,YAAA,GACrBvI,KAAK,CAACgB,IAAI,cAAAuH,YAAA,uBAAVA,YAAA,CAAYE,KAAK,KACjBzI,KAAK,CAAClB,OAAO,IACb,QAAQkB,KAAK,CAAC0I,MAAM,IAAI,SAAS,mCAAmC;QACvF9I,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE2I,YAAY,CAAC;QACnDrK,SAAS,CAAC;UAAEuJ,OAAO,EAAEc;QAAa,CAAC,CAAC;MACtC;IACF,CAAC,SAAS;MACRhK,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;EAED,MAAMmE,eAAe,GAAItC,KAAK,IAAK;IACjC,IAAI,CAACA,KAAK,CAACqC,WAAW,EAAE,OAAO,IAAI;IAEnC,MAAMiG,cAAc,GAAG3K,QAAQ,CAACqC,KAAK,CAACqC,WAAW,CAACrC,KAAK,CAAC;IACxD,MAAMuI,aAAa,GAAGvI,KAAK,CAACqC,WAAW,CAACd,KAAK;;IAE7C;IACA,IAAI,OAAOgH,aAAa,KAAK,SAAS,IAAIA,aAAa,KAAK,MAAM,IAAIA,aAAa,KAAK,OAAO,EAAE;MAC/F,OAAOD,cAAc,MAAMC,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,MAAM,CAAC;IAChF;IAEA,OAAOD,cAAc,KAAKC,aAAa;EACzC,CAAC;EAED,MAAMC,qBAAqB,GAAGA,CAAA,KAAM;IAClC,IAAI,CAAC/K,UAAU,IAAI,CAACA,UAAU,CAAC4B,MAAM,EAAE,OAAO,CAAC,CAAC;;IAEhD;IACA,MAAMoJ,SAAS,GAAGhL,UAAU,CAAC4B,MAAM,CAACwF,GAAG,CAAC7E,KAAK,IAAIA,KAAK,CAACE,GAAG,CAAC;IAC3D,MAAMwI,aAAa,GAAGD,SAAS,CAACrB,MAAM,CAAC,CAAClH,GAAG,EAAEyI,KAAK,KAAKF,SAAS,CAACG,OAAO,CAAC1I,GAAG,CAAC,KAAKyI,KAAK,CAAC;IACxF,IAAID,aAAa,CAACpI,MAAM,GAAG,CAAC,EAAE;MAC5Bf,OAAO,CAACc,IAAI,CAAC,6BAA6B,EAAEqI,aAAa,CAAC;MAC1DnJ,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE/B,UAAU,CAAC4B,MAAM,CAAC;IAC/C;IAEA,MAAMwJ,QAAQ,GAAG,CAAC,CAAC;IACnBpL,UAAU,CAAC4B,MAAM,CAACU,OAAO,CAAEC,KAAK,IAAK;MACnC,MAAM8I,UAAU,GAAG9I,KAAK,CAAC+I,OAAO,IAAI,SAAS;MAC7C,IAAI,CAACF,QAAQ,CAACC,UAAU,CAAC,EAAE;QACzBD,QAAQ,CAACC,UAAU,CAAC,GAAG;UACrBV,KAAK,EAAEY,eAAe,CAACF,UAAU,CAAC;UAClCzJ,MAAM,EAAE;QACV,CAAC;MACH;MACAwJ,QAAQ,CAACC,UAAU,CAAC,CAACzJ,MAAM,CAACe,IAAI,CAACJ,KAAK,CAAC;IACzC,CAAC,CAAC;IAEF,OAAO6I,QAAQ;EACjB,CAAC;EAED,MAAMG,eAAe,GAAIF,UAAU,IAAK;IACtC,MAAMG,MAAM,GAAG;MACbC,YAAY,EAAE,sBAAsB;MACpCC,WAAW,EAAE,qBAAqB;MAClCC,YAAY,EAAE,sBAAsB;MACpCC,YAAY,EAAE,sBAAsB;MACpCC,aAAa,EAAE,uBAAuB;MACtCC,eAAe,EAAE,kBAAkB;MACnCC,WAAW,EAAE,qBAAqB;MAClCC,gBAAgB,EAAE,mBAAmB;MACrCC,aAAa,EAAE,uBAAuB;MACtCrC,OAAO,EAAE;IACX,CAAC;IACD,OAAO4B,MAAM,CAACH,UAAU,CAAC,IAAIA,UAAU;EACzC,CAAC;EAED,MAAMD,QAAQ,GAAGpL,UAAU,GAAG+K,qBAAqB,CAAC,CAAC,GAAG,CAAC,CAAC;EAI1D,oBACElM,OAAA;IAAKqN,SAAS,EAAC,qBAAqB;IAAAC,QAAA,gBAClCtN,OAAA;MAAKqN,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BtN,OAAA;QAAAsN,QAAA,EAAKjN,IAAI,KAAK,QAAQ,GAAG,mBAAmB,GAAG;MAAa;QAAAkN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,EACjEvM,UAAU,iBACTnB,OAAA;QAAKqN,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBtN,OAAA;UAAMqN,SAAS,EAAC,WAAW;UAAAC,QAAA,EAAEnM,UAAU,CAAC4G;QAAI;UAAAwF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,EACnDvM,UAAU,CAACwM,WAAW,iBACrB3N,OAAA;UAAMqN,SAAS,EAAC,kBAAkB;UAAAC,QAAA,EAAEnM,UAAU,CAACwM;QAAW;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAClE;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAELnM,MAAM,CAACwJ,OAAO,iBACb/K,OAAA;MAAKqN,SAAS,EAAC,mBAAmB;MAAAC,QAAA,EAAE/L,MAAM,CAACwJ;IAAO;MAAAwC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CACzD,eAED1N,OAAA;MAAME,QAAQ,EAAE0H,YAAa;MAACyF,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAEnDtN,OAAA;QAAKqN,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BtN,OAAA;UAAAsN,QAAA,EAAI;QAA6B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAGtC1N,OAAA;UAAKqN,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBtN,OAAA;YAAOqN,SAAS,EAAC,YAAY;YAAAC,QAAA,GAAC,WACnB,eAAAtN,OAAA;cAAMqN,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC,eACR1N,OAAA;YACEiF,KAAK,EAAEpE,gBAAiB;YACxB+M,QAAQ,EAAE7I,oBAAqB;YAC/B8I,QAAQ,EAAEpM,OAAO,CAAClB,SAAU;YAC5B8M,SAAS,EAAE,eAAe9L,MAAM,CAACkE,QAAQ,GAAG,OAAO,GAAG,EAAE,EAAG;YAC3DG,QAAQ;YAAA0H,QAAA,gBAERtN,OAAA;cAAQiF,KAAK,EAAC,EAAE;cAAAqI,QAAA,EACb7L,OAAO,CAAClB,SAAS,GAAG,sBAAsB,GAAG;YAAiB;cAAAgN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzD,CAAC,EACRnN,SAAS,CAACgI,GAAG,CAAC9C,QAAQ,iBACrBzF,OAAA;cAA0BiF,KAAK,EAAEQ,QAAQ,CAAC2F,EAAG;cAAAkC,QAAA,EAC1C7H,QAAQ,CAACsC;YAAI,GADHtC,QAAQ,CAAC2F,EAAE;cAAAmC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEhB,CACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,EACRnM,MAAM,CAACkE,QAAQ,iBACdzF,OAAA;YAAKqN,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAE/L,MAAM,CAACkE;UAAQ;YAAA8H,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACtD,EACAnM,MAAM,CAAChB,SAAS,iBACfP,OAAA;YAAKqN,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAE/L,MAAM,CAAChB;UAAS;YAAAgN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACvD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGN1N,OAAA;UAAKqN,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBtN,OAAA;YAAOqN,SAAS,EAAC,YAAY;YAAAC,QAAA,GAAC,WACnB,eAAAtN,OAAA;cAAMqN,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC,eACR1N,OAAA;YACEiF,KAAK,EAAElE,gBAAiB;YACxB6M,QAAQ,EAAEzI,oBAAqB;YAC/B0I,QAAQ,EAAE,CAAChN,gBAAgB,IAAIY,OAAO,CAAChB,UAAW;YAClD4M,SAAS,EAAE,eAAe9L,MAAM,CAACmE,QAAQ,GAAG,OAAO,GAAG,EAAE,EAAG;YAC3DE,QAAQ;YAAA0H,QAAA,gBAERtN,OAAA;cAAQiF,KAAK,EAAC,EAAE;cAAAqI,QAAA,EACb,CAACzM,gBAAgB,GACd,uBAAuB,GACvBY,OAAO,CAAChB,UAAU,GAChB,uBAAuB,GACvB;YAAiB;cAAA8M,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEjB,CAAC,EACRjN,UAAU,CAAC8H,GAAG,CAAC7C,QAAQ,iBACtB1F,OAAA;cAA0BiF,KAAK,EAAES,QAAQ,CAAC0F,EAAG;cAAAkC,QAAA,EAC1C5H,QAAQ,CAACqC;YAAI,GADHrC,QAAQ,CAAC0F,EAAE;cAAAmC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEhB,CACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,EACRnM,MAAM,CAACmE,QAAQ,iBACd1F,OAAA;YAAKqN,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAE/L,MAAM,CAACmE;UAAQ;YAAA6H,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACtD,EACAnM,MAAM,CAACd,UAAU,iBAChBT,OAAA;YAAKqN,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAE/L,MAAM,CAACd;UAAU;YAAA8M,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACxD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,EAGL5L,gBAAgB,CAACI,uBAAuB,iBACvClC,OAAA;UAAKqN,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBtN,OAAA;YAAOqN,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAE9B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACR1N,OAAA;YACEiF,KAAK,EAAEhE,mBAAoB;YAC3B2M,QAAQ,EAAExI,uBAAwB;YAClCyI,QAAQ,EAAE,CAAC9M,gBAAgB,IAAIU,OAAO,CAACd,aAAc;YACrD0M,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAEvBtN,OAAA;cAAQiF,KAAK,EAAC,EAAE;cAAAqI,QAAA,EACb,CAACvM,gBAAgB,GACd,uBAAuB,GACvBU,OAAO,CAACd,aAAa,GACnB,0BAA0B,GAC1B;YAA+B;cAAA4M,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAE/B,CAAC,EACR/M,aAAa,CAAC4H,GAAG,CAACuF,WAAW,iBAC5B9N,OAAA;cAA6BiF,KAAK,EAAE6I,WAAW,CAAC1C,EAAG;cAAAkC,QAAA,EAChDQ,WAAW,CAAC/F;YAAI,GADN+F,WAAW,CAAC1C,EAAE;cAAAmC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEnB,CACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,EACRnM,MAAM,CAACZ,aAAa,iBACnBX,OAAA;YAAKqN,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAE/L,MAAM,CAACZ;UAAa;YAAA4M,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAC3D;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN,EAGAjM,OAAO,CAACE,IAAI,iBACX3B,OAAA;UAAKqN,SAAS,EAAC,iBAAiB;UAAAC,QAAA,eAC9BtN,OAAA;YAAAsN,QAAA,EAAM;UAA6B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CACN,EAEA5L,gBAAgB,CAACK,OAAO,IAAI,CAACV,OAAO,CAACE,IAAI,iBACxC3B,OAAA;UAAKqN,SAAS,EAAE,kBACdvL,gBAAgB,CAACE,eAAe,IAAIF,gBAAgB,CAACG,kBAAkB,GACnE,SAAS,GACTH,gBAAgB,CAACK,OAAO,CAAC4L,QAAQ,CAAC,SAAS,CAAC,GAC1C,OAAO,GACP,MAAM,EACX;UAAAT,QAAA,EACAxL,gBAAgB,CAACK;QAAO;UAAAoL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CACN,EAEAnM,MAAM,CAACI,IAAI,iBACV3B,OAAA;UAAKqN,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAE/L,MAAM,CAACI;QAAI;UAAA4L,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAClD;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,EAGLvM,UAAU,IAAIuG,MAAM,CAACmD,OAAO,CAAC0B,QAAQ,CAAC,CAAChE,GAAG,CAAC,CAAC,CAACiE,UAAU,EAAEC,OAAO,CAAC,kBAChEzM,OAAA;QAAsBqN,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC5CtN,OAAA;UAAAsN,QAAA,EAAKb,OAAO,CAACX;QAAK;UAAAyB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACxB1N,OAAA;UAAKqN,SAAS,EAAC,aAAa;UAAAC,QAAA,EACzBb,OAAO,CAAC1J,MAAM,CACZ+H,MAAM,CAACpH,KAAK,IAAIsC,eAAe,CAACtC,KAAK,CAAC,CAAC,CACvC6E,GAAG,CAAC,CAAC7E,KAAK,EAAEsK,UAAU,kBACrBhO,OAAA,CAACF,SAAS;YAER4D,KAAK,EAAEA,KAAM;YACbuB,KAAK,EAAE5D,QAAQ,CAACqC,KAAK,CAACE,GAAG,CAAE;YAC3BgK,QAAQ,EAAG3I,KAAK,IAAKI,iBAAiB,CAAC3B,KAAK,CAACE,GAAG,EAAEqB,KAAK,CAAE;YACzD5B,KAAK,EAAE9B,MAAM,CAACmC,KAAK,CAACE,GAAG;UAAE,GAJpB,GAAG4I,UAAU,IAAI9I,KAAK,CAACE,GAAG,IAAIoK,UAAU,EAAE;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAKhD,CACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA,GAdElB,UAAU;QAAAe,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAef,CACN,CAAC,eAKF1N,OAAA;QAAKqN,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BtN,OAAA;UACEoG,IAAI,EAAC,QAAQ;UACb6H,OAAO,EAAE9N,QAAS;UAClBkN,SAAS,EAAC,iBAAiB;UAC3BQ,QAAQ,EAAEjM,UAAW;UAAA0L,QAAA,EACtB;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT1N,OAAA;UACEoG,IAAI,EAAC,QAAQ;UACbiH,SAAS,EAAC,iBAAiB;UAC3BQ,QAAQ,EAAEjM,UAAU,IAAI,CAACf,gBAAgB,IAAI,CAACE,gBAAiB;UAAAuM,QAAA,EAE9D1L,UAAU,GACNvB,IAAI,KAAK,QAAQ,GAAG,aAAa,GAAG,aAAa,GACjDA,IAAI,KAAK,QAAQ,GAAG,eAAe,GAAG;QAAgB;UAAAkN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAErD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACpN,EAAA,CAn0BIL,iBAAiB;AAAAiO,EAAA,GAAjBjO,iBAAiB;AAq0BvB,eAAeA,iBAAiB;AAAC,IAAAiO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}