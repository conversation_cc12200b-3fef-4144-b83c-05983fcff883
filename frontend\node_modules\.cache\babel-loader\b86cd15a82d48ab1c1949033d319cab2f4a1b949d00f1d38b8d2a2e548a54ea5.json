{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Advanced\\\\Advanced CRM filed all use\\\\frontend\\\\src\\\\components\\\\CategoryManagement.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport Pagination from './Pagination';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst API_BASE_URL = 'http://localhost:5000/api';\nconst CategoryManagement = () => {\n  _s();\n  const [divisions, setDivisions] = useState([]);\n  const [categories, setCategories] = useState([]);\n  const [allCategories, setAllCategories] = useState([]);\n  const [subCategories, setSubCategories] = useState([]);\n  const [selectedDivision, setSelectedDivision] = useState('');\n  const [selectedCategory, setSelectedCategory] = useState('');\n  const [newCategory, setNewCategory] = useState('');\n  const [newSubCategory, setNewSubCategory] = useState('');\n  const [loading, setLoading] = useState(false);\n  const [currentPage, setCurrentPage] = useState(1);\n  const [itemsPerPage] = useState(10);\n  useEffect(() => {\n    fetchDivisions();\n    fetchAllCategories();\n  }, []);\n  useEffect(() => {\n    if (selectedDivision) {\n      fetchCategories(selectedDivision);\n      setSelectedCategory(''); // Reset category selection when division changes\n    } else {\n      setCategories([]);\n      setSelectedCategory('');\n    }\n  }, [selectedDivision]);\n  useEffect(() => {\n    if (selectedCategory) {\n      fetchSubCategories(selectedCategory);\n    }\n  }, [selectedCategory]);\n  const fetchDivisions = async () => {\n    try {\n      const response = await axios.get(`${API_BASE_URL}/divisions`);\n      setDivisions(response.data);\n    } catch (error) {\n      console.error('Error fetching divisions:', error);\n    }\n  };\n  const fetchCategories = async divisionId => {\n    try {\n      const response = await axios.get(`${API_BASE_URL}/categories/division/${divisionId}`);\n      setCategories(response.data);\n    } catch (error) {\n      console.error('Error fetching categories:', error);\n    }\n  };\n  const fetchAllCategories = async () => {\n    try {\n      const response = await axios.get(`${API_BASE_URL}/categories`);\n      setAllCategories(response.data);\n    } catch (error) {\n      console.error('Error fetching all categories:', error);\n    }\n  };\n  const fetchSubCategories = async categoryId => {\n    try {\n      const response = await axios.get(`${API_BASE_URL}/categories/${categoryId}`);\n      setSubCategories(response.data.subCategories || []);\n    } catch (error) {\n      console.error('Error fetching sub-categories:', error);\n    }\n  };\n  const handleCategorySubmit = async e => {\n    e.preventDefault();\n    if (!newCategory.trim() || !selectedDivision) return;\n    setLoading(true);\n    try {\n      await axios.post(`${API_BASE_URL}/categories`, {\n        Name: newCategory.trim(),\n        DivisionId: parseInt(selectedDivision)\n      });\n      setNewCategory('');\n      fetchCategories(selectedDivision);\n      fetchAllCategories(); // Refresh the overview\n      alert('Category created successfully!');\n    } catch (error) {\n      var _error$response, _error$response$data, _error$response2, _error$response2$data;\n      console.error('Error creating category:', error);\n      if ((_error$response = error.response) !== null && _error$response !== void 0 && (_error$response$data = _error$response.data) !== null && _error$response$data !== void 0 && _error$response$data.errors) {\n        const errorMessages = Object.values(error.response.data.errors).flat().join(', ');\n        alert(`Error creating category: ${errorMessages}`);\n      } else if ((_error$response2 = error.response) !== null && _error$response2 !== void 0 && (_error$response2$data = _error$response2.data) !== null && _error$response2$data !== void 0 && _error$response2$data.message) {\n        alert(`Error creating category: ${error.response.data.message}`);\n      } else {\n        alert('Error creating category. Please try again.');\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleSubCategorySubmit = async e => {\n    e.preventDefault();\n    if (!newSubCategory.trim() || !selectedCategory) return;\n    setLoading(true);\n    try {\n      await axios.post(`${API_BASE_URL}/subcategories`, {\n        Name: newSubCategory.trim(),\n        CategoryId: parseInt(selectedCategory)\n      });\n      setNewSubCategory('');\n      fetchSubCategories(selectedCategory);\n      fetchAllCategories(); // Refresh the overview\n      alert('Sub-category created successfully!');\n    } catch (error) {\n      var _error$response3, _error$response3$data, _error$response4, _error$response4$data;\n      console.error('Error creating sub-category:', error);\n      if ((_error$response3 = error.response) !== null && _error$response3 !== void 0 && (_error$response3$data = _error$response3.data) !== null && _error$response3$data !== void 0 && _error$response3$data.errors) {\n        const errorMessages = Object.values(error.response.data.errors).flat().join(', ');\n        alert(`Error creating sub-category: ${errorMessages}`);\n      } else if ((_error$response4 = error.response) !== null && _error$response4 !== void 0 && (_error$response4$data = _error$response4.data) !== null && _error$response4$data !== void 0 && _error$response4$data.message) {\n        alert(`Error creating sub-category: ${error.response.data.message}`);\n      } else {\n        alert('Error creating sub-category. Please try again.');\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Calculate pagination for categories overview\n  const totalItems = allCategories.length;\n  const startIndex = (currentPage - 1) * itemsPerPage;\n  const endIndex = startIndex + itemsPerPage;\n  const currentItems = allCategories.slice(startIndex, endIndex);\n  const handlePageChange = page => {\n    setCurrentPage(page);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      children: \"Category Management\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 147,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Add Category\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"divisionSelect\",\n              children: \"Select Division\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 154,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              id: \"divisionSelect\",\n              className: \"form-control\",\n              value: selectedDivision,\n              onChange: e => setSelectedDivision(e.target.value),\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"Select Division\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 161,\n                columnNumber: 17\n              }, this), divisions.map(division => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: division.id,\n                children: division.name\n              }, division.id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 163,\n                columnNumber: 19\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 155,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n            onSubmit: handleCategorySubmit,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"categoryName\",\n                children: \"Category Name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 172,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                id: \"categoryName\",\n                className: \"form-control\",\n                value: newCategory,\n                onChange: e => setNewCategory(e.target.value),\n                placeholder: \"Enter category name\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 173,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              className: \"btn btn-primary\",\n              disabled: loading || !selectedDivision,\n              children: loading ? 'Adding...' : 'Add Category'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 183,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 150,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Add Sub-Category\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 196,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"categorySelect\",\n              children: \"Select Category\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 198,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              id: \"categorySelect\",\n              className: \"form-control\",\n              value: selectedCategory,\n              onChange: e => setSelectedCategory(e.target.value),\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"Select Category\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 205,\n                columnNumber: 17\n              }, this), categories.map(category => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: category.id,\n                children: category.name\n              }, category.id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 207,\n                columnNumber: 19\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 199,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n            onSubmit: handleSubCategorySubmit,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"subCategoryName\",\n                children: \"Sub-Category Name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 216,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                id: \"subCategoryName\",\n                className: \"form-control\",\n                value: newSubCategory,\n                onChange: e => setNewSubCategory(e.target.value),\n                placeholder: \"Enter sub-category name\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 217,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 215,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              className: \"btn btn-primary\",\n              disabled: loading || !selectedCategory,\n              children: loading ? 'Adding...' : 'Add Sub-Category'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 227,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 195,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 194,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 149,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"Categories Overview\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 240,\n        columnNumber: 9\n      }, this), allCategories.length === 0 ? /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"No categories found. Add some categories to see them here.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 242,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"table\", {\n          className: \"table\",\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Division\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 248,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Category\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 249,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Sub-Categories\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 250,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 247,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            children: currentItems.map(category => {\n              var _category$division;\n              return /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                  children: ((_category$division = category.division) === null || _category$division === void 0 ? void 0 : _category$division.name) || 'Unknown Division'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 256,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: category.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 257,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: category.subCategories && category.subCategories.length > 0 ? category.subCategories.map(sub => sub.name).join(', ') : 'No sub-categories'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 258,\n                  columnNumber: 21\n                }, this)]\n              }, category.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 255,\n                columnNumber: 19\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 253,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 245,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Pagination, {\n          currentPage: currentPage,\n          totalItems: totalItems,\n          itemsPerPage: itemsPerPage,\n          onPageChange: handlePageChange\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 268,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 239,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 146,\n    columnNumber: 5\n  }, this);\n};\n_s(CategoryManagement, \"ew9uSQ3OgwuteD3XtlsxH9mAMpM=\");\n_c = CategoryManagement;\nexport default CategoryManagement;\nvar _c;\n$RefreshReg$(_c, \"CategoryManagement\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "Pagination", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "API_BASE_URL", "CategoryManagement", "_s", "divisions", "setDivisions", "categories", "setCategories", "allCategories", "setAllCategories", "subCategories", "setSubCategories", "selectedDivision", "setSelectedDivision", "selectedCate<PERSON><PERSON>", "setSelectedCategory", "newCategory", "setNewCategory", "newSubCategory", "setNewSubCategory", "loading", "setLoading", "currentPage", "setCurrentPage", "itemsPerPage", "fetchDivisions", "fetchAllCategories", "fetchCategories", "fetchSubCategories", "response", "get", "data", "error", "console", "divisionId", "categoryId", "handleCategorySubmit", "e", "preventDefault", "trim", "post", "Name", "DivisionId", "parseInt", "alert", "_error$response", "_error$response$data", "_error$response2", "_error$response2$data", "errors", "errorMessages", "Object", "values", "flat", "join", "message", "handleSubCategorySubmit", "CategoryId", "_error$response3", "_error$response3$data", "_error$response4", "_error$response4$data", "totalItems", "length", "startIndex", "endIndex", "currentItems", "slice", "handlePageChange", "page", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "htmlFor", "id", "value", "onChange", "target", "map", "division", "name", "onSubmit", "type", "placeholder", "required", "disabled", "category", "_category$division", "sub", "onPageChange", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/Advanced/Advanced CRM filed all use/frontend/src/components/CategoryManagement.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport axios from 'axios';\r\nimport Pagination from './Pagination';\r\n\r\nconst API_BASE_URL = 'http://localhost:5000/api';\r\n\r\nconst CategoryManagement = () => {\r\n  const [divisions, setDivisions] = useState([]);\r\n  const [categories, setCategories] = useState([]);\r\n  const [allCategories, setAllCategories] = useState([]);\r\n  const [subCategories, setSubCategories] = useState([]);\r\n  const [selectedDivision, setSelectedDivision] = useState('');\r\n  const [selectedCategory, setSelectedCategory] = useState('');\r\n  const [newCategory, setNewCategory] = useState('');\r\n  const [newSubCategory, setNewSubCategory] = useState('');\r\n  const [loading, setLoading] = useState(false);\r\n  const [currentPage, setCurrentPage] = useState(1);\r\n  const [itemsPerPage] = useState(10);\r\n\r\n  useEffect(() => {\r\n    fetchDivisions();\r\n    fetchAllCategories();\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    if (selectedDivision) {\r\n      fetchCategories(selectedDivision);\r\n      setSelectedCategory(''); // Reset category selection when division changes\r\n    } else {\r\n      setCategories([]);\r\n      setSelectedCategory('');\r\n    }\r\n  }, [selectedDivision]);\r\n\r\n  useEffect(() => {\r\n    if (selectedCategory) {\r\n      fetchSubCategories(selectedCategory);\r\n    }\r\n  }, [selectedCategory]);\r\n\r\n  const fetchDivisions = async () => {\r\n    try {\r\n      const response = await axios.get(`${API_BASE_URL}/divisions`);\r\n      setDivisions(response.data);\r\n    } catch (error) {\r\n      console.error('Error fetching divisions:', error);\r\n    }\r\n  };\r\n\r\n  const fetchCategories = async (divisionId) => {\r\n    try {\r\n      const response = await axios.get(`${API_BASE_URL}/categories/division/${divisionId}`);\r\n      setCategories(response.data);\r\n    } catch (error) {\r\n      console.error('Error fetching categories:', error);\r\n    }\r\n  };\r\n\r\n  const fetchAllCategories = async () => {\r\n    try {\r\n      const response = await axios.get(`${API_BASE_URL}/categories`);\r\n      setAllCategories(response.data);\r\n    } catch (error) {\r\n      console.error('Error fetching all categories:', error);\r\n    }\r\n  };\r\n\r\n  const fetchSubCategories = async (categoryId) => {\r\n    try {\r\n      const response = await axios.get(`${API_BASE_URL}/categories/${categoryId}`);\r\n      setSubCategories(response.data.subCategories || []);\r\n    } catch (error) {\r\n      console.error('Error fetching sub-categories:', error);\r\n    }\r\n  };\r\n\r\n  const handleCategorySubmit = async (e) => {\r\n    e.preventDefault();\r\n    if (!newCategory.trim() || !selectedDivision) return;\r\n\r\n    setLoading(true);\r\n    try {\r\n      await axios.post(`${API_BASE_URL}/categories`, {\r\n        Name: newCategory.trim(),\r\n        DivisionId: parseInt(selectedDivision)\r\n      });\r\n      setNewCategory('');\r\n      fetchCategories(selectedDivision);\r\n      fetchAllCategories(); // Refresh the overview\r\n      alert('Category created successfully!');\r\n    } catch (error) {\r\n      console.error('Error creating category:', error);\r\n      if (error.response?.data?.errors) {\r\n        const errorMessages = Object.values(error.response.data.errors).flat().join(', ');\r\n        alert(`Error creating category: ${errorMessages}`);\r\n      } else if (error.response?.data?.message) {\r\n        alert(`Error creating category: ${error.response.data.message}`);\r\n      } else {\r\n        alert('Error creating category. Please try again.');\r\n      }\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleSubCategorySubmit = async (e) => {\r\n    e.preventDefault();\r\n    if (!newSubCategory.trim() || !selectedCategory) return;\r\n\r\n    setLoading(true);\r\n    try {\r\n      await axios.post(`${API_BASE_URL}/subcategories`, {\r\n        Name: newSubCategory.trim(),\r\n        CategoryId: parseInt(selectedCategory)\r\n      });\r\n      setNewSubCategory('');\r\n      fetchSubCategories(selectedCategory);\r\n      fetchAllCategories(); // Refresh the overview\r\n      alert('Sub-category created successfully!');\r\n    } catch (error) {\r\n      console.error('Error creating sub-category:', error);\r\n      if (error.response?.data?.errors) {\r\n        const errorMessages = Object.values(error.response.data.errors).flat().join(', ');\r\n        alert(`Error creating sub-category: ${errorMessages}`);\r\n      } else if (error.response?.data?.message) {\r\n        alert(`Error creating sub-category: ${error.response.data.message}`);\r\n      } else {\r\n        alert('Error creating sub-category. Please try again.');\r\n      }\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  // Calculate pagination for categories overview\r\n  const totalItems = allCategories.length;\r\n  const startIndex = (currentPage - 1) * itemsPerPage;\r\n  const endIndex = startIndex + itemsPerPage;\r\n  const currentItems = allCategories.slice(startIndex, endIndex);\r\n\r\n  const handlePageChange = (page) => {\r\n    setCurrentPage(page);\r\n  };\r\n\r\n  return (\r\n    <div>\r\n      <h1>Category Management</h1>\r\n\r\n      <div className=\"row\">\r\n        <div className=\"col\">\r\n          <div className=\"card\">\r\n            <h3>Add Category</h3>\r\n            <div className=\"form-group\">\r\n              <label htmlFor=\"divisionSelect\">Select Division</label>\r\n              <select\r\n                id=\"divisionSelect\"\r\n                className=\"form-control\"\r\n                value={selectedDivision}\r\n                onChange={(e) => setSelectedDivision(e.target.value)}\r\n              >\r\n                <option value=\"\">Select Division</option>\r\n                {divisions.map((division) => (\r\n                  <option key={division.id} value={division.id}>\r\n                    {division.name}\r\n                  </option>\r\n                ))}\r\n              </select>\r\n            </div>\r\n\r\n            <form onSubmit={handleCategorySubmit}>\r\n              <div className=\"form-group\">\r\n                <label htmlFor=\"categoryName\">Category Name</label>\r\n                <input\r\n                  type=\"text\"\r\n                  id=\"categoryName\"\r\n                  className=\"form-control\"\r\n                  value={newCategory}\r\n                  onChange={(e) => setNewCategory(e.target.value)}\r\n                  placeholder=\"Enter category name\"\r\n                  required\r\n                />\r\n              </div>\r\n              <button\r\n                type=\"submit\"\r\n                className=\"btn btn-primary\"\r\n                disabled={loading || !selectedDivision}\r\n              >\r\n                {loading ? 'Adding...' : 'Add Category'}\r\n              </button>\r\n            </form>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"col\">\r\n          <div className=\"card\">\r\n            <h3>Add Sub-Category</h3>\r\n            <div className=\"form-group\">\r\n              <label htmlFor=\"categorySelect\">Select Category</label>\r\n              <select\r\n                id=\"categorySelect\"\r\n                className=\"form-control\"\r\n                value={selectedCategory}\r\n                onChange={(e) => setSelectedCategory(e.target.value)}\r\n              >\r\n                <option value=\"\">Select Category</option>\r\n                {categories.map((category) => (\r\n                  <option key={category.id} value={category.id}>\r\n                    {category.name}\r\n                  </option>\r\n                ))}\r\n              </select>\r\n            </div>\r\n\r\n            <form onSubmit={handleSubCategorySubmit}>\r\n              <div className=\"form-group\">\r\n                <label htmlFor=\"subCategoryName\">Sub-Category Name</label>\r\n                <input\r\n                  type=\"text\"\r\n                  id=\"subCategoryName\"\r\n                  className=\"form-control\"\r\n                  value={newSubCategory}\r\n                  onChange={(e) => setNewSubCategory(e.target.value)}\r\n                  placeholder=\"Enter sub-category name\"\r\n                  required\r\n                />\r\n              </div>\r\n              <button\r\n                type=\"submit\"\r\n                className=\"btn btn-primary\"\r\n                disabled={loading || !selectedCategory}\r\n              >\r\n                {loading ? 'Adding...' : 'Add Sub-Category'}\r\n              </button>\r\n            </form>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"card\">\r\n        <h3>Categories Overview</h3>\r\n        {allCategories.length === 0 ? (\r\n          <p>No categories found. Add some categories to see them here.</p>\r\n        ) : (\r\n          <>\r\n            <table className=\"table\">\r\n              <thead>\r\n                <tr>\r\n                  <th>Division</th>\r\n                  <th>Category</th>\r\n                  <th>Sub-Categories</th>\r\n                </tr>\r\n              </thead>\r\n              <tbody>\r\n                {currentItems.map((category) => (\r\n                  <tr key={category.id}>\r\n                    <td>{category.division?.name || 'Unknown Division'}</td>\r\n                    <td>{category.name}</td>\r\n                    <td>\r\n                      {category.subCategories && category.subCategories.length > 0\r\n                        ? category.subCategories.map(sub => sub.name).join(', ')\r\n                        : 'No sub-categories'\r\n                      }\r\n                    </td>\r\n                  </tr>\r\n                ))}\r\n              </tbody>\r\n            </table>\r\n            <Pagination\r\n              currentPage={currentPage}\r\n              totalItems={totalItems}\r\n              itemsPerPage={itemsPerPage}\r\n              onPageChange={handlePageChange}\r\n            />\r\n          </>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default CategoryManagement;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,UAAU,MAAM,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEtC,MAAMC,YAAY,GAAG,2BAA2B;AAEhD,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC/B,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGZ,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACa,UAAU,EAAEC,aAAa,CAAC,GAAGd,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACe,aAAa,EAAEC,gBAAgB,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACiB,aAAa,EAAEC,gBAAgB,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACmB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACqB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACuB,WAAW,EAAEC,cAAc,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACyB,cAAc,EAAEC,iBAAiB,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC2B,OAAO,EAAEC,UAAU,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC6B,WAAW,EAAEC,cAAc,CAAC,GAAG9B,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAAC+B,YAAY,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EAEnCC,SAAS,CAAC,MAAM;IACd+B,cAAc,CAAC,CAAC;IAChBC,kBAAkB,CAAC,CAAC;EACtB,CAAC,EAAE,EAAE,CAAC;EAENhC,SAAS,CAAC,MAAM;IACd,IAAIkB,gBAAgB,EAAE;MACpBe,eAAe,CAACf,gBAAgB,CAAC;MACjCG,mBAAmB,CAAC,EAAE,CAAC,CAAC,CAAC;IAC3B,CAAC,MAAM;MACLR,aAAa,CAAC,EAAE,CAAC;MACjBQ,mBAAmB,CAAC,EAAE,CAAC;IACzB;EACF,CAAC,EAAE,CAACH,gBAAgB,CAAC,CAAC;EAEtBlB,SAAS,CAAC,MAAM;IACd,IAAIoB,gBAAgB,EAAE;MACpBc,kBAAkB,CAACd,gBAAgB,CAAC;IACtC;EACF,CAAC,EAAE,CAACA,gBAAgB,CAAC,CAAC;EAEtB,MAAMW,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACF,MAAMI,QAAQ,GAAG,MAAMlC,KAAK,CAACmC,GAAG,CAAC,GAAG7B,YAAY,YAAY,CAAC;MAC7DI,YAAY,CAACwB,QAAQ,CAACE,IAAI,CAAC;IAC7B,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;IACnD;EACF,CAAC;EAED,MAAML,eAAe,GAAG,MAAOO,UAAU,IAAK;IAC5C,IAAI;MACF,MAAML,QAAQ,GAAG,MAAMlC,KAAK,CAACmC,GAAG,CAAC,GAAG7B,YAAY,wBAAwBiC,UAAU,EAAE,CAAC;MACrF3B,aAAa,CAACsB,QAAQ,CAACE,IAAI,CAAC;IAC9B,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;IACpD;EACF,CAAC;EAED,MAAMN,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACF,MAAMG,QAAQ,GAAG,MAAMlC,KAAK,CAACmC,GAAG,CAAC,GAAG7B,YAAY,aAAa,CAAC;MAC9DQ,gBAAgB,CAACoB,QAAQ,CAACE,IAAI,CAAC;IACjC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;IACxD;EACF,CAAC;EAED,MAAMJ,kBAAkB,GAAG,MAAOO,UAAU,IAAK;IAC/C,IAAI;MACF,MAAMN,QAAQ,GAAG,MAAMlC,KAAK,CAACmC,GAAG,CAAC,GAAG7B,YAAY,eAAekC,UAAU,EAAE,CAAC;MAC5ExB,gBAAgB,CAACkB,QAAQ,CAACE,IAAI,CAACrB,aAAa,IAAI,EAAE,CAAC;IACrD,CAAC,CAAC,OAAOsB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;IACxD;EACF,CAAC;EAED,MAAMI,oBAAoB,GAAG,MAAOC,CAAC,IAAK;IACxCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAI,CAACtB,WAAW,CAACuB,IAAI,CAAC,CAAC,IAAI,CAAC3B,gBAAgB,EAAE;IAE9CS,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAM1B,KAAK,CAAC6C,IAAI,CAAC,GAAGvC,YAAY,aAAa,EAAE;QAC7CwC,IAAI,EAAEzB,WAAW,CAACuB,IAAI,CAAC,CAAC;QACxBG,UAAU,EAAEC,QAAQ,CAAC/B,gBAAgB;MACvC,CAAC,CAAC;MACFK,cAAc,CAAC,EAAE,CAAC;MAClBU,eAAe,CAACf,gBAAgB,CAAC;MACjCc,kBAAkB,CAAC,CAAC,CAAC,CAAC;MACtBkB,KAAK,CAAC,gCAAgC,CAAC;IACzC,CAAC,CAAC,OAAOZ,KAAK,EAAE;MAAA,IAAAa,eAAA,EAAAC,oBAAA,EAAAC,gBAAA,EAAAC,qBAAA;MACdf,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChD,KAAAa,eAAA,GAAIb,KAAK,CAACH,QAAQ,cAAAgB,eAAA,gBAAAC,oBAAA,GAAdD,eAAA,CAAgBd,IAAI,cAAAe,oBAAA,eAApBA,oBAAA,CAAsBG,MAAM,EAAE;QAChC,MAAMC,aAAa,GAAGC,MAAM,CAACC,MAAM,CAACpB,KAAK,CAACH,QAAQ,CAACE,IAAI,CAACkB,MAAM,CAAC,CAACI,IAAI,CAAC,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;QACjFV,KAAK,CAAC,4BAA4BM,aAAa,EAAE,CAAC;MACpD,CAAC,MAAM,KAAAH,gBAAA,GAAIf,KAAK,CAACH,QAAQ,cAAAkB,gBAAA,gBAAAC,qBAAA,GAAdD,gBAAA,CAAgBhB,IAAI,cAAAiB,qBAAA,eAApBA,qBAAA,CAAsBO,OAAO,EAAE;QACxCX,KAAK,CAAC,4BAA4BZ,KAAK,CAACH,QAAQ,CAACE,IAAI,CAACwB,OAAO,EAAE,CAAC;MAClE,CAAC,MAAM;QACLX,KAAK,CAAC,4CAA4C,CAAC;MACrD;IACF,CAAC,SAAS;MACRvB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMmC,uBAAuB,GAAG,MAAOnB,CAAC,IAAK;IAC3CA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAI,CAACpB,cAAc,CAACqB,IAAI,CAAC,CAAC,IAAI,CAACzB,gBAAgB,EAAE;IAEjDO,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAM1B,KAAK,CAAC6C,IAAI,CAAC,GAAGvC,YAAY,gBAAgB,EAAE;QAChDwC,IAAI,EAAEvB,cAAc,CAACqB,IAAI,CAAC,CAAC;QAC3BkB,UAAU,EAAEd,QAAQ,CAAC7B,gBAAgB;MACvC,CAAC,CAAC;MACFK,iBAAiB,CAAC,EAAE,CAAC;MACrBS,kBAAkB,CAACd,gBAAgB,CAAC;MACpCY,kBAAkB,CAAC,CAAC,CAAC,CAAC;MACtBkB,KAAK,CAAC,oCAAoC,CAAC;IAC7C,CAAC,CAAC,OAAOZ,KAAK,EAAE;MAAA,IAAA0B,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA;MACd5B,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,KAAA0B,gBAAA,GAAI1B,KAAK,CAACH,QAAQ,cAAA6B,gBAAA,gBAAAC,qBAAA,GAAdD,gBAAA,CAAgB3B,IAAI,cAAA4B,qBAAA,eAApBA,qBAAA,CAAsBV,MAAM,EAAE;QAChC,MAAMC,aAAa,GAAGC,MAAM,CAACC,MAAM,CAACpB,KAAK,CAACH,QAAQ,CAACE,IAAI,CAACkB,MAAM,CAAC,CAACI,IAAI,CAAC,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;QACjFV,KAAK,CAAC,gCAAgCM,aAAa,EAAE,CAAC;MACxD,CAAC,MAAM,KAAAU,gBAAA,GAAI5B,KAAK,CAACH,QAAQ,cAAA+B,gBAAA,gBAAAC,qBAAA,GAAdD,gBAAA,CAAgB7B,IAAI,cAAA8B,qBAAA,eAApBA,qBAAA,CAAsBN,OAAO,EAAE;QACxCX,KAAK,CAAC,gCAAgCZ,KAAK,CAACH,QAAQ,CAACE,IAAI,CAACwB,OAAO,EAAE,CAAC;MACtE,CAAC,MAAM;QACLX,KAAK,CAAC,gDAAgD,CAAC;MACzD;IACF,CAAC,SAAS;MACRvB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMyC,UAAU,GAAGtD,aAAa,CAACuD,MAAM;EACvC,MAAMC,UAAU,GAAG,CAAC1C,WAAW,GAAG,CAAC,IAAIE,YAAY;EACnD,MAAMyC,QAAQ,GAAGD,UAAU,GAAGxC,YAAY;EAC1C,MAAM0C,YAAY,GAAG1D,aAAa,CAAC2D,KAAK,CAACH,UAAU,EAAEC,QAAQ,CAAC;EAE9D,MAAMG,gBAAgB,GAAIC,IAAI,IAAK;IACjC9C,cAAc,CAAC8C,IAAI,CAAC;EACtB,CAAC;EAED,oBACEvE,OAAA;IAAAwE,QAAA,gBACExE,OAAA;MAAAwE,QAAA,EAAI;IAAmB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAE5B5E,OAAA;MAAK6E,SAAS,EAAC,KAAK;MAAAL,QAAA,gBAClBxE,OAAA;QAAK6E,SAAS,EAAC,KAAK;QAAAL,QAAA,eAClBxE,OAAA;UAAK6E,SAAS,EAAC,MAAM;UAAAL,QAAA,gBACnBxE,OAAA;YAAAwE,QAAA,EAAI;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrB5E,OAAA;YAAK6E,SAAS,EAAC,YAAY;YAAAL,QAAA,gBACzBxE,OAAA;cAAO8E,OAAO,EAAC,gBAAgB;cAAAN,QAAA,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACvD5E,OAAA;cACE+E,EAAE,EAAC,gBAAgB;cACnBF,SAAS,EAAC,cAAc;cACxBG,KAAK,EAAElE,gBAAiB;cACxBmE,QAAQ,EAAG1C,CAAC,IAAKxB,mBAAmB,CAACwB,CAAC,CAAC2C,MAAM,CAACF,KAAK,CAAE;cAAAR,QAAA,gBAErDxE,OAAA;gBAAQgF,KAAK,EAAC,EAAE;gBAAAR,QAAA,EAAC;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EACxCtE,SAAS,CAAC6E,GAAG,CAAEC,QAAQ,iBACtBpF,OAAA;gBAA0BgF,KAAK,EAAEI,QAAQ,CAACL,EAAG;gBAAAP,QAAA,EAC1CY,QAAQ,CAACC;cAAI,GADHD,QAAQ,CAACL,EAAE;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEhB,CACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAEN5E,OAAA;YAAMsF,QAAQ,EAAEhD,oBAAqB;YAAAkC,QAAA,gBACnCxE,OAAA;cAAK6E,SAAS,EAAC,YAAY;cAAAL,QAAA,gBACzBxE,OAAA;gBAAO8E,OAAO,EAAC,cAAc;gBAAAN,QAAA,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACnD5E,OAAA;gBACEuF,IAAI,EAAC,MAAM;gBACXR,EAAE,EAAC,cAAc;gBACjBF,SAAS,EAAC,cAAc;gBACxBG,KAAK,EAAE9D,WAAY;gBACnB+D,QAAQ,EAAG1C,CAAC,IAAKpB,cAAc,CAACoB,CAAC,CAAC2C,MAAM,CAACF,KAAK,CAAE;gBAChDQ,WAAW,EAAC,qBAAqB;gBACjCC,QAAQ;cAAA;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN5E,OAAA;cACEuF,IAAI,EAAC,QAAQ;cACbV,SAAS,EAAC,iBAAiB;cAC3Ba,QAAQ,EAAEpE,OAAO,IAAI,CAACR,gBAAiB;cAAA0D,QAAA,EAEtClD,OAAO,GAAG,WAAW,GAAG;YAAc;cAAAmD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN5E,OAAA;QAAK6E,SAAS,EAAC,KAAK;QAAAL,QAAA,eAClBxE,OAAA;UAAK6E,SAAS,EAAC,MAAM;UAAAL,QAAA,gBACnBxE,OAAA;YAAAwE,QAAA,EAAI;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzB5E,OAAA;YAAK6E,SAAS,EAAC,YAAY;YAAAL,QAAA,gBACzBxE,OAAA;cAAO8E,OAAO,EAAC,gBAAgB;cAAAN,QAAA,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACvD5E,OAAA;cACE+E,EAAE,EAAC,gBAAgB;cACnBF,SAAS,EAAC,cAAc;cACxBG,KAAK,EAAEhE,gBAAiB;cACxBiE,QAAQ,EAAG1C,CAAC,IAAKtB,mBAAmB,CAACsB,CAAC,CAAC2C,MAAM,CAACF,KAAK,CAAE;cAAAR,QAAA,gBAErDxE,OAAA;gBAAQgF,KAAK,EAAC,EAAE;gBAAAR,QAAA,EAAC;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EACxCpE,UAAU,CAAC2E,GAAG,CAAEQ,QAAQ,iBACvB3F,OAAA;gBAA0BgF,KAAK,EAAEW,QAAQ,CAACZ,EAAG;gBAAAP,QAAA,EAC1CmB,QAAQ,CAACN;cAAI,GADHM,QAAQ,CAACZ,EAAE;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEhB,CACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAEN5E,OAAA;YAAMsF,QAAQ,EAAE5B,uBAAwB;YAAAc,QAAA,gBACtCxE,OAAA;cAAK6E,SAAS,EAAC,YAAY;cAAAL,QAAA,gBACzBxE,OAAA;gBAAO8E,OAAO,EAAC,iBAAiB;gBAAAN,QAAA,EAAC;cAAiB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC1D5E,OAAA;gBACEuF,IAAI,EAAC,MAAM;gBACXR,EAAE,EAAC,iBAAiB;gBACpBF,SAAS,EAAC,cAAc;gBACxBG,KAAK,EAAE5D,cAAe;gBACtB6D,QAAQ,EAAG1C,CAAC,IAAKlB,iBAAiB,CAACkB,CAAC,CAAC2C,MAAM,CAACF,KAAK,CAAE;gBACnDQ,WAAW,EAAC,yBAAyB;gBACrCC,QAAQ;cAAA;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN5E,OAAA;cACEuF,IAAI,EAAC,QAAQ;cACbV,SAAS,EAAC,iBAAiB;cAC3Ba,QAAQ,EAAEpE,OAAO,IAAI,CAACN,gBAAiB;cAAAwD,QAAA,EAEtClD,OAAO,GAAG,WAAW,GAAG;YAAkB;cAAAmD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEN5E,OAAA;MAAK6E,SAAS,EAAC,MAAM;MAAAL,QAAA,gBACnBxE,OAAA;QAAAwE,QAAA,EAAI;MAAmB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,EAC3BlE,aAAa,CAACuD,MAAM,KAAK,CAAC,gBACzBjE,OAAA;QAAAwE,QAAA,EAAG;MAA0D;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,gBAEjE5E,OAAA,CAAAE,SAAA;QAAAsE,QAAA,gBACExE,OAAA;UAAO6E,SAAS,EAAC,OAAO;UAAAL,QAAA,gBACtBxE,OAAA;YAAAwE,QAAA,eACExE,OAAA;cAAAwE,QAAA,gBACExE,OAAA;gBAAAwE,QAAA,EAAI;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjB5E,OAAA;gBAAAwE,QAAA,EAAI;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjB5E,OAAA;gBAAAwE,QAAA,EAAI;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACR5E,OAAA;YAAAwE,QAAA,EACGJ,YAAY,CAACe,GAAG,CAAEQ,QAAQ;cAAA,IAAAC,kBAAA;cAAA,oBACzB5F,OAAA;gBAAAwE,QAAA,gBACExE,OAAA;kBAAAwE,QAAA,EAAK,EAAAoB,kBAAA,GAAAD,QAAQ,CAACP,QAAQ,cAAAQ,kBAAA,uBAAjBA,kBAAA,CAAmBP,IAAI,KAAI;gBAAkB;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACxD5E,OAAA;kBAAAwE,QAAA,EAAKmB,QAAQ,CAACN;gBAAI;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACxB5E,OAAA;kBAAAwE,QAAA,EACGmB,QAAQ,CAAC/E,aAAa,IAAI+E,QAAQ,CAAC/E,aAAa,CAACqD,MAAM,GAAG,CAAC,GACxD0B,QAAQ,CAAC/E,aAAa,CAACuE,GAAG,CAACU,GAAG,IAAIA,GAAG,CAACR,IAAI,CAAC,CAAC7B,IAAI,CAAC,IAAI,CAAC,GACtD;gBAAmB;kBAAAiB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAErB,CAAC;cAAA,GAREe,QAAQ,CAACZ,EAAE;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAShB,CAAC;YAAA,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACR5E,OAAA,CAACF,UAAU;UACT0B,WAAW,EAAEA,WAAY;UACzBwC,UAAU,EAAEA,UAAW;UACvBtC,YAAY,EAAEA,YAAa;UAC3BoE,YAAY,EAAExB;QAAiB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC;MAAA,eACF,CACH;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACvE,EAAA,CAhRID,kBAAkB;AAAA2F,EAAA,GAAlB3F,kBAAkB;AAkRxB,eAAeA,kBAAkB;AAAC,IAAA2F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}