namespace CrmApi.Models.ImportExport
{
    public class ImportValidationError
    {
        public int RowNumber { get; set; }
        public string FieldName { get; set; } = string.Empty;
        public string ErrorMessage { get; set; } = string.Empty;
        public string? FieldValue { get; set; }
        public ErrorSeverity Severity { get; set; } = ErrorSeverity.Error;
        public string ErrorCode { get; set; } = string.Empty;
    }

    public enum ErrorSeverity
    {
        Warning = 1,
        Error = 2,
        Critical = 3
    }

    public class ImportProgressUpdate
    {
        public string JobId { get; set; } = string.Empty;
        public ImportStatus Status { get; set; }
        public int TotalRows { get; set; }
        public int ProcessedRows { get; set; }
        public int SuccessfulRows { get; set; }
        public int FailedRows { get; set; }
        public string? CurrentOperation { get; set; }
        public DateTime LastUpdated { get; set; } = DateTime.UtcNow;
        public double ProgressPercentage => TotalRows > 0 ? (double)ProcessedRows / TotalRows * 100 : 0;
    }
}
