{"ast": null, "code": "const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';\nclass ApiService {\n  constructor() {\n    this.baseURL = API_BASE_URL;\n  }\n  async request(endpoint, options = {}) {\n    const url = `${this.baseURL}${endpoint}`;\n    const config = {\n      headers: {\n        'Content-Type': 'application/json',\n        ...options.headers\n      },\n      ...options\n    };\n    try {\n      const response = await fetch(url, config);\n      if (!response.ok) {\n        const errorData = await response.json().catch(() => ({}));\n        // DEBUG: Log the complete error response from backend\n        console.log('🚨 Backend Error Response Debug:');\n        console.log('Status:', response.status);\n        console.log('Status Text:', response.statusText);\n        console.log('Error Data:', errorData);\n        console.log('Error Data (JSON):', JSON.stringify(errorData, null, 2));\n        throw new ApiError(response.status, errorData.message || 'An error occurred', errorData);\n      }\n\n      // Handle blob responses (for file downloads)\n      if (options.responseType === 'blob') {\n        return {\n          data: await response.blob()\n        };\n      }\n      const data = await response.json();\n      return {\n        data\n      }; // Wrap in data property for consistency\n    } catch (error) {\n      if (error instanceof ApiError) {\n        throw error;\n      }\n      throw new ApiError(0, 'Network error', {\n        originalError: error.message\n      });\n    }\n  }\n\n  // Generic HTTP methods\n  async get(endpoint, options = {}) {\n    return this.request(endpoint, {\n      method: 'GET',\n      ...options\n    });\n  }\n  async post(endpoint, data = null, options = {}) {\n    const config = {\n      method: 'POST',\n      ...options\n    };\n    if (data) {\n      config.body = JSON.stringify(data);\n    }\n    return this.request(endpoint, config);\n  }\n  async put(endpoint, data = null, options = {}) {\n    const config = {\n      method: 'PUT',\n      ...options\n    };\n    if (data) {\n      config.body = JSON.stringify(data);\n    }\n    return this.request(endpoint, config);\n  }\n  async delete(endpoint, options = {}) {\n    return this.request(endpoint, {\n      method: 'DELETE',\n      ...options\n    });\n  }\n\n  // Division endpoints\n  async getDivisions() {\n    return this.get('/divisions');\n  }\n  async getDivision(id) {\n    return this.get(`/divisions/${id}`);\n  }\n\n  // Category endpoints\n  async getCategories() {\n    return this.get('/categories');\n  }\n  async getCategory(id) {\n    return this.get(`/categories/${id}`);\n  }\n  async getCategoriesByDivision(divisionId) {\n    return this.get(`/categories/division/${divisionId}`);\n  }\n\n  // SubCategory endpoints\n  async getSubCategories() {\n    return this.get('/subcategories');\n  }\n  async getSubCategory(id) {\n    return this.get(`/subcategories/${id}`);\n  }\n  async getSubCategoriesByCategory(categoryId) {\n    return this.get(`/subcategories/category/${categoryId}`);\n  }\n\n  // States endpoints\n  async getStates() {\n    return this.get('/states');\n  }\n\n  // Person endpoints\n  async getPersons(params = {}) {\n    const queryString = new URLSearchParams(params).toString();\n    return this.get(`/persons${queryString ? `?${queryString}` : ''}`);\n  }\n  async getPerson(id) {\n    return this.get(`/persons/${id}`);\n  }\n  async createPerson(personData) {\n    return this.post('/persons', personData);\n  }\n  async updatePerson(id, personData) {\n    return this.put(`/persons/${id}`, personData);\n  }\n  async deletePerson(id) {\n    return this.delete(`/persons/${id}`);\n  }\n  async searchPersons(searchRequest) {\n    return this.post('/persons/search', searchRequest);\n  }\n  async getPersonsByDivision(divisionId) {\n    return this.get(`/persons/division/${divisionId}`);\n  }\n  async getPersonsByCategory(categoryId) {\n    return this.get(`/persons/category/${categoryId}`);\n  }\n  async getPersonsBySubCategory(subCategoryId) {\n    return this.get(`/persons/subcategory/${subCategoryId}`);\n  }\n  async getPersonStatistics() {\n    return this.get('/persons/statistics');\n  }\n  async getPersonEnums() {\n    return this.get('/persons/enums');\n  }\n\n  // Bulk operations\n  async bulkCreatePersons(personsData) {\n    return this.post('/persons/bulk', personsData);\n  }\n  async bulkSoftDeletePersons(personIds) {\n    return this.post('/persons/bulk/soft-delete', personIds);\n  }\n  async bulkRestorePersons(personIds) {\n    return this.post('/persons/bulk/restore', personIds);\n  }\n}\nclass ApiError extends Error {\n  constructor(status, message, data = {}) {\n    super(message);\n    this.name = 'ApiError';\n    this.status = status;\n    this.data = data;\n  }\n  isValidationError() {\n    return this.status === 400 && this.data.errors;\n  }\n  isNotFoundError() {\n    return this.status === 404;\n  }\n  isServerError() {\n    return this.status >= 500;\n  }\n  getValidationErrors() {\n    return this.data.errors || {};\n  }\n}\nexport { ApiService, ApiError };\nexport default new ApiService();", "map": {"version": 3, "names": ["API_BASE_URL", "process", "env", "REACT_APP_API_URL", "ApiService", "constructor", "baseURL", "request", "endpoint", "options", "url", "config", "headers", "response", "fetch", "ok", "errorData", "json", "catch", "console", "log", "status", "statusText", "JSON", "stringify", "ApiError", "message", "responseType", "data", "blob", "error", "originalError", "get", "method", "post", "body", "put", "delete", "getDivisions", "getDivision", "id", "getCategories", "getCategory", "getCategoriesByDivision", "divisionId", "getSubCategories", "getSubCategory", "getSubCategoriesByCategory", "categoryId", "getStates", "<PERSON><PERSON><PERSON><PERSON>", "params", "queryString", "URLSearchParams", "toString", "<PERSON><PERSON><PERSON>", "create<PERSON>erson", "personData", "update<PERSON><PERSON>", "deletePerson", "search<PERSON><PERSON>s", "searchRequest", "getPersonsByDivision", "getPersonsByCategory", "getPersonsBySubCategory", "subCategoryId", "getPersonStatistics", "getPersonEnums", "bulkCreatePersons", "personsData", "bulkSoftDeletePersons", "personIds", "bulkRestorePersons", "Error", "name", "isValidationError", "errors", "isNotFoundError", "isServerError", "getValidationErrors"], "sources": ["C:/Users/<USER>/Downloads/Advanced/Advanced CRM filed all use/frontend/src/services/apiService.js"], "sourcesContent": ["const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';\n\nclass ApiService {\n  constructor() {\n    this.baseURL = API_BASE_URL;\n  }\n\n  async request(endpoint, options = {}) {\n    const url = `${this.baseURL}${endpoint}`;\n    const config = {\n      headers: {\n        'Content-Type': 'application/json',\n        ...options.headers,\n      },\n      ...options,\n    };\n\n    try {\n      const response = await fetch(url, config);\n\n      if (!response.ok) {\n        const errorData = await response.json().catch(() => ({}));\n        // DEBUG: Log the complete error response from backend\n        console.log('🚨 Backend Error Response Debug:');\n        console.log('Status:', response.status);\n        console.log('Status Text:', response.statusText);\n        console.log('Error Data:', errorData);\n        console.log('Error Data (JSON):', JSON.stringify(errorData, null, 2));\n        throw new ApiError(response.status, errorData.message || 'An error occurred', errorData);\n      }\n\n      // Handle blob responses (for file downloads)\n      if (options.responseType === 'blob') {\n        return { data: await response.blob() };\n      }\n\n      const data = await response.json();\n      return { data }; // Wrap in data property for consistency\n    } catch (error) {\n      if (error instanceof ApiError) {\n        throw error;\n      }\n      throw new ApiError(0, 'Network error', { originalError: error.message });\n    }\n  }\n\n  // Generic HTTP methods\n  async get(endpoint, options = {}) {\n    return this.request(endpoint, { method: 'GET', ...options });\n  }\n\n  async post(endpoint, data = null, options = {}) {\n    const config = { method: 'POST', ...options };\n    if (data) {\n      config.body = JSON.stringify(data);\n    }\n    return this.request(endpoint, config);\n  }\n\n  async put(endpoint, data = null, options = {}) {\n    const config = { method: 'PUT', ...options };\n    if (data) {\n      config.body = JSON.stringify(data);\n    }\n    return this.request(endpoint, config);\n  }\n\n  async delete(endpoint, options = {}) {\n    return this.request(endpoint, { method: 'DELETE', ...options });\n  }\n\n  // Division endpoints\n  async getDivisions() {\n    return this.get('/divisions');\n  }\n\n  async getDivision(id) {\n    return this.get(`/divisions/${id}`);\n  }\n\n  // Category endpoints\n  async getCategories() {\n    return this.get('/categories');\n  }\n\n  async getCategory(id) {\n    return this.get(`/categories/${id}`);\n  }\n\n  async getCategoriesByDivision(divisionId) {\n    return this.get(`/categories/division/${divisionId}`);\n  }\n\n  // SubCategory endpoints\n  async getSubCategories() {\n    return this.get('/subcategories');\n  }\n\n  async getSubCategory(id) {\n    return this.get(`/subcategories/${id}`);\n  }\n\n  async getSubCategoriesByCategory(categoryId) {\n    return this.get(`/subcategories/category/${categoryId}`);\n  }\n\n  // States endpoints\n  async getStates() {\n    return this.get('/states');\n  }\n\n  // Person endpoints\n  async getPersons(params = {}) {\n    const queryString = new URLSearchParams(params).toString();\n    return this.get(`/persons${queryString ? `?${queryString}` : ''}`);\n  }\n\n  async getPerson(id) {\n    return this.get(`/persons/${id}`);\n  }\n\n  async createPerson(personData) {\n    return this.post('/persons', personData);\n  }\n\n  async updatePerson(id, personData) {\n    return this.put(`/persons/${id}`, personData);\n  }\n\n  async deletePerson(id) {\n    return this.delete(`/persons/${id}`);\n  }\n\n  async searchPersons(searchRequest) {\n    return this.post('/persons/search', searchRequest);\n  }\n\n  async getPersonsByDivision(divisionId) {\n    return this.get(`/persons/division/${divisionId}`);\n  }\n\n  async getPersonsByCategory(categoryId) {\n    return this.get(`/persons/category/${categoryId}`);\n  }\n\n  async getPersonsBySubCategory(subCategoryId) {\n    return this.get(`/persons/subcategory/${subCategoryId}`);\n  }\n\n  async getPersonStatistics() {\n    return this.get('/persons/statistics');\n  }\n\n  async getPersonEnums() {\n    return this.get('/persons/enums');\n  }\n\n  // Bulk operations\n  async bulkCreatePersons(personsData) {\n    return this.post('/persons/bulk', personsData);\n  }\n\n  async bulkSoftDeletePersons(personIds) {\n    return this.post('/persons/bulk/soft-delete', personIds);\n  }\n\n  async bulkRestorePersons(personIds) {\n    return this.post('/persons/bulk/restore', personIds);\n  }\n}\n\nclass ApiError extends Error {\n  constructor(status, message, data = {}) {\n    super(message);\n    this.name = 'ApiError';\n    this.status = status;\n    this.data = data;\n  }\n\n  isValidationError() {\n    return this.status === 400 && this.data.errors;\n  }\n\n  isNotFoundError() {\n    return this.status === 404;\n  }\n\n  isServerError() {\n    return this.status >= 500;\n  }\n\n  getValidationErrors() {\n    return this.data.errors || {};\n  }\n}\n\nexport { ApiService, ApiError };\nexport default new ApiService();\n"], "mappings": "AAAA,MAAMA,YAAY,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,2BAA2B;AAEjF,MAAMC,UAAU,CAAC;EACfC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,OAAO,GAAGN,YAAY;EAC7B;EAEA,MAAMO,OAAOA,CAACC,QAAQ,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;IACpC,MAAMC,GAAG,GAAG,GAAG,IAAI,CAACJ,OAAO,GAAGE,QAAQ,EAAE;IACxC,MAAMG,MAAM,GAAG;MACbC,OAAO,EAAE;QACP,cAAc,EAAE,kBAAkB;QAClC,GAAGH,OAAO,CAACG;MACb,CAAC;MACD,GAAGH;IACL,CAAC;IAED,IAAI;MACF,MAAMI,QAAQ,GAAG,MAAMC,KAAK,CAACJ,GAAG,EAAEC,MAAM,CAAC;MAEzC,IAAI,CAACE,QAAQ,CAACE,EAAE,EAAE;QAChB,MAAMC,SAAS,GAAG,MAAMH,QAAQ,CAACI,IAAI,CAAC,CAAC,CAACC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QACzD;QACAC,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;QAC/CD,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEP,QAAQ,CAACQ,MAAM,CAAC;QACvCF,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEP,QAAQ,CAACS,UAAU,CAAC;QAChDH,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEJ,SAAS,CAAC;QACrCG,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEG,IAAI,CAACC,SAAS,CAACR,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;QACrE,MAAM,IAAIS,QAAQ,CAACZ,QAAQ,CAACQ,MAAM,EAAEL,SAAS,CAACU,OAAO,IAAI,mBAAmB,EAAEV,SAAS,CAAC;MAC1F;;MAEA;MACA,IAAIP,OAAO,CAACkB,YAAY,KAAK,MAAM,EAAE;QACnC,OAAO;UAAEC,IAAI,EAAE,MAAMf,QAAQ,CAACgB,IAAI,CAAC;QAAE,CAAC;MACxC;MAEA,MAAMD,IAAI,GAAG,MAAMf,QAAQ,CAACI,IAAI,CAAC,CAAC;MAClC,OAAO;QAAEW;MAAK,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,OAAOE,KAAK,EAAE;MACd,IAAIA,KAAK,YAAYL,QAAQ,EAAE;QAC7B,MAAMK,KAAK;MACb;MACA,MAAM,IAAIL,QAAQ,CAAC,CAAC,EAAE,eAAe,EAAE;QAAEM,aAAa,EAAED,KAAK,CAACJ;MAAQ,CAAC,CAAC;IAC1E;EACF;;EAEA;EACA,MAAMM,GAAGA,CAACxB,QAAQ,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;IAChC,OAAO,IAAI,CAACF,OAAO,CAACC,QAAQ,EAAE;MAAEyB,MAAM,EAAE,KAAK;MAAE,GAAGxB;IAAQ,CAAC,CAAC;EAC9D;EAEA,MAAMyB,IAAIA,CAAC1B,QAAQ,EAAEoB,IAAI,GAAG,IAAI,EAAEnB,OAAO,GAAG,CAAC,CAAC,EAAE;IAC9C,MAAME,MAAM,GAAG;MAAEsB,MAAM,EAAE,MAAM;MAAE,GAAGxB;IAAQ,CAAC;IAC7C,IAAImB,IAAI,EAAE;MACRjB,MAAM,CAACwB,IAAI,GAAGZ,IAAI,CAACC,SAAS,CAACI,IAAI,CAAC;IACpC;IACA,OAAO,IAAI,CAACrB,OAAO,CAACC,QAAQ,EAAEG,MAAM,CAAC;EACvC;EAEA,MAAMyB,GAAGA,CAAC5B,QAAQ,EAAEoB,IAAI,GAAG,IAAI,EAAEnB,OAAO,GAAG,CAAC,CAAC,EAAE;IAC7C,MAAME,MAAM,GAAG;MAAEsB,MAAM,EAAE,KAAK;MAAE,GAAGxB;IAAQ,CAAC;IAC5C,IAAImB,IAAI,EAAE;MACRjB,MAAM,CAACwB,IAAI,GAAGZ,IAAI,CAACC,SAAS,CAACI,IAAI,CAAC;IACpC;IACA,OAAO,IAAI,CAACrB,OAAO,CAACC,QAAQ,EAAEG,MAAM,CAAC;EACvC;EAEA,MAAM0B,MAAMA,CAAC7B,QAAQ,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;IACnC,OAAO,IAAI,CAACF,OAAO,CAACC,QAAQ,EAAE;MAAEyB,MAAM,EAAE,QAAQ;MAAE,GAAGxB;IAAQ,CAAC,CAAC;EACjE;;EAEA;EACA,MAAM6B,YAAYA,CAAA,EAAG;IACnB,OAAO,IAAI,CAACN,GAAG,CAAC,YAAY,CAAC;EAC/B;EAEA,MAAMO,WAAWA,CAACC,EAAE,EAAE;IACpB,OAAO,IAAI,CAACR,GAAG,CAAC,cAAcQ,EAAE,EAAE,CAAC;EACrC;;EAEA;EACA,MAAMC,aAAaA,CAAA,EAAG;IACpB,OAAO,IAAI,CAACT,GAAG,CAAC,aAAa,CAAC;EAChC;EAEA,MAAMU,WAAWA,CAACF,EAAE,EAAE;IACpB,OAAO,IAAI,CAACR,GAAG,CAAC,eAAeQ,EAAE,EAAE,CAAC;EACtC;EAEA,MAAMG,uBAAuBA,CAACC,UAAU,EAAE;IACxC,OAAO,IAAI,CAACZ,GAAG,CAAC,wBAAwBY,UAAU,EAAE,CAAC;EACvD;;EAEA;EACA,MAAMC,gBAAgBA,CAAA,EAAG;IACvB,OAAO,IAAI,CAACb,GAAG,CAAC,gBAAgB,CAAC;EACnC;EAEA,MAAMc,cAAcA,CAACN,EAAE,EAAE;IACvB,OAAO,IAAI,CAACR,GAAG,CAAC,kBAAkBQ,EAAE,EAAE,CAAC;EACzC;EAEA,MAAMO,0BAA0BA,CAACC,UAAU,EAAE;IAC3C,OAAO,IAAI,CAAChB,GAAG,CAAC,2BAA2BgB,UAAU,EAAE,CAAC;EAC1D;;EAEA;EACA,MAAMC,SAASA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACjB,GAAG,CAAC,SAAS,CAAC;EAC5B;;EAEA;EACA,MAAMkB,UAAUA,CAACC,MAAM,GAAG,CAAC,CAAC,EAAE;IAC5B,MAAMC,WAAW,GAAG,IAAIC,eAAe,CAACF,MAAM,CAAC,CAACG,QAAQ,CAAC,CAAC;IAC1D,OAAO,IAAI,CAACtB,GAAG,CAAC,WAAWoB,WAAW,GAAG,IAAIA,WAAW,EAAE,GAAG,EAAE,EAAE,CAAC;EACpE;EAEA,MAAMG,SAASA,CAACf,EAAE,EAAE;IAClB,OAAO,IAAI,CAACR,GAAG,CAAC,YAAYQ,EAAE,EAAE,CAAC;EACnC;EAEA,MAAMgB,YAAYA,CAACC,UAAU,EAAE;IAC7B,OAAO,IAAI,CAACvB,IAAI,CAAC,UAAU,EAAEuB,UAAU,CAAC;EAC1C;EAEA,MAAMC,YAAYA,CAAClB,EAAE,EAAEiB,UAAU,EAAE;IACjC,OAAO,IAAI,CAACrB,GAAG,CAAC,YAAYI,EAAE,EAAE,EAAEiB,UAAU,CAAC;EAC/C;EAEA,MAAME,YAAYA,CAACnB,EAAE,EAAE;IACrB,OAAO,IAAI,CAACH,MAAM,CAAC,YAAYG,EAAE,EAAE,CAAC;EACtC;EAEA,MAAMoB,aAAaA,CAACC,aAAa,EAAE;IACjC,OAAO,IAAI,CAAC3B,IAAI,CAAC,iBAAiB,EAAE2B,aAAa,CAAC;EACpD;EAEA,MAAMC,oBAAoBA,CAAClB,UAAU,EAAE;IACrC,OAAO,IAAI,CAACZ,GAAG,CAAC,qBAAqBY,UAAU,EAAE,CAAC;EACpD;EAEA,MAAMmB,oBAAoBA,CAACf,UAAU,EAAE;IACrC,OAAO,IAAI,CAAChB,GAAG,CAAC,qBAAqBgB,UAAU,EAAE,CAAC;EACpD;EAEA,MAAMgB,uBAAuBA,CAACC,aAAa,EAAE;IAC3C,OAAO,IAAI,CAACjC,GAAG,CAAC,wBAAwBiC,aAAa,EAAE,CAAC;EAC1D;EAEA,MAAMC,mBAAmBA,CAAA,EAAG;IAC1B,OAAO,IAAI,CAAClC,GAAG,CAAC,qBAAqB,CAAC;EACxC;EAEA,MAAMmC,cAAcA,CAAA,EAAG;IACrB,OAAO,IAAI,CAACnC,GAAG,CAAC,gBAAgB,CAAC;EACnC;;EAEA;EACA,MAAMoC,iBAAiBA,CAACC,WAAW,EAAE;IACnC,OAAO,IAAI,CAACnC,IAAI,CAAC,eAAe,EAAEmC,WAAW,CAAC;EAChD;EAEA,MAAMC,qBAAqBA,CAACC,SAAS,EAAE;IACrC,OAAO,IAAI,CAACrC,IAAI,CAAC,2BAA2B,EAAEqC,SAAS,CAAC;EAC1D;EAEA,MAAMC,kBAAkBA,CAACD,SAAS,EAAE;IAClC,OAAO,IAAI,CAACrC,IAAI,CAAC,uBAAuB,EAAEqC,SAAS,CAAC;EACtD;AACF;AAEA,MAAM9C,QAAQ,SAASgD,KAAK,CAAC;EAC3BpE,WAAWA,CAACgB,MAAM,EAAEK,OAAO,EAAEE,IAAI,GAAG,CAAC,CAAC,EAAE;IACtC,KAAK,CAACF,OAAO,CAAC;IACd,IAAI,CAACgD,IAAI,GAAG,UAAU;IACtB,IAAI,CAACrD,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACO,IAAI,GAAGA,IAAI;EAClB;EAEA+C,iBAAiBA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACtD,MAAM,KAAK,GAAG,IAAI,IAAI,CAACO,IAAI,CAACgD,MAAM;EAChD;EAEAC,eAAeA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACxD,MAAM,KAAK,GAAG;EAC5B;EAEAyD,aAAaA,CAAA,EAAG;IACd,OAAO,IAAI,CAACzD,MAAM,IAAI,GAAG;EAC3B;EAEA0D,mBAAmBA,CAAA,EAAG;IACpB,OAAO,IAAI,CAACnD,IAAI,CAACgD,MAAM,IAAI,CAAC,CAAC;EAC/B;AACF;AAEA,SAASxE,UAAU,EAAEqB,QAAQ;AAC7B,eAAe,IAAIrB,UAAU,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}