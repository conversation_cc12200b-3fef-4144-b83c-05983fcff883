.form-selection-view {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.form-selection-header {
  text-align: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #e1e5e9;
}

.form-selection-header h2 {
  margin: 0 0 0.5rem 0;
  color: #495057;
  font-size: 2rem;
}

.form-selection-header p {
  margin: 0 0 1rem 0;
  color: #6c757d;
  font-size: 1.1rem;
}

.form-selection-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.form-selection-content {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.division-section {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  overflow: hidden;
}

.division-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem;
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
}

.division-header:hover {
  background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
}

.division-header h3 {
  margin: 0;
  font-size: 1.3rem;
}

.form-count {
  background: rgba(255,255,255,0.2);
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 500;
}

.expand-icon {
  font-size: 1.2rem;
  transition: transform 0.3s ease;
}

.division-content {
  padding: 1.5rem;
}

.category-section {
  margin-top: 1rem;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  overflow: hidden;
}

.category-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 1.5rem;
  background: #f8f9fa;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.category-header:hover {
  background: #e9ecef;
}

.category-header h4 {
  margin: 0;
  color: #495057;
  font-size: 1.1rem;
}

.category-content {
  padding: 1rem 1.5rem;
  background: #fafbfc;
}

.forms-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
}

.form-card {
  background: white;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.form-card:hover {
  border-color: #007bff;
  box-shadow: 0 4px 12px rgba(0,123,255,0.15);
  transform: translateY(-2px);
}

.form-card.default-form {
  border-color: #28a745;
}

.form-card.default-form:hover {
  border-color: #1e7e34;
  box-shadow: 0 4px 12px rgba(40,167,69,0.15);
}

.form-card.custom-form {
  border-color: #fd7e14;
}

.form-card.custom-form:hover {
  border-color: #e55a00;
  box-shadow: 0 4px 12px rgba(253,126,20,0.15);
}

.form-card.division-form {
  border-color: #007bff;
}

.form-card.category-form {
  border-color: #6f42c1;
}

.form-card.category-form:hover {
  border-color: #5a32a3;
  box-shadow: 0 4px 12px rgba(111,66,193,0.15);
}

.form-card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;
}

.form-card-header h4 {
  margin: 0;
  color: #495057;
  font-size: 1.1rem;
  font-weight: 600;
}

.form-type {
  background: #e9ecef;
  color: #495057;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
}

.default-form .form-type {
  background: #d4edda;
  color: #155724;
}

.custom-form .form-type {
  background: #ffeaa7;
  color: #d63031;
}

.division-form .form-type {
  background: #cce7ff;
  color: #004085;
}

.category-form .form-type {
  background: #e2d9f3;
  color: #4a148c;
}

.form-card-body p {
  margin: 0 0 1rem 0;
  color: #6c757d;
  font-size: 0.9rem;
  line-height: 1.4;
}

.form-stats {
  display: flex;
  gap: 1rem;
  font-size: 0.875rem;
  color: #6c757d;
}

.form-stats span {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.no-forms-message {
  text-align: center;
  padding: 3rem 2rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.no-forms-message h3 {
  margin: 0 0 1rem 0;
  color: #495057;
}

.no-forms-message p {
  margin: 0 0 1rem 0;
  color: #6c757d;
}

.no-forms-message ul {
  text-align: left;
  display: inline-block;
  color: #6c757d;
}

.clickable {
  cursor: pointer;
  user-select: none;
}

/* Responsive Design */
@media (max-width: 768px) {
  .form-selection-view {
    padding: 1rem;
  }
  
  .forms-grid {
    grid-template-columns: 1fr;
  }
  
  .division-header,
  .category-header {
    padding: 1rem;
  }
  
  .division-content,
  .category-content {
    padding: 1rem;
  }
  
  .form-card {
    padding: 1rem;
  }
}
