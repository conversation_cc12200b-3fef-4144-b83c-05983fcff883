import React, { useState, useEffect } from 'react';
import './FieldConfigModal.css';

const FieldConfigModal = ({ field, onSave, onCancel }) => {
  const [config, setConfig] = useState({
    key: '',
    label: '',
    type: '',
    required: false,
    placeholder: '',
    helpText: '',
    validation: {},
    conditional: null,
    options: []
  });

  useEffect(() => {
    if (field) {
      setConfig({
        key: field.key,
        label: field.label,
        type: field.type,
        required: field.required || false,
        placeholder: field.placeholder || '',
        helpText: field.helpText || '',
        validation: field.validation || {},
        conditional: field.conditional || null,
        options: field.options || []
      });
    }
  }, [field]);

  const handleInputChange = (key, value) => {
    setConfig(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const handleValidationChange = (key, value) => {
    setConfig(prev => ({
      ...prev,
      validation: {
        ...prev.validation,
        [key]: value
      }
    }));
  };

  const handleConditionalChange = (key, value) => {
    setConfig(prev => ({
      ...prev,
      conditional: prev.conditional ? {
        ...prev.conditional,
        [key]: value
      } : { [key]: value }
    }));
  };

  const handleSave = () => {
    const updatedField = {
      ...field,
      ...config
    };
    onSave(updatedField);
  };

  const renderValidationOptions = () => {
    switch (config.type) {
      case 'text':
      case 'textarea':
        return (
          <>
            <div className="form-group">
              <label>Minimum Length</label>
              <input
                type="number"
                value={config.validation.minLength || ''}
                onChange={(e) => handleValidationChange('minLength', parseInt(e.target.value) || undefined)}
                placeholder="Minimum character length"
              />
            </div>
            <div className="form-group">
              <label>Maximum Length</label>
              <input
                type="number"
                value={config.validation.maxLength || ''}
                onChange={(e) => handleValidationChange('maxLength', parseInt(e.target.value) || undefined)}
                placeholder="Maximum character length"
              />
            </div>
            <div className="form-group">
              <label>Pattern (Regex)</label>
              <input
                type="text"
                value={config.validation.pattern || ''}
                onChange={(e) => handleValidationChange('pattern', e.target.value)}
                placeholder="Regular expression pattern"
              />
            </div>
          </>
        );
      case 'number':
        return (
          <>
            <div className="form-group">
              <label>Minimum Value</label>
              <input
                type="number"
                value={config.validation.min || ''}
                onChange={(e) => handleValidationChange('min', parseInt(e.target.value) || undefined)}
                placeholder="Minimum value"
              />
            </div>
            <div className="form-group">
              <label>Maximum Value</label>
              <input
                type="number"
                value={config.validation.max || ''}
                onChange={(e) => handleValidationChange('max', parseInt(e.target.value) || undefined)}
                placeholder="Maximum value"
              />
            </div>
          </>
        );
      case 'email':
        return (
          <div className="form-group">
            <label>
              <input
                type="checkbox"
                checked={config.validation.allowMultiple || false}
                onChange={(e) => handleValidationChange('allowMultiple', e.target.checked)}
              />
              Allow multiple emails (comma-separated)
            </label>
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <div className="modal-overlay">
      <div className="modal-content">
        <div className="modal-header">
          <h3>Configure Field: {field?.label}</h3>
          <button type="button" onClick={onCancel} className="close-button">×</button>
        </div>

        <div className="modal-body">
          <div className="form-group">
            <label>Field Label *</label>
            <input
              type="text"
              value={config.label}
              onChange={(e) => handleInputChange('label', e.target.value)}
              placeholder="Enter field label"
            />
          </div>

          <div className="form-group">
            <label>Field Type</label>
            <select
              value={config.type}
              onChange={(e) => handleInputChange('type', e.target.value)}
            >
              <option value="text">Text</option>
              <option value="textarea">Textarea</option>
              <option value="number">Number</option>
              <option value="email">Email</option>
              <option value="tel">Phone</option>
              <option value="url">URL</option>
              <option value="date">Date</option>
              <option value="checkbox">Checkbox</option>
              <option value="select">Select</option>
              <option value="multiselect">Multi-Select</option>
              <option value="array">Array</option>
            </select>
          </div>

          <div className="form-group">
            <label>
              <input
                type="checkbox"
                checked={config.required}
                onChange={(e) => handleInputChange('required', e.target.checked)}
              />
              Required Field
            </label>
          </div>

          <div className="form-group">
            <label>Placeholder Text</label>
            <input
              type="text"
              value={config.placeholder}
              onChange={(e) => handleInputChange('placeholder', e.target.value)}
              placeholder="Enter placeholder text"
            />
          </div>

          <div className="form-group">
            <label>Help Text</label>
            <textarea
              value={config.helpText}
              onChange={(e) => handleInputChange('helpText', e.target.value)}
              placeholder="Enter help text for users"
              rows="2"
            />
          </div>

          {/* Validation Options */}
          <div className="config-section">
            <h4>Validation Rules</h4>
            {renderValidationOptions()}
          </div>

          {/* Conditional Display */}
          <div className="config-section">
            <h4>Conditional Display</h4>
            <div className="form-group">
              <label>Show when field:</label>
              <input
                type="text"
                value={config.conditional?.field || ''}
                onChange={(e) => handleConditionalChange('field', e.target.value)}
                placeholder="Field name (e.g., hasAssociate)"
              />
            </div>
            <div className="form-group">
              <label>Has value:</label>
              <input
                type="text"
                value={config.conditional?.value || ''}
                onChange={(e) => handleConditionalChange('value', e.target.value)}
                placeholder="Value (e.g., true, false, or specific value)"
              />
            </div>
          </div>

          {/* Options for select/multiselect */}
          {(config.type === 'select' || config.type === 'multiselect') && (
            <div className="config-section">
              <h4>Options</h4>
              <div className="options-list">
                {config.options.map((option, index) => (
                  <div key={index} className="option-item">
                    <input
                      type="text"
                      value={option.label}
                      onChange={(e) => {
                        const newOptions = [...config.options];
                        newOptions[index] = { ...option, label: e.target.value };
                        handleInputChange('options', newOptions);
                      }}
                      placeholder="Option label"
                    />
                    <input
                      type="text"
                      value={option.value}
                      onChange={(e) => {
                        const newOptions = [...config.options];
                        newOptions[index] = { ...option, value: e.target.value };
                        handleInputChange('options', newOptions);
                      }}
                      placeholder="Option value"
                    />
                    <button
                      type="button"
                      onClick={() => {
                        const newOptions = config.options.filter((_, i) => i !== index);
                        handleInputChange('options', newOptions);
                      }}
                      className="btn-remove"
                    >
                      Remove
                    </button>
                  </div>
                ))}
                <button
                  type="button"
                  onClick={() => {
                    const newOptions = [...config.options, { label: '', value: '' }];
                    handleInputChange('options', newOptions);
                  }}
                  className="btn-add"
                >
                  Add Option
                </button>
              </div>
            </div>
          )}
        </div>

        <div className="modal-footer">
          <button type="button" onClick={onCancel} className="btn btn-outline">
            Cancel
          </button>
          <button type="button" onClick={handleSave} className="btn btn-primary">
            Save Configuration
          </button>
        </div>
      </div>
    </div>
  );
};

export default FieldConfigModal;
