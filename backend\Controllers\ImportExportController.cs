using Microsoft.AspNetCore.Mvc;
using CrmApi.Models.ImportExport;
using CrmApi.Services.ImportExport;

namespace CrmApi.Controllers
{
    [ApiController]
    [Route("api/import-export")]
    public class ImportExportController : ControllerBase
    {
        private readonly IImportExportService _importExportService;
        private readonly ILogger<ImportExportController> _logger;

        public ImportExportController(IImportExportService importExportService, ILogger<ImportExportController> logger)
        {
            _importExportService = importExportService;
            _logger = logger;
        }

        /// <summary>
        /// Upload and import person data from CSV or Excel file
        /// </summary>
        [HttpPost("persons/import")]
        [RequestSizeLimit(10 * 1024 * 1024)] // 10MB limit
        public async Task<ActionResult<PersonImportResponse>> ImportPersons([FromForm] PersonImportRequest request)
        {
            try
            {
                // Validate file
                if (request.File == null || request.File.Length == 0)
                {
                    return BadRequest("No file uploaded");
                }

                // Validate file size (10MB limit)
                if (request.File.Length > 10 * 1024 * 1024)
                {
                    return BadRequest("File size exceeds 10MB limit");
                }

                // Validate file extension
                var allowedExtensions = new[] { ".csv", ".xlsx", ".xls" };
                var fileExtension = Path.GetExtension(request.File.FileName).ToLowerInvariant();
                if (!allowedExtensions.Contains(fileExtension))
                {
                    return BadRequest($"Unsupported file format. Allowed formats: {string.Join(", ", allowedExtensions)}");
                }

                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                // Log the received request for debugging
                _logger.LogInformation("Import request received - DefaultDivisionId: {DivisionId}, DefaultCategoryId: {CategoryId}, DefaultSubCategoryId: {SubCategoryId}, DefaultValues: {DefaultValues}, FieldMapping: {FieldMapping}",
                    request.DefaultDivisionId, request.DefaultCategoryId, request.DefaultSubCategoryId, request.DefaultValues ?? "null", request.FieldMapping ?? "null");

                var response = await _importExportService.ImportPersonsAsync(request);
                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to import persons");
                return StatusCode(500, new { message = "Import failed", error = ex.Message });
            }
        }

        /// <summary>
        /// Validate import file without actually importing data
        /// </summary>
        [HttpPost("persons/validate")]
        [RequestSizeLimit(10 * 1024 * 1024)] // 10MB limit
        public async Task<ActionResult<PersonImportResponse>> ValidateImportFile([FromForm] PersonImportRequest request)
        {
            try
            {
                // Validate file
                if (request.File == null || request.File.Length == 0)
                {
                    return BadRequest("No file uploaded");
                }

                // Validate file size (10MB limit)
                if (request.File.Length > 10 * 1024 * 1024)
                {
                    return BadRequest("File size exceeds 10MB limit");
                }

                // Validate file extension
                var allowedExtensions = new[] { ".csv", ".xlsx", ".xls" };
                var fileExtension = Path.GetExtension(request.File.FileName).ToLowerInvariant();
                if (!allowedExtensions.Contains(fileExtension))
                {
                    return BadRequest($"Unsupported file format. Allowed formats: {string.Join(", ", allowedExtensions)}");
                }

                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var response = await _importExportService.ValidateImportFileAsync(request);
                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to validate import file");
                return StatusCode(500, new { message = "Validation failed", error = ex.Message });
            }
        }

        /// <summary>
        /// Get import job status and progress
        /// </summary>
        [HttpGet("persons/import-status/{jobId}")]
        public async Task<ActionResult<PersonImportResponse>> GetImportStatus(string jobId)
        {
            try
            {
                var response = await _importExportService.GetImportStatusAsync(jobId);
                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get import status for job {JobId}", jobId);
                return NotFound(new { message = "Import job not found", jobId });
            }
        }

        /// <summary>
        /// Get detailed import progress
        /// </summary>
        [HttpGet("persons/import-progress/{jobId}")]
        public async Task<ActionResult<ImportProgressUpdate>> GetImportProgress(string jobId)
        {
            try
            {
                var progress = await _importExportService.GetImportProgressAsync(jobId);
                return Ok(progress);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get import progress for job {JobId}", jobId);
                return NotFound(new { message = "Import progress not found", jobId });
            }
        }

        /// <summary>
        /// Cancel a running import job
        /// </summary>
        [HttpPost("persons/import-cancel/{jobId}")]
        public async Task<IActionResult> CancelImport(string jobId)
        {
            try
            {
                var result = await _importExportService.CancelImportAsync(jobId);
                if (result)
                {
                    return Ok(new { message = "Import job cancelled successfully", jobId });
                }
                else
                {
                    return BadRequest(new { message = "Import job could not be cancelled", jobId });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to cancel import job {JobId}", jobId);
                return StatusCode(500, new { message = "Failed to cancel import", error = ex.Message });
            }
        }

        /// <summary>
        /// Export person data to CSV or Excel format
        /// </summary>
        [HttpPost("persons/export")]
        public async Task<IActionResult> ExportPersons([FromBody] PersonExportRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var response = await _importExportService.ExportPersonsAsync(request);
                
                return File(
                    response.FileContent,
                    response.ContentType,
                    response.FileName
                );
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to export persons");
                return StatusCode(500, new { message = "Export failed", error = ex.Message });
            }
        }

        /// <summary>
        /// Download import template file
        /// </summary>
        [HttpGet("persons/template")]
        public async Task<IActionResult> GetImportTemplate([FromQuery] FileFormat format = FileFormat.CSV, [FromQuery] bool includeSampleData = false)
        {
            try
            {
                var request = new ExportTemplateRequest
                {
                    Format = format,
                    IncludeSampleData = includeSampleData,
                    IncludeValidationRules = true
                };

                var response = await _importExportService.GenerateImportTemplateAsync(request);
                
                return File(
                    response.FileContent,
                    response.ContentType,
                    response.FileName
                );
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to generate import template");
                return StatusCode(500, new { message = "Template generation failed", error = ex.Message });
            }
        }

        /// <summary>
        /// Get available export columns
        /// </summary>
        [HttpGet("persons/export-columns")]
        public ActionResult<object> GetExportColumns()
        {
            var columns = new
            {
                AllColumns = PersonImportColumns.AllColumns,
                RequiredColumns = PersonImportColumns.RequiredColumns,
                ColumnDescriptions = new Dictionary<string, string>
                {
                    { PersonImportColumns.DivisionName, "Name of the division" },
                    { PersonImportColumns.CategoryName, "Name of the category" },
                    { PersonImportColumns.SubCategoryName, "Name of the subcategory (optional)" },
                    { PersonImportColumns.Name, "Full name of the person (required)" },
                    { PersonImportColumns.MobileNumber, "Mobile number in Indian format (required)" },
                    { PersonImportColumns.Nature, "Business nature: Business, Corporate, Agriculture, Individual (required)" },
                    { PersonImportColumns.Gender, "Gender: Male, Female, Other" },
                    { PersonImportColumns.PrimaryEmailId, "Primary email address" },
                    { PersonImportColumns.StarRating, "Rating from 1 to 5" }
                }
            };

            return Ok(columns);
        }

        /// <summary>
        /// Get import/export statistics
        /// </summary>
        [HttpGet("persons/statistics")]
        public async Task<ActionResult<object>> GetImportExportStatistics()
        {
            try
            {
                // This would typically come from a database or cache
                var statistics = new
                {
                    TotalImportsToday = 0,
                    TotalExportsToday = 0,
                    TotalImportsThisMonth = 0,
                    TotalExportsThisMonth = 0,
                    AverageImportTime = "0 seconds",
                    AverageExportTime = "0 seconds",
                    MostCommonImportErrors = new[]
                    {
                        "Invalid mobile number format",
                        "Division not found",
                        "Category not found",
                        "Invalid email format"
                    }
                };

                return Ok(statistics);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get import/export statistics");
                return StatusCode(500, new { message = "Failed to get statistics", error = ex.Message });
            }
        }

        /// <summary>
        /// Clean up old import jobs and temporary files
        /// </summary>
        [HttpPost("cleanup")]
        public async Task<IActionResult> CleanupOldData([FromQuery] int daysOld = 7)
        {
            try
            {
                var cutoffDate = DateTime.UtcNow.AddDays(-daysOld);
                
                await _importExportService.CleanupCompletedJobsAsync(cutoffDate);
                await _importExportService.CleanupTempFilesAsync(cutoffDate);

                return Ok(new { message = $"Cleaned up data older than {daysOld} days", cutoffDate });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to cleanup old data");
                return StatusCode(500, new { message = "Cleanup failed", error = ex.Message });
            }
        }
    }
}
