using Microsoft.EntityFrameworkCore;
using CrmApi.Data;
using CrmApi.Models.State;

namespace CrmApi.Repositories.State
{
    public class StateRepository : IStateRepository
    {
        private readonly CrmDbContext _context;

        public StateRepository(CrmDbContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<Models.State.State>> GetAllAsync()
        {
            return await _context.States.ToListAsync();
        }

        public async Task<Models.State.State?> GetByIdAsync(int id)
        {
            return await _context.States.FindAsync(id);
        }

        public async Task<Models.State.State> CreateAsync(Models.State.State state)
        {
            state.CreatedAt = DateTime.UtcNow;
            
            _context.States.Add(state);
            await _context.SaveChangesAsync();
            return state;
        }

        public async Task<Models.State.State> UpdateAsync(Models.State.State state)
        {
            _context.Entry(state).State = EntityState.Modified;
            await _context.SaveChangesAsync();
            return state;
        }

        public async Task<bool> DeleteAsync(int id)
        {
            var state = await _context.States.FindAsync(id);
            if (state == null)
                return false;

            _context.States.Remove(state);
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> ExistsAsync(int id)
        {
            return await _context.States.AnyAsync(s => s.Id == id);
        }

        public async Task<bool> NameExistsAsync(string name, int? excludeId = null)
        {
            var query = _context.States.Where(s => s.Name == name);
            if (excludeId.HasValue)
                query = query.Where(s => s.Id != excludeId.Value);
            
            return await query.AnyAsync();
        }
    }
}
