# CRM Database Partitioning Guide

## Overview

The CRM system implements **table partitioning** on the Persons table based on the hierarchical structure of Division → Category → SubCategory. This provides significant performance improvements for large datasets by physically separating data into smaller, more manageable chunks.

## 🏗️ Partitioning Strategy

### Partition Key Structure
- **Primary Partition Key**: `division_id`
- **Secondary Partition Key**: `category_id` 
- **Tertiary Partition Key**: `subcategory_id` (with NULL handling)

### Partition Function
```sql
CREATE PARTITION FUNCTION PF_PersonsHierarchy (INT, INT, INT)
AS RANGE RIGHT FOR VALUES (
    (1, 1, 1), (1, 1, 2), (1, 1, 3), -- Division 1, Category 1, SubCategories 1-3
    (1, 2, 1), (1, 2, 2), (1, 2, 3), -- Division 1, Category 2, SubCategories 1-3
    -- ... more partitions
);
```

### Benefits
- ✅ **Query Performance**: 10-100x faster queries when filtering by partition keys
- ✅ **Parallel Processing**: Multiple partitions can be processed simultaneously
- ✅ **Maintenance**: Easier backup, restore, and maintenance operations
- ✅ **Scalability**: Better handling of large datasets (millions of records)
- ✅ **Index Efficiency**: Smaller, more efficient indexes per partition

## 🚀 Backend Implementation

### 1. Partition-Aware Service

The `PartitionAwarePersonService` provides optimized methods for partition-based operations:

```csharp
// Get persons from specific partition
var persons = await partitionService.GetPersonsByPartitionAsync(
    divisionId: 1, 
    categoryId: 2, 
    subCategoryId: 3
);

// Search within partitions (most efficient)
var searchRequest = new PersonSearchRequest 
{
    DivisionId = 1,      // Always specify for best performance
    CategoryId = 2,      // Highly recommended
    SubCategoryId = 3,   // Optional but improves performance
    Name = "John",
    // ... other filters
};
var results = await partitionService.SearchPersonsInPartitionAsync(searchRequest);
```

### 2. Partition-Aware Controller

The `PartitionedPersonsController` exposes partition-optimized endpoints:

```http
# Get persons from specific partition
GET /api/partitionedpersons/partition/1?categoryId=2&subCategoryId=3

# Get person by ID within partition (fastest lookup)
GET /api/partitionedpersons/partition/1/category/2/person/123

# Search within partitions
POST /api/partitionedpersons/search
{
  "divisionId": 1,
  "categoryId": 2,
  "name": "John"
}

# Get partition statistics
GET /api/partitionedpersons/partitions/statistics
```

### 3. Updated Repository

The `PersonRepository` has been updated to prioritize partition filters:

```csharp
// Partition filters are applied FIRST for optimal performance
if (request.DivisionId.HasValue)
    query = query.Where(p => p.DivisionId == request.DivisionId.Value);

if (request.CategoryId.HasValue)
    query = query.Where(p => p.CategoryId == request.CategoryId.Value);

if (request.SubCategoryId.HasValue)
    query = query.Where(p => p.SubCategoryId == request.SubCategoryId.Value);

// Then apply other filters...
```

## 📊 Performance Optimization Guidelines

### 1. Always Include Partition Keys in Queries

**❌ Slow Query (Scans all partitions):**
```csharp
var persons = await context.Persons
    .Where(p => p.Name.Contains("John"))
    .ToListAsync();
```

**✅ Fast Query (Targets specific partition):**
```csharp
var persons = await context.Persons
    .Where(p => p.DivisionId == 1 && p.CategoryId == 2)
    .Where(p => p.Name.Contains("John"))
    .ToListAsync();
```

### 2. Query Performance Hierarchy

1. **Fastest**: Division + Category + SubCategory specified
2. **Fast**: Division + Category specified
3. **Moderate**: Division only specified
4. **Slow**: No partition keys specified (scans all partitions)

### 3. Index Strategy

All indexes are **partition-aligned** and include partition keys:

```sql
-- Partition-aligned indexes
CREATE INDEX IX_persons_mobile_number 
ON persons(mobile_number, division_id, category_id, partition_key);

CREATE INDEX IX_persons_name 
ON persons(name, division_id, category_id, partition_key);
```

## 🔧 Migration and Setup

### 1. Apply Partitioning Migration

```bash
# Run the partitioning migration
dotnet ef database update

# This will:
# - Create partition function and scheme
# - Recreate persons table as partitioned
# - Migrate existing data
# - Create partition-aligned indexes
```

### 2. Verify Partitioning

```sql
-- Check partition function
SELECT * FROM sys.partition_functions WHERE name = 'PF_PersonsHierarchy';

-- Check partition scheme  
SELECT * FROM sys.partition_schemes WHERE name = 'PS_PersonsHierarchy';

-- View partition distribution
SELECT 
    p.partition_number,
    p.rows,
    rv.value
FROM sys.partitions p
JOIN sys.partition_range_values rv ON p.partition_number = rv.boundary_id + 1
WHERE p.object_id = OBJECT_ID('persons')
ORDER BY p.partition_number;
```

## 📈 Monitoring and Statistics

### 1. Partition Statistics API

```http
GET /api/partitionedpersons/partitions/statistics
```

Returns:
```json
[
  {
    "divisionId": 1,
    "categoryId": 1,
    "subCategoryId": 1,
    "personCount": 1250,
    "averageStarRating": 4.2,
    "totalTransactionValue": 15000000,
    "lastActivity": "2024-12-01T10:30:00Z"
  }
]
```

### 2. Partition Information

```http
GET /api/partitionedpersons/partition/1/info?categoryId=2
```

Returns:
```json
{
  "divisionId": 1,
  "categoryId": 2,
  "totalPersons": 500,
  "activePersons": 485,
  "deletedPersons": 15,
  "averageStarRating": 4.1,
  "totalTransactionValue": 8500000,
  "lastUpdated": "2024-12-01T10:30:00Z"
}
```

## 🎯 Best Practices

### 1. Frontend Integration

Always pass partition parameters from the frontend:

```javascript
// When user selects division/category in UI
const searchParams = {
  divisionId: selectedDivision.id,    // Always include
  categoryId: selectedCategory?.id,   // Include if available
  subCategoryId: selectedSubCategory?.id, // Include if available
  name: searchTerm,
  // ... other filters
};

// Use partition-aware endpoint
const response = await fetch('/api/partitionedpersons/search', {
  method: 'POST',
  body: JSON.stringify(searchParams)
});
```

### 2. Import/Export Optimization

For bulk operations, group by partition:

```csharp
// Group import data by partition for optimal performance
var partitionGroups = importData
    .GroupBy(d => new { d.DivisionId, d.CategoryId, d.SubCategoryId })
    .ToList();

foreach (var group in partitionGroups)
{
    // Process each partition group separately
    await ProcessPartitionGroup(group);
}
```

### 3. Reporting and Analytics

Use partition elimination for faster reports:

```csharp
// Monthly report for specific division
var monthlyStats = await context.Persons
    .Where(p => p.DivisionId == divisionId)  // Partition elimination
    .Where(p => p.CreatedAt >= startDate && p.CreatedAt <= endDate)
    .GroupBy(p => p.CreatedAt.Month)
    .Select(g => new { Month = g.Key, Count = g.Count() })
    .ToListAsync();
```

## ⚠️ Important Considerations

### 1. Primary Key Requirements

Partitioned tables require partition keys in the primary key:

```sql
-- Primary key MUST include partition keys
CONSTRAINT PK_persons_partitioned 
PRIMARY KEY (id, division_id, category_id, partition_key)
```

### 2. Foreign Key Limitations

Foreign keys must include partition keys or be disabled:

```csharp
// When referencing partitioned table, include partition keys
public class PersonNote 
{
    public int PersonId { get; set; }
    public int PersonDivisionId { get; set; }  // Required for FK
    public int PersonCategoryId { get; set; }   // Required for FK
    // ...
}
```

### 3. Unique Constraints

Unique constraints must include partition keys:

```sql
-- Unique constraint includes partition keys
CREATE UNIQUE INDEX IX_persons_mobile_division_category_unique 
ON persons(mobile_number, division_id, category_id, partition_key) 
WHERE is_deleted = 0;
```

## 🔄 Maintenance Operations

### 1. Adding New Partitions

When adding new divisions/categories:

```sql
-- Split existing partition to add new range
ALTER PARTITION FUNCTION PF_PersonsHierarchy()
SPLIT RANGE (4, 1, 1);  -- Add new division 4, category 1, subcategory 1
```

### 2. Partition Maintenance

```sql
-- Check partition health
SELECT 
    OBJECT_NAME(p.object_id) AS table_name,
    p.partition_number,
    p.rows,
    au.total_pages * 8 / 1024 AS size_mb
FROM sys.partitions p
JOIN sys.allocation_units au ON p.partition_id = au.container_id
WHERE p.object_id = OBJECT_ID('persons')
ORDER BY p.partition_number;
```

### 3. Performance Monitoring

```sql
-- Monitor partition elimination in query plans
SET STATISTICS IO ON;
SELECT * FROM persons 
WHERE division_id = 1 AND category_id = 2;
-- Check for "Partition Elimination: True" in execution plan
```

## 🚀 Expected Performance Gains

### Query Performance Improvements

| Operation | Without Partitioning | With Partitioning | Improvement |
|-----------|---------------------|-------------------|-------------|
| Search by Division | 2-5 seconds | 0.1-0.3 seconds | **10-50x faster** |
| Search by Division+Category | 1-3 seconds | 0.05-0.1 seconds | **20-60x faster** |
| Person by ID (with partition) | 0.5-1 seconds | 0.01-0.05 seconds | **10-100x faster** |
| Bulk Import | 5-15 minutes | 1-3 minutes | **3-5x faster** |
| Reports by Division | 10-30 seconds | 1-3 seconds | **10-30x faster** |

### Scalability Benefits

- **1M+ Records**: Significant performance improvement
- **10M+ Records**: Essential for acceptable performance  
- **100M+ Records**: Required for system usability

The partitioning system ensures the CRM remains performant as data grows to enterprise scale!
