.person-management {
  min-height: 100vh;
  background-color: #f8f9fa;
  display: flex;
  flex-direction: column;
}

.management-header {
  background-color: white;
  border-bottom: 1px solid #e1e5e9;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 2rem;
}

.header-content h1 {
  margin: 0;
  color: #495057;
  font-size: 1.75rem;
}

.header-stats {
  display: flex;
  gap: 1.5rem;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.stat-label {
  font-size: 0.75rem;
  color: #6c757d;
  margin-bottom: 0.25rem;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: #007bff;
}

.header-nav {
  display: flex;
  padding: 0 2rem;
  background-color: #f8f9fa;
  border-top: 1px solid #e1e5e9;
}

.nav-btn {
  background: none;
  border: none;
  padding: 1rem 1.5rem;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  color: #6c757d;
  border-bottom: 3px solid transparent;
  transition: all 0.2s ease;
}

.nav-btn:hover {
  color: #495057;
  background-color: #e9ecef;
}

.nav-btn.active {
  color: #007bff;
  border-bottom-color: #007bff;
  background-color: white;
}

.notification {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 1rem 1.5rem;
  border-radius: 4px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  display: flex;
  align-items: center;
  gap: 1rem;
  min-width: 300px;
  animation: slideIn 0.3s ease;
}

.notification.success {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.notification.error {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.notification.warning {
  background-color: #fff3cd;
  color: #856404;
  border: 1px solid #ffeaa7;
}

.notification-close {
  background: none;
  border: none;
  font-size: 1.2rem;
  cursor: pointer;
  color: inherit;
  opacity: 0.7;
}

.notification-close:hover {
  opacity: 1;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.management-content {
  flex: 1;
  padding: 2rem;
  overflow-y: auto;
}

.management-footer {
  background-color: white;
  border-top: 1px solid #e1e5e9;
  padding: 2rem;
  margin-top: auto;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.footer-section h4 {
  margin: 0 0 1rem 0;
  color: #495057;
  font-size: 1rem;
}

.quick-actions {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.quick-action-btn {
  background-color: #007bff;
  color: white;
  border: none;
  padding: 0.75rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.875rem;
  transition: background-color 0.2s ease;
  text-align: left;
}

.quick-action-btn:hover {
  background-color: #0056b3;
}

.footer-stats {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.footer-stat {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem;
  background-color: #f8f9fa;
  border-radius: 4px;
  font-size: 0.875rem;
}

.footer-stat span:first-child {
  color: #6c757d;
}

.footer-stat span:last-child {
  font-weight: 600;
  color: #495057;
}

.footer-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  font-size: 0.875rem;
  color: #6c757d;
}

/* Responsive design */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
    padding: 1rem;
  }
  
  .header-stats {
    flex-direction: row;
    gap: 1rem;
    width: 100%;
    justify-content: space-around;
  }
  
  .header-nav {
    padding: 0 1rem;
    overflow-x: auto;
  }
  
  .nav-btn {
    white-space: nowrap;
    padding: 0.75rem 1rem;
  }
  
  .management-content {
    padding: 1rem;
  }
  
  .management-footer {
    padding: 1rem;
  }
  
  .footer-content {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .notification {
    position: fixed;
    top: 10px;
    left: 10px;
    right: 10px;
    min-width: auto;
  }
}

@media (max-width: 480px) {
  .header-content h1 {
    font-size: 1.5rem;
  }
  
  .header-stats {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .stat-item {
    flex-direction: row;
    justify-content: space-between;
    width: 100%;
    padding: 0.5rem;
    background-color: #f8f9fa;
    border-radius: 4px;
  }
  
  .stat-value {
    font-size: 1.25rem;
  }
  
  .nav-btn {
    font-size: 0.75rem;
    padding: 0.5rem 0.75rem;
  }
}

/* Print styles */
@media print {
  .management-header,
  .management-footer,
  .notification {
    display: none;
  }
  
  .management-content {
    padding: 0;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .management-header {
    border-bottom-width: 2px;
  }
  
  .nav-btn.active {
    border-bottom-width: 4px;
  }
  
  .notification {
    border-width: 2px;
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .nav-btn,
  .quick-action-btn,
  .notification {
    transition: none;
  }
  
  .notification {
    animation: none;
  }
}
