using System.Text.Json.Serialization;

namespace CrmApi.Models.SubCategory
{
    public class SubCategory
    {
        public int Id { get; set; }
        public int CategoryId { get; set; }
        public string Name { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
        
        [JsonIgnore]
        public Category.Category Category { get; set; } = null!;
    }
}
