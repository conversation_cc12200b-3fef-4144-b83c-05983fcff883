using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.IdentityModel.Tokens;
using System.Text;
using CrmApi.Data;
using CrmApi.Services.Admin;
using CrmApi.Services.Division;
using CrmApi.Services.Category;
using CrmApi.Services.SubCategory;
using CrmApi.Services.State;
using CrmApi.Repositories.Admin;
using CrmApi.Repositories.Division;
using CrmApi.Repositories.Category;
using CrmApi.Repositories.SubCategory;
using CrmApi.Repositories.State;
using CrmApi.Repositories.Person;
using CrmApi.Services.Dashboard;
using CrmApi.Services.Person;
using CrmApi.Services.ImportExport;
using CrmApi.Middleware;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
builder.Services.AddControllers()
    .AddJsonOptions(options =>
    {
        options.JsonSerializerOptions.PropertyNamingPolicy = System.Text.Json.JsonNamingPolicy.CamelCase;
        options.JsonSerializerOptions.PropertyNameCaseInsensitive = true;
        options.JsonSerializerOptions.ReferenceHandler = System.Text.Json.Serialization.ReferenceHandler.IgnoreCycles;
        options.JsonSerializerOptions.DefaultIgnoreCondition = System.Text.Json.Serialization.JsonIgnoreCondition.WhenWritingNull;
    });
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

// Add Entity Framework
builder.Services.AddDbContext<CrmDbContext>(options =>
    options.UseMySql(
        builder.Configuration.GetConnectionString("DefaultConnection"),
        new MySqlServerVersion(new Version(8, 0, 21))
    ));

// Add Repository Services
builder.Services.AddScoped<IAdminRepository, AdminRepository>();
builder.Services.AddScoped<IDivisionRepository, DivisionRepository>();
builder.Services.AddScoped<ICategoryRepository, CategoryRepository>();
builder.Services.AddScoped<ISubCategoryRepository, SubCategoryRepository>();
builder.Services.AddScoped<IStateRepository, StateRepository>();
builder.Services.AddScoped<IPersonRepository, PersonRepository>();

// Add Business Services
builder.Services.AddScoped<IAdminService, AdminService>();
builder.Services.AddScoped<IDivisionService, DivisionService>();
builder.Services.AddScoped<ICategoryService, CategoryService>();
builder.Services.AddScoped<ISubCategoryService, SubCategoryService>();
builder.Services.AddScoped<IStateService, StateService>();
builder.Services.AddScoped<IPersonService, PersonService>();
builder.Services.AddScoped<IPartitionAwarePersonService, PartitionAwarePersonService>();
builder.Services.AddScoped<IDashboardService, DashboardService>();
builder.Services.AddScoped<IImportExportService, ImportExportService>();
builder.Services.AddScoped<IFileValidationService, FileValidationService>();

// Add JWT Authentication
var jwtSettings = builder.Configuration.GetSection("JwtSettings");
var key = Encoding.ASCII.GetBytes(jwtSettings["SecretKey"] ?? "your-super-secret-key-that-is-at-least-32-characters-long");

builder.Services.AddAuthentication(options =>
{
    options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
    options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
})
.AddJwtBearer(options =>
{
    options.RequireHttpsMetadata = false;
    options.SaveToken = true;
    options.TokenValidationParameters = new TokenValidationParameters
    {
        ValidateIssuerSigningKey = true,
        IssuerSigningKey = new SymmetricSecurityKey(key),
        ValidateIssuer = true,
        ValidIssuer = jwtSettings["Issuer"] ?? "CrmApi",
        ValidateAudience = true,
        ValidAudience = jwtSettings["Audience"] ?? "CrmClient",
        ValidateLifetime = true,
        ClockSkew = TimeSpan.Zero
    };
});

// Add CORS
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowReactApp",
        policy =>
        {
            policy.WithOrigins("http://localhost:3000", "http://localhost:3001")
                  .AllowAnyHeader()
                  .AllowAnyMethod();
        });
});

var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

// Only use HTTPS redirection in production or when HTTPS is properly configured
// app.UseHttpsRedirection();

// Add global exception handling middleware
app.UseMiddleware<GlobalExceptionMiddleware>();

app.UseCors("AllowReactApp");
app.UseAuthentication();
app.UseAuthorization();

// Add a default route for the root path
app.MapGet("/", () => "CRM API is running! Visit /swagger for API documentation.");

// Add a test endpoint to check database connectivity
app.MapGet("/test-db", async (CrmDbContext context) =>
{
    try
    {
        var adminCount = await context.Admins.CountAsync();
        var admin = await context.Admins.FirstOrDefaultAsync();
        return Results.Ok(new { 
            message = "Database connected successfully!", 
            adminCount = adminCount,
            firstAdmin = admin != null ? new { 
                admin.Id,
                admin.Username, 
                admin.Email, 
                admin.FirstName,
                admin.LastName,
                admin.IsActive,
                PasswordHashLength = admin.PasswordHash != null ? admin.PasswordHash.Length : 0,
                PasswordHashStart = admin.PasswordHash != null && admin.PasswordHash.Length > 0 ? 
                    admin.PasswordHash.Substring(0, Math.Min(10, admin.PasswordHash.Length)) : ""
            } : null
        });
    }
    catch (Exception ex)
    {
        return Results.BadRequest(new { error = ex.Message });
    }
});

// Add a debug endpoint to test password verification
app.MapPost("/debug-password", async (CrmDbContext context, CrmApi.Models.Admin.LoginRequest request) =>
{
    try
    {
        var admin = await context.Admins.FirstOrDefaultAsync(a => a.Username == request.Username);
        if (admin == null)
        {
            return Results.Ok(new { found = false, message = "Admin user not found" });
        }
        
        var isValid = BCrypt.Net.BCrypt.Verify(request.Password, admin.PasswordHash);
        return Results.Ok(new { 
            found = true,
            username = admin.Username,
            isActive = admin.IsActive,
            passwordValid = isValid,
            hashLength = admin.PasswordHash.Length,
            hashStart = admin.PasswordHash.Substring(0, 10)
        });
    }
    catch (Exception ex)
    {
        return Results.BadRequest(new { error = ex.Message });
    }
});

app.MapControllers();

app.Run();