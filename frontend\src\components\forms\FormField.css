.form-field {
  margin-bottom: 1.5rem;
}

.form-field.checkbox-field {
  margin-bottom: 1rem;
}

.form-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: #495057;
  font-size: 0.875rem;
}

.required-indicator {
  color: #dc3545;
  margin-left: 0.25rem;
}

.form-input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 1rem;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  background-color: white;
}

.form-input:focus {
  outline: none;
  border-color: #80bdff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-input:disabled {
  background-color: #e9ecef;
  opacity: 1;
}

.form-input.error {
  border-color: #dc3545;
}

.form-input.error:focus {
  border-color: #dc3545;
  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

/* Textarea specific styles */
textarea.form-input {
  resize: vertical;
  min-height: 80px;
}

/* Select specific styles */
select.form-input {
  cursor: pointer;
}

select.form-input[multiple] {
  height: auto;
  min-height: 120px;
}

select.form-input[multiple] option {
  padding: 0.5rem;
}

select.form-input[multiple] option:checked {
  background-color: #007bff;
  color: white;
}

/* Checkbox specific styles */
.checkbox-wrapper {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.form-checkbox {
  width: 1.2rem !important;
  height: 1.2rem;
  cursor: pointer;
  accent-color: #007bff;
}

.checkbox-label {
  font-weight: 600;
  color: #495057;
  cursor: pointer;
  margin: 0;
  font-size: 0.875rem;
}

.checkbox-label:hover {
  color: #007bff;
}

/* Help text */
.help-text {
  margin-top: 0.25rem;
  font-size: 0.875rem;
  color: #6c757d;
  font-style: italic;
}

/* Error message */
.error-message {
  margin-top: 0.25rem;
  font-size: 0.875rem;
  color: #dc3545;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.error-message::before {
  content: "⚠";
  font-size: 1rem;
}

/* Number input specific styles */
input[type="number"].form-input {
  -moz-appearance: textfield;
}

input[type="number"].form-input::-webkit-outer-spin-button,
input[type="number"].form-input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Date input specific styles */
input[type="date"].form-input {
  cursor: pointer;
}

input[type="date"].form-input::-webkit-calendar-picker-indicator {
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 3px;
}

input[type="date"].form-input::-webkit-calendar-picker-indicator:hover {
  background-color: #f8f9fa;
}

/* URL and Email input styles */
input[type="url"].form-input,
input[type="email"].form-input {
  font-family: monospace;
}

/* Tel input styles */
input[type="tel"].form-input {
  letter-spacing: 0.5px;
}

/* Array input styles */
.form-field input[placeholder*="comma"] {
  font-family: monospace;
  font-size: 0.9rem;
}

/* Focus states for better accessibility */
.form-input:focus-visible {
  outline: 2px solid #007bff;
  outline-offset: 2px;
}

.form-checkbox:focus-visible {
  outline: 2px solid #007bff;
  outline-offset: 2px;
}

/* Responsive design */
@media (max-width: 768px) {
  .form-input {
    padding: 0.875rem;
    font-size: 1.1rem;
  }
  
  .form-checkbox {
    width: 1.4rem !important;
    height: 1.4rem;
  }
  
  .checkbox-label {
    font-size: 1rem;
  }
  
  select.form-input[multiple] {
    min-height: 150px;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .form-input {
    border-width: 2px;
  }
  
  .form-input:focus {
    border-width: 3px;
  }
  
  .error-message {
    font-weight: bold;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .form-input {
    transition: none;
  }
}
