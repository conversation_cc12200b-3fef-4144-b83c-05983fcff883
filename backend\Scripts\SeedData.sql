-- CRM Database Data Seeding Script
-- This script inserts all initial data including states, divisions, categories, and subcategories

USE CrmDatabase;
GO

-- Insert all Indian states and union territories
IF NOT EXISTS (SELECT 1 FROM states)
BEGIN
    INSERT INTO states (name, code, capital, region, is_active, created_at, updated_at) VALUES
    -- States
    ('Andhra Pradesh', 'AP', 'Amaravati', 'South', 1, GETUTCDATE(), GETUTCDATE()),
    ('Arunachal Pradesh', 'AR', 'Itanagar', 'Northeast', 1, GETUTCDATE(), GETUTCDATE()),
    ('Assam', 'AS', 'Dispur', 'Northeast', 1, GETUTCDATE(), GETUTCDATE()),
    ('Bihar', 'BR', 'Patna', 'East', 1, GETUTCDATE(), GETUTCDATE()),
    ('Chhattisgarh', 'CG', 'Raipur', 'Central', 1, GETUTCDATE(), GETUTCDATE()),
    ('Goa', 'GA', 'Panaji', 'West', 1, GETUTCDATE(), GETUTCDATE()),
    ('Gujarat', 'GJ', 'Gandhinagar', 'West', 1, GETUTCDATE(), GETUTCDATE()),
    ('Haryana', 'HR', 'Chandigarh', 'North', 1, GETUTCDATE(), GETUTCDATE()),
    ('Himachal Pradesh', 'HP', 'Shimla', 'North', 1, GETUTCDATE(), GETUTCDATE()),
    ('Jharkhand', 'JH', 'Ranchi', 'East', 1, GETUTCDATE(), GETUTCDATE()),
    ('Karnataka', 'KA', 'Bengaluru', 'South', 1, GETUTCDATE(), GETUTCDATE()),
    ('Kerala', 'KL', 'Thiruvananthapuram', 'South', 1, GETUTCDATE(), GETUTCDATE()),
    ('Madhya Pradesh', 'MP', 'Bhopal', 'Central', 1, GETUTCDATE(), GETUTCDATE()),
    ('Maharashtra', 'MH', 'Mumbai', 'West', 1, GETUTCDATE(), GETUTCDATE()),
    ('Manipur', 'MN', 'Imphal', 'Northeast', 1, GETUTCDATE(), GETUTCDATE()),
    ('Meghalaya', 'ML', 'Shillong', 'Northeast', 1, GETUTCDATE(), GETUTCDATE()),
    ('Mizoram', 'MZ', 'Aizawl', 'Northeast', 1, GETUTCDATE(), GETUTCDATE()),
    ('Nagaland', 'NL', 'Kohima', 'Northeast', 1, GETUTCDATE(), GETUTCDATE()),
    ('Odisha', 'OR', 'Bhubaneswar', 'East', 1, GETUTCDATE(), GETUTCDATE()),
    ('Punjab', 'PB', 'Chandigarh', 'North', 1, GETUTCDATE(), GETUTCDATE()),
    ('Rajasthan', 'RJ', 'Jaipur', 'West', 1, GETUTCDATE(), GETUTCDATE()),
    ('Sikkim', 'SK', 'Gangtok', 'Northeast', 1, GETUTCDATE(), GETUTCDATE()),
    ('Tamil Nadu', 'TN', 'Chennai', 'South', 1, GETUTCDATE(), GETUTCDATE()),
    ('Telangana', 'TG', 'Hyderabad', 'South', 1, GETUTCDATE(), GETUTCDATE()),
    ('Tripura', 'TR', 'Agartala', 'Northeast', 1, GETUTCDATE(), GETUTCDATE()),
    ('Uttar Pradesh', 'UP', 'Lucknow', 'North', 1, GETUTCDATE(), GETUTCDATE()),
    ('Uttarakhand', 'UK', 'Dehradun', 'North', 1, GETUTCDATE(), GETUTCDATE()),
    ('West Bengal', 'WB', 'Kolkata', 'East', 1, GETUTCDATE(), GETUTCDATE()),
    
    -- Union Territories
    ('Andaman and Nicobar Islands', 'AN', 'Port Blair', 'Islands', 1, GETUTCDATE(), GETUTCDATE()),
    ('Chandigarh', 'CH', 'Chandigarh', 'North', 1, GETUTCDATE(), GETUTCDATE()),
    ('Dadra and Nagar Haveli and Daman and Diu', 'DH', 'Daman', 'West', 1, GETUTCDATE(), GETUTCDATE()),
    ('Delhi', 'DL', 'New Delhi', 'North', 1, GETUTCDATE(), GETUTCDATE()),
    ('Jammu and Kashmir', 'JK', 'Srinagar (Summer), Jammu (Winter)', 'North', 1, GETUTCDATE(), GETUTCDATE()),
    ('Ladakh', 'LA', 'Leh', 'North', 1, GETUTCDATE(), GETUTCDATE()),
    ('Lakshadweep', 'LD', 'Kavaratti', 'Islands', 1, GETUTCDATE(), GETUTCDATE()),
    ('Puducherry', 'PY', 'Puducherry', 'South', 1, GETUTCDATE(), GETUTCDATE());
    
    PRINT 'States and Union Territories inserted successfully (36 records)';
END
ELSE
BEGIN
    PRINT 'States already exist, skipping insertion';
END
GO

-- Insert sample divisions
IF NOT EXISTS (SELECT 1 FROM divisions)
BEGIN
    INSERT INTO divisions (name, description, is_active, created_at, updated_at) VALUES
    ('Real Estate', 'Real Estate and Property Development', 1, GETUTCDATE(), GETUTCDATE()),
    ('Construction', 'Construction and Infrastructure', 1, GETUTCDATE(), GETUTCDATE()),
    ('Finance', 'Financial Services and Banking', 1, GETUTCDATE(), GETUTCDATE()),
    ('Technology', 'Information Technology and Software', 1, GETUTCDATE(), GETUTCDATE()),
    ('Healthcare', 'Healthcare and Medical Services', 1, GETUTCDATE(), GETUTCDATE()),
    ('Education', 'Educational Institutions and Services', 1, GETUTCDATE(), GETUTCDATE()),
    ('Retail', 'Retail and Consumer Goods', 1, GETUTCDATE(), GETUTCDATE()),
    ('Manufacturing', 'Manufacturing and Industrial', 1, GETUTCDATE(), GETUTCDATE()),
    ('Agriculture', 'Agriculture and Farming', 1, GETUTCDATE(), GETUTCDATE()),
    ('Government', 'Government and Public Sector', 1, GETUTCDATE(), GETUTCDATE());
    
    PRINT 'Sample divisions inserted successfully (10 records)';
END
ELSE
BEGIN
    PRINT 'Divisions already exist, skipping insertion';
END
GO

-- Insert sample categories
IF NOT EXISTS (SELECT 1 FROM categories)
BEGIN
    -- Categories for Real Estate (division_id = 1)
    INSERT INTO categories (name, description, division_id, is_active, created_at, updated_at) VALUES
    ('Residential', 'Residential Property Development', 1, 1, GETUTCDATE(), GETUTCDATE()),
    ('Commercial', 'Commercial Property Development', 1, 1, GETUTCDATE(), GETUTCDATE()),
    ('Industrial', 'Industrial Property Development', 1, 1, GETUTCDATE(), GETUTCDATE()),
    ('Retail Spaces', 'Retail and Shopping Centers', 1, 1, GETUTCDATE(), GETUTCDATE()),
    ('Hospitality', 'Hotels and Hospitality Properties', 1, 1, GETUTCDATE(), GETUTCDATE());
    
    -- Categories for Construction (division_id = 2)
    INSERT INTO categories (name, description, division_id, is_active, created_at, updated_at) VALUES
    ('Building Construction', 'Residential and Commercial Building Construction', 2, 1, GETUTCDATE(), GETUTCDATE()),
    ('Infrastructure', 'Roads, Bridges, and Infrastructure Development', 2, 1, GETUTCDATE(), GETUTCDATE()),
    ('Interior Design', 'Interior Design and Decoration Services', 2, 1, GETUTCDATE(), GETUTCDATE()),
    ('Architecture', 'Architectural Design and Planning', 2, 1, GETUTCDATE(), GETUTCDATE()),
    ('Project Management', 'Construction Project Management', 2, 1, GETUTCDATE(), GETUTCDATE());
    
    -- Categories for Finance (division_id = 3)
    INSERT INTO categories (name, description, division_id, is_active, created_at, updated_at) VALUES
    ('Banking', 'Banking and Financial Institutions', 3, 1, GETUTCDATE(), GETUTCDATE()),
    ('Insurance', 'Insurance Companies and Services', 3, 1, GETUTCDATE(), GETUTCDATE()),
    ('Investment', 'Investment and Wealth Management', 3, 1, GETUTCDATE(), GETUTCDATE()),
    ('Loans & Credit', 'Loan and Credit Services', 3, 1, GETUTCDATE(), GETUTCDATE()),
    ('Financial Advisory', 'Financial Planning and Advisory Services', 3, 1, GETUTCDATE(), GETUTCDATE());
    
    PRINT 'Sample categories inserted successfully (15 records)';
END
ELSE
BEGIN
    PRINT 'Categories already exist, skipping insertion';
END
GO

-- Insert sample subcategories
IF NOT EXISTS (SELECT 1 FROM subcategories)
BEGIN
    -- Subcategories for Residential (category_id = 1)
    INSERT INTO subcategories (name, description, category_id, is_active, created_at, updated_at) VALUES
    ('Apartments', 'Apartment and Flat Development', 1, 1, GETUTCDATE(), GETUTCDATE()),
    ('Villas', 'Villa and Independent House Development', 1, 1, GETUTCDATE(), GETUTCDATE()),
    ('Plots', 'Residential Plot Development', 1, 1, GETUTCDATE(), GETUTCDATE()),
    ('Gated Communities', 'Gated Community Development', 1, 1, GETUTCDATE(), GETUTCDATE()),
    ('Affordable Housing', 'Affordable Housing Projects', 1, 1, GETUTCDATE(), GETUTCDATE());
    
    -- Subcategories for Commercial (category_id = 2)
    INSERT INTO subcategories (name, description, category_id, is_active, created_at, updated_at) VALUES
    ('Office Spaces', 'Office Building Development', 2, 1, GETUTCDATE(), GETUTCDATE()),
    ('Shopping Malls', 'Shopping Mall Development', 2, 1, GETUTCDATE(), GETUTCDATE()),
    ('Warehouses', 'Warehouse and Storage Facilities', 2, 1, GETUTCDATE(), GETUTCDATE()),
    ('Co-working Spaces', 'Co-working and Shared Office Spaces', 2, 1, GETUTCDATE(), GETUTCDATE()),
    ('Business Parks', 'Business Park Development', 2, 1, GETUTCDATE(), GETUTCDATE());
    
    PRINT 'Sample subcategories inserted successfully (10 records)';
END
ELSE
BEGIN
    PRINT 'Subcategories already exist, skipping insertion';
END
GO

-- Create an admin user table if it doesn't exist
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='admins' AND xtype='U')
BEGIN
    CREATE TABLE admins (
        id int IDENTITY(1,1) PRIMARY KEY,
        username nvarchar(50) NOT NULL,
        email nvarchar(255) NOT NULL,
        password_hash nvarchar(max) NOT NULL,
        first_name nvarchar(100) NOT NULL,
        last_name nvarchar(100) NOT NULL,
        is_active bit NOT NULL DEFAULT 1,
        created_at datetime2 NOT NULL DEFAULT GETUTCDATE(),
        updated_at datetime2 NOT NULL DEFAULT GETUTCDATE(),
        last_login_at datetime2 NULL,
        
        CONSTRAINT UQ_admins_username UNIQUE (username),
        CONSTRAINT UQ_admins_email UNIQUE (email)
    );
    
    CREATE INDEX IX_admins_username ON admins(username);
    CREATE INDEX IX_admins_email ON admins(email);
    
    PRINT 'Admins table created successfully';
END
GO

-- Insert a default admin user (password: Admin@123)
IF NOT EXISTS (SELECT 1 FROM admins WHERE username = 'admin')
BEGIN
    INSERT INTO admins (username, email, password_hash, first_name, last_name, is_active, created_at, updated_at) VALUES
    ('admin', '<EMAIL>', '$2a$11$rQZJKjKjKjKjKjKjKjKjKOeH8H8H8H8H8H8H8H8H8H8H8H8H8H8H8', 'System', 'Administrator', 1, GETUTCDATE(), GETUTCDATE());
    
    PRINT 'Default admin user created (username: admin, password: Admin@123)';
END
ELSE
BEGIN
    PRINT 'Admin user already exists, skipping creation';
END
GO

PRINT '';
PRINT '=== DATA SEEDING COMPLETED SUCCESSFULLY ===';
PRINT '';
PRINT 'Summary of inserted data:';
PRINT '- 36 Indian states and union territories';
PRINT '- 10 business divisions';
PRINT '- 15 categories across different divisions';
PRINT '- 10 subcategories for detailed classification';
PRINT '- 1 default admin user';
PRINT '';
PRINT 'Database is now ready for use!';
PRINT 'You can start the API server and begin creating persons.';
GO
