import React, { useState, useEffect } from 'react';
import apiService from '../../services/apiService';
import formConfigService from '../../services/formConfigService';
import './FormSelectionView.css';

const FormSelectionView = ({ onFormSelect, onCancel }) => {
  const [divisions, setDivisions] = useState([]);
  const [categories, setCategories] = useState([]);
  const [subCategories, setSubCategories] = useState([]);
  const [availableForms, setAvailableForms] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedDivision, setSelectedDivision] = useState(null);
  const [selectedCategory, setSelectedCategory] = useState(null);

  useEffect(() => {
    loadDivisions();
    loadAvailableForms();

    // Debug: Check localStorage for saved forms
    console.log('=== DEBUGGING FORM STORAGE ===');
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.includes('form_config')) {
        console.log('Found form in storage:', key, JSON.parse(localStorage.getItem(key)));
      }
    }
    console.log('=== END DEBUG ===');
  }, []);

  const loadDivisions = async () => {
    try {
      const response = await apiService.getDivisions();
      setDivisions(response.data || []);
    } catch (error) {
      console.error('Error loading divisions:', error);
    }
  };

  const loadCategories = async (divisionId) => {
    try {
      const response = await apiService.getCategoriesByDivision(divisionId);
      setCategories(response.data || []);
    } catch (error) {
      console.error('Error loading categories:', error);
      setCategories([]);
    }
  };

  const loadSubCategories = async (categoryId) => {
    try {
      const response = await apiService.getSubCategoriesByCategory(categoryId);
      setSubCategories(response.data || []);
    } catch (error) {
      console.error('Error loading subcategories:', error);
      setSubCategories([]);
    }
  };

  const loadAvailableForms = async () => {
    try {
      const forms = formConfigService.getAllFormConfigs();

      // Add default form
      const defaultForm = {
        ...formConfigService.getDefaultFormConfig(),
        divisionName: 'General',
        categoryName: 'Default',
        isDefault: true
      };

      // Process saved forms and enrich with division/category information
      const processedForms = await Promise.all(forms.map(async (form) => {
        let divisionName = 'General';
        let categoryName = 'Custom';

        // Try to get division and category names if IDs are available
        if (form.hierarchy?.divisionId) {
          try {
            const division = divisions.find(d => d.id === form.hierarchy.divisionId);
            if (division) {
              divisionName = division.name;
            }
          } catch (error) {
            console.error('Error getting division name:', error);
          }
        }

        if (form.hierarchy?.categoryId) {
          try {
            const response = await apiService.getCategoriesByDivision(form.hierarchy.divisionId);
            const category = response.data?.find(c => c.id === form.hierarchy.categoryId);
            if (category) {
              categoryName = category.name;
            }
          } catch (error) {
            console.error('Error getting category name:', error);
          }
        }

        return {
          ...form,
          divisionName,
          categoryName,
          divisionId: form.hierarchy?.divisionId,
          categoryId: form.hierarchy?.categoryId,
          subCategoryId: form.hierarchy?.subCategoryId,
          isCustom: true
        };
      }));

      setAvailableForms([defaultForm, ...processedForms]);
    } catch (error) {
      console.error('Error loading forms:', error);
      // Fallback to just default form
      const defaultForm = {
        ...formConfigService.getDefaultFormConfig(),
        divisionName: 'General',
        categoryName: 'Default',
        isDefault: true
      };
      setAvailableForms([defaultForm]);
    } finally {
      setLoading(false);
    }
  };

  const handleDivisionClick = async (division) => {
    setSelectedDivision(division);
    setSelectedCategory(null);
    setSubCategories([]);
    await loadCategories(division.id);
  };

  const handleCategoryClick = async (category) => {
    setSelectedCategory(category);
    await loadSubCategories(category.id);
  };

  const handleFormSelect = (form) => {
    onFormSelect(form);
  };

  const getFormsForDivision = (divisionId, divisionName) => {
    if (divisionName === 'General') {
      // For General section, show default form and custom forms without specific division
      return availableForms.filter(form =>
        form.isDefault ||
        (!form.divisionId && form.isCustom) ||
        form.divisionName === 'General'
      );
    }
    // For specific divisions, show forms assigned to that division
    return availableForms.filter(form =>
      form.divisionId === divisionId ||
      form.divisionName === divisionName
    );
  };

  const getFormsForCategory = (categoryId) => {
    return availableForms.filter(form => form.categoryId === categoryId);
  };

  const getFormsForSubCategory = (subCategoryId) => {
    return availableForms.filter(form => form.subCategoryId === subCategoryId);
  };

  if (loading) {
    return (
      <div className="form-selection-loading">
        <div className="loading-spinner"></div>
        <p>Loading available forms...</p>
      </div>
    );
  }

  return (
    <div className="form-selection-view">
      <div className="form-selection-header">
        <h2>Select a Form to Create Person</h2>
        <p>Choose from available forms organized by division and category</p>
        <button onClick={onCancel} className="btn btn-outline">
          ← Back to Person List
        </button>
      </div>

      <div className="form-selection-content">
        {/* Default/General Forms */}
        <div className="division-section">
          <div className="division-header">
            <h3>📋 General Forms</h3>
            <span className="form-count">
              {getFormsForDivision(null, 'General').length} form(s)
            </span>
          </div>
          <div className="forms-grid">
            {getFormsForDivision(null, 'General').map(form => (
              <div
                key={form.id}
                className={`form-card ${form.isDefault ? 'default-form' : 'custom-form'}`}
                onClick={() => handleFormSelect(form)}
              >
                <div className="form-card-header">
                  <h4>{form.name}</h4>
                  <span className="form-type">
                    {form.isDefault ? 'Default' : 'Custom'}
                  </span>
                </div>
                <div className="form-card-body">
                  <p>{form.description}</p>
                  <div className="form-stats">
                    <span>📝 {form.fields?.length || 0} fields</span>
                    <span>📂 {Object.keys(form.sections || {}).length} sections</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Division-wise Forms */}
        {/* Show divisions from API */}
        {divisions.map(division => {
          const divisionForms = getFormsForDivision(division.id, division.name);
          if (divisionForms.length === 0) return null;

          return (
            <div key={division.id} className="division-section">
              <div
                className="division-header clickable"
                onClick={() => handleDivisionClick(division)}
              >
                <h3>🏢 {division.name}</h3>
                <span className="form-count">{divisionForms.length} form(s)</span>
                <span className="expand-icon">
                  {selectedDivision?.id === division.id ? '▼' : '▶'}
                </span>
              </div>

              {selectedDivision?.id === division.id && (
                <div className="division-content">
                  {/* Division-level forms */}
                  {divisionForms.filter(f => !f.categoryId).length > 0 && (
                    <div className="forms-grid">
                      {divisionForms.filter(f => !f.categoryId).map(form => (
                        <div
                          key={form.id}
                          className="form-card division-form"
                          onClick={() => handleFormSelect(form)}
                        >
                          <div className="form-card-header">
                            <h4>{form.name}</h4>
                            <span className="form-type">Division</span>
                          </div>
                          <div className="form-card-body">
                            <p>{form.description}</p>
                            <div className="form-stats">
                              <span>📝 {form.fields?.length || 0} fields</span>
                              <span>📂 {Object.keys(form.sections || {}).length} sections</span>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}

                  {/* Categories */}
                  {categories.map(category => {
                    const categoryForms = getFormsForCategory(category.id);
                    if (categoryForms.length === 0) return null;

                    return (
                      <div key={category.id} className="category-section">
                        <div 
                          className="category-header clickable"
                          onClick={() => handleCategoryClick(category)}
                        >
                          <h4>🏪 {category.name}</h4>
                          <span className="form-count">{categoryForms.length} form(s)</span>
                          <span className="expand-icon">
                            {selectedCategory?.id === category.id ? '▼' : '▶'}
                          </span>
                        </div>

                        {selectedCategory?.id === category.id && (
                          <div className="category-content">
                            <div className="forms-grid">
                              {categoryForms.map(form => (
                                <div
                                  key={form.id}
                                  className="form-card category-form"
                                  onClick={() => handleFormSelect(form)}
                                >
                                  <div className="form-card-header">
                                    <h4>{form.name}</h4>
                                    <span className="form-type">
                                      {form.subCategoryId ? 'SubCategory' : 'Category'}
                                    </span>
                                  </div>
                                  <div className="form-card-body">
                                    <p>{form.description}</p>
                                    <div className="form-stats">
                                      <span>📝 {form.fields?.length || 0} fields</span>
                                      <span>📂 {Object.keys(form.sections || {}).length} sections</span>
                                    </div>
                                  </div>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    );
                  })}
                </div>
              )}
            </div>
          );
        })}

        {/* Show forms with custom division names that don't match API divisions */}
        {(() => {
          const customDivisions = [...new Set(
            availableForms
              .filter(form =>
                form.isCustom &&
                form.divisionName &&
                form.divisionName !== 'General' &&
                !divisions.some(d => d.name === form.divisionName)
              )
              .map(form => form.divisionName)
          )];

          return customDivisions.map(divisionName => {
            const customForms = availableForms.filter(form =>
              form.divisionName === divisionName
            );

            return (
              <div key={`custom-${divisionName}`} className="division-section">
                <div className="division-header">
                  <h3>🏢 {divisionName} (Custom)</h3>
                  <span className="form-count">{customForms.length} form(s)</span>
                </div>
                <div className="division-content">
                  <div className="forms-grid">
                    {customForms.map(form => (
                      <div
                        key={form.id}
                        className="form-card custom-form"
                        onClick={() => handleFormSelect(form)}
                      >
                        <div className="form-card-header">
                          <h4>{form.name}</h4>
                          <span className="form-type">Custom</span>
                        </div>
                        <div className="form-card-body">
                          <p>{form.description}</p>
                          <div className="form-stats">
                            <span>📝 {form.fields?.length || 0} fields</span>
                            <span>📂 {Object.keys(form.sections || {}).length} sections</span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            );
          });
        })()}

        {/* No forms message */}
        {availableForms.length === 0 && (
          <div className="no-forms-message">
            <h3>No Forms Available</h3>
            <p>No custom forms have been created yet. You can:</p>
            <ul>
              <li>Use the default general form above</li>
              <li>Create custom forms using the Form Builder</li>
            </ul>
          </div>
        )}
      </div>
    </div>
  );
};

export default FormSelectionView;
