{"ast": null, "code": "const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';\nclass ApiService {\n  constructor() {\n    this.baseURL = API_BASE_URL;\n  }\n  async request(endpoint, options = {}) {\n    const url = `${this.baseURL}${endpoint}`;\n    const config = {\n      headers: {\n        'Content-Type': 'application/json',\n        ...options.headers\n      },\n      ...options\n    };\n    try {\n      const response = await fetch(url, config);\n      if (!response.ok) {\n        const errorData = await response.json().catch(() => ({}));\n        throw new ApiError(response.status, errorData.message || 'An error occurred', errorData);\n      }\n\n      // Handle blob responses (for file downloads)\n      if (options.responseType === 'blob') {\n        return {\n          data: await response.blob()\n        };\n      }\n      const data = await response.json();\n      return {\n        data\n      }; // Wrap in data property for consistency\n    } catch (error) {\n      if (error instanceof ApiError) {\n        throw error;\n      }\n      throw new ApiError(0, 'Network error', {\n        originalError: error.message\n      });\n    }\n  }\n\n  // Generic HTTP methods\n  async get(endpoint, options = {}) {\n    return this.request(endpoint, {\n      method: 'GET',\n      ...options\n    });\n  }\n  async post(endpoint, data = null, options = {}) {\n    const config = {\n      method: 'POST',\n      ...options\n    };\n    if (data) {\n      config.body = JSON.stringify(data);\n    }\n    return this.request(endpoint, config);\n  }\n  async put(endpoint, data = null, options = {}) {\n    const config = {\n      method: 'PUT',\n      ...options\n    };\n    if (data) {\n      config.body = JSON.stringify(data);\n    }\n    return this.request(endpoint, config);\n  }\n  async delete(endpoint, options = {}) {\n    return this.request(endpoint, {\n      method: 'DELETE',\n      ...options\n    });\n  }\n\n  // Division endpoints\n  async getDivisions() {\n    return this.get('/divisions');\n  }\n  async getDivision(id) {\n    return this.get(`/divisions/${id}`);\n  }\n\n  // Category endpoints\n  async getCategories() {\n    return this.get('/categories');\n  }\n  async getCategory(id) {\n    return this.get(`/categories/${id}`);\n  }\n  async getCategoriesByDivision(divisionId) {\n    return this.get(`/categories/division/${divisionId}`);\n  }\n\n  // SubCategory endpoints\n  async getSubCategories() {\n    return this.get('/subcategories');\n  }\n  async getSubCategory(id) {\n    return this.get(`/subcategories/${id}`);\n  }\n  async getSubCategoriesByCategory(categoryId) {\n    return this.get(`/subcategories/category/${categoryId}`);\n  }\n\n  // States endpoints\n  async getStates() {\n    return this.get('/states');\n  }\n\n  // Person endpoints\n  async getPersons(params = {}) {\n    const queryString = new URLSearchParams(params).toString();\n    return this.get(`/persons${queryString ? `?${queryString}` : ''}`);\n  }\n  async getPerson(id) {\n    return this.get(`/persons/${id}`);\n  }\n  async createPerson(personData) {\n    return this.post('/persons', personData);\n  }\n  async updatePerson(id, personData) {\n    return this.put(`/persons/${id}`, personData);\n  }\n  async deletePerson(id) {\n    return this.delete(`/persons/${id}`);\n  }\n  async searchPersons(searchRequest) {\n    return this.post('/persons/search', searchRequest);\n  }\n  async getPersonsByDivision(divisionId) {\n    return this.get(`/persons/division/${divisionId}`);\n  }\n  async getPersonsByCategory(categoryId) {\n    return this.get(`/persons/category/${categoryId}`);\n  }\n  async getPersonsBySubCategory(subCategoryId) {\n    return this.get(`/persons/subcategory/${subCategoryId}`);\n  }\n  async getPersonStatistics() {\n    return this.get('/persons/statistics');\n  }\n  async getPersonEnums() {\n    return this.get('/persons/enums');\n  }\n\n  // Bulk operations\n  async bulkCreatePersons(personsData) {\n    return this.post('/persons/bulk', personsData);\n  }\n  async bulkSoftDeletePersons(personIds) {\n    return this.post('/persons/bulk/soft-delete', personIds);\n  }\n  async bulkRestorePersons(personIds) {\n    return this.post('/persons/bulk/restore', personIds);\n  }\n}\nclass ApiError extends Error {\n  constructor(status, message, data = {}) {\n    super(message);\n    this.name = 'ApiError';\n    this.status = status;\n    this.data = data;\n  }\n  isValidationError() {\n    return this.status === 400 && this.data.errors;\n  }\n  isNotFoundError() {\n    return this.status === 404;\n  }\n  isServerError() {\n    return this.status >= 500;\n  }\n  getValidationErrors() {\n    return this.data.errors || {};\n  }\n}\nexport { ApiService, ApiError };\nexport default new ApiService();", "map": {"version": 3, "names": ["API_BASE_URL", "process", "env", "REACT_APP_API_URL", "ApiService", "constructor", "baseURL", "request", "endpoint", "options", "url", "config", "headers", "response", "fetch", "ok", "errorData", "json", "catch", "ApiError", "status", "message", "responseType", "data", "blob", "error", "originalError", "get", "method", "post", "body", "JSON", "stringify", "put", "delete", "getDivisions", "getDivision", "id", "getCategories", "getCategory", "getCategoriesByDivision", "divisionId", "getSubCategories", "getSubCategory", "getSubCategoriesByCategory", "categoryId", "getStates", "<PERSON><PERSON><PERSON><PERSON>", "params", "queryString", "URLSearchParams", "toString", "<PERSON><PERSON><PERSON>", "create<PERSON>erson", "personData", "update<PERSON><PERSON>", "deletePerson", "search<PERSON><PERSON>s", "searchRequest", "getPersonsByDivision", "getPersonsByCategory", "getPersonsBySubCategory", "subCategoryId", "getPersonStatistics", "getPersonEnums", "bulkCreatePersons", "personsData", "bulkSoftDeletePersons", "personIds", "bulkRestorePersons", "Error", "name", "isValidationError", "errors", "isNotFoundError", "isServerError", "getValidationErrors"], "sources": ["C:/Users/<USER>/Downloads/Advanced/Advanced CRM filed all use/frontend/src/services/apiService.js"], "sourcesContent": ["const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';\n\nclass ApiService {\n  constructor() {\n    this.baseURL = API_BASE_URL;\n  }\n\n  async request(endpoint, options = {}) {\n    const url = `${this.baseURL}${endpoint}`;\n    const config = {\n      headers: {\n        'Content-Type': 'application/json',\n        ...options.headers,\n      },\n      ...options,\n    };\n\n    try {\n      const response = await fetch(url, config);\n\n      if (!response.ok) {\n        const errorData = await response.json().catch(() => ({}));\n        throw new ApiError(response.status, errorData.message || 'An error occurred', errorData);\n      }\n\n      // Handle blob responses (for file downloads)\n      if (options.responseType === 'blob') {\n        return { data: await response.blob() };\n      }\n\n      const data = await response.json();\n      return { data }; // Wrap in data property for consistency\n    } catch (error) {\n      if (error instanceof ApiError) {\n        throw error;\n      }\n      throw new ApiError(0, 'Network error', { originalError: error.message });\n    }\n  }\n\n  // Generic HTTP methods\n  async get(endpoint, options = {}) {\n    return this.request(endpoint, { method: 'GET', ...options });\n  }\n\n  async post(endpoint, data = null, options = {}) {\n    const config = { method: 'POST', ...options };\n    if (data) {\n      config.body = JSON.stringify(data);\n    }\n    return this.request(endpoint, config);\n  }\n\n  async put(endpoint, data = null, options = {}) {\n    const config = { method: 'PUT', ...options };\n    if (data) {\n      config.body = JSON.stringify(data);\n    }\n    return this.request(endpoint, config);\n  }\n\n  async delete(endpoint, options = {}) {\n    return this.request(endpoint, { method: 'DELETE', ...options });\n  }\n\n  // Division endpoints\n  async getDivisions() {\n    return this.get('/divisions');\n  }\n\n  async getDivision(id) {\n    return this.get(`/divisions/${id}`);\n  }\n\n  // Category endpoints\n  async getCategories() {\n    return this.get('/categories');\n  }\n\n  async getCategory(id) {\n    return this.get(`/categories/${id}`);\n  }\n\n  async getCategoriesByDivision(divisionId) {\n    return this.get(`/categories/division/${divisionId}`);\n  }\n\n  // SubCategory endpoints\n  async getSubCategories() {\n    return this.get('/subcategories');\n  }\n\n  async getSubCategory(id) {\n    return this.get(`/subcategories/${id}`);\n  }\n\n  async getSubCategoriesByCategory(categoryId) {\n    return this.get(`/subcategories/category/${categoryId}`);\n  }\n\n  // States endpoints\n  async getStates() {\n    return this.get('/states');\n  }\n\n  // Person endpoints\n  async getPersons(params = {}) {\n    const queryString = new URLSearchParams(params).toString();\n    return this.get(`/persons${queryString ? `?${queryString}` : ''}`);\n  }\n\n  async getPerson(id) {\n    return this.get(`/persons/${id}`);\n  }\n\n  async createPerson(personData) {\n    return this.post('/persons', personData);\n  }\n\n  async updatePerson(id, personData) {\n    return this.put(`/persons/${id}`, personData);\n  }\n\n  async deletePerson(id) {\n    return this.delete(`/persons/${id}`);\n  }\n\n  async searchPersons(searchRequest) {\n    return this.post('/persons/search', searchRequest);\n  }\n\n  async getPersonsByDivision(divisionId) {\n    return this.get(`/persons/division/${divisionId}`);\n  }\n\n  async getPersonsByCategory(categoryId) {\n    return this.get(`/persons/category/${categoryId}`);\n  }\n\n  async getPersonsBySubCategory(subCategoryId) {\n    return this.get(`/persons/subcategory/${subCategoryId}`);\n  }\n\n  async getPersonStatistics() {\n    return this.get('/persons/statistics');\n  }\n\n  async getPersonEnums() {\n    return this.get('/persons/enums');\n  }\n\n  // Bulk operations\n  async bulkCreatePersons(personsData) {\n    return this.post('/persons/bulk', personsData);\n  }\n\n  async bulkSoftDeletePersons(personIds) {\n    return this.post('/persons/bulk/soft-delete', personIds);\n  }\n\n  async bulkRestorePersons(personIds) {\n    return this.post('/persons/bulk/restore', personIds);\n  }\n}\n\nclass ApiError extends Error {\n  constructor(status, message, data = {}) {\n    super(message);\n    this.name = 'ApiError';\n    this.status = status;\n    this.data = data;\n  }\n\n  isValidationError() {\n    return this.status === 400 && this.data.errors;\n  }\n\n  isNotFoundError() {\n    return this.status === 404;\n  }\n\n  isServerError() {\n    return this.status >= 500;\n  }\n\n  getValidationErrors() {\n    return this.data.errors || {};\n  }\n}\n\nexport { ApiService, ApiError };\nexport default new ApiService();\n"], "mappings": "AAAA,MAAMA,YAAY,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,2BAA2B;AAEjF,MAAMC,UAAU,CAAC;EACfC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,OAAO,GAAGN,YAAY;EAC7B;EAEA,MAAMO,OAAOA,CAACC,QAAQ,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;IACpC,MAAMC,GAAG,GAAG,GAAG,IAAI,CAACJ,OAAO,GAAGE,QAAQ,EAAE;IACxC,MAAMG,MAAM,GAAG;MACbC,OAAO,EAAE;QACP,cAAc,EAAE,kBAAkB;QAClC,GAAGH,OAAO,CAACG;MACb,CAAC;MACD,GAAGH;IACL,CAAC;IAED,IAAI;MACF,MAAMI,QAAQ,GAAG,MAAMC,KAAK,CAACJ,GAAG,EAAEC,MAAM,CAAC;MAEzC,IAAI,CAACE,QAAQ,CAACE,EAAE,EAAE;QAChB,MAAMC,SAAS,GAAG,MAAMH,QAAQ,CAACI,IAAI,CAAC,CAAC,CAACC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QACzD,MAAM,IAAIC,QAAQ,CAACN,QAAQ,CAACO,MAAM,EAAEJ,SAAS,CAACK,OAAO,IAAI,mBAAmB,EAAEL,SAAS,CAAC;MAC1F;;MAEA;MACA,IAAIP,OAAO,CAACa,YAAY,KAAK,MAAM,EAAE;QACnC,OAAO;UAAEC,IAAI,EAAE,MAAMV,QAAQ,CAACW,IAAI,CAAC;QAAE,CAAC;MACxC;MAEA,MAAMD,IAAI,GAAG,MAAMV,QAAQ,CAACI,IAAI,CAAC,CAAC;MAClC,OAAO;QAAEM;MAAK,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,OAAOE,KAAK,EAAE;MACd,IAAIA,KAAK,YAAYN,QAAQ,EAAE;QAC7B,MAAMM,KAAK;MACb;MACA,MAAM,IAAIN,QAAQ,CAAC,CAAC,EAAE,eAAe,EAAE;QAAEO,aAAa,EAAED,KAAK,CAACJ;MAAQ,CAAC,CAAC;IAC1E;EACF;;EAEA;EACA,MAAMM,GAAGA,CAACnB,QAAQ,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;IAChC,OAAO,IAAI,CAACF,OAAO,CAACC,QAAQ,EAAE;MAAEoB,MAAM,EAAE,KAAK;MAAE,GAAGnB;IAAQ,CAAC,CAAC;EAC9D;EAEA,MAAMoB,IAAIA,CAACrB,QAAQ,EAAEe,IAAI,GAAG,IAAI,EAAEd,OAAO,GAAG,CAAC,CAAC,EAAE;IAC9C,MAAME,MAAM,GAAG;MAAEiB,MAAM,EAAE,MAAM;MAAE,GAAGnB;IAAQ,CAAC;IAC7C,IAAIc,IAAI,EAAE;MACRZ,MAAM,CAACmB,IAAI,GAAGC,IAAI,CAACC,SAAS,CAACT,IAAI,CAAC;IACpC;IACA,OAAO,IAAI,CAAChB,OAAO,CAACC,QAAQ,EAAEG,MAAM,CAAC;EACvC;EAEA,MAAMsB,GAAGA,CAACzB,QAAQ,EAAEe,IAAI,GAAG,IAAI,EAAEd,OAAO,GAAG,CAAC,CAAC,EAAE;IAC7C,MAAME,MAAM,GAAG;MAAEiB,MAAM,EAAE,KAAK;MAAE,GAAGnB;IAAQ,CAAC;IAC5C,IAAIc,IAAI,EAAE;MACRZ,MAAM,CAACmB,IAAI,GAAGC,IAAI,CAACC,SAAS,CAACT,IAAI,CAAC;IACpC;IACA,OAAO,IAAI,CAAChB,OAAO,CAACC,QAAQ,EAAEG,MAAM,CAAC;EACvC;EAEA,MAAMuB,MAAMA,CAAC1B,QAAQ,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;IACnC,OAAO,IAAI,CAACF,OAAO,CAACC,QAAQ,EAAE;MAAEoB,MAAM,EAAE,QAAQ;MAAE,GAAGnB;IAAQ,CAAC,CAAC;EACjE;;EAEA;EACA,MAAM0B,YAAYA,CAAA,EAAG;IACnB,OAAO,IAAI,CAACR,GAAG,CAAC,YAAY,CAAC;EAC/B;EAEA,MAAMS,WAAWA,CAACC,EAAE,EAAE;IACpB,OAAO,IAAI,CAACV,GAAG,CAAC,cAAcU,EAAE,EAAE,CAAC;EACrC;;EAEA;EACA,MAAMC,aAAaA,CAAA,EAAG;IACpB,OAAO,IAAI,CAACX,GAAG,CAAC,aAAa,CAAC;EAChC;EAEA,MAAMY,WAAWA,CAACF,EAAE,EAAE;IACpB,OAAO,IAAI,CAACV,GAAG,CAAC,eAAeU,EAAE,EAAE,CAAC;EACtC;EAEA,MAAMG,uBAAuBA,CAACC,UAAU,EAAE;IACxC,OAAO,IAAI,CAACd,GAAG,CAAC,wBAAwBc,UAAU,EAAE,CAAC;EACvD;;EAEA;EACA,MAAMC,gBAAgBA,CAAA,EAAG;IACvB,OAAO,IAAI,CAACf,GAAG,CAAC,gBAAgB,CAAC;EACnC;EAEA,MAAMgB,cAAcA,CAACN,EAAE,EAAE;IACvB,OAAO,IAAI,CAACV,GAAG,CAAC,kBAAkBU,EAAE,EAAE,CAAC;EACzC;EAEA,MAAMO,0BAA0BA,CAACC,UAAU,EAAE;IAC3C,OAAO,IAAI,CAAClB,GAAG,CAAC,2BAA2BkB,UAAU,EAAE,CAAC;EAC1D;;EAEA;EACA,MAAMC,SAASA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACnB,GAAG,CAAC,SAAS,CAAC;EAC5B;;EAEA;EACA,MAAMoB,UAAUA,CAACC,MAAM,GAAG,CAAC,CAAC,EAAE;IAC5B,MAAMC,WAAW,GAAG,IAAIC,eAAe,CAACF,MAAM,CAAC,CAACG,QAAQ,CAAC,CAAC;IAC1D,OAAO,IAAI,CAACxB,GAAG,CAAC,WAAWsB,WAAW,GAAG,IAAIA,WAAW,EAAE,GAAG,EAAE,EAAE,CAAC;EACpE;EAEA,MAAMG,SAASA,CAACf,EAAE,EAAE;IAClB,OAAO,IAAI,CAACV,GAAG,CAAC,YAAYU,EAAE,EAAE,CAAC;EACnC;EAEA,MAAMgB,YAAYA,CAACC,UAAU,EAAE;IAC7B,OAAO,IAAI,CAACzB,IAAI,CAAC,UAAU,EAAEyB,UAAU,CAAC;EAC1C;EAEA,MAAMC,YAAYA,CAAClB,EAAE,EAAEiB,UAAU,EAAE;IACjC,OAAO,IAAI,CAACrB,GAAG,CAAC,YAAYI,EAAE,EAAE,EAAEiB,UAAU,CAAC;EAC/C;EAEA,MAAME,YAAYA,CAACnB,EAAE,EAAE;IACrB,OAAO,IAAI,CAACH,MAAM,CAAC,YAAYG,EAAE,EAAE,CAAC;EACtC;EAEA,MAAMoB,aAAaA,CAACC,aAAa,EAAE;IACjC,OAAO,IAAI,CAAC7B,IAAI,CAAC,iBAAiB,EAAE6B,aAAa,CAAC;EACpD;EAEA,MAAMC,oBAAoBA,CAAClB,UAAU,EAAE;IACrC,OAAO,IAAI,CAACd,GAAG,CAAC,qBAAqBc,UAAU,EAAE,CAAC;EACpD;EAEA,MAAMmB,oBAAoBA,CAACf,UAAU,EAAE;IACrC,OAAO,IAAI,CAAClB,GAAG,CAAC,qBAAqBkB,UAAU,EAAE,CAAC;EACpD;EAEA,MAAMgB,uBAAuBA,CAACC,aAAa,EAAE;IAC3C,OAAO,IAAI,CAACnC,GAAG,CAAC,wBAAwBmC,aAAa,EAAE,CAAC;EAC1D;EAEA,MAAMC,mBAAmBA,CAAA,EAAG;IAC1B,OAAO,IAAI,CAACpC,GAAG,CAAC,qBAAqB,CAAC;EACxC;EAEA,MAAMqC,cAAcA,CAAA,EAAG;IACrB,OAAO,IAAI,CAACrC,GAAG,CAAC,gBAAgB,CAAC;EACnC;;EAEA;EACA,MAAMsC,iBAAiBA,CAACC,WAAW,EAAE;IACnC,OAAO,IAAI,CAACrC,IAAI,CAAC,eAAe,EAAEqC,WAAW,CAAC;EAChD;EAEA,MAAMC,qBAAqBA,CAACC,SAAS,EAAE;IACrC,OAAO,IAAI,CAACvC,IAAI,CAAC,2BAA2B,EAAEuC,SAAS,CAAC;EAC1D;EAEA,MAAMC,kBAAkBA,CAACD,SAAS,EAAE;IAClC,OAAO,IAAI,CAACvC,IAAI,CAAC,uBAAuB,EAAEuC,SAAS,CAAC;EACtD;AACF;AAEA,MAAMjD,QAAQ,SAASmD,KAAK,CAAC;EAC3BjE,WAAWA,CAACe,MAAM,EAAEC,OAAO,EAAEE,IAAI,GAAG,CAAC,CAAC,EAAE;IACtC,KAAK,CAACF,OAAO,CAAC;IACd,IAAI,CAACkD,IAAI,GAAG,UAAU;IACtB,IAAI,CAACnD,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACG,IAAI,GAAGA,IAAI;EAClB;EAEAiD,iBAAiBA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACpD,MAAM,KAAK,GAAG,IAAI,IAAI,CAACG,IAAI,CAACkD,MAAM;EAChD;EAEAC,eAAeA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACtD,MAAM,KAAK,GAAG;EAC5B;EAEAuD,aAAaA,CAAA,EAAG;IACd,OAAO,IAAI,CAACvD,MAAM,IAAI,GAAG;EAC3B;EAEAwD,mBAAmBA,CAAA,EAAG;IACpB,OAAO,IAAI,CAACrD,IAAI,CAACkD,MAAM,IAAI,CAAC,CAAC;EAC/B;AACF;AAEA,SAASrE,UAAU,EAAEe,QAAQ;AAC7B,eAAe,IAAIf,UAAU,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}