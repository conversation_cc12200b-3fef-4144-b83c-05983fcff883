{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Advanced\\\\Advanced CRM filed all use\\\\frontend\\\\src\\\\components\\\\import\\\\FileUpload.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef } from 'react';\nimport * as XLSX from 'xlsx';\nimport './FileUpload.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst FileUpload = ({\n  onFileUpload,\n  importSettings,\n  onSettingsChange,\n  error\n}) => {\n  _s();\n  const [dragActive, setDragActive] = useState(false);\n  const [selectedFile, setSelectedFile] = useState(null);\n  const [fileInfo, setFileInfo] = useState(null);\n  const [parsing, setParsing] = useState(false);\n  const [parseError, setParseError] = useState(null);\n  const fileInputRef = useRef(null);\n  const handleDrag = e => {\n    e.preventDefault();\n    e.stopPropagation();\n    if (e.type === 'dragenter' || e.type === 'dragover') {\n      setDragActive(true);\n    } else if (e.type === 'dragleave') {\n      setDragActive(false);\n    }\n  };\n  const handleDrop = e => {\n    e.preventDefault();\n    e.stopPropagation();\n    setDragActive(false);\n    if (e.dataTransfer.files && e.dataTransfer.files[0]) {\n      handleFileSelect(e.dataTransfer.files[0]);\n    }\n  };\n  const handleFileInputChange = e => {\n    if (e.target.files && e.target.files[0]) {\n      handleFileSelect(e.target.files[0]);\n    }\n  };\n  const handleFileSelect = file => {\n    // Validate file type\n    const allowedTypes = ['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',\n    // .xlsx\n    'application/vnd.ms-excel',\n    // .xls\n    'text/csv' // .csv\n    ];\n    const allowedExtensions = ['.xlsx', '.xls', '.csv'];\n    const fileExtension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'));\n    if (!allowedTypes.includes(file.type) && !allowedExtensions.includes(fileExtension)) {\n      setParseError('Please select a valid Excel (.xlsx, .xls) or CSV file');\n      return;\n    }\n\n    // Validate file size (10MB limit)\n    if (file.size > 10 * 1024 * 1024) {\n      setParseError('File size must be less than 10MB');\n      return;\n    }\n    setSelectedFile(file);\n    setFileInfo({\n      name: file.name,\n      size: formatFileSize(file.size),\n      type: file.type || 'Unknown',\n      lastModified: new Date(file.lastModified).toLocaleDateString()\n    });\n    setParseError(null);\n  };\n  const parseFile = async () => {\n    if (!selectedFile) return;\n    setParsing(true);\n    setParseError(null);\n    try {\n      const headers = await extractHeaders(selectedFile);\n      onFileUpload(selectedFile, headers);\n    } catch (err) {\n      console.error('File parsing error:', err);\n      setParseError(err.message || 'Failed to parse file');\n    } finally {\n      setParsing(false);\n    }\n  };\n  const extractHeaders = file => {\n    return new Promise((resolve, reject) => {\n      const reader = new FileReader();\n      reader.onload = e => {\n        try {\n          const data = e.target.result;\n          let headers = [];\n          if (file.name.toLowerCase().endsWith('.csv')) {\n            // Parse CSV\n            const lines = data.split('\\n');\n            if (lines.length > 0) {\n              headers = lines[0].split(',').map(header => header.trim().replace(/\"/g, ''));\n            }\n          } else {\n            // Parse Excel with better error handling\n            console.log('Parsing Excel file:', file.name, 'Size:', file.size, 'Type:', file.type);\n            try {\n              // Check if the data looks like HTML\n              if (typeof data === 'string' && (data.includes('<html>') || data.includes('<table>'))) {\n                throw new Error('HTML file detected. Please upload a proper Excel (.xlsx, .xls) file.');\n              }\n              const workbook = XLSX.read(data, {\n                type: 'binary'\n              });\n              console.log('Workbook loaded successfully');\n              if (!workbook.SheetNames || workbook.SheetNames.length === 0) {\n                throw new Error('No sheets found in the Excel file. The file might be corrupted.');\n              }\n              console.log('Available sheets:', workbook.SheetNames);\n              const firstSheetName = workbook.SheetNames[0];\n              const worksheet = workbook.Sheets[firstSheetName];\n              if (!worksheet) {\n                throw new Error(`Could not read the sheet \"${firstSheetName}\".`);\n              }\n              const jsonData = XLSX.utils.sheet_to_json(worksheet, {\n                header: 1\n              });\n              console.log('Extracted', jsonData.length, 'rows from Excel');\n              if (jsonData.length > 0) {\n                headers = jsonData[0].map(header => String(header || '').trim());\n                console.log('Headers found:', headers);\n              } else {\n                throw new Error('The Excel file appears to be empty.');\n              }\n            } catch (xlsxError) {\n              console.error('Excel parsing error:', xlsxError);\n              if (xlsxError.message && xlsxError.message.toLowerCase().includes('table')) {\n                throw new Error('Invalid file format. The file contains HTML content instead of Excel data. Please upload a proper .xlsx or .xls file.');\n              }\n              if (xlsxError.message && xlsxError.message.includes('ZIP')) {\n                throw new Error('The Excel file is corrupted or not a valid .xlsx file. Please try re-saving the file in Excel.');\n              }\n              throw new Error(`Excel parsing failed: ${xlsxError.message}`);\n            }\n          }\n          if (headers.length === 0) {\n            reject(new Error('No headers found in file'));\n            return;\n          }\n          resolve(headers.filter(header => header !== ''));\n        } catch (err) {\n          reject(new Error('Failed to parse file: ' + err.message));\n        }\n      };\n      reader.onerror = () => {\n        reject(new Error('Failed to read file'));\n      };\n      if (file.name.toLowerCase().endsWith('.csv')) {\n        reader.readAsText(file);\n      } else {\n        reader.readAsBinaryString(file);\n      }\n    });\n  };\n  const formatFileSize = bytes => {\n    if (bytes === 0) return '0 Bytes';\n    const k = 1024;\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  };\n  const downloadTemplate = async () => {\n    try {\n      const response = await fetch('/api/import-export/persons/template?format=Excel&includeSampleData=true');\n      if (!response.ok) {\n        throw new Error('Failed to download template');\n      }\n      const blob = await response.blob();\n      const url = window.URL.createObjectURL(blob);\n      const a = document.createElement('a');\n      a.href = url;\n      a.download = 'person_import_template.xlsx';\n      document.body.appendChild(a);\n      a.click();\n      window.URL.revokeObjectURL(url);\n      document.body.removeChild(a);\n    } catch (err) {\n      console.error('Template download error:', err);\n      setParseError('Failed to download template');\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"file-upload\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"upload-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"Upload File\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 210,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"template-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Don't have a file? Download our template to get started:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 214,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: downloadTemplate,\n          className: \"btn btn-outline\",\n          children: \"\\uD83D\\uDCE5 Download Template\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 215,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 213,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `drop-zone ${dragActive ? 'active' : ''} ${selectedFile ? 'has-file' : ''}`,\n        onDragEnter: handleDrag,\n        onDragLeave: handleDrag,\n        onDragOver: handleDrag,\n        onDrop: handleDrop,\n        onClick: () => {\n          var _fileInputRef$current;\n          return (_fileInputRef$current = fileInputRef.current) === null || _fileInputRef$current === void 0 ? void 0 : _fileInputRef$current.click();\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n          ref: fileInputRef,\n          type: \"file\",\n          accept: \".xlsx,.xls,.csv\",\n          onChange: handleFileInputChange,\n          style: {\n            display: 'none'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 229,\n          columnNumber: 11\n        }, this), selectedFile ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"file-selected\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"file-icon\",\n            children: \"\\uD83D\\uDCC4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 239,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"file-details\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"file-name\",\n              children: fileInfo.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 241,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"file-meta\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: fileInfo.size\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 243,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"\\u2022\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 244,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: fileInfo.lastModified\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 245,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 242,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: e => {\n              e.stopPropagation();\n              setSelectedFile(null);\n              setFileInfo(null);\n            },\n            className: \"remove-file\",\n            children: \"\\u2715\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 248,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 238,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"drop-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"drop-icon\",\n            children: \"\\uD83D\\uDCC1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 261,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"drop-text\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Click to upload\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 263,\n                columnNumber: 20\n              }, this), \" or drag and drop\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 263,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Excel (.xlsx, .xls) or CSV files only\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 264,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Maximum file size: 10MB\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 265,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 262,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 260,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 221,\n        columnNumber: 9\n      }, this), parseError && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"error-message\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"error-icon\",\n          children: \"\\u26A0\\uFE0F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 273,\n          columnNumber: 13\n        }, this), parseError]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 272,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 209,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"settings-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"Import Settings\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 281,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"setting-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"Import Mode:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 284,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n          value: importSettings.importMode,\n          onChange: e => onSettingsChange('importMode', e.target.value),\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"SkipDuplicates\",\n            children: \"Skip Duplicates\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 289,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"UpdateExisting\",\n            children: \"Update Existing\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 290,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"FailOnDuplicates\",\n            children: \"Fail on Duplicates\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 291,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 285,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"setting-help\",\n          children: [importSettings.importMode === 'SkipDuplicates' && 'Skip records that already exist (based on mobile number + division + category)', importSettings.importMode === 'UpdateExisting' && 'Update existing records with new data', importSettings.importMode === 'FailOnDuplicates' && 'Stop import if duplicates are found']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 293,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 283,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"setting-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"checkbox\",\n            checked: importSettings.validateOnly,\n            onChange: e => onSettingsChange('validateOnly', e.target.checked)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 305,\n            columnNumber: 13\n          }, this), \"Validate Only (don't import data)\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 304,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"setting-help\",\n          children: \"Check this to validate the file without actually importing the data\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 312,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 303,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"setting-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"Batch Size:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 318,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n          value: importSettings.batchSize,\n          onChange: e => onSettingsChange('batchSize', parseInt(e.target.value)),\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: 50,\n            children: \"50 records per batch\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 323,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: 100,\n            children: \"100 records per batch\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 324,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: 200,\n            children: \"200 records per batch\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 325,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: 500,\n            children: \"500 records per batch\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 326,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 319,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"setting-help\",\n          children: \"Smaller batches are slower but more reliable for large files\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 328,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 317,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 280,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"upload-actions\",\n      children: /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: parseFile,\n        disabled: !selectedFile || parsing,\n        className: \"btn btn-primary\",\n        children: parsing ? 'Processing...' : 'Continue to Field Mapping'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 336,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 335,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 208,\n    columnNumber: 5\n  }, this);\n};\n_s(FileUpload, \"bS8YVESSiKUDck2VqNEAqYzqYK8=\");\n_c = FileUpload;\nexport default FileUpload;\nvar _c;\n$RefreshReg$(_c, \"FileUpload\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "XLSX", "jsxDEV", "_jsxDEV", "FileUpload", "onFileUpload", "importSettings", "onSettingsChange", "error", "_s", "dragActive", "setDragActive", "selectedFile", "setSelectedFile", "fileInfo", "setFileInfo", "parsing", "setParsing", "parseError", "setParseError", "fileInputRef", "handleDrag", "e", "preventDefault", "stopPropagation", "type", "handleDrop", "dataTransfer", "files", "handleFileSelect", "handleFileInputChange", "target", "file", "allowedTypes", "allowedExtensions", "fileExtension", "name", "toLowerCase", "substring", "lastIndexOf", "includes", "size", "formatFileSize", "lastModified", "Date", "toLocaleDateString", "parseFile", "headers", "extractHeaders", "err", "console", "message", "Promise", "resolve", "reject", "reader", "FileReader", "onload", "data", "result", "endsWith", "lines", "split", "length", "map", "header", "trim", "replace", "log", "Error", "workbook", "read", "SheetNames", "firstSheetName", "worksheet", "Sheets", "jsonData", "utils", "sheet_to_json", "String", "xlsxError", "filter", "onerror", "readAsText", "readAsBinaryString", "bytes", "k", "sizes", "i", "Math", "floor", "parseFloat", "pow", "toFixed", "downloadTemplate", "response", "fetch", "ok", "blob", "url", "window", "URL", "createObjectURL", "a", "document", "createElement", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "revokeObjectURL", "<PERSON><PERSON><PERSON><PERSON>", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "onDragEnter", "onDragLeave", "onDragOver", "onDrop", "_fileInputRef$current", "current", "ref", "accept", "onChange", "style", "display", "value", "importMode", "checked", "validateOnly", "batchSize", "parseInt", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/Advanced/Advanced CRM filed all use/frontend/src/components/import/FileUpload.js"], "sourcesContent": ["import React, { useState, useRef } from 'react';\nimport * as XLSX from 'xlsx';\nimport './FileUpload.css';\n\nconst FileUpload = ({ onFileUpload, importSettings, onSettingsChange, error }) => {\n  const [dragActive, setDragActive] = useState(false);\n  const [selectedFile, setSelectedFile] = useState(null);\n  const [fileInfo, setFileInfo] = useState(null);\n  const [parsing, setParsing] = useState(false);\n  const [parseError, setParseError] = useState(null);\n  const fileInputRef = useRef(null);\n\n  const handleDrag = (e) => {\n    e.preventDefault();\n    e.stopPropagation();\n    if (e.type === 'dragenter' || e.type === 'dragover') {\n      setDragActive(true);\n    } else if (e.type === 'dragleave') {\n      setDragActive(false);\n    }\n  };\n\n  const handleDrop = (e) => {\n    e.preventDefault();\n    e.stopPropagation();\n    setDragActive(false);\n    \n    if (e.dataTransfer.files && e.dataTransfer.files[0]) {\n      handleFileSelect(e.dataTransfer.files[0]);\n    }\n  };\n\n  const handleFileInputChange = (e) => {\n    if (e.target.files && e.target.files[0]) {\n      handleFileSelect(e.target.files[0]);\n    }\n  };\n\n  const handleFileSelect = (file) => {\n    // Validate file type\n    const allowedTypes = [\n      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx\n      'application/vnd.ms-excel', // .xls\n      'text/csv' // .csv\n    ];\n\n    const allowedExtensions = ['.xlsx', '.xls', '.csv'];\n    const fileExtension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'));\n\n    if (!allowedTypes.includes(file.type) && !allowedExtensions.includes(fileExtension)) {\n      setParseError('Please select a valid Excel (.xlsx, .xls) or CSV file');\n      return;\n    }\n\n    // Validate file size (10MB limit)\n    if (file.size > 10 * 1024 * 1024) {\n      setParseError('File size must be less than 10MB');\n      return;\n    }\n\n    setSelectedFile(file);\n    setFileInfo({\n      name: file.name,\n      size: formatFileSize(file.size),\n      type: file.type || 'Unknown',\n      lastModified: new Date(file.lastModified).toLocaleDateString()\n    });\n    setParseError(null);\n  };\n\n  const parseFile = async () => {\n    if (!selectedFile) return;\n\n    setParsing(true);\n    setParseError(null);\n\n    try {\n      const headers = await extractHeaders(selectedFile);\n      onFileUpload(selectedFile, headers);\n    } catch (err) {\n      console.error('File parsing error:', err);\n      setParseError(err.message || 'Failed to parse file');\n    } finally {\n      setParsing(false);\n    }\n  };\n\n  const extractHeaders = (file) => {\n    return new Promise((resolve, reject) => {\n      const reader = new FileReader();\n      \n      reader.onload = (e) => {\n        try {\n          const data = e.target.result;\n          let headers = [];\n\n          if (file.name.toLowerCase().endsWith('.csv')) {\n            // Parse CSV\n            const lines = data.split('\\n');\n            if (lines.length > 0) {\n              headers = lines[0].split(',').map(header => header.trim().replace(/\"/g, ''));\n            }\n          } else {\n            // Parse Excel with better error handling\n            console.log('Parsing Excel file:', file.name, 'Size:', file.size, 'Type:', file.type);\n\n            try {\n              // Check if the data looks like HTML\n              if (typeof data === 'string' && (data.includes('<html>') || data.includes('<table>'))) {\n                throw new Error('HTML file detected. Please upload a proper Excel (.xlsx, .xls) file.');\n              }\n\n              const workbook = XLSX.read(data, { type: 'binary' });\n              console.log('Workbook loaded successfully');\n\n              if (!workbook.SheetNames || workbook.SheetNames.length === 0) {\n                throw new Error('No sheets found in the Excel file. The file might be corrupted.');\n              }\n\n              console.log('Available sheets:', workbook.SheetNames);\n\n              const firstSheetName = workbook.SheetNames[0];\n              const worksheet = workbook.Sheets[firstSheetName];\n\n              if (!worksheet) {\n                throw new Error(`Could not read the sheet \"${firstSheetName}\".`);\n              }\n\n              const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });\n              console.log('Extracted', jsonData.length, 'rows from Excel');\n\n              if (jsonData.length > 0) {\n                headers = jsonData[0].map(header => String(header || '').trim());\n                console.log('Headers found:', headers);\n              } else {\n                throw new Error('The Excel file appears to be empty.');\n              }\n            } catch (xlsxError) {\n              console.error('Excel parsing error:', xlsxError);\n\n              if (xlsxError.message && xlsxError.message.toLowerCase().includes('table')) {\n                throw new Error('Invalid file format. The file contains HTML content instead of Excel data. Please upload a proper .xlsx or .xls file.');\n              }\n\n              if (xlsxError.message && xlsxError.message.includes('ZIP')) {\n                throw new Error('The Excel file is corrupted or not a valid .xlsx file. Please try re-saving the file in Excel.');\n              }\n\n              throw new Error(`Excel parsing failed: ${xlsxError.message}`);\n            }\n          }\n\n          if (headers.length === 0) {\n            reject(new Error('No headers found in file'));\n            return;\n          }\n\n          resolve(headers.filter(header => header !== ''));\n        } catch (err) {\n          reject(new Error('Failed to parse file: ' + err.message));\n        }\n      };\n\n      reader.onerror = () => {\n        reject(new Error('Failed to read file'));\n      };\n\n      if (file.name.toLowerCase().endsWith('.csv')) {\n        reader.readAsText(file);\n      } else {\n        reader.readAsBinaryString(file);\n      }\n    });\n  };\n\n  const formatFileSize = (bytes) => {\n    if (bytes === 0) return '0 Bytes';\n    const k = 1024;\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  };\n\n  const downloadTemplate = async () => {\n    try {\n      const response = await fetch('/api/import-export/persons/template?format=Excel&includeSampleData=true');\n      \n      if (!response.ok) {\n        throw new Error('Failed to download template');\n      }\n\n      const blob = await response.blob();\n      const url = window.URL.createObjectURL(blob);\n      const a = document.createElement('a');\n      a.href = url;\n      a.download = 'person_import_template.xlsx';\n      document.body.appendChild(a);\n      a.click();\n      window.URL.revokeObjectURL(url);\n      document.body.removeChild(a);\n    } catch (err) {\n      console.error('Template download error:', err);\n      setParseError('Failed to download template');\n    }\n  };\n\n  return (\n    <div className=\"file-upload\">\n      <div className=\"upload-section\">\n        <h3>Upload File</h3>\n        \n        {/* Template Download */}\n        <div className=\"template-section\">\n          <p>Don't have a file? Download our template to get started:</p>\n          <button onClick={downloadTemplate} className=\"btn btn-outline\">\n            📥 Download Template\n          </button>\n        </div>\n\n        {/* File Drop Zone */}\n        <div\n          className={`drop-zone ${dragActive ? 'active' : ''} ${selectedFile ? 'has-file' : ''}`}\n          onDragEnter={handleDrag}\n          onDragLeave={handleDrag}\n          onDragOver={handleDrag}\n          onDrop={handleDrop}\n          onClick={() => fileInputRef.current?.click()}\n        >\n          <input\n            ref={fileInputRef}\n            type=\"file\"\n            accept=\".xlsx,.xls,.csv\"\n            onChange={handleFileInputChange}\n            style={{ display: 'none' }}\n          />\n          \n          {selectedFile ? (\n            <div className=\"file-selected\">\n              <div className=\"file-icon\">📄</div>\n              <div className=\"file-details\">\n                <div className=\"file-name\">{fileInfo.name}</div>\n                <div className=\"file-meta\">\n                  <span>{fileInfo.size}</span>\n                  <span>•</span>\n                  <span>{fileInfo.lastModified}</span>\n                </div>\n              </div>\n              <button \n                onClick={(e) => {\n                  e.stopPropagation();\n                  setSelectedFile(null);\n                  setFileInfo(null);\n                }}\n                className=\"remove-file\"\n              >\n                ✕\n              </button>\n            </div>\n          ) : (\n            <div className=\"drop-content\">\n              <div className=\"drop-icon\">📁</div>\n              <div className=\"drop-text\">\n                <p><strong>Click to upload</strong> or drag and drop</p>\n                <p>Excel (.xlsx, .xls) or CSV files only</p>\n                <p>Maximum file size: 10MB</p>\n              </div>\n            </div>\n          )}\n        </div>\n\n        {parseError && (\n          <div className=\"error-message\">\n            <span className=\"error-icon\">⚠️</span>\n            {parseError}\n          </div>\n        )}\n      </div>\n\n      {/* Import Settings */}\n      <div className=\"settings-section\">\n        <h3>Import Settings</h3>\n        \n        <div className=\"setting-group\">\n          <label>Import Mode:</label>\n          <select\n            value={importSettings.importMode}\n            onChange={(e) => onSettingsChange('importMode', e.target.value)}\n          >\n            <option value=\"SkipDuplicates\">Skip Duplicates</option>\n            <option value=\"UpdateExisting\">Update Existing</option>\n            <option value=\"FailOnDuplicates\">Fail on Duplicates</option>\n          </select>\n          <div className=\"setting-help\">\n            {importSettings.importMode === 'SkipDuplicates' && \n              'Skip records that already exist (based on mobile number + division + category)'}\n            {importSettings.importMode === 'UpdateExisting' && \n              'Update existing records with new data'}\n            {importSettings.importMode === 'FailOnDuplicates' && \n              'Stop import if duplicates are found'}\n          </div>\n        </div>\n\n        <div className=\"setting-group\">\n          <label>\n            <input\n              type=\"checkbox\"\n              checked={importSettings.validateOnly}\n              onChange={(e) => onSettingsChange('validateOnly', e.target.checked)}\n            />\n            Validate Only (don't import data)\n          </label>\n          <div className=\"setting-help\">\n            Check this to validate the file without actually importing the data\n          </div>\n        </div>\n\n        <div className=\"setting-group\">\n          <label>Batch Size:</label>\n          <select\n            value={importSettings.batchSize}\n            onChange={(e) => onSettingsChange('batchSize', parseInt(e.target.value))}\n          >\n            <option value={50}>50 records per batch</option>\n            <option value={100}>100 records per batch</option>\n            <option value={200}>200 records per batch</option>\n            <option value={500}>500 records per batch</option>\n          </select>\n          <div className=\"setting-help\">\n            Smaller batches are slower but more reliable for large files\n          </div>\n        </div>\n      </div>\n\n      {/* Action Buttons */}\n      <div className=\"upload-actions\">\n        <button\n          onClick={parseFile}\n          disabled={!selectedFile || parsing}\n          className=\"btn btn-primary\"\n        >\n          {parsing ? 'Processing...' : 'Continue to Field Mapping'}\n        </button>\n      </div>\n    </div>\n  );\n};\n\nexport default FileUpload;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AAC/C,OAAO,KAAKC,IAAI,MAAM,MAAM;AAC5B,OAAO,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,UAAU,GAAGA,CAAC;EAAEC,YAAY;EAAEC,cAAc;EAAEC,gBAAgB;EAAEC;AAAM,CAAC,KAAK;EAAAC,EAAA;EAChF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGZ,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACa,YAAY,EAAEC,eAAe,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACe,QAAQ,EAAEC,WAAW,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAACiB,OAAO,EAAEC,UAAU,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACmB,UAAU,EAAEC,aAAa,CAAC,GAAGpB,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAMqB,YAAY,GAAGpB,MAAM,CAAC,IAAI,CAAC;EAEjC,MAAMqB,UAAU,GAAIC,CAAC,IAAK;IACxBA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBD,CAAC,CAACE,eAAe,CAAC,CAAC;IACnB,IAAIF,CAAC,CAACG,IAAI,KAAK,WAAW,IAAIH,CAAC,CAACG,IAAI,KAAK,UAAU,EAAE;MACnDd,aAAa,CAAC,IAAI,CAAC;IACrB,CAAC,MAAM,IAAIW,CAAC,CAACG,IAAI,KAAK,WAAW,EAAE;MACjCd,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;EAED,MAAMe,UAAU,GAAIJ,CAAC,IAAK;IACxBA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBD,CAAC,CAACE,eAAe,CAAC,CAAC;IACnBb,aAAa,CAAC,KAAK,CAAC;IAEpB,IAAIW,CAAC,CAACK,YAAY,CAACC,KAAK,IAAIN,CAAC,CAACK,YAAY,CAACC,KAAK,CAAC,CAAC,CAAC,EAAE;MACnDC,gBAAgB,CAACP,CAAC,CAACK,YAAY,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC;IAC3C;EACF,CAAC;EAED,MAAME,qBAAqB,GAAIR,CAAC,IAAK;IACnC,IAAIA,CAAC,CAACS,MAAM,CAACH,KAAK,IAAIN,CAAC,CAACS,MAAM,CAACH,KAAK,CAAC,CAAC,CAAC,EAAE;MACvCC,gBAAgB,CAACP,CAAC,CAACS,MAAM,CAACH,KAAK,CAAC,CAAC,CAAC,CAAC;IACrC;EACF,CAAC;EAED,MAAMC,gBAAgB,GAAIG,IAAI,IAAK;IACjC;IACA,MAAMC,YAAY,GAAG,CACnB,mEAAmE;IAAE;IACrE,0BAA0B;IAAE;IAC5B,UAAU,CAAC;IAAA,CACZ;IAED,MAAMC,iBAAiB,GAAG,CAAC,OAAO,EAAE,MAAM,EAAE,MAAM,CAAC;IACnD,MAAMC,aAAa,GAAGH,IAAI,CAACI,IAAI,CAACC,WAAW,CAAC,CAAC,CAACC,SAAS,CAACN,IAAI,CAACI,IAAI,CAACG,WAAW,CAAC,GAAG,CAAC,CAAC;IAEnF,IAAI,CAACN,YAAY,CAACO,QAAQ,CAACR,IAAI,CAACP,IAAI,CAAC,IAAI,CAACS,iBAAiB,CAACM,QAAQ,CAACL,aAAa,CAAC,EAAE;MACnFhB,aAAa,CAAC,uDAAuD,CAAC;MACtE;IACF;;IAEA;IACA,IAAIa,IAAI,CAACS,IAAI,GAAG,EAAE,GAAG,IAAI,GAAG,IAAI,EAAE;MAChCtB,aAAa,CAAC,kCAAkC,CAAC;MACjD;IACF;IAEAN,eAAe,CAACmB,IAAI,CAAC;IACrBjB,WAAW,CAAC;MACVqB,IAAI,EAAEJ,IAAI,CAACI,IAAI;MACfK,IAAI,EAAEC,cAAc,CAACV,IAAI,CAACS,IAAI,CAAC;MAC/BhB,IAAI,EAAEO,IAAI,CAACP,IAAI,IAAI,SAAS;MAC5BkB,YAAY,EAAE,IAAIC,IAAI,CAACZ,IAAI,CAACW,YAAY,CAAC,CAACE,kBAAkB,CAAC;IAC/D,CAAC,CAAC;IACF1B,aAAa,CAAC,IAAI,CAAC;EACrB,CAAC;EAED,MAAM2B,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAI,CAAClC,YAAY,EAAE;IAEnBK,UAAU,CAAC,IAAI,CAAC;IAChBE,aAAa,CAAC,IAAI,CAAC;IAEnB,IAAI;MACF,MAAM4B,OAAO,GAAG,MAAMC,cAAc,CAACpC,YAAY,CAAC;MAClDP,YAAY,CAACO,YAAY,EAAEmC,OAAO,CAAC;IACrC,CAAC,CAAC,OAAOE,GAAG,EAAE;MACZC,OAAO,CAAC1C,KAAK,CAAC,qBAAqB,EAAEyC,GAAG,CAAC;MACzC9B,aAAa,CAAC8B,GAAG,CAACE,OAAO,IAAI,sBAAsB,CAAC;IACtD,CAAC,SAAS;MACRlC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM+B,cAAc,GAAIhB,IAAI,IAAK;IAC/B,OAAO,IAAIoB,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;MACtC,MAAMC,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAE/BD,MAAM,CAACE,MAAM,GAAInC,CAAC,IAAK;QACrB,IAAI;UACF,MAAMoC,IAAI,GAAGpC,CAAC,CAACS,MAAM,CAAC4B,MAAM;UAC5B,IAAIZ,OAAO,GAAG,EAAE;UAEhB,IAAIf,IAAI,CAACI,IAAI,CAACC,WAAW,CAAC,CAAC,CAACuB,QAAQ,CAAC,MAAM,CAAC,EAAE;YAC5C;YACA,MAAMC,KAAK,GAAGH,IAAI,CAACI,KAAK,CAAC,IAAI,CAAC;YAC9B,IAAID,KAAK,CAACE,MAAM,GAAG,CAAC,EAAE;cACpBhB,OAAO,GAAGc,KAAK,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAACE,GAAG,CAACC,MAAM,IAAIA,MAAM,CAACC,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;YAC9E;UACF,CAAC,MAAM;YACL;YACAjB,OAAO,CAACkB,GAAG,CAAC,qBAAqB,EAAEpC,IAAI,CAACI,IAAI,EAAE,OAAO,EAAEJ,IAAI,CAACS,IAAI,EAAE,OAAO,EAAET,IAAI,CAACP,IAAI,CAAC;YAErF,IAAI;cACF;cACA,IAAI,OAAOiC,IAAI,KAAK,QAAQ,KAAKA,IAAI,CAAClB,QAAQ,CAAC,QAAQ,CAAC,IAAIkB,IAAI,CAAClB,QAAQ,CAAC,SAAS,CAAC,CAAC,EAAE;gBACrF,MAAM,IAAI6B,KAAK,CAAC,sEAAsE,CAAC;cACzF;cAEA,MAAMC,QAAQ,GAAGrE,IAAI,CAACsE,IAAI,CAACb,IAAI,EAAE;gBAAEjC,IAAI,EAAE;cAAS,CAAC,CAAC;cACpDyB,OAAO,CAACkB,GAAG,CAAC,8BAA8B,CAAC;cAE3C,IAAI,CAACE,QAAQ,CAACE,UAAU,IAAIF,QAAQ,CAACE,UAAU,CAACT,MAAM,KAAK,CAAC,EAAE;gBAC5D,MAAM,IAAIM,KAAK,CAAC,iEAAiE,CAAC;cACpF;cAEAnB,OAAO,CAACkB,GAAG,CAAC,mBAAmB,EAAEE,QAAQ,CAACE,UAAU,CAAC;cAErD,MAAMC,cAAc,GAAGH,QAAQ,CAACE,UAAU,CAAC,CAAC,CAAC;cAC7C,MAAME,SAAS,GAAGJ,QAAQ,CAACK,MAAM,CAACF,cAAc,CAAC;cAEjD,IAAI,CAACC,SAAS,EAAE;gBACd,MAAM,IAAIL,KAAK,CAAC,6BAA6BI,cAAc,IAAI,CAAC;cAClE;cAEA,MAAMG,QAAQ,GAAG3E,IAAI,CAAC4E,KAAK,CAACC,aAAa,CAACJ,SAAS,EAAE;gBAAET,MAAM,EAAE;cAAE,CAAC,CAAC;cACnEf,OAAO,CAACkB,GAAG,CAAC,WAAW,EAAEQ,QAAQ,CAACb,MAAM,EAAE,iBAAiB,CAAC;cAE5D,IAAIa,QAAQ,CAACb,MAAM,GAAG,CAAC,EAAE;gBACvBhB,OAAO,GAAG6B,QAAQ,CAAC,CAAC,CAAC,CAACZ,GAAG,CAACC,MAAM,IAAIc,MAAM,CAACd,MAAM,IAAI,EAAE,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC;gBAChEhB,OAAO,CAACkB,GAAG,CAAC,gBAAgB,EAAErB,OAAO,CAAC;cACxC,CAAC,MAAM;gBACL,MAAM,IAAIsB,KAAK,CAAC,qCAAqC,CAAC;cACxD;YACF,CAAC,CAAC,OAAOW,SAAS,EAAE;cAClB9B,OAAO,CAAC1C,KAAK,CAAC,sBAAsB,EAAEwE,SAAS,CAAC;cAEhD,IAAIA,SAAS,CAAC7B,OAAO,IAAI6B,SAAS,CAAC7B,OAAO,CAACd,WAAW,CAAC,CAAC,CAACG,QAAQ,CAAC,OAAO,CAAC,EAAE;gBAC1E,MAAM,IAAI6B,KAAK,CAAC,uHAAuH,CAAC;cAC1I;cAEA,IAAIW,SAAS,CAAC7B,OAAO,IAAI6B,SAAS,CAAC7B,OAAO,CAACX,QAAQ,CAAC,KAAK,CAAC,EAAE;gBAC1D,MAAM,IAAI6B,KAAK,CAAC,gGAAgG,CAAC;cACnH;cAEA,MAAM,IAAIA,KAAK,CAAC,yBAAyBW,SAAS,CAAC7B,OAAO,EAAE,CAAC;YAC/D;UACF;UAEA,IAAIJ,OAAO,CAACgB,MAAM,KAAK,CAAC,EAAE;YACxBT,MAAM,CAAC,IAAIe,KAAK,CAAC,0BAA0B,CAAC,CAAC;YAC7C;UACF;UAEAhB,OAAO,CAACN,OAAO,CAACkC,MAAM,CAAChB,MAAM,IAAIA,MAAM,KAAK,EAAE,CAAC,CAAC;QAClD,CAAC,CAAC,OAAOhB,GAAG,EAAE;UACZK,MAAM,CAAC,IAAIe,KAAK,CAAC,wBAAwB,GAAGpB,GAAG,CAACE,OAAO,CAAC,CAAC;QAC3D;MACF,CAAC;MAEDI,MAAM,CAAC2B,OAAO,GAAG,MAAM;QACrB5B,MAAM,CAAC,IAAIe,KAAK,CAAC,qBAAqB,CAAC,CAAC;MAC1C,CAAC;MAED,IAAIrC,IAAI,CAACI,IAAI,CAACC,WAAW,CAAC,CAAC,CAACuB,QAAQ,CAAC,MAAM,CAAC,EAAE;QAC5CL,MAAM,CAAC4B,UAAU,CAACnD,IAAI,CAAC;MACzB,CAAC,MAAM;QACLuB,MAAM,CAAC6B,kBAAkB,CAACpD,IAAI,CAAC;MACjC;IACF,CAAC,CAAC;EACJ,CAAC;EAED,MAAMU,cAAc,GAAI2C,KAAK,IAAK;IAChC,IAAIA,KAAK,KAAK,CAAC,EAAE,OAAO,SAAS;IACjC,MAAMC,CAAC,GAAG,IAAI;IACd,MAAMC,KAAK,GAAG,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IACzC,MAAMC,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACrB,GAAG,CAACiB,KAAK,CAAC,GAAGI,IAAI,CAACrB,GAAG,CAACkB,CAAC,CAAC,CAAC;IACnD,OAAOK,UAAU,CAAC,CAACN,KAAK,GAAGI,IAAI,CAACG,GAAG,CAACN,CAAC,EAAEE,CAAC,CAAC,EAAEK,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGN,KAAK,CAACC,CAAC,CAAC;EACzE,CAAC;EAED,MAAMM,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,yEAAyE,CAAC;MAEvG,IAAI,CAACD,QAAQ,CAACE,EAAE,EAAE;QAChB,MAAM,IAAI5B,KAAK,CAAC,6BAA6B,CAAC;MAChD;MAEA,MAAM6B,IAAI,GAAG,MAAMH,QAAQ,CAACG,IAAI,CAAC,CAAC;MAClC,MAAMC,GAAG,GAAGC,MAAM,CAACC,GAAG,CAACC,eAAe,CAACJ,IAAI,CAAC;MAC5C,MAAMK,CAAC,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACrCF,CAAC,CAACG,IAAI,GAAGP,GAAG;MACZI,CAAC,CAACI,QAAQ,GAAG,6BAA6B;MAC1CH,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,CAAC,CAAC;MAC5BA,CAAC,CAACO,KAAK,CAAC,CAAC;MACTV,MAAM,CAACC,GAAG,CAACU,eAAe,CAACZ,GAAG,CAAC;MAC/BK,QAAQ,CAACI,IAAI,CAACI,WAAW,CAACT,CAAC,CAAC;IAC9B,CAAC,CAAC,OAAOtD,GAAG,EAAE;MACZC,OAAO,CAAC1C,KAAK,CAAC,0BAA0B,EAAEyC,GAAG,CAAC;MAC9C9B,aAAa,CAAC,6BAA6B,CAAC;IAC9C;EACF,CAAC;EAED,oBACEhB,OAAA;IAAK8G,SAAS,EAAC,aAAa;IAAAC,QAAA,gBAC1B/G,OAAA;MAAK8G,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7B/G,OAAA;QAAA+G,QAAA,EAAI;MAAW;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAGpBnH,OAAA;QAAK8G,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/B/G,OAAA;UAAA+G,QAAA,EAAG;QAAwD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAC/DnH,OAAA;UAAQoH,OAAO,EAAEzB,gBAAiB;UAACmB,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAAC;QAE/D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAGNnH,OAAA;QACE8G,SAAS,EAAE,aAAavG,UAAU,GAAG,QAAQ,GAAG,EAAE,IAAIE,YAAY,GAAG,UAAU,GAAG,EAAE,EAAG;QACvF4G,WAAW,EAAEnG,UAAW;QACxBoG,WAAW,EAAEpG,UAAW;QACxBqG,UAAU,EAAErG,UAAW;QACvBsG,MAAM,EAAEjG,UAAW;QACnB6F,OAAO,EAAEA,CAAA;UAAA,IAAAK,qBAAA;UAAA,QAAAA,qBAAA,GAAMxG,YAAY,CAACyG,OAAO,cAAAD,qBAAA,uBAApBA,qBAAA,CAAsBd,KAAK,CAAC,CAAC;QAAA,CAAC;QAAAI,QAAA,gBAE7C/G,OAAA;UACE2H,GAAG,EAAE1G,YAAa;UAClBK,IAAI,EAAC,MAAM;UACXsG,MAAM,EAAC,iBAAiB;UACxBC,QAAQ,EAAElG,qBAAsB;UAChCmG,KAAK,EAAE;YAAEC,OAAO,EAAE;UAAO;QAAE;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC,EAED1G,YAAY,gBACXT,OAAA;UAAK8G,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5B/G,OAAA;YAAK8G,SAAS,EAAC,WAAW;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACnCnH,OAAA;YAAK8G,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3B/G,OAAA;cAAK8G,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAEpG,QAAQ,CAACsB;YAAI;cAAA+E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAChDnH,OAAA;cAAK8G,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxB/G,OAAA;gBAAA+G,QAAA,EAAOpG,QAAQ,CAAC2B;cAAI;gBAAA0E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC5BnH,OAAA;gBAAA+G,QAAA,EAAM;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACdnH,OAAA;gBAAA+G,QAAA,EAAOpG,QAAQ,CAAC6B;cAAY;gBAAAwE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNnH,OAAA;YACEoH,OAAO,EAAGjG,CAAC,IAAK;cACdA,CAAC,CAACE,eAAe,CAAC,CAAC;cACnBX,eAAe,CAAC,IAAI,CAAC;cACrBE,WAAW,CAAC,IAAI,CAAC;YACnB,CAAE;YACFkG,SAAS,EAAC,aAAa;YAAAC,QAAA,EACxB;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,gBAENnH,OAAA;UAAK8G,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3B/G,OAAA;YAAK8G,SAAS,EAAC,WAAW;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACnCnH,OAAA;YAAK8G,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxB/G,OAAA;cAAA+G,QAAA,gBAAG/G,OAAA;gBAAA+G,QAAA,EAAQ;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,qBAAiB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACxDnH,OAAA;cAAA+G,QAAA,EAAG;YAAqC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC5CnH,OAAA;cAAA+G,QAAA,EAAG;YAAuB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,EAELpG,UAAU,iBACTf,OAAA;QAAK8G,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5B/G,OAAA;UAAM8G,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,EACrCpG,UAAU;MAAA;QAAAiG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGNnH,OAAA;MAAK8G,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/B/G,OAAA;QAAA+G,QAAA,EAAI;MAAe;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAExBnH,OAAA;QAAK8G,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5B/G,OAAA;UAAA+G,QAAA,EAAO;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC3BnH,OAAA;UACEgI,KAAK,EAAE7H,cAAc,CAAC8H,UAAW;UACjCJ,QAAQ,EAAG1G,CAAC,IAAKf,gBAAgB,CAAC,YAAY,EAAEe,CAAC,CAACS,MAAM,CAACoG,KAAK,CAAE;UAAAjB,QAAA,gBAEhE/G,OAAA;YAAQgI,KAAK,EAAC,gBAAgB;YAAAjB,QAAA,EAAC;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACvDnH,OAAA;YAAQgI,KAAK,EAAC,gBAAgB;YAAAjB,QAAA,EAAC;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACvDnH,OAAA;YAAQgI,KAAK,EAAC,kBAAkB;YAAAjB,QAAA,EAAC;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtD,CAAC,eACTnH,OAAA;UAAK8G,SAAS,EAAC,cAAc;UAAAC,QAAA,GAC1B5G,cAAc,CAAC8H,UAAU,KAAK,gBAAgB,IAC7C,gFAAgF,EACjF9H,cAAc,CAAC8H,UAAU,KAAK,gBAAgB,IAC7C,uCAAuC,EACxC9H,cAAc,CAAC8H,UAAU,KAAK,kBAAkB,IAC/C,qCAAqC;QAAA;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENnH,OAAA;QAAK8G,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5B/G,OAAA;UAAA+G,QAAA,gBACE/G,OAAA;YACEsB,IAAI,EAAC,UAAU;YACf4G,OAAO,EAAE/H,cAAc,CAACgI,YAAa;YACrCN,QAAQ,EAAG1G,CAAC,IAAKf,gBAAgB,CAAC,cAAc,EAAEe,CAAC,CAACS,MAAM,CAACsG,OAAO;UAAE;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrE,CAAC,qCAEJ;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACRnH,OAAA;UAAK8G,SAAS,EAAC,cAAc;UAAAC,QAAA,EAAC;QAE9B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENnH,OAAA;QAAK8G,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5B/G,OAAA;UAAA+G,QAAA,EAAO;QAAW;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC1BnH,OAAA;UACEgI,KAAK,EAAE7H,cAAc,CAACiI,SAAU;UAChCP,QAAQ,EAAG1G,CAAC,IAAKf,gBAAgB,CAAC,WAAW,EAAEiI,QAAQ,CAAClH,CAAC,CAACS,MAAM,CAACoG,KAAK,CAAC,CAAE;UAAAjB,QAAA,gBAEzE/G,OAAA;YAAQgI,KAAK,EAAE,EAAG;YAAAjB,QAAA,EAAC;UAAoB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAChDnH,OAAA;YAAQgI,KAAK,EAAE,GAAI;YAAAjB,QAAA,EAAC;UAAqB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAClDnH,OAAA;YAAQgI,KAAK,EAAE,GAAI;YAAAjB,QAAA,EAAC;UAAqB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAClDnH,OAAA;YAAQgI,KAAK,EAAE,GAAI;YAAAjB,QAAA,EAAC;UAAqB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CAAC,eACTnH,OAAA;UAAK8G,SAAS,EAAC,cAAc;UAAAC,QAAA,EAAC;QAE9B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNnH,OAAA;MAAK8G,SAAS,EAAC,gBAAgB;MAAAC,QAAA,eAC7B/G,OAAA;QACEoH,OAAO,EAAEzE,SAAU;QACnB2F,QAAQ,EAAE,CAAC7H,YAAY,IAAII,OAAQ;QACnCiG,SAAS,EAAC,iBAAiB;QAAAC,QAAA,EAE1BlG,OAAO,GAAG,eAAe,GAAG;MAA2B;QAAAmG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC7G,EAAA,CArVIL,UAAU;AAAAsI,EAAA,GAAVtI,UAAU;AAuVhB,eAAeA,UAAU;AAAC,IAAAsI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}