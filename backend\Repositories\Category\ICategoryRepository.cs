using CrmApi.Models.Category;

namespace CrmApi.Repositories.Category
{
    public interface ICategoryRepository
    {
        Task<IEnumerable<Models.Category.Category>> GetAllAsync();
        Task<IEnumerable<Models.Category.Category>> GetAllWithRelationsAsync();
        Task<Models.Category.Category?> GetByIdAsync(int id);
        Task<Models.Category.Category?> GetByIdWithRelationsAsync(int id);
        Task<IEnumerable<Models.Category.Category>> GetByDivisionIdAsync(int divisionId);
        Task<Models.Category.Category> CreateAsync(Models.Category.Category category);
        Task<Models.Category.Category> UpdateAsync(Models.Category.Category category);
        Task<bool> DeleteAsync(int id);
        Task<bool> ExistsAsync(int id);
        Task<bool> NameExistsInDivisionAsync(string name, int divisionId, int? excludeId = null);
    }
}
