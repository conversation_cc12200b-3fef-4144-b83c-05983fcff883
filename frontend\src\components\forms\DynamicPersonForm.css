.dynamic-person-form {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.form-header {
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e1e5e9;
}

.form-header h2 {
  margin: 0 0 0.5rem 0;
  color: #495057;
  font-size: 1.75rem;
}

.form-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.form-name {
  font-weight: 600;
  color: #007bff;
  font-size: 1rem;
}

.form-description {
  color: #6c757d;
  font-size: 0.875rem;
  font-style: italic;
}

.person-form {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.form-section {
  background-color: #f8f9fa;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  padding: 1.5rem;
}

.form-section h3 {
  margin: 0 0 1.5rem 0;
  color: #495057;
  font-size: 1.25rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #007bff;
  display: inline-block;
}

.form-fields {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

/* Single column for certain field types */
.form-fields .form-field:has(textarea),
.form-fields .form-field:has(select[multiple]),
.form-fields .form-field:has(input[type="url"]) {
  grid-column: 1 / -1;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding-top: 2rem;
  border-top: 1px solid #e1e5e9;
  margin-top: 2rem;
}

/* Loading and error states */
.dynamic-form-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
}

.loading-spinner {
  width: 3rem;
  height: 3rem;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.dynamic-form-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
  color: #dc3545;
}

.dynamic-form-error p {
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

/* Alert styles */
.alert {
  padding: 1rem 1.25rem;
  margin-bottom: 1.5rem;
  border: 1px solid transparent;
  border-radius: 4px;
  font-size: 0.875rem;
}

.alert-error {
  color: #721c24;
  background-color: #f8d7da;
  border-color: #f5c6cb;
}

.alert-success {
  color: #155724;
  background-color: #d4edda;
  border-color: #c3e6cb;
}

.alert-warning {
  color: #856404;
  background-color: #fff3cd;
  border-color: #ffeaa7;
}

.alert-info {
  color: #0c5460;
  background-color: #d1ecf1;
  border-color: #bee5eb;
}

/* Button styles */
.btn {
  padding: 0.75rem 1.5rem;
  border: 1px solid transparent;
  border-radius: 4px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  text-decoration: none;
  display: inline-block;
  transition: all 0.2s ease;
  min-width: 120px;
}

.btn-primary {
  background-color: #007bff;
  border-color: #007bff;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background-color: #0056b3;
  border-color: #0056b3;
}

.btn-primary:disabled {
  background-color: #6c757d;
  border-color: #6c757d;
  cursor: not-allowed;
  opacity: 0.65;
}

.btn-outline {
  background-color: transparent;
  border-color: #6c757d;
  color: #6c757d;
}

.btn-outline:hover:not(:disabled) {
  background-color: #6c757d;
  color: white;
}

.btn-outline:disabled {
  opacity: 0.65;
  cursor: not-allowed;
}

/* Error message styles */
.error-message {
  color: #dc3545;
  font-size: 0.875rem;
  margin-top: 0.5rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.error-message::before {
  content: "⚠";
  font-size: 1rem;
}

/* Responsive design */
@media (max-width: 768px) {
  .dynamic-person-form {
    padding: 1rem;
    margin: 1rem;
  }
  
  .form-header h2 {
    font-size: 1.5rem;
  }
  
  .form-section {
    padding: 1rem;
  }
  
  .form-fields {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .form-actions {
    flex-direction: column;
    align-items: stretch;
  }
  
  .btn {
    padding: 1rem;
    font-size: 1.1rem;
  }
}

@media (max-width: 480px) {
  .dynamic-person-form {
    padding: 0.5rem;
    margin: 0.5rem;
  }
  
  .form-section {
    padding: 0.75rem;
  }
  
  .form-section h3 {
    font-size: 1.1rem;
  }
}

/* Print styles */
@media print {
  .dynamic-person-form {
    box-shadow: none;
    border: 1px solid #000;
  }
  
  .form-actions {
    display: none;
  }
  
  .form-section {
    break-inside: avoid;
    page-break-inside: avoid;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .form-section {
    border-width: 2px;
  }
  
  .form-section h3 {
    border-bottom-width: 3px;
  }
  
  .btn {
    border-width: 2px;
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .btn,
  .loading-spinner {
    transition: none;
    animation: none;
  }
}

/* Hierarchical Dropdown Styles */
.form-select {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 1rem;
  background-color: white;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-select:focus {
  border-color: #007bff;
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-select:disabled {
  background-color: #e9ecef;
  opacity: 1;
  cursor: not-allowed;
}

.form-select.error {
  border-color: #dc3545;
}

.form-select.error:focus {
  border-color: #dc3545;
  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

/* Status Messages */
.loading-message {
  padding: 1rem;
  background-color: #e3f2fd;
  border: 1px solid #bbdefb;
  border-radius: 4px;
  color: #1976d2;
  text-align: center;
  margin-top: 1rem;
}

.status-message {
  padding: 1rem;
  border-radius: 4px;
  margin-top: 1rem;
  font-weight: 500;
}

.status-message.success {
  background-color: #d4edda;
  border: 1px solid #c3e6cb;
  color: #155724;
}

.status-message.error {
  background-color: #f8d7da;
  border: 1px solid #f5c6cb;
  color: #721c24;
}

.status-message.info {
  background-color: #d1ecf1;
  border: 1px solid #bee5eb;
  color: #0c5460;
}

/* No Form Available Message */
.no-form-message {
  text-align: center;
  padding: 2rem;
  background-color: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 8px;
  color: #856404;
}

.no-form-message h3 {
  margin: 0 0 1rem 0;
  color: #856404;
}

.no-form-message p {
  margin: 0.5rem 0;
  line-height: 1.5;
}
