using BCrypt.Net;

namespace CrmApi.Scripts
{
    public class CreateAdminUser
    {
        public static void Main()
        {
            string password = "admin123";
            string hashedPassword = BCrypt.Net.BCrypt.HashPassword(password);
            
            Console.WriteLine($"Password: {password}");
            Console.WriteLine($"Hashed Password: {hashedPassword}");
            Console.WriteLine();
            Console.WriteLine("SQL Insert Statement:");
            Console.WriteLine($"INSERT INTO admins (username, email, password_hash, first_name, last_name) VALUES ('admin', '<EMAIL>', '{hashedPassword}', 'Admin', 'User');");
        }
    }
}