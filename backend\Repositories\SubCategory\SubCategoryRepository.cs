using Microsoft.EntityFrameworkCore;
using CrmApi.Data;
using CrmApi.Models.SubCategory;

namespace CrmApi.Repositories.SubCategory
{
    public class SubCategoryRepository : ISubCategoryRepository
    {
        private readonly CrmDbContext _context;

        public SubCategoryRepository(CrmDbContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<Models.SubCategory.SubCategory>> GetAllAsync()
        {
            return await _context.SubCategories.ToListAsync();
        }

        public async Task<IEnumerable<Models.SubCategory.SubCategory>> GetAllWithRelationsAsync()
        {
            return await _context.SubCategories
                .Include(sc => sc.Category)
                .ThenInclude(c => c.Division)
                .ToListAsync();
        }

        public async Task<Models.SubCategory.SubCategory?> GetByIdAsync(int id)
        {
            return await _context.SubCategories.FindAsync(id);
        }

        public async Task<Models.SubCategory.SubCategory?> GetByIdWithRelationsAsync(int id)
        {
            return await _context.SubCategories
                .Include(sc => sc.Category)
                .ThenInclude(c => c.Division)
                .FirstOrDefaultAsync(sc => sc.Id == id);
        }

        public async Task<IEnumerable<Models.SubCategory.SubCategory>> GetByCategoryIdAsync(int categoryId)
        {
            return await _context.SubCategories
                .Where(sc => sc.CategoryId == categoryId)
                .ToListAsync();
        }

        public async Task<Models.SubCategory.SubCategory> CreateAsync(Models.SubCategory.SubCategory subCategory)
        {
            subCategory.CreatedAt = DateTime.UtcNow;
            subCategory.UpdatedAt = DateTime.UtcNow;
            
            _context.SubCategories.Add(subCategory);
            await _context.SaveChangesAsync();
            return subCategory;
        }

        public async Task<Models.SubCategory.SubCategory> UpdateAsync(Models.SubCategory.SubCategory subCategory)
        {
            subCategory.UpdatedAt = DateTime.UtcNow;
            _context.Entry(subCategory).State = EntityState.Modified;
            await _context.SaveChangesAsync();
            return subCategory;
        }

        public async Task<bool> DeleteAsync(int id)
        {
            var subCategory = await _context.SubCategories.FindAsync(id);
            if (subCategory == null)
                return false;

            _context.SubCategories.Remove(subCategory);
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> ExistsAsync(int id)
        {
            return await _context.SubCategories.AnyAsync(sc => sc.Id == id);
        }

        public async Task<bool> NameExistsInCategoryAsync(string name, int categoryId, int? excludeId = null)
        {
            var query = _context.SubCategories.Where(sc => sc.Name == name && sc.CategoryId == categoryId);
            if (excludeId.HasValue)
                query = query.Where(sc => sc.Id != excludeId.Value);
            
            return await query.AnyAsync();
        }
    }
}
