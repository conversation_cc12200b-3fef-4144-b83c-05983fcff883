{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Advanced\\\\Advanced CRM filed all use\\\\frontend\\\\src\\\\components\\\\PersonManagement.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport FormBuilder from './forms/FormBuilder';\nimport DynamicPersonForm from './forms/DynamicPersonForm';\nimport PersonList from './PersonList';\nimport ImportPersons from './import/ImportPersons';\nimport formConfigService from '../services/formConfigService';\nimport './PersonManagement.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PersonManagement = () => {\n  _s();\n  const [currentView, setCurrentView] = useState('list'); // 'list', 'create', 'edit', 'formBuilder', 'import'\n  const [selectedPerson, setSelectedPerson] = useState(null);\n  const [notification, setNotification] = useState(null);\n  const [showImportModal, setShowImportModal] = useState(false);\n  const showNotification = (message, type = 'success') => {\n    setNotification({\n      message,\n      type\n    });\n    setTimeout(() => setNotification(null), 5000);\n  };\n  const handleCreatePerson = () => {\n    setSelectedPerson(null);\n    setCurrentView('create');\n  };\n  const handleEditPerson = person => {\n    setSelectedPerson(person);\n    setCurrentView('edit');\n  };\n  const handlePersonSubmit = result => {\n    const action = currentView === 'create' ? 'created' : 'updated';\n    showNotification(`Person ${action} successfully!`);\n    setCurrentView('list');\n    setSelectedPerson(null);\n  };\n  const handleFormBuilderOpen = () => {\n    setCurrentView('formBuilder');\n  };\n  const handleFormBuilderSave = config => {\n    showNotification(`Form configuration \"${config.name}\" saved successfully!`);\n    setCurrentView('list');\n  };\n  const handleImportOpen = () => {\n    setShowImportModal(true);\n  };\n  const handleImportSuccess = results => {\n    showNotification(`Import completed! ${results.successfulRows} persons imported successfully.`);\n    setShowImportModal(false);\n    // Refresh the person list if we're on the list view\n    if (currentView === 'list') {\n      // The PersonList component should handle refreshing\n    }\n  };\n  const handleImportClose = () => {\n    setShowImportModal(false);\n  };\n  const handleCancel = () => {\n    setCurrentView('list');\n    setSelectedPerson(null);\n    setSelectedForm(null);\n  };\n  const getFormStatistics = () => {\n    return formConfigService.getFormStatistics();\n  };\n  const renderCurrentView = () => {\n    switch (currentView) {\n      case 'formSelection':\n        return /*#__PURE__*/_jsxDEV(FormSelectionView, {\n          onFormSelect: handleFormSelect,\n          onCancel: handleCancel\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 11\n        }, this);\n      case 'create':\n        return /*#__PURE__*/_jsxDEV(DynamicPersonForm, {\n          mode: \"create\",\n          onSubmit: handlePersonSubmit,\n          onCancel: handleCancel\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 11\n        }, this);\n      case 'edit':\n        return /*#__PURE__*/_jsxDEV(DynamicPersonForm, {\n          mode: \"edit\",\n          initialData: selectedPerson,\n          onSubmit: handlePersonSubmit,\n          onCancel: handleCancel\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 94,\n          columnNumber: 11\n        }, this);\n      case 'formBuilder':\n        return /*#__PURE__*/_jsxDEV(FormBuilder, {\n          onSave: handleFormBuilderSave,\n          onCancel: handleCancel\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 104,\n          columnNumber: 11\n        }, this);\n      case 'list':\n      default:\n        return /*#__PURE__*/_jsxDEV(PersonList, {\n          onCreatePerson: handleCreatePerson,\n          onEditPerson: handleEditPerson,\n          onFormBuilder: handleFormBuilderOpen,\n          onImportPersons: handleImportOpen\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 113,\n          columnNumber: 11\n        }, this);\n    }\n  };\n  const statistics = getFormStatistics();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"person-management\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"management-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          children: \"Person Management System\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"header-stats\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"stat-label\",\n              children: \"Total Forms:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 133,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"stat-value\",\n              children: statistics.totalForms\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 134,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"stat-label\",\n              children: \"Category Forms:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 137,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"stat-value\",\n              children: statistics.categoryForms\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 138,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 136,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"stat-label\",\n              children: \"SubCategory Forms:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 141,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"stat-value\",\n              children: statistics.subCategoryForms\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 142,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 140,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 129,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-nav\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setCurrentView('list'),\n          className: `nav-btn ${currentView === 'list' ? 'active' : ''}`,\n          children: \"\\uD83D\\uDCCB Person List\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 149,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleCreatePerson,\n          className: `nav-btn ${currentView === 'create' ? 'active' : ''}`,\n          children: \"\\u2795 Create Person\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 155,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleFormBuilderOpen,\n          className: `nav-btn ${currentView === 'formBuilder' ? 'active' : ''}`,\n          children: \"\\uD83D\\uDD27 Form Builder\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 161,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleImportOpen,\n          className: \"nav-btn\",\n          children: \"\\uD83D\\uDCE5 Import Persons\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 167,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 148,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 128,\n      columnNumber: 7\n    }, this), notification && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `notification ${notification.type}`,\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        children: notification.message\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 179,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => setNotification(null),\n        className: \"notification-close\",\n        children: \"\\xD7\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 180,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 178,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"management-content\",\n      children: renderCurrentView()\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 187,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"management-footer\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"footer-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"footer-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            children: \"Quick Actions\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"quick-actions\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleCreatePerson,\n              className: \"quick-action-btn\",\n              children: \"Create New Person\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 197,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleFormBuilderOpen,\n              className: \"quick-action-btn\",\n              children: \"Design Custom Form\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 200,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleImportOpen,\n              className: \"quick-action-btn\",\n              children: \"Import Persons from File\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 203,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 196,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"footer-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            children: \"Form Statistics\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 210,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"footer-stats\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"footer-stat\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Average Fields per Form:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 213,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: statistics.averageFieldCount\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 214,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 212,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"footer-stat\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Most Used Fields:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 217,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: statistics.mostUsedFields.slice(0, 3).map(f => f.field).join(', ')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 218,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 216,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 211,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 209,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"footer-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            children: \"System Info\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 224,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"footer-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: \"Dynamic Form Builder v1.0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 226,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: \"React.js Frontend\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 227,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: \"Local Storage Configuration\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 228,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 225,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 223,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 193,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 192,\n      columnNumber: 7\n    }, this), showImportModal && /*#__PURE__*/_jsxDEV(ImportPersons, {\n      onClose: handleImportClose,\n      onSuccess: handleImportSuccess\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 236,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 126,\n    columnNumber: 5\n  }, this);\n};\n_s(PersonManagement, \"JoZRaQBbva0YxvBMychW+zke+vc=\");\n_c = PersonManagement;\nexport default PersonManagement;\nvar _c;\n$RefreshReg$(_c, \"PersonManagement\");", "map": {"version": 3, "names": ["React", "useState", "FormBuilder", "DynamicPersonForm", "PersonList", "<PERSON><PERSON>rt<PERSON><PERSON><PERSON>", "formConfigService", "jsxDEV", "_jsxDEV", "PersonManagement", "_s", "current<PERSON>iew", "set<PERSON><PERSON><PERSON>View", "<PERSON><PERSON><PERSON>", "setSelected<PERSON><PERSON>", "notification", "setNotification", "showImportModal", "setShowImportModal", "showNotification", "message", "type", "setTimeout", "handleCreate<PERSON>erson", "handleEditPerson", "person", "handlePersonSubmit", "result", "action", "handleFormBuilderOpen", "handleFormBuilderSave", "config", "name", "handleImportOpen", "handleImportSuccess", "results", "successfulRows", "handleImportClose", "handleCancel", "setSelectedForm", "getFormStatistics", "renderCurrentView", "FormSelectionView", "onFormSelect", "handleFormSelect", "onCancel", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "mode", "onSubmit", "initialData", "onSave", "on<PERSON><PERSON><PERSON><PERSON>", "onEdit<PERSON>erson", "onFormBuilder", "onImport<PERSON>ersons", "statistics", "className", "children", "totalForms", "categoryForms", "subCategoryForms", "onClick", "averageFieldCount", "mostUsedFields", "slice", "map", "f", "field", "join", "onClose", "onSuccess", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/Advanced/Advanced CRM filed all use/frontend/src/components/PersonManagement.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport FormBuilder from './forms/FormBuilder';\nimport DynamicPersonForm from './forms/DynamicPersonForm';\nimport PersonList from './PersonList';\nimport ImportPersons from './import/ImportPersons';\nimport formConfigService from '../services/formConfigService';\nimport './PersonManagement.css';\n\nconst PersonManagement = () => {\n  const [currentView, setCurrentView] = useState('list'); // 'list', 'create', 'edit', 'formBuilder', 'import'\n  const [selectedPerson, setSelectedPerson] = useState(null);\n  const [notification, setNotification] = useState(null);\n  const [showImportModal, setShowImportModal] = useState(false);\n\n  const showNotification = (message, type = 'success') => {\n    setNotification({ message, type });\n    setTimeout(() => setNotification(null), 5000);\n  };\n\n  const handleCreatePerson = () => {\n    setSelectedPerson(null);\n    setCurrentView('create');\n  };\n\n  const handleEditPerson = (person) => {\n    setSelectedPerson(person);\n    setCurrentView('edit');\n  };\n\n  const handlePersonSubmit = (result) => {\n    const action = currentView === 'create' ? 'created' : 'updated';\n    showNotification(`Person ${action} successfully!`);\n    setCurrentView('list');\n    setSelectedPerson(null);\n  };\n\n  const handleFormBuilderOpen = () => {\n    setCurrentView('formBuilder');\n  };\n\n  const handleFormBuilderSave = (config) => {\n    showNotification(`Form configuration \"${config.name}\" saved successfully!`);\n    setCurrentView('list');\n  };\n\n  const handleImportOpen = () => {\n    setShowImportModal(true);\n  };\n\n  const handleImportSuccess = (results) => {\n    showNotification(`Import completed! ${results.successfulRows} persons imported successfully.`);\n    setShowImportModal(false);\n    // Refresh the person list if we're on the list view\n    if (currentView === 'list') {\n      // The PersonList component should handle refreshing\n    }\n  };\n\n  const handleImportClose = () => {\n    setShowImportModal(false);\n  };\n\n  const handleCancel = () => {\n    setCurrentView('list');\n    setSelectedPerson(null);\n    setSelectedForm(null);\n  };\n\n  const getFormStatistics = () => {\n    return formConfigService.getFormStatistics();\n  };\n\n  const renderCurrentView = () => {\n    switch (currentView) {\n      case 'formSelection':\n        return (\n          <FormSelectionView\n            onFormSelect={handleFormSelect}\n            onCancel={handleCancel}\n          />\n        );\n\n      case 'create':\n        return (\n          <DynamicPersonForm\n            mode=\"create\"\n            onSubmit={handlePersonSubmit}\n            onCancel={handleCancel}\n          />\n        );\n      \n      case 'edit':\n        return (\n          <DynamicPersonForm\n            mode=\"edit\"\n            initialData={selectedPerson}\n            onSubmit={handlePersonSubmit}\n            onCancel={handleCancel}\n          />\n        );\n      \n      case 'formBuilder':\n        return (\n          <FormBuilder\n            onSave={handleFormBuilderSave}\n            onCancel={handleCancel}\n          />\n        );\n      \n      case 'list':\n      default:\n        return (\n          <PersonList\n            onCreatePerson={handleCreatePerson}\n            onEditPerson={handleEditPerson}\n            onFormBuilder={handleFormBuilderOpen}\n            onImportPersons={handleImportOpen}\n          />\n        );\n    }\n  };\n\n  const statistics = getFormStatistics();\n\n  return (\n    <div className=\"person-management\">\n      {/* Header */}\n      <div className=\"management-header\">\n        <div className=\"header-content\">\n          <h1>Person Management System</h1>\n          <div className=\"header-stats\">\n            <div className=\"stat-item\">\n              <span className=\"stat-label\">Total Forms:</span>\n              <span className=\"stat-value\">{statistics.totalForms}</span>\n            </div>\n            <div className=\"stat-item\">\n              <span className=\"stat-label\">Category Forms:</span>\n              <span className=\"stat-value\">{statistics.categoryForms}</span>\n            </div>\n            <div className=\"stat-item\">\n              <span className=\"stat-label\">SubCategory Forms:</span>\n              <span className=\"stat-value\">{statistics.subCategoryForms}</span>\n            </div>\n          </div>\n        </div>\n        \n        {/* Navigation */}\n        <div className=\"header-nav\">\n          <button\n            onClick={() => setCurrentView('list')}\n            className={`nav-btn ${currentView === 'list' ? 'active' : ''}`}\n          >\n            📋 Person List\n          </button>\n          <button\n            onClick={handleCreatePerson}\n            className={`nav-btn ${currentView === 'create' ? 'active' : ''}`}\n          >\n            ➕ Create Person\n          </button>\n          <button\n            onClick={handleFormBuilderOpen}\n            className={`nav-btn ${currentView === 'formBuilder' ? 'active' : ''}`}\n          >\n            🔧 Form Builder\n          </button>\n          <button\n            onClick={handleImportOpen}\n            className=\"nav-btn\"\n          >\n            📥 Import Persons\n          </button>\n        </div>\n      </div>\n\n      {/* Notification */}\n      {notification && (\n        <div className={`notification ${notification.type}`}>\n          <span>{notification.message}</span>\n          <button onClick={() => setNotification(null)} className=\"notification-close\">\n            ×\n          </button>\n        </div>\n      )}\n\n      {/* Main Content */}\n      <div className=\"management-content\">\n        {renderCurrentView()}\n      </div>\n\n      {/* Footer */}\n      <div className=\"management-footer\">\n        <div className=\"footer-content\">\n          <div className=\"footer-section\">\n            <h4>Quick Actions</h4>\n            <div className=\"quick-actions\">\n              <button onClick={handleCreatePerson} className=\"quick-action-btn\">\n                Create New Person\n              </button>\n              <button onClick={handleFormBuilderOpen} className=\"quick-action-btn\">\n                Design Custom Form\n              </button>\n              <button onClick={handleImportOpen} className=\"quick-action-btn\">\n                Import Persons from File\n              </button>\n            </div>\n          </div>\n          \n          <div className=\"footer-section\">\n            <h4>Form Statistics</h4>\n            <div className=\"footer-stats\">\n              <div className=\"footer-stat\">\n                <span>Average Fields per Form:</span>\n                <span>{statistics.averageFieldCount}</span>\n              </div>\n              <div className=\"footer-stat\">\n                <span>Most Used Fields:</span>\n                <span>{statistics.mostUsedFields.slice(0, 3).map(f => f.field).join(', ')}</span>\n              </div>\n            </div>\n          </div>\n          \n          <div className=\"footer-section\">\n            <h4>System Info</h4>\n            <div className=\"footer-info\">\n              <div>Dynamic Form Builder v1.0</div>\n              <div>React.js Frontend</div>\n              <div>Local Storage Configuration</div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Import Modal */}\n      {showImportModal && (\n        <ImportPersons\n          onClose={handleImportClose}\n          onSuccess={handleImportSuccess}\n        />\n      )}\n    </div>\n  );\n};\n\nexport default PersonManagement;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,iBAAiB,MAAM,2BAA2B;AACzD,OAAOC,UAAU,MAAM,cAAc;AACrC,OAAOC,aAAa,MAAM,wBAAwB;AAClD,OAAOC,iBAAiB,MAAM,+BAA+B;AAC7D,OAAO,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhC,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC7B,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGX,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;EACxD,MAAM,CAACY,cAAc,EAAEC,iBAAiB,CAAC,GAAGb,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACc,YAAY,EAAEC,eAAe,CAAC,GAAGf,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACgB,eAAe,EAAEC,kBAAkB,CAAC,GAAGjB,QAAQ,CAAC,KAAK,CAAC;EAE7D,MAAMkB,gBAAgB,GAAGA,CAACC,OAAO,EAAEC,IAAI,GAAG,SAAS,KAAK;IACtDL,eAAe,CAAC;MAAEI,OAAO;MAAEC;IAAK,CAAC,CAAC;IAClCC,UAAU,CAAC,MAAMN,eAAe,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC;EAC/C,CAAC;EAED,MAAMO,kBAAkB,GAAGA,CAAA,KAAM;IAC/BT,iBAAiB,CAAC,IAAI,CAAC;IACvBF,cAAc,CAAC,QAAQ,CAAC;EAC1B,CAAC;EAED,MAAMY,gBAAgB,GAAIC,MAAM,IAAK;IACnCX,iBAAiB,CAACW,MAAM,CAAC;IACzBb,cAAc,CAAC,MAAM,CAAC;EACxB,CAAC;EAED,MAAMc,kBAAkB,GAAIC,MAAM,IAAK;IACrC,MAAMC,MAAM,GAAGjB,WAAW,KAAK,QAAQ,GAAG,SAAS,GAAG,SAAS;IAC/DQ,gBAAgB,CAAC,UAAUS,MAAM,gBAAgB,CAAC;IAClDhB,cAAc,CAAC,MAAM,CAAC;IACtBE,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAMe,qBAAqB,GAAGA,CAAA,KAAM;IAClCjB,cAAc,CAAC,aAAa,CAAC;EAC/B,CAAC;EAED,MAAMkB,qBAAqB,GAAIC,MAAM,IAAK;IACxCZ,gBAAgB,CAAC,uBAAuBY,MAAM,CAACC,IAAI,uBAAuB,CAAC;IAC3EpB,cAAc,CAAC,MAAM,CAAC;EACxB,CAAC;EAED,MAAMqB,gBAAgB,GAAGA,CAAA,KAAM;IAC7Bf,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,MAAMgB,mBAAmB,GAAIC,OAAO,IAAK;IACvChB,gBAAgB,CAAC,qBAAqBgB,OAAO,CAACC,cAAc,iCAAiC,CAAC;IAC9FlB,kBAAkB,CAAC,KAAK,CAAC;IACzB;IACA,IAAIP,WAAW,KAAK,MAAM,EAAE;MAC1B;IAAA;EAEJ,CAAC;EAED,MAAM0B,iBAAiB,GAAGA,CAAA,KAAM;IAC9BnB,kBAAkB,CAAC,KAAK,CAAC;EAC3B,CAAC;EAED,MAAMoB,YAAY,GAAGA,CAAA,KAAM;IACzB1B,cAAc,CAAC,MAAM,CAAC;IACtBE,iBAAiB,CAAC,IAAI,CAAC;IACvByB,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,OAAOlC,iBAAiB,CAACkC,iBAAiB,CAAC,CAAC;EAC9C,CAAC;EAED,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,QAAQ9B,WAAW;MACjB,KAAK,eAAe;QAClB,oBACEH,OAAA,CAACkC,iBAAiB;UAChBC,YAAY,EAAEC,gBAAiB;UAC/BC,QAAQ,EAAEP;QAAa;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC;MAGN,KAAK,QAAQ;QACX,oBACEzC,OAAA,CAACL,iBAAiB;UAChB+C,IAAI,EAAC,QAAQ;UACbC,QAAQ,EAAEzB,kBAAmB;UAC7BmB,QAAQ,EAAEP;QAAa;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC;MAGN,KAAK,MAAM;QACT,oBACEzC,OAAA,CAACL,iBAAiB;UAChB+C,IAAI,EAAC,MAAM;UACXE,WAAW,EAAEvC,cAAe;UAC5BsC,QAAQ,EAAEzB,kBAAmB;UAC7BmB,QAAQ,EAAEP;QAAa;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC;MAGN,KAAK,aAAa;QAChB,oBACEzC,OAAA,CAACN,WAAW;UACVmD,MAAM,EAAEvB,qBAAsB;UAC9Be,QAAQ,EAAEP;QAAa;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC;MAGN,KAAK,MAAM;MACX;QACE,oBACEzC,OAAA,CAACJ,UAAU;UACTkD,cAAc,EAAE/B,kBAAmB;UACnCgC,YAAY,EAAE/B,gBAAiB;UAC/BgC,aAAa,EAAE3B,qBAAsB;UACrC4B,eAAe,EAAExB;QAAiB;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC;IAER;EACF,CAAC;EAED,MAAMS,UAAU,GAAGlB,iBAAiB,CAAC,CAAC;EAEtC,oBACEhC,OAAA;IAAKmD,SAAS,EAAC,mBAAmB;IAAAC,QAAA,gBAEhCpD,OAAA;MAAKmD,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChCpD,OAAA;QAAKmD,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BpD,OAAA;UAAAoD,QAAA,EAAI;QAAwB;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACjCzC,OAAA;UAAKmD,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BpD,OAAA;YAAKmD,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBpD,OAAA;cAAMmD,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAY;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAChDzC,OAAA;cAAMmD,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAEF,UAAU,CAACG;YAAU;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxD,CAAC,eACNzC,OAAA;YAAKmD,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBpD,OAAA;cAAMmD,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAe;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACnDzC,OAAA;cAAMmD,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAEF,UAAU,CAACI;YAAa;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D,CAAC,eACNzC,OAAA;YAAKmD,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBpD,OAAA;cAAMmD,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAkB;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACtDzC,OAAA;cAAMmD,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAEF,UAAU,CAACK;YAAgB;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNzC,OAAA;QAAKmD,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBpD,OAAA;UACEwD,OAAO,EAAEA,CAAA,KAAMpD,cAAc,CAAC,MAAM,CAAE;UACtC+C,SAAS,EAAE,WAAWhD,WAAW,KAAK,MAAM,GAAG,QAAQ,GAAG,EAAE,EAAG;UAAAiD,QAAA,EAChE;QAED;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTzC,OAAA;UACEwD,OAAO,EAAEzC,kBAAmB;UAC5BoC,SAAS,EAAE,WAAWhD,WAAW,KAAK,QAAQ,GAAG,QAAQ,GAAG,EAAE,EAAG;UAAAiD,QAAA,EAClE;QAED;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTzC,OAAA;UACEwD,OAAO,EAAEnC,qBAAsB;UAC/B8B,SAAS,EAAE,WAAWhD,WAAW,KAAK,aAAa,GAAG,QAAQ,GAAG,EAAE,EAAG;UAAAiD,QAAA,EACvE;QAED;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTzC,OAAA;UACEwD,OAAO,EAAE/B,gBAAiB;UAC1B0B,SAAS,EAAC,SAAS;UAAAC,QAAA,EACpB;QAED;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLlC,YAAY,iBACXP,OAAA;MAAKmD,SAAS,EAAE,gBAAgB5C,YAAY,CAACM,IAAI,EAAG;MAAAuC,QAAA,gBAClDpD,OAAA;QAAAoD,QAAA,EAAO7C,YAAY,CAACK;MAAO;QAAA0B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACnCzC,OAAA;QAAQwD,OAAO,EAAEA,CAAA,KAAMhD,eAAe,CAAC,IAAI,CAAE;QAAC2C,SAAS,EAAC,oBAAoB;QAAAC,QAAA,EAAC;MAE7E;QAAAd,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CACN,eAGDzC,OAAA;MAAKmD,SAAS,EAAC,oBAAoB;MAAAC,QAAA,EAChCnB,iBAAiB,CAAC;IAAC;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CAAC,eAGNzC,OAAA;MAAKmD,SAAS,EAAC,mBAAmB;MAAAC,QAAA,eAChCpD,OAAA;QAAKmD,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BpD,OAAA;UAAKmD,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BpD,OAAA;YAAAoD,QAAA,EAAI;UAAa;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACtBzC,OAAA;YAAKmD,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5BpD,OAAA;cAAQwD,OAAO,EAAEzC,kBAAmB;cAACoC,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAC;YAElE;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTzC,OAAA;cAAQwD,OAAO,EAAEnC,qBAAsB;cAAC8B,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAC;YAErE;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTzC,OAAA;cAAQwD,OAAO,EAAE/B,gBAAiB;cAAC0B,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAC;YAEhE;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENzC,OAAA;UAAKmD,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BpD,OAAA;YAAAoD,QAAA,EAAI;UAAe;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxBzC,OAAA;YAAKmD,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BpD,OAAA;cAAKmD,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BpD,OAAA;gBAAAoD,QAAA,EAAM;cAAwB;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACrCzC,OAAA;gBAAAoD,QAAA,EAAOF,UAAU,CAACO;cAAiB;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CAAC,eACNzC,OAAA;cAAKmD,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BpD,OAAA;gBAAAoD,QAAA,EAAM;cAAiB;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC9BzC,OAAA;gBAAAoD,QAAA,EAAOF,UAAU,CAACQ,cAAc,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACC,KAAK,CAAC,CAACC,IAAI,CAAC,IAAI;cAAC;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9E,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENzC,OAAA;UAAKmD,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BpD,OAAA;YAAAoD,QAAA,EAAI;UAAW;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpBzC,OAAA;YAAKmD,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BpD,OAAA;cAAAoD,QAAA,EAAK;YAAyB;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACpCzC,OAAA;cAAAoD,QAAA,EAAK;YAAiB;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC5BzC,OAAA;cAAAoD,QAAA,EAAK;YAA2B;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLhC,eAAe,iBACdT,OAAA,CAACH,aAAa;MACZmE,OAAO,EAAEnC,iBAAkB;MAC3BoC,SAAS,EAAEvC;IAAoB;MAAAY,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChC,CACF;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACvC,EAAA,CA1OID,gBAAgB;AAAAiE,EAAA,GAAhBjE,gBAAgB;AA4OtB,eAAeA,gBAAgB;AAAC,IAAAiE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}