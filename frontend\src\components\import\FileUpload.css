.file-upload {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.upload-section h3,
.settings-section h3 {
  margin: 0 0 1rem 0;
  color: #495057;
  font-size: 1.25rem;
}

.template-section {
  background-color: #e7f3ff;
  border: 1px solid #b3d7ff;
  border-radius: 4px;
  padding: 1rem;
  margin-bottom: 1.5rem;
}

.template-section p {
  margin: 0 0 0.5rem 0;
  color: #0056b3;
}

.drop-zone {
  border: 2px dashed #ced4da;
  border-radius: 8px;
  padding: 3rem 2rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: #f8f9fa;
}

.drop-zone:hover {
  border-color: #007bff;
  background-color: #e7f3ff;
}

.drop-zone.active {
  border-color: #007bff;
  background-color: #e7f3ff;
  border-style: solid;
}

.drop-zone.has-file {
  border-color: #28a745;
  background-color: #d4edda;
}

.drop-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.drop-icon {
  font-size: 3rem;
  color: #6c757d;
}

.drop-text p {
  margin: 0.25rem 0;
  color: #6c757d;
}

.drop-text p:first-child {
  font-size: 1.1rem;
  color: #495057;
}

.file-selected {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background-color: white;
  border-radius: 4px;
  border: 1px solid #28a745;
}

.file-icon {
  font-size: 2rem;
  color: #28a745;
}

.file-details {
  flex: 1;
  text-align: left;
}

.file-name {
  font-weight: 600;
  color: #495057;
  margin-bottom: 0.25rem;
}

.file-meta {
  font-size: 0.875rem;
  color: #6c757d;
}

.file-meta span {
  margin: 0 0.5rem;
}

.remove-file {
  background: none;
  border: none;
  font-size: 1.25rem;
  cursor: pointer;
  color: #dc3545;
  padding: 0.25rem;
  border-radius: 4px;
}

.remove-file:hover {
  background-color: #f8d7da;
}

.settings-section {
  background-color: #f8f9fa;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  padding: 1.5rem;
}

.setting-group {
  margin-bottom: 1.5rem;
}

.setting-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: #495057;
}

.setting-group select {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 1rem;
  background-color: white;
}

.setting-group input[type="checkbox"] {
  margin-right: 0.5rem;
  width: 1.2rem;
  height: 1.2rem;
}

.setting-help {
  margin-top: 0.5rem;
  font-size: 0.875rem;
  color: #6c757d;
  font-style: italic;
}

.upload-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #e1e5e9;
}

.error-message {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 1rem;
  padding: 1rem;
  background-color: #f8d7da;
  border: 1px solid #f5c6cb;
  border-radius: 4px;
  color: #721c24;
}

.error-icon {
  font-size: 1.25rem;
}

.btn {
  padding: 0.75rem 1.5rem;
  border: 1px solid transparent;
  border-radius: 4px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  text-decoration: none;
  display: inline-block;
  transition: all 0.2s ease;
}

.btn-primary {
  background-color: #007bff;
  border-color: #007bff;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background-color: #0056b3;
  border-color: #0056b3;
}

.btn-primary:disabled {
  background-color: #6c757d;
  border-color: #6c757d;
  cursor: not-allowed;
  opacity: 0.65;
}

.btn-outline {
  background-color: transparent;
  border-color: #6c757d;
  color: #6c757d;
}

.btn-outline:hover {
  background-color: #6c757d;
  color: white;
}

/* Responsive design */
@media (max-width: 768px) {
  .file-upload {
    gap: 1.5rem;
  }
  
  .drop-zone {
    padding: 2rem 1rem;
  }
  
  .drop-icon {
    font-size: 2rem;
  }
  
  .file-selected {
    flex-direction: column;
    text-align: center;
  }
  
  .settings-section {
    padding: 1rem;
  }
  
  .upload-actions {
    justify-content: stretch;
  }
  
  .btn {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .drop-zone {
    padding: 1.5rem 0.5rem;
  }
  
  .drop-text p {
    font-size: 0.875rem;
  }
  
  .template-section {
    padding: 0.75rem;
  }
  
  .settings-section {
    padding: 0.75rem;
  }
}
