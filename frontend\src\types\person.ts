// Enums
export enum PersonNature {
  Business = 1,
  Corporate = 2,
  Agriculture = 3,
  Individual = 4
}

export enum Gender {
  Male = 1,
  Female = 2,
  Other = 3
}

export enum WorkingProfile {
  Business = 1,
  Corporate = 2,
  Agriculture = 3
}

// Base interfaces
export interface Division {
  id: number;
  name: string;
  createdAt: string;
  updatedAt: string;
  categories?: Category[];
}

export interface Category {
  id: number;
  divisionId: number;
  name: string;
  createdAt: string;
  updatedAt: string;
  division?: Division;
  subCategories?: SubCategory[];
}

export interface SubCategory {
  id: number;
  categoryId: number;
  name: string;
  createdAt: string;
  updatedAt: string;
  category?: Category;
}

// Person interfaces
export interface Person {
  id: number;
  divisionId: number;
  categoryId: number;
  subCategoryId?: number;
  name: string;
  mobileNumber: string;
  nature: PersonNature;
  natureDisplay: string;
  gender?: Gender;
  genderDisplay?: string;
  alternateNumbers: string[];
  primaryEmailId: string;
  alternateEmailIds: string[];
  website: string;
  dateOfBirth?: string;
  isMarried?: boolean;
  dateOfMarriage?: string;
  workingState: string;
  domesticState: string;
  district: string;
  address: string;
  workingArea: string;
  hasAssociate: boolean;
  associateName: string;
  associateRelation: string;
  associateMobile: string;
  usingWebsite: boolean;
  websiteLink: string;
  usingCRMApp: boolean;
  crmAppLink: string;
  transactionValue?: number;
  reraRegistrationNumber: string;
  workingProfiles: WorkingProfile[];
  workingProfilesDisplay: string[];
  starRating?: number;
  source: string;
  remarks: string;
  firmName: string;
  numberOfOffices?: number;
  numberOfBranches?: number;
  totalEmployeeStrength?: number;
  authorizedPersonName: string;
  authorizedPersonEmail: string;
  designation: string;
  marketingContact: string;
  marketingDesignation: string;
  placeOfPosting: string;
  department: string;
  createdAt: string;
  updatedAt: string;
  division?: Division;
  category?: Category;
  subCategory?: SubCategory;
}

export interface CreatePersonRequest {
  divisionId: number;
  categoryId: number;
  subCategoryId?: number;
  name: string;
  mobileNumber: string;
  nature: PersonNature;
  gender?: Gender;
  alternateNumbers: string[];
  primaryEmailId: string;
  alternateEmailIds: string[];
  website: string;
  dateOfBirth?: string;
  isMarried?: boolean;
  dateOfMarriage?: string;
  workingState: string;
  domesticState: string;
  district: string;
  address: string;
  workingArea: string;
  hasAssociate: boolean;
  associateName: string;
  associateRelation: string;
  associateMobile: string;
  usingWebsite: boolean;
  websiteLink: string;
  usingCRMApp: boolean;
  crmAppLink: string;
  transactionValue?: number;
  reraRegistrationNumber: string;
  workingProfiles: WorkingProfile[];
  starRating?: number;
  source: string;
  remarks: string;
  firmName: string;
  numberOfOffices?: number;
  numberOfBranches?: number;
  totalEmployeeStrength?: number;
  authorizedPersonName: string;
  authorizedPersonEmail: string;
  designation: string;
  marketingContact: string;
  marketingDesignation: string;
  placeOfPosting: string;
  department: string;
}

export interface UpdatePersonRequest {
  divisionId?: number;
  categoryId?: number;
  subCategoryId?: number;
  name?: string;
  mobileNumber?: string;
  nature?: PersonNature;
  gender?: Gender;
  alternateNumbers?: string[];
  primaryEmailId?: string;
  alternateEmailIds?: string[];
  website?: string;
  dateOfBirth?: string;
  isMarried?: boolean;
  dateOfMarriage?: string;
  workingState?: string;
  domesticState?: string;
  district?: string;
  address?: string;
  workingArea?: string;
  hasAssociate?: boolean;
  associateName?: string;
  associateRelation?: string;
  associateMobile?: string;
  usingWebsite?: boolean;
  websiteLink?: string;
  usingCRMApp?: boolean;
  crmAppLink?: string;
  transactionValue?: number;
  reraRegistrationNumber?: string;
  workingProfiles?: WorkingProfile[];
  starRating?: number;
  source?: string;
  remarks?: string;
  firmName?: string;
  numberOfOffices?: number;
  numberOfBranches?: number;
  totalEmployeeStrength?: number;
  authorizedPersonName?: string;
  authorizedPersonEmail?: string;
  designation?: string;
  marketingContact?: string;
  marketingDesignation?: string;
  placeOfPosting?: string;
  department?: string;
}

// Search and pagination
export interface PersonSearchRequest {
  page: number;
  pageSize: number;
  name?: string;
  mobileNumber?: string;
  email?: string;
  divisionId?: number;
  categoryId?: number;
  subCategoryId?: number;
  nature?: PersonNature;
  gender?: Gender;
  workingState?: string;
  district?: string;
  firmName?: string;
  minStarRating?: number;
  maxStarRating?: number;
  minTransactionValue?: number;
  maxTransactionValue?: number;
  hasAssociate?: boolean;
  usingWebsite?: boolean;
  usingCRMApp?: boolean;
  createdAfter?: string;
  createdBefore?: string;
  sortBy: string;
  sortDirection: string;
  includeDivision: boolean;
  includeCategory: boolean;
  includeSubCategory: boolean;
}

export interface PersonSearchResponse {
  persons: Person[];
  totalCount: number;
  page: number;
  pageSize: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

// API Error types
export interface ValidationError {
  [key: string]: string[];
}

export interface ApiError {
  type: string;
  message: string;
  details?: string;
  errors?: ValidationError;
  timestamp: string;
  traceId?: string;
}
