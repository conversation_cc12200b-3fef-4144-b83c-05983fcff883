{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Advanced\\\\Advanced CRM filed all use\\\\frontend\\\\src\\\\components\\\\import\\\\FieldMapping.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { PersonFieldDefinitions, getAllPersonFields } from '../../constants/personConstants';\nimport './FieldMapping.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst FieldMapping = ({\n  fileHeaders,\n  onMappingComplete,\n  onBack,\n  error\n}) => {\n  _s();\n  const [mapping, setMapping] = useState({});\n  const [unmappedHeaders, setUnmappedHeaders] = useState([]);\n  const [requiredFieldsStatus, setRequiredFieldsStatus] = useState({});\n  const [autoMappingSuggestions, setAutoMappingSuggestions] = useState({});\n  const [defaultValues, setDefaultValues] = useState({});\n  const personFields = getAllPersonFields();\n  const requiredFields = personFields.filter(field => field.required);\n  useEffect(() => {\n    // Auto-suggest mappings based on header names\n    const suggestions = autoMapFields();\n    setAutoMappingSuggestions(suggestions);\n    setMapping(suggestions);\n    updateUnmappedHeaders(suggestions);\n    updateRequiredFieldsStatus(suggestions);\n  }, [fileHeaders]);\n  const autoMapFields = () => {\n    const suggestions = {};\n    fileHeaders.forEach(header => {\n      const normalizedHeader = header.toLowerCase().trim();\n\n      // Find exact matches first\n      const exactMatch = personFields.find(field => field.label.toLowerCase() === normalizedHeader || field.key.toLowerCase() === normalizedHeader);\n      if (exactMatch) {\n        suggestions[header] = exactMatch.key;\n        return;\n      }\n\n      // Find partial matches\n      const partialMatch = personFields.find(field => {\n        const fieldLabel = field.label.toLowerCase();\n        const fieldKey = field.key.toLowerCase();\n        return fieldLabel.includes(normalizedHeader) || normalizedHeader.includes(fieldLabel) || fieldKey.includes(normalizedHeader) || normalizedHeader.includes(fieldKey);\n      });\n      if (partialMatch) {\n        suggestions[header] = partialMatch.key;\n      }\n    });\n    return suggestions;\n  };\n  const handleMappingChange = (fileHeader, personFieldKey) => {\n    const newMapping = {\n      ...mapping\n    };\n    if (personFieldKey === '') {\n      delete newMapping[fileHeader];\n    } else {\n      // Remove any existing mapping to this person field\n      Object.keys(newMapping).forEach(key => {\n        if (newMapping[key] === personFieldKey && key !== fileHeader) {\n          delete newMapping[key];\n        }\n      });\n      newMapping[fileHeader] = personFieldKey;\n    }\n    setMapping(newMapping);\n    updateUnmappedHeaders(newMapping);\n    updateRequiredFieldsStatus(newMapping);\n  };\n  const updateUnmappedHeaders = currentMapping => {\n    const mapped = Object.keys(currentMapping);\n    const unmapped = fileHeaders.filter(header => !mapped.includes(header));\n    setUnmappedHeaders(unmapped);\n  };\n  const updateRequiredFieldsStatus = currentMapping => {\n    const mappedFields = Object.values(currentMapping);\n    const status = {};\n    requiredFields.forEach(field => {\n      // Field is satisfied if it's mapped from Excel OR has a default value\n      const isMapped = mappedFields.includes(field.key);\n      const hasDefaultValue = defaultValues[field.key] && defaultValues[field.key].trim() !== '';\n      status[field.key] = isMapped || hasDefaultValue;\n    });\n    setRequiredFieldsStatus(status);\n  };\n  const applyAutoMapping = () => {\n    setMapping(autoMappingSuggestions);\n    updateUnmappedHeaders(autoMappingSuggestions);\n    updateRequiredFieldsStatus(autoMappingSuggestions);\n  };\n  const clearAllMappings = () => {\n    setMapping({});\n    updateUnmappedHeaders({});\n    updateRequiredFieldsStatus({});\n  };\n  const handleContinue = () => {\n    // Validate that all required fields are mapped\n    const missingRequired = requiredFields.filter(field => !requiredFieldsStatus[field.key]);\n    if (missingRequired.length > 0) {\n      alert(`Please map the following required fields: ${missingRequired.map(f => f.label).join(', ')}`);\n      return;\n    }\n    onMappingComplete(mapping);\n  };\n  const getPersonFieldByKey = key => {\n    return personFields.find(field => field.key === key);\n  };\n  const getMappedPersonFields = () => {\n    return Object.values(mapping).map(key => getPersonFieldByKey(key)).filter(Boolean);\n  };\n  const getAvailablePersonFields = currentHeader => {\n    const mappedFields = Object.entries(mapping).filter(([header, _]) => header !== currentHeader).map(([_, fieldKey]) => fieldKey);\n    return personFields.filter(field => !mappedFields.includes(field.key));\n  };\n  const groupPersonFieldsBySection = fields => {\n    const sections = {};\n    fields.forEach(field => {\n      const sectionKey = field.section || 'general';\n      if (!sections[sectionKey]) {\n        var _PersonFieldDefinitio;\n        sections[sectionKey] = {\n          title: ((_PersonFieldDefinitio = PersonFieldDefinitions[sectionKey]) === null || _PersonFieldDefinitio === void 0 ? void 0 : _PersonFieldDefinitio.title) || sectionKey,\n          fields: []\n        };\n      }\n      sections[sectionKey].fields.push(field);\n    });\n    return sections;\n  };\n  const renderFieldSelect = fileHeader => {\n    const availableFields = getAvailablePersonFields(fileHeader);\n    const sections = groupPersonFieldsBySection(availableFields);\n    const currentMapping = mapping[fileHeader] || '';\n    return /*#__PURE__*/_jsxDEV(\"select\", {\n      value: currentMapping,\n      onChange: e => handleMappingChange(fileHeader, e.target.value),\n      className: `field-select ${currentMapping ? 'mapped' : 'unmapped'}`,\n      children: [/*#__PURE__*/_jsxDEV(\"option\", {\n        value: \"\",\n        children: \"-- Select Field --\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 167,\n        columnNumber: 9\n      }, this), Object.entries(sections).map(([sectionKey, section]) => /*#__PURE__*/_jsxDEV(\"optgroup\", {\n        label: section.title,\n        children: section.fields.map(field => /*#__PURE__*/_jsxDEV(\"option\", {\n          value: field.key,\n          children: [field.label, \" \", field.required ? '*' : '']\n        }, field.key, true, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 15\n        }, this))\n      }, sectionKey, false, {\n        fileName: _jsxFileName,\n        lineNumber: 169,\n        columnNumber: 11\n      }, this))]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 162,\n      columnNumber: 7\n    }, this);\n  };\n  const allRequiredMapped = requiredFields.every(field => requiredFieldsStatus[field.key]);\n  const mappingCount = Object.keys(mapping).length;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"field-mapping\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mapping-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"Map File Columns to Person Fields\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 187,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Match your file columns with the corresponding person fields. Required fields are marked with *\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 188,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mapping-stats\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"stat-value\",\n            children: mappingCount\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 192,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"stat-label\",\n            children: \"Mapped Fields\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 193,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 191,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"stat-value\",\n            children: unmappedHeaders.length\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 196,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"stat-label\",\n            children: \"Unmapped Columns\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 195,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: `stat-value ${allRequiredMapped ? 'success' : 'error'}`,\n            children: [Object.values(requiredFieldsStatus).filter(Boolean).length, \"/\", requiredFields.length]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"stat-label\",\n            children: \"Required Fields\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 203,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 199,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 190,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mapping-actions\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: applyAutoMapping,\n          className: \"btn btn-outline\",\n          children: \"\\uD83E\\uDD16 Auto Map\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 208,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: clearAllMappings,\n          className: \"btn btn-outline\",\n          children: \"\\uD83D\\uDDD1\\uFE0F Clear All\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 211,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 207,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 186,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"required-fields-status\",\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        children: \"Required Fields Status\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 219,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"required-fields-grid\",\n        children: requiredFields.map(field => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `required-field ${requiredFieldsStatus[field.key] ? 'mapped' : 'unmapped'}`,\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"field-name\",\n            children: field.label\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"field-status\",\n            children: requiredFieldsStatus[field.key] ? '✅' : '❌'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 227,\n            columnNumber: 15\n          }, this)]\n        }, field.key, true, {\n          fileName: _jsxFileName,\n          lineNumber: 222,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 220,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 218,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mapping-table-container\",\n      children: /*#__PURE__*/_jsxDEV(\"table\", {\n        className: \"mapping-table\",\n        children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n          children: /*#__PURE__*/_jsxDEV(\"tr\", {\n            children: [/*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"File Column\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 240,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"Sample Data\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 241,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"Maps To\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 242,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"Field Type\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 243,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"Required\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 244,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 239,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 238,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n          children: fileHeaders.map(header => {\n            const mappedField = mapping[header] ? getPersonFieldByKey(mapping[header]) : null;\n            return /*#__PURE__*/_jsxDEV(\"tr\", {\n              className: mapping[header] ? 'mapped-row' : 'unmapped-row',\n              children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"file-header\",\n                  children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: header\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 254,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 253,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 252,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"sample-data\",\n                  children: /*#__PURE__*/_jsxDEV(\"em\", {\n                    children: \"Sample data preview\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 260,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 258,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 257,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: renderFieldSelect(header)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 263,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"field-type\",\n                  children: mappedField ? /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"type-badge\",\n                    children: mappedField.type\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 269,\n                    columnNumber: 25\n                  }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"no-mapping\",\n                    children: \"-\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 271,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 267,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 266,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"required-indicator\",\n                  children: mappedField !== null && mappedField !== void 0 && mappedField.required ? /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"required-badge\",\n                    children: \"Required\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 278,\n                    columnNumber: 25\n                  }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"optional-badge\",\n                    children: \"Optional\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 280,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 276,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 275,\n                columnNumber: 19\n              }, this)]\n            }, header, true, {\n              fileName: _jsxFileName,\n              lineNumber: 251,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 247,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 237,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 236,\n      columnNumber: 7\n    }, this), unmappedHeaders.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"unmapped-warning\",\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        children: \"\\u26A0\\uFE0F Unmapped Columns\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 294,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"The following columns from your file will be ignored during import:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 295,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"unmapped-list\",\n        children: unmappedHeaders.map(header => /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"unmapped-header\",\n          children: header\n        }, header, false, {\n          fileName: _jsxFileName,\n          lineNumber: 298,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 296,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 293,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mapping-summary\",\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        children: \"Mapping Summary\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 306,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"summary-content\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"summary-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n            children: [\"Mapped Fields (\", mappingCount, \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 309,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mapped-fields-list\",\n            children: getMappedPersonFields().map(field => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mapped-field-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"field-label\",\n                children: field.label\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 313,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `field-badge ${field.required ? 'required' : 'optional'}`,\n                children: field.required ? 'Required' : 'Optional'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 314,\n                columnNumber: 19\n              }, this)]\n            }, field.key, true, {\n              fileName: _jsxFileName,\n              lineNumber: 312,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 310,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 308,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 307,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 305,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mapping-navigation\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: onBack,\n        className: \"btn btn-outline\",\n        children: \"\\u2190 Back to Upload\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 326,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handleContinue,\n        disabled: !allRequiredMapped,\n        className: \"btn btn-primary\",\n        children: \"Continue to Import \\u2192\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 329,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 325,\n      columnNumber: 7\n    }, this), !allRequiredMapped && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"validation-error\",\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"error-icon\",\n        children: \"\\u26A0\\uFE0F\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 340,\n        columnNumber: 11\n      }, this), \"Please map all required fields before continuing\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 339,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 185,\n    columnNumber: 5\n  }, this);\n};\n_s(FieldMapping, \"t29/vcSLj8MSUT3UW6tchgSGeTY=\");\n_c = FieldMapping;\nexport default FieldMapping;\nvar _c;\n$RefreshReg$(_c, \"FieldMapping\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "PersonFieldDefinitions", "<PERSON><PERSON><PERSON><PERSON><PERSON>F<PERSON>s", "jsxDEV", "_jsxDEV", "FieldMapping", "fileHeaders", "onMappingComplete", "onBack", "error", "_s", "mapping", "setMapping", "unmappedHeaders", "setUnmappedHeaders", "requiredFields<PERSON><PERSON>us", "setRequiredFieldsStatus", "autoMappingSuggestions", "setAutoMappingSuggestions", "defaultValues", "set<PERSON>efault<PERSON><PERSON><PERSON>", "personFields", "requiredFields", "filter", "field", "required", "suggestions", "autoMapFields", "updateUnmappedHeaders", "updateRequiredFieldsStatus", "for<PERSON>ach", "header", "normalizedHeader", "toLowerCase", "trim", "exactMatch", "find", "label", "key", "partialMatch", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "includes", "handleMappingChange", "fileHeader", "person<PERSON><PERSON><PERSON><PERSON>", "newMapping", "Object", "keys", "currentMapping", "mapped", "unmapped", "<PERSON><PERSON><PERSON>s", "values", "status", "isMapped", "hasDefaultValue", "applyAutoMapping", "clearAllMappings", "handleContinue", "missingRequired", "length", "alert", "map", "f", "join", "getPersonFieldByKey", "get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>s", "Boolean", "getAvailablePersonFields", "<PERSON><PERSON><PERSON><PERSON>", "entries", "_", "groupPersonFieldsBySection", "fields", "sections", "sectionKey", "section", "_PersonFieldDefinitio", "title", "push", "renderFieldSelect", "availableFields", "value", "onChange", "e", "target", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "allRequiredMapped", "every", "mappingCount", "onClick", "mappedField", "type", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/Advanced/Advanced CRM filed all use/frontend/src/components/import/FieldMapping.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { PersonFieldDefinitions, getAllPersonFields } from '../../constants/personConstants';\nimport './FieldMapping.css';\n\nconst FieldMapping = ({ fileHeaders, onMappingComplete, onBack, error }) => {\n  const [mapping, setMapping] = useState({});\n  const [unmappedHeaders, setUnmappedHeaders] = useState([]);\n  const [requiredFieldsStatus, setRequiredFieldsStatus] = useState({});\n  const [autoMappingSuggestions, setAutoMappingSuggestions] = useState({});\n  const [defaultValues, setDefaultValues] = useState({});\n\n  const personFields = getAllPersonFields();\n  const requiredFields = personFields.filter(field => field.required);\n\n  useEffect(() => {\n    // Auto-suggest mappings based on header names\n    const suggestions = autoMapFields();\n    setAutoMappingSuggestions(suggestions);\n    setMapping(suggestions);\n    updateUnmappedHeaders(suggestions);\n    updateRequiredFieldsStatus(suggestions);\n  }, [fileHeaders]);\n\n  const autoMapFields = () => {\n    const suggestions = {};\n    \n    fileHeaders.forEach(header => {\n      const normalizedHeader = header.toLowerCase().trim();\n      \n      // Find exact matches first\n      const exactMatch = personFields.find(field => \n        field.label.toLowerCase() === normalizedHeader ||\n        field.key.toLowerCase() === normalizedHeader\n      );\n      \n      if (exactMatch) {\n        suggestions[header] = exactMatch.key;\n        return;\n      }\n\n      // Find partial matches\n      const partialMatch = personFields.find(field => {\n        const fieldLabel = field.label.toLowerCase();\n        const fieldKey = field.key.toLowerCase();\n        \n        return fieldLabel.includes(normalizedHeader) || \n               normalizedHeader.includes(fieldLabel) ||\n               fieldKey.includes(normalizedHeader) ||\n               normalizedHeader.includes(fieldKey);\n      });\n\n      if (partialMatch) {\n        suggestions[header] = partialMatch.key;\n      }\n    });\n\n    return suggestions;\n  };\n\n  const handleMappingChange = (fileHeader, personFieldKey) => {\n    const newMapping = { ...mapping };\n    \n    if (personFieldKey === '') {\n      delete newMapping[fileHeader];\n    } else {\n      // Remove any existing mapping to this person field\n      Object.keys(newMapping).forEach(key => {\n        if (newMapping[key] === personFieldKey && key !== fileHeader) {\n          delete newMapping[key];\n        }\n      });\n      \n      newMapping[fileHeader] = personFieldKey;\n    }\n    \n    setMapping(newMapping);\n    updateUnmappedHeaders(newMapping);\n    updateRequiredFieldsStatus(newMapping);\n  };\n\n  const updateUnmappedHeaders = (currentMapping) => {\n    const mapped = Object.keys(currentMapping);\n    const unmapped = fileHeaders.filter(header => !mapped.includes(header));\n    setUnmappedHeaders(unmapped);\n  };\n\n  const updateRequiredFieldsStatus = (currentMapping) => {\n    const mappedFields = Object.values(currentMapping);\n    const status = {};\n\n    requiredFields.forEach(field => {\n      // Field is satisfied if it's mapped from Excel OR has a default value\n      const isMapped = mappedFields.includes(field.key);\n      const hasDefaultValue = defaultValues[field.key] && defaultValues[field.key].trim() !== '';\n      status[field.key] = isMapped || hasDefaultValue;\n    });\n\n    setRequiredFieldsStatus(status);\n  };\n\n  const applyAutoMapping = () => {\n    setMapping(autoMappingSuggestions);\n    updateUnmappedHeaders(autoMappingSuggestions);\n    updateRequiredFieldsStatus(autoMappingSuggestions);\n  };\n\n  const clearAllMappings = () => {\n    setMapping({});\n    updateUnmappedHeaders({});\n    updateRequiredFieldsStatus({});\n  };\n\n  const handleContinue = () => {\n    // Validate that all required fields are mapped\n    const missingRequired = requiredFields.filter(field => !requiredFieldsStatus[field.key]);\n    \n    if (missingRequired.length > 0) {\n      alert(`Please map the following required fields: ${missingRequired.map(f => f.label).join(', ')}`);\n      return;\n    }\n\n    onMappingComplete(mapping);\n  };\n\n  const getPersonFieldByKey = (key) => {\n    return personFields.find(field => field.key === key);\n  };\n\n  const getMappedPersonFields = () => {\n    return Object.values(mapping).map(key => getPersonFieldByKey(key)).filter(Boolean);\n  };\n\n  const getAvailablePersonFields = (currentHeader) => {\n    const mappedFields = Object.entries(mapping)\n      .filter(([header, _]) => header !== currentHeader)\n      .map(([_, fieldKey]) => fieldKey);\n    \n    return personFields.filter(field => !mappedFields.includes(field.key));\n  };\n\n  const groupPersonFieldsBySection = (fields) => {\n    const sections = {};\n    fields.forEach(field => {\n      const sectionKey = field.section || 'general';\n      if (!sections[sectionKey]) {\n        sections[sectionKey] = {\n          title: PersonFieldDefinitions[sectionKey]?.title || sectionKey,\n          fields: []\n        };\n      }\n      sections[sectionKey].fields.push(field);\n    });\n    return sections;\n  };\n\n  const renderFieldSelect = (fileHeader) => {\n    const availableFields = getAvailablePersonFields(fileHeader);\n    const sections = groupPersonFieldsBySection(availableFields);\n    const currentMapping = mapping[fileHeader] || '';\n\n    return (\n      <select\n        value={currentMapping}\n        onChange={(e) => handleMappingChange(fileHeader, e.target.value)}\n        className={`field-select ${currentMapping ? 'mapped' : 'unmapped'}`}\n      >\n        <option value=\"\">-- Select Field --</option>\n        {Object.entries(sections).map(([sectionKey, section]) => (\n          <optgroup key={sectionKey} label={section.title}>\n            {section.fields.map(field => (\n              <option key={field.key} value={field.key}>\n                {field.label} {field.required ? '*' : ''}\n              </option>\n            ))}\n          </optgroup>\n        ))}\n      </select>\n    );\n  };\n\n  const allRequiredMapped = requiredFields.every(field => requiredFieldsStatus[field.key]);\n  const mappingCount = Object.keys(mapping).length;\n\n  return (\n    <div className=\"field-mapping\">\n      <div className=\"mapping-header\">\n        <h3>Map File Columns to Person Fields</h3>\n        <p>Match your file columns with the corresponding person fields. Required fields are marked with *</p>\n        \n        <div className=\"mapping-stats\">\n          <div className=\"stat\">\n            <span className=\"stat-value\">{mappingCount}</span>\n            <span className=\"stat-label\">Mapped Fields</span>\n          </div>\n          <div className=\"stat\">\n            <span className=\"stat-value\">{unmappedHeaders.length}</span>\n            <span className=\"stat-label\">Unmapped Columns</span>\n          </div>\n          <div className=\"stat\">\n            <span className={`stat-value ${allRequiredMapped ? 'success' : 'error'}`}>\n              {Object.values(requiredFieldsStatus).filter(Boolean).length}/{requiredFields.length}\n            </span>\n            <span className=\"stat-label\">Required Fields</span>\n          </div>\n        </div>\n\n        <div className=\"mapping-actions\">\n          <button onClick={applyAutoMapping} className=\"btn btn-outline\">\n            🤖 Auto Map\n          </button>\n          <button onClick={clearAllMappings} className=\"btn btn-outline\">\n            🗑️ Clear All\n          </button>\n        </div>\n      </div>\n\n      {/* Required Fields Status */}\n      <div className=\"required-fields-status\">\n        <h4>Required Fields Status</h4>\n        <div className=\"required-fields-grid\">\n          {requiredFields.map(field => (\n            <div \n              key={field.key}\n              className={`required-field ${requiredFieldsStatus[field.key] ? 'mapped' : 'unmapped'}`}\n            >\n              <span className=\"field-name\">{field.label}</span>\n              <span className=\"field-status\">\n                {requiredFieldsStatus[field.key] ? '✅' : '❌'}\n              </span>\n            </div>\n          ))}\n        </div>\n      </div>\n\n      {/* Mapping Table */}\n      <div className=\"mapping-table-container\">\n        <table className=\"mapping-table\">\n          <thead>\n            <tr>\n              <th>File Column</th>\n              <th>Sample Data</th>\n              <th>Maps To</th>\n              <th>Field Type</th>\n              <th>Required</th>\n            </tr>\n          </thead>\n          <tbody>\n            {fileHeaders.map(header => {\n              const mappedField = mapping[header] ? getPersonFieldByKey(mapping[header]) : null;\n              return (\n                <tr key={header} className={mapping[header] ? 'mapped-row' : 'unmapped-row'}>\n                  <td>\n                    <div className=\"file-header\">\n                      <strong>{header}</strong>\n                    </div>\n                  </td>\n                  <td>\n                    <div className=\"sample-data\">\n                      {/* This would show sample data from the file */}\n                      <em>Sample data preview</em>\n                    </div>\n                  </td>\n                  <td>\n                    {renderFieldSelect(header)}\n                  </td>\n                  <td>\n                    <div className=\"field-type\">\n                      {mappedField ? (\n                        <span className=\"type-badge\">{mappedField.type}</span>\n                      ) : (\n                        <span className=\"no-mapping\">-</span>\n                      )}\n                    </div>\n                  </td>\n                  <td>\n                    <div className=\"required-indicator\">\n                      {mappedField?.required ? (\n                        <span className=\"required-badge\">Required</span>\n                      ) : (\n                        <span className=\"optional-badge\">Optional</span>\n                      )}\n                    </div>\n                  </td>\n                </tr>\n              );\n            })}\n          </tbody>\n        </table>\n      </div>\n\n      {/* Unmapped Headers Warning */}\n      {unmappedHeaders.length > 0 && (\n        <div className=\"unmapped-warning\">\n          <h4>⚠️ Unmapped Columns</h4>\n          <p>The following columns from your file will be ignored during import:</p>\n          <div className=\"unmapped-list\">\n            {unmappedHeaders.map(header => (\n              <span key={header} className=\"unmapped-header\">{header}</span>\n            ))}\n          </div>\n        </div>\n      )}\n\n      {/* Mapping Summary */}\n      <div className=\"mapping-summary\">\n        <h4>Mapping Summary</h4>\n        <div className=\"summary-content\">\n          <div className=\"summary-section\">\n            <h5>Mapped Fields ({mappingCount})</h5>\n            <div className=\"mapped-fields-list\">\n              {getMappedPersonFields().map(field => (\n                <div key={field.key} className=\"mapped-field-item\">\n                  <span className=\"field-label\">{field.label}</span>\n                  <span className={`field-badge ${field.required ? 'required' : 'optional'}`}>\n                    {field.required ? 'Required' : 'Optional'}\n                  </span>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Navigation */}\n      <div className=\"mapping-navigation\">\n        <button onClick={onBack} className=\"btn btn-outline\">\n          ← Back to Upload\n        </button>\n        <button\n          onClick={handleContinue}\n          disabled={!allRequiredMapped}\n          className=\"btn btn-primary\"\n        >\n          Continue to Import →\n        </button>\n      </div>\n\n      {!allRequiredMapped && (\n        <div className=\"validation-error\">\n          <span className=\"error-icon\">⚠️</span>\n          Please map all required fields before continuing\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default FieldMapping;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,sBAAsB,EAAEC,kBAAkB,QAAQ,iCAAiC;AAC5F,OAAO,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5B,MAAMC,YAAY,GAAGA,CAAC;EAAEC,WAAW;EAAEC,iBAAiB;EAAEC,MAAM;EAAEC;AAAM,CAAC,KAAK;EAAAC,EAAA;EAC1E,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGb,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC1C,MAAM,CAACc,eAAe,EAAEC,kBAAkB,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACgB,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGjB,QAAQ,CAAC,CAAC,CAAC,CAAC;EACpE,MAAM,CAACkB,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGnB,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxE,MAAM,CAACoB,aAAa,EAAEC,gBAAgB,CAAC,GAAGrB,QAAQ,CAAC,CAAC,CAAC,CAAC;EAEtD,MAAMsB,YAAY,GAAGnB,kBAAkB,CAAC,CAAC;EACzC,MAAMoB,cAAc,GAAGD,YAAY,CAACE,MAAM,CAACC,KAAK,IAAIA,KAAK,CAACC,QAAQ,CAAC;EAEnEzB,SAAS,CAAC,MAAM;IACd;IACA,MAAM0B,WAAW,GAAGC,aAAa,CAAC,CAAC;IACnCT,yBAAyB,CAACQ,WAAW,CAAC;IACtCd,UAAU,CAACc,WAAW,CAAC;IACvBE,qBAAqB,CAACF,WAAW,CAAC;IAClCG,0BAA0B,CAACH,WAAW,CAAC;EACzC,CAAC,EAAE,CAACpB,WAAW,CAAC,CAAC;EAEjB,MAAMqB,aAAa,GAAGA,CAAA,KAAM;IAC1B,MAAMD,WAAW,GAAG,CAAC,CAAC;IAEtBpB,WAAW,CAACwB,OAAO,CAACC,MAAM,IAAI;MAC5B,MAAMC,gBAAgB,GAAGD,MAAM,CAACE,WAAW,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC;;MAEpD;MACA,MAAMC,UAAU,GAAGd,YAAY,CAACe,IAAI,CAACZ,KAAK,IACxCA,KAAK,CAACa,KAAK,CAACJ,WAAW,CAAC,CAAC,KAAKD,gBAAgB,IAC9CR,KAAK,CAACc,GAAG,CAACL,WAAW,CAAC,CAAC,KAAKD,gBAC9B,CAAC;MAED,IAAIG,UAAU,EAAE;QACdT,WAAW,CAACK,MAAM,CAAC,GAAGI,UAAU,CAACG,GAAG;QACpC;MACF;;MAEA;MACA,MAAMC,YAAY,GAAGlB,YAAY,CAACe,IAAI,CAACZ,KAAK,IAAI;QAC9C,MAAMgB,UAAU,GAAGhB,KAAK,CAACa,KAAK,CAACJ,WAAW,CAAC,CAAC;QAC5C,MAAMQ,QAAQ,GAAGjB,KAAK,CAACc,GAAG,CAACL,WAAW,CAAC,CAAC;QAExC,OAAOO,UAAU,CAACE,QAAQ,CAACV,gBAAgB,CAAC,IACrCA,gBAAgB,CAACU,QAAQ,CAACF,UAAU,CAAC,IACrCC,QAAQ,CAACC,QAAQ,CAACV,gBAAgB,CAAC,IACnCA,gBAAgB,CAACU,QAAQ,CAACD,QAAQ,CAAC;MAC5C,CAAC,CAAC;MAEF,IAAIF,YAAY,EAAE;QAChBb,WAAW,CAACK,MAAM,CAAC,GAAGQ,YAAY,CAACD,GAAG;MACxC;IACF,CAAC,CAAC;IAEF,OAAOZ,WAAW;EACpB,CAAC;EAED,MAAMiB,mBAAmB,GAAGA,CAACC,UAAU,EAAEC,cAAc,KAAK;IAC1D,MAAMC,UAAU,GAAG;MAAE,GAAGnC;IAAQ,CAAC;IAEjC,IAAIkC,cAAc,KAAK,EAAE,EAAE;MACzB,OAAOC,UAAU,CAACF,UAAU,CAAC;IAC/B,CAAC,MAAM;MACL;MACAG,MAAM,CAACC,IAAI,CAACF,UAAU,CAAC,CAAChB,OAAO,CAACQ,GAAG,IAAI;QACrC,IAAIQ,UAAU,CAACR,GAAG,CAAC,KAAKO,cAAc,IAAIP,GAAG,KAAKM,UAAU,EAAE;UAC5D,OAAOE,UAAU,CAACR,GAAG,CAAC;QACxB;MACF,CAAC,CAAC;MAEFQ,UAAU,CAACF,UAAU,CAAC,GAAGC,cAAc;IACzC;IAEAjC,UAAU,CAACkC,UAAU,CAAC;IACtBlB,qBAAqB,CAACkB,UAAU,CAAC;IACjCjB,0BAA0B,CAACiB,UAAU,CAAC;EACxC,CAAC;EAED,MAAMlB,qBAAqB,GAAIqB,cAAc,IAAK;IAChD,MAAMC,MAAM,GAAGH,MAAM,CAACC,IAAI,CAACC,cAAc,CAAC;IAC1C,MAAME,QAAQ,GAAG7C,WAAW,CAACiB,MAAM,CAACQ,MAAM,IAAI,CAACmB,MAAM,CAACR,QAAQ,CAACX,MAAM,CAAC,CAAC;IACvEjB,kBAAkB,CAACqC,QAAQ,CAAC;EAC9B,CAAC;EAED,MAAMtB,0BAA0B,GAAIoB,cAAc,IAAK;IACrD,MAAMG,YAAY,GAAGL,MAAM,CAACM,MAAM,CAACJ,cAAc,CAAC;IAClD,MAAMK,MAAM,GAAG,CAAC,CAAC;IAEjBhC,cAAc,CAACQ,OAAO,CAACN,KAAK,IAAI;MAC9B;MACA,MAAM+B,QAAQ,GAAGH,YAAY,CAACV,QAAQ,CAAClB,KAAK,CAACc,GAAG,CAAC;MACjD,MAAMkB,eAAe,GAAGrC,aAAa,CAACK,KAAK,CAACc,GAAG,CAAC,IAAInB,aAAa,CAACK,KAAK,CAACc,GAAG,CAAC,CAACJ,IAAI,CAAC,CAAC,KAAK,EAAE;MAC1FoB,MAAM,CAAC9B,KAAK,CAACc,GAAG,CAAC,GAAGiB,QAAQ,IAAIC,eAAe;IACjD,CAAC,CAAC;IAEFxC,uBAAuB,CAACsC,MAAM,CAAC;EACjC,CAAC;EAED,MAAMG,gBAAgB,GAAGA,CAAA,KAAM;IAC7B7C,UAAU,CAACK,sBAAsB,CAAC;IAClCW,qBAAqB,CAACX,sBAAsB,CAAC;IAC7CY,0BAA0B,CAACZ,sBAAsB,CAAC;EACpD,CAAC;EAED,MAAMyC,gBAAgB,GAAGA,CAAA,KAAM;IAC7B9C,UAAU,CAAC,CAAC,CAAC,CAAC;IACdgB,qBAAqB,CAAC,CAAC,CAAC,CAAC;IACzBC,0BAA0B,CAAC,CAAC,CAAC,CAAC;EAChC,CAAC;EAED,MAAM8B,cAAc,GAAGA,CAAA,KAAM;IAC3B;IACA,MAAMC,eAAe,GAAGtC,cAAc,CAACC,MAAM,CAACC,KAAK,IAAI,CAACT,oBAAoB,CAACS,KAAK,CAACc,GAAG,CAAC,CAAC;IAExF,IAAIsB,eAAe,CAACC,MAAM,GAAG,CAAC,EAAE;MAC9BC,KAAK,CAAC,6CAA6CF,eAAe,CAACG,GAAG,CAACC,CAAC,IAAIA,CAAC,CAAC3B,KAAK,CAAC,CAAC4B,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;MAClG;IACF;IAEA1D,iBAAiB,CAACI,OAAO,CAAC;EAC5B,CAAC;EAED,MAAMuD,mBAAmB,GAAI5B,GAAG,IAAK;IACnC,OAAOjB,YAAY,CAACe,IAAI,CAACZ,KAAK,IAAIA,KAAK,CAACc,GAAG,KAAKA,GAAG,CAAC;EACtD,CAAC;EAED,MAAM6B,qBAAqB,GAAGA,CAAA,KAAM;IAClC,OAAOpB,MAAM,CAACM,MAAM,CAAC1C,OAAO,CAAC,CAACoD,GAAG,CAACzB,GAAG,IAAI4B,mBAAmB,CAAC5B,GAAG,CAAC,CAAC,CAACf,MAAM,CAAC6C,OAAO,CAAC;EACpF,CAAC;EAED,MAAMC,wBAAwB,GAAIC,aAAa,IAAK;IAClD,MAAMlB,YAAY,GAAGL,MAAM,CAACwB,OAAO,CAAC5D,OAAO,CAAC,CACzCY,MAAM,CAAC,CAAC,CAACQ,MAAM,EAAEyC,CAAC,CAAC,KAAKzC,MAAM,KAAKuC,aAAa,CAAC,CACjDP,GAAG,CAAC,CAAC,CAACS,CAAC,EAAE/B,QAAQ,CAAC,KAAKA,QAAQ,CAAC;IAEnC,OAAOpB,YAAY,CAACE,MAAM,CAACC,KAAK,IAAI,CAAC4B,YAAY,CAACV,QAAQ,CAAClB,KAAK,CAACc,GAAG,CAAC,CAAC;EACxE,CAAC;EAED,MAAMmC,0BAA0B,GAAIC,MAAM,IAAK;IAC7C,MAAMC,QAAQ,GAAG,CAAC,CAAC;IACnBD,MAAM,CAAC5C,OAAO,CAACN,KAAK,IAAI;MACtB,MAAMoD,UAAU,GAAGpD,KAAK,CAACqD,OAAO,IAAI,SAAS;MAC7C,IAAI,CAACF,QAAQ,CAACC,UAAU,CAAC,EAAE;QAAA,IAAAE,qBAAA;QACzBH,QAAQ,CAACC,UAAU,CAAC,GAAG;UACrBG,KAAK,EAAE,EAAAD,qBAAA,GAAA7E,sBAAsB,CAAC2E,UAAU,CAAC,cAAAE,qBAAA,uBAAlCA,qBAAA,CAAoCC,KAAK,KAAIH,UAAU;UAC9DF,MAAM,EAAE;QACV,CAAC;MACH;MACAC,QAAQ,CAACC,UAAU,CAAC,CAACF,MAAM,CAACM,IAAI,CAACxD,KAAK,CAAC;IACzC,CAAC,CAAC;IACF,OAAOmD,QAAQ;EACjB,CAAC;EAED,MAAMM,iBAAiB,GAAIrC,UAAU,IAAK;IACxC,MAAMsC,eAAe,GAAGb,wBAAwB,CAACzB,UAAU,CAAC;IAC5D,MAAM+B,QAAQ,GAAGF,0BAA0B,CAACS,eAAe,CAAC;IAC5D,MAAMjC,cAAc,GAAGtC,OAAO,CAACiC,UAAU,CAAC,IAAI,EAAE;IAEhD,oBACExC,OAAA;MACE+E,KAAK,EAAElC,cAAe;MACtBmC,QAAQ,EAAGC,CAAC,IAAK1C,mBAAmB,CAACC,UAAU,EAAEyC,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;MACjEI,SAAS,EAAE,gBAAgBtC,cAAc,GAAG,QAAQ,GAAG,UAAU,EAAG;MAAAuC,QAAA,gBAEpEpF,OAAA;QAAQ+E,KAAK,EAAC,EAAE;QAAAK,QAAA,EAAC;MAAkB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,EAC3C7C,MAAM,CAACwB,OAAO,CAACI,QAAQ,CAAC,CAACZ,GAAG,CAAC,CAAC,CAACa,UAAU,EAAEC,OAAO,CAAC,kBAClDzE,OAAA;QAA2BiC,KAAK,EAAEwC,OAAO,CAACE,KAAM;QAAAS,QAAA,EAC7CX,OAAO,CAACH,MAAM,CAACX,GAAG,CAACvC,KAAK,iBACvBpB,OAAA;UAAwB+E,KAAK,EAAE3D,KAAK,CAACc,GAAI;UAAAkD,QAAA,GACtChE,KAAK,CAACa,KAAK,EAAC,GAAC,EAACb,KAAK,CAACC,QAAQ,GAAG,GAAG,GAAG,EAAE;QAAA,GAD7BD,KAAK,CAACc,GAAG;UAAAmD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEd,CACT;MAAC,GALWhB,UAAU;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAMf,CACX,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAEb,CAAC;EAED,MAAMC,iBAAiB,GAAGvE,cAAc,CAACwE,KAAK,CAACtE,KAAK,IAAIT,oBAAoB,CAACS,KAAK,CAACc,GAAG,CAAC,CAAC;EACxF,MAAMyD,YAAY,GAAGhD,MAAM,CAACC,IAAI,CAACrC,OAAO,CAAC,CAACkD,MAAM;EAEhD,oBACEzD,OAAA;IAAKmF,SAAS,EAAC,eAAe;IAAAC,QAAA,gBAC5BpF,OAAA;MAAKmF,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7BpF,OAAA;QAAAoF,QAAA,EAAI;MAAiC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC1CxF,OAAA;QAAAoF,QAAA,EAAG;MAA+F;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAEtGxF,OAAA;QAAKmF,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BpF,OAAA;UAAKmF,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnBpF,OAAA;YAAMmF,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAEO;UAAY;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAClDxF,OAAA;YAAMmF,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9C,CAAC,eACNxF,OAAA;UAAKmF,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnBpF,OAAA;YAAMmF,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAE3E,eAAe,CAACgD;UAAM;YAAA4B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC5DxF,OAAA;YAAMmF,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD,CAAC,eACNxF,OAAA;UAAKmF,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnBpF,OAAA;YAAMmF,SAAS,EAAE,cAAcM,iBAAiB,GAAG,SAAS,GAAG,OAAO,EAAG;YAAAL,QAAA,GACtEzC,MAAM,CAACM,MAAM,CAACtC,oBAAoB,CAAC,CAACQ,MAAM,CAAC6C,OAAO,CAAC,CAACP,MAAM,EAAC,GAAC,EAACvC,cAAc,CAACuC,MAAM;UAAA;YAAA4B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/E,CAAC,eACPxF,OAAA;YAAMmF,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENxF,OAAA;QAAKmF,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9BpF,OAAA;UAAQ4F,OAAO,EAAEvC,gBAAiB;UAAC8B,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAAC;QAE/D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTxF,OAAA;UAAQ4F,OAAO,EAAEtC,gBAAiB;UAAC6B,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAAC;QAE/D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNxF,OAAA;MAAKmF,SAAS,EAAC,wBAAwB;MAAAC,QAAA,gBACrCpF,OAAA;QAAAoF,QAAA,EAAI;MAAsB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC/BxF,OAAA;QAAKmF,SAAS,EAAC,sBAAsB;QAAAC,QAAA,EAClClE,cAAc,CAACyC,GAAG,CAACvC,KAAK,iBACvBpB,OAAA;UAEEmF,SAAS,EAAE,kBAAkBxE,oBAAoB,CAACS,KAAK,CAACc,GAAG,CAAC,GAAG,QAAQ,GAAG,UAAU,EAAG;UAAAkD,QAAA,gBAEvFpF,OAAA;YAAMmF,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAEhE,KAAK,CAACa;UAAK;YAAAoD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACjDxF,OAAA;YAAMmF,SAAS,EAAC,cAAc;YAAAC,QAAA,EAC3BzE,oBAAoB,CAACS,KAAK,CAACc,GAAG,CAAC,GAAG,GAAG,GAAG;UAAG;YAAAmD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC,CAAC;QAAA,GANFpE,KAAK,CAACc,GAAG;UAAAmD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAOX,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNxF,OAAA;MAAKmF,SAAS,EAAC,yBAAyB;MAAAC,QAAA,eACtCpF,OAAA;QAAOmF,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC9BpF,OAAA;UAAAoF,QAAA,eACEpF,OAAA;YAAAoF,QAAA,gBACEpF,OAAA;cAAAoF,QAAA,EAAI;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpBxF,OAAA;cAAAoF,QAAA,EAAI;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpBxF,OAAA;cAAAoF,QAAA,EAAI;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChBxF,OAAA;cAAAoF,QAAA,EAAI;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnBxF,OAAA;cAAAoF,QAAA,EAAI;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACRxF,OAAA;UAAAoF,QAAA,EACGlF,WAAW,CAACyD,GAAG,CAAChC,MAAM,IAAI;YACzB,MAAMkE,WAAW,GAAGtF,OAAO,CAACoB,MAAM,CAAC,GAAGmC,mBAAmB,CAACvD,OAAO,CAACoB,MAAM,CAAC,CAAC,GAAG,IAAI;YACjF,oBACE3B,OAAA;cAAiBmF,SAAS,EAAE5E,OAAO,CAACoB,MAAM,CAAC,GAAG,YAAY,GAAG,cAAe;cAAAyD,QAAA,gBAC1EpF,OAAA;gBAAAoF,QAAA,eACEpF,OAAA;kBAAKmF,SAAS,EAAC,aAAa;kBAAAC,QAAA,eAC1BpF,OAAA;oBAAAoF,QAAA,EAASzD;kBAAM;oBAAA0D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACLxF,OAAA;gBAAAoF,QAAA,eACEpF,OAAA;kBAAKmF,SAAS,EAAC,aAAa;kBAAAC,QAAA,eAE1BpF,OAAA;oBAAAoF,QAAA,EAAI;kBAAmB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACLxF,OAAA;gBAAAoF,QAAA,EACGP,iBAAiB,CAAClD,MAAM;cAAC;gBAAA0D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxB,CAAC,eACLxF,OAAA;gBAAAoF,QAAA,eACEpF,OAAA;kBAAKmF,SAAS,EAAC,YAAY;kBAAAC,QAAA,EACxBS,WAAW,gBACV7F,OAAA;oBAAMmF,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAES,WAAW,CAACC;kBAAI;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,gBAEtDxF,OAAA;oBAAMmF,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBACrC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACLxF,OAAA;gBAAAoF,QAAA,eACEpF,OAAA;kBAAKmF,SAAS,EAAC,oBAAoB;kBAAAC,QAAA,EAChCS,WAAW,aAAXA,WAAW,eAAXA,WAAW,CAAExE,QAAQ,gBACpBrB,OAAA;oBAAMmF,SAAS,EAAC,gBAAgB;oBAAAC,QAAA,EAAC;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,gBAEhDxF,OAAA;oBAAMmF,SAAS,EAAC,gBAAgB;oBAAAC,QAAA,EAAC;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAChD;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA,GAhCE7D,MAAM;cAAA0D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAiCX,CAAC;UAET,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,EAGL/E,eAAe,CAACgD,MAAM,GAAG,CAAC,iBACzBzD,OAAA;MAAKmF,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/BpF,OAAA;QAAAoF,QAAA,EAAI;MAAmB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC5BxF,OAAA;QAAAoF,QAAA,EAAG;MAAmE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAC1ExF,OAAA;QAAKmF,SAAS,EAAC,eAAe;QAAAC,QAAA,EAC3B3E,eAAe,CAACkD,GAAG,CAAChC,MAAM,iBACzB3B,OAAA;UAAmBmF,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAAEzD;QAAM,GAA3CA,MAAM;UAAA0D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAA4C,CAC9D;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGDxF,OAAA;MAAKmF,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9BpF,OAAA;QAAAoF,QAAA,EAAI;MAAe;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACxBxF,OAAA;QAAKmF,SAAS,EAAC,iBAAiB;QAAAC,QAAA,eAC9BpF,OAAA;UAAKmF,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9BpF,OAAA;YAAAoF,QAAA,GAAI,iBAAe,EAACO,YAAY,EAAC,GAAC;UAAA;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACvCxF,OAAA;YAAKmF,SAAS,EAAC,oBAAoB;YAAAC,QAAA,EAChCrB,qBAAqB,CAAC,CAAC,CAACJ,GAAG,CAACvC,KAAK,iBAChCpB,OAAA;cAAqBmF,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChDpF,OAAA;gBAAMmF,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAEhE,KAAK,CAACa;cAAK;gBAAAoD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAClDxF,OAAA;gBAAMmF,SAAS,EAAE,eAAe/D,KAAK,CAACC,QAAQ,GAAG,UAAU,GAAG,UAAU,EAAG;gBAAA+D,QAAA,EACxEhE,KAAK,CAACC,QAAQ,GAAG,UAAU,GAAG;cAAU;gBAAAgE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC,CAAC;YAAA,GAJCpE,KAAK,CAACc,GAAG;cAAAmD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAKd,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNxF,OAAA;MAAKmF,SAAS,EAAC,oBAAoB;MAAAC,QAAA,gBACjCpF,OAAA;QAAQ4F,OAAO,EAAExF,MAAO;QAAC+E,SAAS,EAAC,iBAAiB;QAAAC,QAAA,EAAC;MAErD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTxF,OAAA;QACE4F,OAAO,EAAErC,cAAe;QACxBwC,QAAQ,EAAE,CAACN,iBAAkB;QAC7BN,SAAS,EAAC,iBAAiB;QAAAC,QAAA,EAC5B;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,EAEL,CAACC,iBAAiB,iBACjBzF,OAAA;MAAKmF,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/BpF,OAAA;QAAMmF,SAAS,EAAC,YAAY;QAAAC,QAAA,EAAC;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,oDAExC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAClF,EAAA,CArVIL,YAAY;AAAA+F,EAAA,GAAZ/F,YAAY;AAuVlB,eAAeA,YAAY;AAAC,IAAA+F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}