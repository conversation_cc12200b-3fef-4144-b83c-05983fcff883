using System;

namespace CrmApi.Scripts
{
    public class TestPasswordHash
    {
        public static void Main(string[] args)
        {
            string password = "admin123";
            
            // Generate a new hash
            string newHash = BCrypt.Net.BCrypt.HashPassword(password);
            Console.WriteLine($"New BCrypt hash for '{password}': {newHash}");
            
            // Test the hash from migration
            string migrationHash = "$2a$11$8K1p/a0dL2LkqvQOuuHDMuEn/TxjYqGlGXbSHPa4plBdJXfXqSr4W";
            bool isValidMigration = BCrypt.Net.BCrypt.Verify(password, migrationHash);
            Console.WriteLine($"Migration hash is valid: {isValidMigration}");
            
            // Test the new hash
            bool isValidNew = BCrypt.Net.BCrypt.Verify(password, newHash);
            Console.WriteLine($"New hash is valid: {isValidNew}");
            
            // Test wrong password
            bool isValidWrong = BCrypt.Net.BCrypt.Verify("wrongpassword", newHash);
            Console.WriteLine($"Wrong password test: {isValidWrong}");
        }
    }
}
