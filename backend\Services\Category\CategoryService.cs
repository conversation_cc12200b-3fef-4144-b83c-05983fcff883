using CrmApi.Models.Category;
using CrmApi.Models.Division;
using CrmApi.Models.SubCategory;
using CrmApi.Repositories.Category;
using CrmApi.Repositories.Division;
using CrmApi.Exceptions;

namespace CrmApi.Services.Category
{
    public class CategoryService : ICategoryService
    {
        private readonly ICategoryRepository _categoryRepository;
        private readonly IDivisionRepository _divisionRepository;

        public CategoryService(ICategoryRepository categoryRepository, IDivisionRepository divisionRepository)
        {
            _categoryRepository = categoryRepository;
            _divisionRepository = divisionRepository;
        }

        public async Task<IEnumerable<CategoryResponse>> GetAllCategoriesAsync()
        {
            var categories = await _categoryRepository.GetAllWithRelationsAsync();
            return categories.Select(MapToResponse);
        }

        public async Task<CategoryResponse?> GetCategoryByIdAsync(int id)
        {
            var category = await _categoryRepository.GetByIdWithRelationsAsync(id);
            return category != null ? MapToResponse(category) : null;
        }

        public async Task<IEnumerable<CategoryResponse>> GetCategoriesByDivisionAsync(int divisionId)
        {
            var categories = await _categoryRepository.GetByDivisionIdAsync(divisionId);
            return categories.Select(MapToResponse);
        }

        public async Task<CategoryResponse> CreateCategoryAsync(CreateCategoryRequest request)
        {
            // Check if division exists
            if (!await _divisionRepository.ExistsAsync(request.DivisionId))
                throw new NotFoundException($"Division with ID {request.DivisionId} not found");

            // Check if name already exists in division
            if (await _categoryRepository.NameExistsInDivisionAsync(request.Name, request.DivisionId))
                throw new BusinessException($"Category with name '{request.Name}' already exists in this division");

            var category = new Models.Category.Category
            {
                Name = request.Name,
                DivisionId = request.DivisionId
            };

            var createdCategory = await _categoryRepository.CreateAsync(category);
            
            // Get the category with relations for response
            var categoryWithRelations = await _categoryRepository.GetByIdWithRelationsAsync(createdCategory.Id);
            return MapToResponse(categoryWithRelations!);
        }

        public async Task<CategoryResponse> UpdateCategoryAsync(int id, UpdateCategoryRequest request)
        {
            var category = await _categoryRepository.GetByIdAsync(id);
            if (category == null)
                throw new NotFoundException($"Category with ID {id} not found");

            // Check if division exists
            if (!await _divisionRepository.ExistsAsync(request.DivisionId))
                throw new NotFoundException($"Division with ID {request.DivisionId} not found");

            // Check name uniqueness in division
            if (await _categoryRepository.NameExistsInDivisionAsync(request.Name, request.DivisionId, id))
                throw new BusinessException($"Category with name '{request.Name}' already exists in this division");

            category.Name = request.Name;
            category.DivisionId = request.DivisionId;
            
            var updatedCategory = await _categoryRepository.UpdateAsync(category);
            
            // Get the category with relations for response
            var categoryWithRelations = await _categoryRepository.GetByIdWithRelationsAsync(updatedCategory.Id);
            return MapToResponse(categoryWithRelations!);
        }

        public async Task<bool> DeleteCategoryAsync(int id)
        {
            if (!await _categoryRepository.ExistsAsync(id))
                throw new NotFoundException($"Category with ID {id} not found");

            return await _categoryRepository.DeleteAsync(id);
        }

        private CategoryResponse MapToResponse(Models.Category.Category category)
        {
            return new CategoryResponse
            {
                Id = category.Id,
                DivisionId = category.DivisionId,
                Name = category.Name,
                CreatedAt = category.CreatedAt,
                UpdatedAt = category.UpdatedAt,
                Division = category.Division != null ? new DivisionResponse
                {
                    Id = category.Division.Id,
                    Name = category.Division.Name,
                    CreatedAt = category.Division.CreatedAt,
                    UpdatedAt = category.Division.UpdatedAt
                } : null,
                SubCategories = category.SubCategories?.Select(sc => new SubCategoryResponse
                {
                    Id = sc.Id,
                    CategoryId = sc.CategoryId,
                    Name = sc.Name,
                    CreatedAt = sc.CreatedAt,
                    UpdatedAt = sc.UpdatedAt
                }).ToList()
            };
        }
    }
}
