{"ast": null, "code": "import { useState, useEffect } from 'react';\nimport { useConstant } from '../../utils/use-constant.mjs';\nimport { getOrigin, checkTargetForNewValues } from '../../render/utils/setters.mjs';\nimport { makeUseVisualState } from '../../motion/utils/use-visual-state.mjs';\nimport { createBox } from '../../projection/geometry/models.mjs';\nimport { VisualElement } from '../../render/VisualElement.mjs';\nimport { animateVisualElement } from '../interfaces/visual-element.mjs';\nconst createObject = () => ({});\nclass StateVisualElement extends VisualElement {\n  build() {}\n  measureInstanceViewportBox() {\n    return createBox();\n  }\n  resetTransform() {}\n  restoreTransform() {}\n  removeValueFromRenderState() {}\n  renderInstance() {}\n  scrapeMotionValuesFromProps() {\n    return createObject();\n  }\n  getBaseTargetFromProps() {\n    return undefined;\n  }\n  readValueFromInstance(_state, key, options) {\n    return options.initialState[key] || 0;\n  }\n  sortInstanceNodePosition() {\n    return 0;\n  }\n  makeTargetAnimatableFromInstance({\n    transition,\n    transitionEnd,\n    ...target\n  }) {\n    const origin = getOrigin(target, transition || {}, this);\n    checkTargetForNewValues(this, target, origin);\n    return {\n      transition,\n      transitionEnd,\n      ...target\n    };\n  }\n}\nconst useVisualState = makeUseVisualState({\n  scrapeMotionValuesFromProps: createObject,\n  createRenderState: createObject\n});\n/**\n * This is not an officially supported API and may be removed\n * on any version.\n */\nfunction useAnimatedState(initialState) {\n  const [animationState, setAnimationState] = useState(initialState);\n  const visualState = useVisualState({}, false);\n  const element = useConstant(() => {\n    return new StateVisualElement({\n      props: {},\n      visualState,\n      presenceContext: null\n    }, {\n      initialState\n    });\n  });\n  useEffect(() => {\n    element.mount({});\n    return () => element.unmount();\n  }, [element]);\n  useEffect(() => {\n    element.update({\n      onUpdate: v => {\n        setAnimationState({\n          ...v\n        });\n      }\n    }, null);\n  }, [setAnimationState, element]);\n  const startAnimation = useConstant(() => animationDefinition => {\n    return animateVisualElement(element, animationDefinition);\n  });\n  return [animationState, startAnimation];\n}\nexport { useAnimatedState };", "map": {"version": 3, "names": ["useState", "useEffect", "useConstant", "<PERSON><PERSON><PERSON><PERSON>", "checkTargetForNewValues", "makeUseVisualState", "createBox", "VisualElement", "animateVisualElement", "createObject", "StateVisualElement", "build", "measureInstanceViewportBox", "resetTransform", "restoreTransform", "removeValueFromRenderState", "renderInstance", "scrapeMotionValuesFromProps", "getBaseTargetFromProps", "undefined", "readValueFromInstance", "_state", "key", "options", "initialState", "sortInstanceNodePosition", "makeTargetAnimatableFromInstance", "transition", "transitionEnd", "target", "origin", "useVisualState", "createRenderState", "useAnimatedState", "animationState", "setAnimationState", "visualState", "element", "props", "presenceContext", "mount", "unmount", "update", "onUpdate", "v", "startAnimation", "animationDefinition"], "sources": ["C:/Users/<USER>/Downloads/Advanced/Advanced CRM filed all use/frontend/node_modules/framer-motion/dist/es/animation/hooks/use-animated-state.mjs"], "sourcesContent": ["import { useState, useEffect } from 'react';\nimport { useConstant } from '../../utils/use-constant.mjs';\nimport { getOrigin, checkTargetForNewValues } from '../../render/utils/setters.mjs';\nimport { makeUseVisualState } from '../../motion/utils/use-visual-state.mjs';\nimport { createBox } from '../../projection/geometry/models.mjs';\nimport { VisualElement } from '../../render/VisualElement.mjs';\nimport { animateVisualElement } from '../interfaces/visual-element.mjs';\n\nconst createObject = () => ({});\nclass StateVisualElement extends VisualElement {\n    build() { }\n    measureInstanceViewportBox() {\n        return createBox();\n    }\n    resetTransform() { }\n    restoreTransform() { }\n    removeValueFromRenderState() { }\n    renderInstance() { }\n    scrapeMotionValuesFromProps() {\n        return createObject();\n    }\n    getBaseTargetFromProps() {\n        return undefined;\n    }\n    readValueFromInstance(_state, key, options) {\n        return options.initialState[key] || 0;\n    }\n    sortInstanceNodePosition() {\n        return 0;\n    }\n    makeTargetAnimatableFromInstance({ transition, transitionEnd, ...target }) {\n        const origin = getOrigin(target, transition || {}, this);\n        checkTargetForNewValues(this, target, origin);\n        return { transition, transitionEnd, ...target };\n    }\n}\nconst useVisualState = makeUseVisualState({\n    scrapeMotionValuesFromProps: createObject,\n    createRenderState: createObject,\n});\n/**\n * This is not an officially supported API and may be removed\n * on any version.\n */\nfunction useAnimatedState(initialState) {\n    const [animationState, setAnimationState] = useState(initialState);\n    const visualState = useVisualState({}, false);\n    const element = useConstant(() => {\n        return new StateVisualElement({ props: {}, visualState, presenceContext: null }, { initialState });\n    });\n    useEffect(() => {\n        element.mount({});\n        return () => element.unmount();\n    }, [element]);\n    useEffect(() => {\n        element.update({\n            onUpdate: (v) => {\n                setAnimationState({ ...v });\n            },\n        }, null);\n    }, [setAnimationState, element]);\n    const startAnimation = useConstant(() => (animationDefinition) => {\n        return animateVisualElement(element, animationDefinition);\n    });\n    return [animationState, startAnimation];\n}\n\nexport { useAnimatedState };\n"], "mappings": "AAAA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC3C,SAASC,WAAW,QAAQ,8BAA8B;AAC1D,SAASC,SAAS,EAAEC,uBAAuB,QAAQ,gCAAgC;AACnF,SAASC,kBAAkB,QAAQ,yCAAyC;AAC5E,SAASC,SAAS,QAAQ,sCAAsC;AAChE,SAASC,aAAa,QAAQ,gCAAgC;AAC9D,SAASC,oBAAoB,QAAQ,kCAAkC;AAEvE,MAAMC,YAAY,GAAGA,CAAA,MAAO,CAAC,CAAC,CAAC;AAC/B,MAAMC,kBAAkB,SAASH,aAAa,CAAC;EAC3CI,KAAKA,CAAA,EAAG,CAAE;EACVC,0BAA0BA,CAAA,EAAG;IACzB,OAAON,SAAS,CAAC,CAAC;EACtB;EACAO,cAAcA,CAAA,EAAG,CAAE;EACnBC,gBAAgBA,CAAA,EAAG,CAAE;EACrBC,0BAA0BA,CAAA,EAAG,CAAE;EAC/BC,cAAcA,CAAA,EAAG,CAAE;EACnBC,2BAA2BA,CAAA,EAAG;IAC1B,OAAOR,YAAY,CAAC,CAAC;EACzB;EACAS,sBAAsBA,CAAA,EAAG;IACrB,OAAOC,SAAS;EACpB;EACAC,qBAAqBA,CAACC,MAAM,EAAEC,GAAG,EAAEC,OAAO,EAAE;IACxC,OAAOA,OAAO,CAACC,YAAY,CAACF,GAAG,CAAC,IAAI,CAAC;EACzC;EACAG,wBAAwBA,CAAA,EAAG;IACvB,OAAO,CAAC;EACZ;EACAC,gCAAgCA,CAAC;IAAEC,UAAU;IAAEC,aAAa;IAAE,GAAGC;EAAO,CAAC,EAAE;IACvE,MAAMC,MAAM,GAAG3B,SAAS,CAAC0B,MAAM,EAAEF,UAAU,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC;IACxDvB,uBAAuB,CAAC,IAAI,EAAEyB,MAAM,EAAEC,MAAM,CAAC;IAC7C,OAAO;MAAEH,UAAU;MAAEC,aAAa;MAAE,GAAGC;IAAO,CAAC;EACnD;AACJ;AACA,MAAME,cAAc,GAAG1B,kBAAkB,CAAC;EACtCY,2BAA2B,EAAER,YAAY;EACzCuB,iBAAiB,EAAEvB;AACvB,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA,SAASwB,gBAAgBA,CAACT,YAAY,EAAE;EACpC,MAAM,CAACU,cAAc,EAAEC,iBAAiB,CAAC,GAAGnC,QAAQ,CAACwB,YAAY,CAAC;EAClE,MAAMY,WAAW,GAAGL,cAAc,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC;EAC7C,MAAMM,OAAO,GAAGnC,WAAW,CAAC,MAAM;IAC9B,OAAO,IAAIQ,kBAAkB,CAAC;MAAE4B,KAAK,EAAE,CAAC,CAAC;MAAEF,WAAW;MAAEG,eAAe,EAAE;IAAK,CAAC,EAAE;MAAEf;IAAa,CAAC,CAAC;EACtG,CAAC,CAAC;EACFvB,SAAS,CAAC,MAAM;IACZoC,OAAO,CAACG,KAAK,CAAC,CAAC,CAAC,CAAC;IACjB,OAAO,MAAMH,OAAO,CAACI,OAAO,CAAC,CAAC;EAClC,CAAC,EAAE,CAACJ,OAAO,CAAC,CAAC;EACbpC,SAAS,CAAC,MAAM;IACZoC,OAAO,CAACK,MAAM,CAAC;MACXC,QAAQ,EAAGC,CAAC,IAAK;QACbT,iBAAiB,CAAC;UAAE,GAAGS;QAAE,CAAC,CAAC;MAC/B;IACJ,CAAC,EAAE,IAAI,CAAC;EACZ,CAAC,EAAE,CAACT,iBAAiB,EAAEE,OAAO,CAAC,CAAC;EAChC,MAAMQ,cAAc,GAAG3C,WAAW,CAAC,MAAO4C,mBAAmB,IAAK;IAC9D,OAAOtC,oBAAoB,CAAC6B,OAAO,EAAES,mBAAmB,CAAC;EAC7D,CAAC,CAAC;EACF,OAAO,CAACZ,cAAc,EAAEW,cAAc,CAAC;AAC3C;AAEA,SAASZ,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}