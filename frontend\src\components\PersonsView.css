.persons-view {
  padding: 2rem;
  background-color: #f8f9fa;
  min-height: 100vh;
}

.persons-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-left h1 {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin: 0 0 0.5rem 0;
  color: #2c3e50;
  font-size: 2rem;
  font-weight: 600;
}

.header-icon {
  color: #3498db;
}

.header-subtitle {
  margin: 0;
  color: #6c757d;
  font-size: 1rem;
}

.header-actions {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
}

.btn-primary {
  background-color: #3498db;
  color: white;
}

.btn-primary:hover {
  background-color: #2980b9;
  transform: translateY(-1px);
}

.btn-outline {
  background-color: transparent;
  color: #3498db;
  border: 2px solid #3498db;
}

.btn-outline:hover {
  background-color: #3498db;
  color: white;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Filters Panel */
.filters-panel {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.filters-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.filter-group label {
  font-weight: 500;
  color: #2c3e50;
  font-size: 0.9rem;
}

.filter-group input,
.filter-group select {
  padding: 0.75rem;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 0.9rem;
  transition: border-color 0.2s ease;
}

.filter-group input:focus,
.filter-group select:focus {
  outline: none;
  border-color: #3498db;
}

.search-input {
  position: relative;
}

.search-icon {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: #6c757d;
}

.search-input input {
  padding-left: 2.5rem;
}

.filters-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 1.5rem;
  border-top: 1px solid #e9ecef;
}

.results-info {
  color: #6c757d;
  font-size: 0.9rem;
}

/* Content Area */
.persons-content {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.error-message {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  background-color: #fee;
  color: #c33;
  border-left: 4px solid #c33;
}

.loading-state,
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  color: #6c757d;
}

.loading-state svg,
.empty-state svg {
  margin-bottom: 1rem;
  color: #3498db;
}

.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.empty-state h3 {
  margin: 0 0 0.5rem 0;
  color: #2c3e50;
}

.empty-state p {
  margin: 0;
  text-align: center;
}

/* Table Controls */
.table-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #e9ecef;
}

.table-info label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  color: #6c757d;
}

.page-size-control label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  color: #6c757d;
}

.page-size-control select {
  padding: 0.25rem 0.5rem;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  font-size: 0.9rem;
}

/* Table */
.persons-table-container {
  overflow-x: auto;
}

.persons-table {
  width: 100%;
  border-collapse: collapse;
}

.persons-table th,
.persons-table td {
  padding: 1rem;
  text-align: left;
  border-bottom: 1px solid #e9ecef;
}

.persons-table th {
  background-color: #f8f9fa;
  font-weight: 600;
  color: #2c3e50;
  font-size: 0.9rem;
  position: sticky;
  top: 0;
  z-index: 10;
}

.persons-table th.sortable {
  cursor: pointer;
  user-select: none;
  position: relative;
}

.persons-table th.sortable:hover {
  background-color: #e9ecef;
}

.sort-indicator {
  margin-left: 0.5rem;
  font-size: 0.8rem;
}

.persons-table tr:hover {
  background-color: #f8f9fa;
}

.persons-table tr.selected {
  background-color: #e3f2fd;
}

.person-name strong {
  display: block;
  color: #2c3e50;
  font-size: 0.95rem;
}

.person-name small {
  display: block;
  color: #6c757d;
  font-size: 0.8rem;
  margin-top: 0.25rem;
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.85rem;
  color: #6c757d;
}

.hierarchy-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.hierarchy-info div {
  font-weight: 500;
  color: #2c3e50;
  font-size: 0.9rem;
}

.hierarchy-info small {
  color: #6c757d;
  font-size: 0.8rem;
}

.location-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.location-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.85rem;
  color: #6c757d;
}

.nature-badge {
  display: inline-block;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
  text-transform: uppercase;
}

.nature-badge.nature-1 {
  background-color: #e8f5e8;
  color: #2e7d32;
}

.nature-badge.nature-2 {
  background-color: #e3f2fd;
  color: #1976d2;
}

.nature-badge.nature-3 {
  background-color: #fff3e0;
  color: #f57c00;
}

.nature-badge.nature-4 {
  background-color: #fce4ec;
  color: #c2185b;
}

.star-rating {
  display: flex;
  gap: 0.125rem;
}

.star {
  color: #e0e0e0;
  font-size: 0.9rem;
}

.star.filled {
  color: #ffc107;
}

.no-rating {
  color: #6c757d;
  font-size: 0.8rem;
  font-style: italic;
}

.action-buttons {
  display: flex;
  gap: 0.5rem;
}

.btn-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  border: none;
  border-radius: 6px;
  background-color: #f8f9fa;
  color: #6c757d;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-icon:hover {
  background-color: #e9ecef;
  color: #495057;
}

.btn-icon.danger:hover {
  background-color: #dc3545;
  color: white;
}

/* Responsive Design */
@media (max-width: 768px) {
  .persons-view {
    padding: 1rem;
  }

  .persons-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .header-actions {
    justify-content: flex-start;
    flex-wrap: wrap;
  }

  .filters-grid {
    grid-template-columns: 1fr;
  }

  .filters-actions {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .table-controls {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .persons-table {
    font-size: 0.8rem;
  }

  .persons-table th,
  .persons-table td {
    padding: 0.5rem;
  }
}
