'use strict';
// TODO: Remove this entry from `core-js@4`
require('../proposals/accessible-object-hasownproperty');
require('../proposals/array-buffer-transfer');
require('../proposals/array-find-from-last');
require('../proposals/array-from-async-stage-2');
require('../proposals/array-grouping-v2');
require('../proposals/change-array-by-copy-stage-4');
// require('../proposals/error-cause');
require('../proposals/explicit-resource-management');
require('../proposals/float16');
require('../proposals/global-this');
require('../proposals/is-error');
require('../proposals/iterator-helpers-stage-3-2');
require('../proposals/promise-all-settled');
require('../proposals/promise-any');
require('../proposals/promise-try');
require('../proposals/promise-with-resolvers');
require('../proposals/regexp-escaping');
require('../proposals/relative-indexing-method');
require('../proposals/set-methods-v2');
require('../proposals/string-match-all');
require('../proposals/string-replace-all-stage-4');
require('../proposals/well-formed-unicode-strings');

var path = require('../internals/path');

module.exports = path;
