using Microsoft.AspNetCore.Mvc;
using CrmApi.Models.Person;
using CrmApi.Services.Person;

namespace CrmApi.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class PersonsController : ControllerBase
    {
        private readonly IPersonService _personService;

        public PersonsController(IPersonService personService)
        {
            _personService = personService;
        }

        /// <summary>
        /// Get all persons
        /// </summary>
        [HttpGet]
        public async Task<ActionResult<IEnumerable<PersonResponse>>> GetPersons([FromQuery] bool includeDeleted = false)
        {
            var persons = await _personService.GetAllPersonsAsync(includeDeleted);
            return Ok(persons);
        }

        /// <summary>
        /// Get person by ID
        /// </summary>
        [HttpGet("{id}")]
        public async Task<ActionResult<PersonResponse>> GetPerson(int id, [FromQuery] bool includeDeleted = false)
        {
            var person = await _personService.GetPersonByIdAsync(id, includeDeleted);

            if (person == null)
            {
                return NotFound();
            }

            return Ok(person);
        }

        /// <summary>
        /// Search persons with filters and pagination
        /// </summary>
        [HttpPost("search")]
        public async Task<ActionResult<PersonSearchResponse>> SearchPersons([FromBody] PersonSearchRequest request)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var result = await _personService.SearchPersonsAsync(request);
            return Ok(result);
        }

        /// <summary>
        /// Get persons by division
        /// </summary>
        [HttpGet("division/{divisionId}")]
        public async Task<ActionResult<IEnumerable<PersonResponse>>> GetPersonsByDivision(int divisionId, [FromQuery] bool includeDeleted = false)
        {
            var persons = await _personService.GetPersonsByDivisionAsync(divisionId, includeDeleted);
            return Ok(persons);
        }

        /// <summary>
        /// Get persons by category
        /// </summary>
        [HttpGet("category/{categoryId}")]
        public async Task<ActionResult<IEnumerable<PersonResponse>>> GetPersonsByCategory(int categoryId, [FromQuery] bool includeDeleted = false)
        {
            var persons = await _personService.GetPersonsByCategoryAsync(categoryId, includeDeleted);
            return Ok(persons);
        }

        /// <summary>
        /// Get persons by subcategory
        /// </summary>
        [HttpGet("subcategory/{subCategoryId}")]
        public async Task<ActionResult<IEnumerable<PersonResponse>>> GetPersonsBySubCategory(int subCategoryId, [FromQuery] bool includeDeleted = false)
        {
            var persons = await _personService.GetPersonsBySubCategoryAsync(subCategoryId, includeDeleted);
            return Ok(persons);
        }

        /// <summary>
        /// Search persons by mobile number
        /// </summary>
        [HttpGet("mobile/{mobileNumber}")]
        public async Task<ActionResult<IEnumerable<PersonResponse>>> GetPersonsByMobileNumber(string mobileNumber, [FromQuery] bool includeDeleted = false)
        {
            var persons = await _personService.GetPersonsByMobileNumberAsync(mobileNumber, includeDeleted);
            return Ok(persons);
        }

        /// <summary>
        /// Create a new person
        /// </summary>
        [HttpPost]
        public async Task<ActionResult<PersonResponse>> CreatePerson([FromBody] CreatePersonRequest request)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var person = await _personService.CreatePersonAsync(request);
            return CreatedAtAction("GetPerson", new { id = person.Id }, person);
        }

        /// <summary>
        /// Update an existing person
        /// </summary>
        [HttpPut("{id}")]
        public async Task<ActionResult<PersonResponse>> UpdatePerson(int id, [FromBody] UpdatePersonRequest request)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var person = await _personService.UpdatePersonAsync(id, request);
            return Ok(person);
        }

        /// <summary>
        /// Delete a person permanently
        /// </summary>
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeletePerson(int id)
        {
            var result = await _personService.DeletePersonAsync(id);
            if (!result)
            {
                return NotFound();
            }

            return NoContent();
        }

        /// <summary>
        /// Soft delete a person
        /// </summary>
        [HttpPost("{id}/soft-delete")]
        public async Task<IActionResult> SoftDeletePerson(int id)
        {
            var result = await _personService.SoftDeletePersonAsync(id);
            if (!result)
            {
                return NotFound();
            }

            return NoContent();
        }

        /// <summary>
        /// Restore a soft-deleted person
        /// </summary>
        [HttpPost("{id}/restore")]
        public async Task<IActionResult> RestorePerson(int id)
        {
            var result = await _personService.RestorePersonAsync(id);
            if (!result)
            {
                return NotFound();
            }

            return NoContent();
        }

        /// <summary>
        /// Get person statistics
        /// </summary>
        [HttpGet("statistics")]
        public async Task<ActionResult<PersonStatisticsResponse>> GetPersonStatistics()
        {
            var statistics = await _personService.GetPersonStatisticsAsync();
            return Ok(statistics);
        }

        /// <summary>
        /// Get person statistics by division
        /// </summary>
        [HttpGet("statistics/division/{divisionId}")]
        public async Task<ActionResult<PersonStatisticsResponse>> GetPersonStatisticsByDivision(int divisionId)
        {
            var statistics = await _personService.GetPersonStatisticsByDivisionAsync(divisionId);
            return Ok(statistics);
        }

        /// <summary>
        /// Get person statistics by category
        /// </summary>
        [HttpGet("statistics/category/{categoryId}")]
        public async Task<ActionResult<PersonStatisticsResponse>> GetPersonStatisticsByCategory(int categoryId)
        {
            var statistics = await _personService.GetPersonStatisticsByCategoryAsync(categoryId);
            return Ok(statistics);
        }

        /// <summary>
        /// Create multiple persons in bulk
        /// </summary>
        [HttpPost("bulk")]
        public async Task<ActionResult<IEnumerable<PersonResponse>>> CreateBulkPersons([FromBody] IEnumerable<CreatePersonRequest> requests)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var persons = await _personService.CreateBulkPersonsAsync(requests);
            return Ok(persons);
        }

        /// <summary>
        /// Soft delete multiple persons in bulk
        /// </summary>
        [HttpPost("bulk/soft-delete")]
        public async Task<IActionResult> SoftDeleteBulkPersons([FromBody] IEnumerable<int> ids)
        {
            var result = await _personService.SoftDeleteBulkPersonsAsync(ids);
            if (!result)
            {
                return BadRequest("No persons were deleted");
            }

            return NoContent();
        }

        /// <summary>
        /// Restore multiple persons in bulk
        /// </summary>
        [HttpPost("bulk/restore")]
        public async Task<IActionResult> RestoreBulkPersons([FromBody] IEnumerable<int> ids)
        {
            var result = await _personService.RestoreBulkPersonsAsync(ids);
            if (!result)
            {
                return BadRequest("No persons were restored");
            }

            return NoContent();
        }

        /// <summary>
        /// Get person enums for dropdowns
        /// </summary>
        [HttpGet("enums")]
        public ActionResult<object> GetPersonEnums()
        {
            var enums = new
            {
                PersonNature = Enum.GetValues<PersonNature>().Select(x => new { Value = (int)x, Name = x.ToString() }),
                Gender = Enum.GetValues<Gender>().Select(x => new { Value = (int)x, Name = x.ToString() }),
                WorkingProfile = Enum.GetValues<WorkingProfile>().Select(x => new { Value = (int)x, Name = x.ToString() })
            };

            return Ok(enums);
        }

        /// <summary>
        /// Export persons to Excel based on search criteria
        /// </summary>
        [HttpPost("export")]
        public async Task<ActionResult> ExportPersons([FromBody] PersonSearchRequest request)
        {
            try
            {
                // Set a large page size to get all matching records
                request.PageSize = 100000;
                request.Page = 1;

                var searchResult = await _personService.SearchPersonsAsync(request);

                if (searchResult.Persons == null || !searchResult.Persons.Any())
                {
                    return BadRequest("No persons found matching the criteria.");
                }

                // For now, return JSON. In a real implementation, you'd generate Excel
                // You can use libraries like EPPlus or ClosedXML to generate Excel files
                var fileName = $"persons_export_{DateTime.Now:yyyyMMdd_HHmmss}.json";

                return File(
                    System.Text.Encoding.UTF8.GetBytes(System.Text.Json.JsonSerializer.Serialize(searchResult.Persons, new System.Text.Json.JsonSerializerOptions { WriteIndented = true })),
                    "application/json",
                    fileName
                );
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Error exporting persons: {ex.Message}");
            }
        }
    }
}
