.field-mapping {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.mapping-header {
  text-align: center;
}

.mapping-header h3 {
  margin: 0 0 0.5rem 0;
  color: #495057;
  font-size: 1.5rem;
}

.mapping-header p {
  margin: 0 0 1.5rem 0;
  color: #6c757d;
}

.mapping-stats {
  display: flex;
  justify-content: center;
  gap: 2rem;
  margin-bottom: 1.5rem;
}

.stat {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.stat-value {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 0.25rem;
}

.stat-value.success {
  color: #28a745;
}

.stat-value.error {
  color: #dc3545;
}

.stat-label {
  font-size: 0.875rem;
  color: #6c757d;
}

.mapping-actions {
  display: flex;
  justify-content: center;
  gap: 1rem;
}

.required-fields-status {
  background-color: #f8f9fa;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  padding: 1.5rem;
}

.required-fields-status h4 {
  margin: 0 0 1rem 0;
  color: #495057;
}

.required-fields-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 0.75rem;
}

.required-field {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  border-radius: 4px;
  border: 1px solid #e1e5e9;
}

.required-field.mapped {
  background-color: #d4edda;
  border-color: #c3e6cb;
}

.required-field.unmapped {
  background-color: #f8d7da;
  border-color: #f5c6cb;
}

.field-name {
  font-weight: 600;
  color: #495057;
}

.field-status {
  font-size: 1.25rem;
}

.mapping-table-container {
  overflow-x: auto;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
}

.mapping-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.875rem;
}

.mapping-table th {
  background-color: #f8f9fa;
  padding: 1rem 0.75rem;
  text-align: left;
  font-weight: 600;
  color: #495057;
  border-bottom: 2px solid #e1e5e9;
  white-space: nowrap;
}

.mapping-table td {
  padding: 1rem 0.75rem;
  border-bottom: 1px solid #e1e5e9;
  vertical-align: top;
}

.mapped-row {
  background-color: #f8fff8;
}

.unmapped-row {
  background-color: #fff8f8;
}

.file-header strong {
  color: #495057;
  font-weight: 600;
}

.sample-data {
  font-style: italic;
  color: #6c757d;
}

.field-select {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 0.875rem;
}

.field-select.mapped {
  border-color: #28a745;
  background-color: #f8fff8;
}

.field-select.unmapped {
  border-color: #dc3545;
  background-color: #fff8f8;
}

.type-badge {
  background-color: #e9ecef;
  color: #495057;
  padding: 0.25rem 0.5rem;
  border-radius: 3px;
  font-size: 0.75rem;
  font-weight: 600;
}

.no-mapping {
  color: #6c757d;
  font-style: italic;
}

.required-badge {
  background-color: #dc3545;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 3px;
  font-size: 0.75rem;
  font-weight: 600;
}

.optional-badge {
  background-color: #6c757d;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 3px;
  font-size: 0.75rem;
  font-weight: 600;
}

.unmapped-warning {
  background-color: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 8px;
  padding: 1.5rem;
}

.unmapped-warning h4 {
  margin: 0 0 0.5rem 0;
  color: #856404;
}

.unmapped-warning p {
  margin: 0 0 1rem 0;
  color: #856404;
}

.unmapped-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.unmapped-header {
  background-color: #ffc107;
  color: #212529;
  padding: 0.25rem 0.5rem;
  border-radius: 3px;
  font-size: 0.875rem;
  font-weight: 500;
}

.mapping-summary {
  background-color: #f8f9fa;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  padding: 1.5rem;
}

.mapping-summary h4 {
  margin: 0 0 1rem 0;
  color: #495057;
}

.summary-section h5 {
  margin: 0 0 0.75rem 0;
  color: #495057;
  font-size: 1rem;
}

.mapped-fields-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 0.5rem;
}

.mapped-field-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem;
  background-color: white;
  border: 1px solid #e1e5e9;
  border-radius: 4px;
}

.field-label {
  font-weight: 500;
  color: #495057;
}

.field-badge.required {
  background-color: #dc3545;
  color: white;
}

.field-badge.optional {
  background-color: #6c757d;
  color: white;
}

.field-badge {
  padding: 0.125rem 0.375rem;
  border-radius: 3px;
  font-size: 0.75rem;
  font-weight: 600;
}

.mapping-navigation {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 1rem;
  border-top: 1px solid #e1e5e9;
}

.validation-error {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 1rem;
  padding: 1rem;
  background-color: #f8d7da;
  border: 1px solid #f5c6cb;
  border-radius: 4px;
  color: #721c24;
  font-weight: 500;
}

.btn {
  padding: 0.75rem 1.5rem;
  border: 1px solid transparent;
  border-radius: 4px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  text-decoration: none;
  display: inline-block;
  transition: all 0.2s ease;
}

.btn-primary {
  background-color: #007bff;
  border-color: #007bff;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background-color: #0056b3;
  border-color: #0056b3;
}

.btn-primary:disabled {
  background-color: #6c757d;
  border-color: #6c757d;
  cursor: not-allowed;
  opacity: 0.65;
}

.btn-outline {
  background-color: transparent;
  border-color: #6c757d;
  color: #6c757d;
}

.btn-outline:hover {
  background-color: #6c757d;
  color: white;
}

/* Default Values Section */
.default-values-section {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 1.5rem;
  margin-top: 1rem;
}

.default-values-section h4 {
  color: #495057;
  margin: 0 0 0.5rem 0;
  font-size: 1.1rem;
}

.section-description {
  color: #6c757d;
  font-size: 0.9rem;
  margin: 0 0 1.5rem 0;
  line-height: 1.4;
}

.default-values-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.default-value-input {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.default-value-label {
  font-weight: 500;
  color: #495057;
  font-size: 0.9rem;
}

.required-indicator {
  color: #dc3545;
  font-weight: bold;
}

.default-value-input-field,
.default-value-select {
  padding: 0.5rem;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 0.9rem;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.default-value-input-field:focus,
.default-value-select:focus {
  outline: none;
  border-color: #80bdff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.field-hint {
  color: #6c757d;
  font-size: 0.8rem;
  font-style: italic;
}

/* Enhanced Required Field Status */
.required-field .field-status {
  font-size: 0.8rem;
  font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
  .default-values-grid {
    grid-template-columns: 1fr;
  }
}
