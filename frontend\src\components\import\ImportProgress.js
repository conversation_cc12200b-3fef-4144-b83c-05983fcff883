import React, { useState, useEffect } from 'react';
import './ImportProgress.css';

const ImportProgress = ({ importJob, onCancel, error }) => {
  const [progress, setProgress] = useState(0);
  const [timeElapsed, setTimeElapsed] = useState(0);
  const [estimatedTimeRemaining, setEstimatedTimeRemaining] = useState(null);

  useEffect(() => {
    if (importJob) {
      const progressPercentage = importJob.totalRows > 0 
        ? Math.round((importJob.processedRows / importJob.totalRows) * 100)
        : 0;
      setProgress(progressPercentage);
    }
  }, [importJob]);

  useEffect(() => {
    const timer = setInterval(() => {
      if (importJob?.startedAt) {
        const elapsed = Math.floor((Date.now() - new Date(importJob.startedAt).getTime()) / 1000);
        setTimeElapsed(elapsed);

        // Calculate estimated time remaining
        if (importJob.processedRows > 0 && importJob.totalRows > 0) {
          const rate = importJob.processedRows / elapsed;
          const remaining = (importJob.totalRows - importJob.processedRows) / rate;
          setEstimatedTimeRemaining(Math.ceil(remaining));
        }
      }
    }, 1000);

    return () => clearInterval(timer);
  }, [importJob]);

  const formatTime = (seconds) => {
    if (seconds < 60) return `${seconds}s`;
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds}s`;
  };

  // Convert status to string (handles both enum numbers and strings)
  const getStatusString = (status) => {
    if (typeof status === 'string') {
      return status;
    }

    // Handle enum values
    const statusMap = {
      1: 'Pending',
      2: 'Processing',
      3: 'Completed',
      4: 'Failed',
      5: 'Cancelled'
    };

    return statusMap[status] || 'Unknown';
  };

  const getStatusColor = (status) => {
    const statusString = getStatusString(status);
    switch (statusString) {
      case 'Processing': return '#007bff';
      case 'Completed': return '#28a745';
      case 'Failed': return '#dc3545';
      case 'Cancelled': return '#6c757d';
      default: return '#6c757d';
    }
  };

  const getStatusIcon = (status) => {
    const statusString = getStatusString(status);
    switch (statusString) {
      case 'Processing': return '⏳';
      case 'Completed': return '✅';
      case 'Failed': return '❌';
      case 'Cancelled': return '⏹️';
      default: return '⏳';
    }
  };

  if (!importJob) {
    return (
      <div className="import-progress">
        <div className="progress-loading">
          <div className="loading-spinner"></div>
          <p>Starting import...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="import-progress">
      <div className="progress-header">
        <h3>Import Progress</h3>
        <div className="job-info">
          <span className="job-id">Job ID: {importJob.jobId}</span>
          <span 
            className="job-status"
            style={{ color: getStatusColor(importJob.status) }}
          >
            {getStatusIcon(importJob.status)} {getStatusString(importJob.status)}
          </span>
        </div>
      </div>

      {/* Progress Bar */}
      <div className="progress-section">
        <div className="progress-bar-container">
          <div className="progress-bar">
            <div 
              className="progress-fill"
              style={{ 
                width: `${progress}%`,
                backgroundColor: getStatusColor(importJob.status)
              }}
            ></div>
          </div>
          <div className="progress-text">
            {progress}% Complete
          </div>
        </div>

        <div className="progress-details">
          <div className="detail-item">
            <span className="detail-label">Processed:</span>
            <span className="detail-value">
              {importJob.processedRows?.toLocaleString() || 0} / {importJob.totalRows?.toLocaleString() || 0}
            </span>
          </div>
          <div className="detail-item">
            <span className="detail-label">Time Elapsed:</span>
            <span className="detail-value">{formatTime(timeElapsed)}</span>
          </div>
          {estimatedTimeRemaining && getStatusString(importJob.status) === 'Processing' && (
            <div className="detail-item">
              <span className="detail-label">Est. Remaining:</span>
              <span className="detail-value">{formatTime(estimatedTimeRemaining)}</span>
            </div>
          )}
        </div>
      </div>

      {/* Current Operation */}
      {importJob.currentOperation && (
        <div className="current-operation">
          <div className="operation-icon">🔄</div>
          <div className="operation-text">{importJob.currentOperation}</div>
        </div>
      )}

      {/* Statistics */}
      <div className="progress-stats">
        <div className="stats-grid">
          <div className="stat-card success">
            <div className="stat-icon">✅</div>
            <div className="stat-content">
              <div className="stat-value">{importJob.successfulRows?.toLocaleString() || 0}</div>
              <div className="stat-label">Successful</div>
            </div>
          </div>
          
          <div className="stat-card error">
            <div className="stat-icon">❌</div>
            <div className="stat-content">
              <div className="stat-value">{importJob.failedRows?.toLocaleString() || 0}</div>
              <div className="stat-label">Failed</div>
            </div>
          </div>
          
          <div className="stat-card warning">
            <div className="stat-icon">⏭️</div>
            <div className="stat-content">
              <div className="stat-value">{importJob.skippedRows?.toLocaleString() || 0}</div>
              <div className="stat-label">Skipped</div>
            </div>
          </div>
          
          {importJob.updatedRows > 0 && (
            <div className="stat-card info">
              <div className="stat-icon">🔄</div>
              <div className="stat-content">
                <div className="stat-value">{importJob.updatedRows?.toLocaleString() || 0}</div>
                <div className="stat-label">Updated</div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Recent Errors */}
      {importJob.errors && importJob.errors.length > 0 && (
        <div className="recent-errors">
          <h4>Recent Errors</h4>
          <div className="errors-list">
            {importJob.errors.slice(0, 5).map((error, index) => (
              <div key={index} className="error-item">
                <div className="error-row">Row {error.rowNumber}</div>
                <div className="error-field">{error.fieldName}</div>
                <div className="error-message">{error.errorMessage}</div>
              </div>
            ))}
            {importJob.errors.length > 5 && (
              <div className="more-errors">
                +{importJob.errors.length - 5} more errors
              </div>
            )}
          </div>
        </div>
      )}

      {/* Processing Log */}
      <div className="processing-log">
        <h4>Processing Log</h4>
        <div className="log-container">
          <div className="log-entry">
            <span className="log-time">{new Date(importJob.startedAt).toLocaleTimeString()}</span>
            <span className="log-message">Import started</span>
          </div>
          {getStatusString(importJob.status) === 'Processing' && (
            <div className="log-entry current">
              <span className="log-time">{new Date().toLocaleTimeString()}</span>
              <span className="log-message">{importJob.currentOperation || 'Processing...'}</span>
            </div>
          )}
          {importJob.completedAt && (
            <div className="log-entry">
              <span className="log-time">{new Date(importJob.completedAt).toLocaleTimeString()}</span>
              <span className="log-message">Import {getStatusString(importJob.status).toLowerCase()}</span>
            </div>
          )}
        </div>
      </div>

      {/* Actions */}
      <div className="progress-actions">
        {getStatusString(importJob.status) === 'Processing' && (
          <button onClick={onCancel} className="btn btn-outline">
            Cancel Import
          </button>
        )}
        
        {(importJob.status === 'Completed' || importJob.status === 'Failed' || importJob.status === 'Cancelled') && (
          <div className="completion-message">
            <div className={`completion-icon ${importJob.status.toLowerCase()}`}>
              {getStatusIcon(importJob.status)}
            </div>
            <div className="completion-text">
              Import {importJob.status.toLowerCase()}
              {importJob.status === 'Completed' && (
                <div className="completion-details">
                  Successfully processed {importJob.successfulRows} records
                  {importJob.failedRows > 0 && ` with ${importJob.failedRows} errors`}
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      {error && (
        <div className="progress-error">
          <span className="error-icon">⚠️</span>
          <span className="error-message">{error}</span>
        </div>
      )}
    </div>
  );
};

export default ImportProgress;
