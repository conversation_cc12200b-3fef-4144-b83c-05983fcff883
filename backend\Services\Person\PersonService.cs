using CrmApi.Models.Person;
using CrmApi.Models.Division;
using CrmApi.Models.Category;
using CrmApi.Models.SubCategory;
using CrmApi.Repositories.Person;
using CrmApi.Repositories.Division;
using CrmApi.Repositories.Category;
using CrmApi.Repositories.SubCategory;
using CrmApi.Exceptions;

namespace CrmApi.Services.Person
{
    public class PersonService : IPersonService
    {
        private readonly IPersonRepository _personRepository;
        private readonly IDivisionRepository _divisionRepository;
        private readonly ICategoryRepository _categoryRepository;
        private readonly ISubCategoryRepository _subCategoryRepository;

        public PersonService(
            IPersonRepository personRepository,
            IDivisionRepository divisionRepository,
            ICategoryRepository categoryRepository,
            ISubCategoryRepository subCategoryRepository)
        {
            _personRepository = personRepository;
            _divisionRepository = divisionRepository;
            _categoryRepository = categoryRepository;
            _subCategoryRepository = subCategoryRepository;
        }

        public async Task<IEnumerable<PersonResponse>> GetAllPersonsAsync(bool includeDeleted = false)
        {
            var persons = await _personRepository.GetAllAsync(includeDeleted);
            return persons.Select(MapToResponse);
        }

        public async Task<PersonResponse?> GetPersonByIdAsync(int id, bool includeDeleted = false)
        {
            var person = await _personRepository.GetByIdWithRelationsAsync(id, includeDeleted);
            return person != null ? MapToResponse(person) : null;
        }

        public async Task<PersonResponse> CreatePersonAsync(CreatePersonRequest request)
        {
            // Validate business rules
            await ValidatePersonDataAsync(request);

            // Check if division exists
            if (!await _divisionRepository.ExistsAsync(request.DivisionId))
                throw new NotFoundException($"Division with ID {request.DivisionId} not found");

            // Check if category exists
            if (!await _categoryRepository.ExistsAsync(request.CategoryId))
                throw new NotFoundException($"Category with ID {request.CategoryId} not found");

            // Check if subcategory exists (if provided)
            if (request.SubCategoryId.HasValue && !await _subCategoryRepository.ExistsAsync(request.SubCategoryId.Value))
                throw new NotFoundException($"SubCategory with ID {request.SubCategoryId} not found");

            // Check for duplicate mobile number in same division/category
            if (await _personRepository.MobileNumberExistsInDivisionCategoryAsync(
                request.MobileNumber, request.DivisionId, request.CategoryId))
                throw new BusinessException($"A person with mobile number '{request.MobileNumber}' already exists in this division and category");

            var person = MapToEntity(request);
            var createdPerson = await _personRepository.CreateAsync(person);
            
            // Get the person with relations for response
            var personWithRelations = await _personRepository.GetByIdWithRelationsAsync(createdPerson.Id);
            return MapToResponse(personWithRelations!);
        }

        public async Task<PersonResponse> UpdatePersonAsync(int id, UpdatePersonRequest request)
        {
            var person = await _personRepository.GetByIdAsync(id);
            if (person == null)
                throw new NotFoundException($"Person with ID {id} not found");

            // Validate business rules
            await ValidatePersonUpdateDataAsync(id, request);

            // Check if division exists (if provided)
            if (request.DivisionId.HasValue && !await _divisionRepository.ExistsAsync(request.DivisionId.Value))
                throw new NotFoundException($"Division with ID {request.DivisionId} not found");

            // Check if category exists (if provided)
            if (request.CategoryId.HasValue && !await _categoryRepository.ExistsAsync(request.CategoryId.Value))
                throw new NotFoundException($"Category with ID {request.CategoryId} not found");

            // Check if subcategory exists (if provided)
            if (request.SubCategoryId.HasValue && !await _subCategoryRepository.ExistsAsync(request.SubCategoryId.Value))
                throw new NotFoundException($"SubCategory with ID {request.SubCategoryId} not found");

            // Check for duplicate mobile number in same division/category (if mobile number is being updated)
            if (!string.IsNullOrEmpty(request.MobileNumber))
            {
                var divisionId = request.DivisionId ?? person.DivisionId;
                var categoryId = request.CategoryId ?? person.CategoryId;
                
                if (await _personRepository.MobileNumberExistsInDivisionCategoryAsync(
                    request.MobileNumber, divisionId, categoryId, id))
                    throw new BusinessException($"A person with mobile number '{request.MobileNumber}' already exists in this division and category");
            }

            // Update person properties
            UpdatePersonFromRequest(person, request);
            
            var updatedPerson = await _personRepository.UpdateAsync(person);
            
            // Get the person with relations for response
            var personWithRelations = await _personRepository.GetByIdWithRelationsAsync(updatedPerson.Id);
            return MapToResponse(personWithRelations!);
        }

        public async Task<bool> DeletePersonAsync(int id)
        {
            if (!await _personRepository.ExistsAsync(id))
                throw new NotFoundException($"Person with ID {id} not found");

            return await _personRepository.DeleteAsync(id);
        }

        public async Task<bool> SoftDeletePersonAsync(int id)
        {
            if (!await _personRepository.ExistsAsync(id))
                throw new NotFoundException($"Person with ID {id} not found");

            return await _personRepository.SoftDeleteAsync(id);
        }

        public async Task<bool> RestorePersonAsync(int id)
        {
            if (!await _personRepository.ExistsAsync(id, includeDeleted: true))
                throw new NotFoundException($"Person with ID {id} not found");

            return await _personRepository.RestoreAsync(id);
        }

        public async Task<PersonSearchResponse> SearchPersonsAsync(PersonSearchRequest request)
        {
            var (persons, totalCount) = await _personRepository.SearchAsync(request);
            
            return new PersonSearchResponse
            {
                Persons = persons.Select(MapToResponse).ToList(),
                TotalCount = totalCount,
                Page = request.Page,
                PageSize = request.PageSize
            };
        }

        public async Task<IEnumerable<PersonResponse>> GetPersonsByDivisionAsync(int divisionId, bool includeDeleted = false)
        {
            var persons = await _personRepository.GetByDivisionAsync(divisionId, includeDeleted);
            return persons.Select(MapToResponse);
        }

        public async Task<IEnumerable<PersonResponse>> GetPersonsByCategoryAsync(int categoryId, bool includeDeleted = false)
        {
            var persons = await _personRepository.GetByCategoryAsync(categoryId, includeDeleted);
            return persons.Select(MapToResponse);
        }

        public async Task<IEnumerable<PersonResponse>> GetPersonsBySubCategoryAsync(int subCategoryId, bool includeDeleted = false)
        {
            var persons = await _personRepository.GetBySubCategoryAsync(subCategoryId, includeDeleted);
            return persons.Select(MapToResponse);
        }

        public async Task<IEnumerable<PersonResponse>> GetPersonsByMobileNumberAsync(string mobileNumber, bool includeDeleted = false)
        {
            var persons = await _personRepository.GetByMobileNumberAsync(mobileNumber, includeDeleted);
            return persons.Select(MapToResponse);
        }

        public async Task<bool> ValidatePersonDataAsync(CreatePersonRequest request)
        {
            var errors = new List<string>();

            // Validate conditional required fields
            if (request.IsMarried == true && !request.DateOfMarriage.HasValue)
                errors.Add("Date of marriage is required when person is married");

            if (request.HasAssociate)
            {
                if (string.IsNullOrEmpty(request.AssociateName))
                    errors.Add("Associate name is required when person has an associate");
                
                if (string.IsNullOrEmpty(request.AssociateRelation))
                    errors.Add("Associate relation is required when person has an associate");
                
                if (string.IsNullOrEmpty(request.AssociateMobile))
                    errors.Add("Associate mobile is required when person has an associate");
            }

            if (request.UsingWebsite && string.IsNullOrEmpty(request.WebsiteLink))
                errors.Add("Website link is required when person is using website");

            if (request.UsingCRMApp && string.IsNullOrEmpty(request.CRMAppLink))
                errors.Add("CRM app link is required when person is using CRM app");

            // Validate date logic
            if (request.DateOfBirth.HasValue && request.DateOfBirth.Value > DateTime.Now)
                errors.Add("Date of birth cannot be in the future");

            if (request.DateOfMarriage.HasValue && request.DateOfMarriage.Value > DateTime.Now)
                errors.Add("Date of marriage cannot be in the future");

            if (request.DateOfBirth.HasValue && request.DateOfMarriage.HasValue && 
                request.DateOfMarriage.Value < request.DateOfBirth.Value)
                errors.Add("Date of marriage cannot be before date of birth");

            // Validate alternate numbers format
            if (request.AlternateNumbers?.Any() == true)
            {
                foreach (var number in request.AlternateNumbers)
                {
                    if (!System.Text.RegularExpressions.Regex.IsMatch(number, @"^(\+91[\-\s]?)?[0]?(91)?[789]\d{9}$"))
                        errors.Add($"Invalid alternate number format: {number}");
                }
            }

            // Validate alternate emails format
            if (request.AlternateEmailIds?.Any() == true)
            {
                foreach (var email in request.AlternateEmailIds)
                {
                    if (!new System.ComponentModel.DataAnnotations.EmailAddressAttribute().IsValid(email))
                        errors.Add($"Invalid alternate email format: {email}");
                }
            }

            if (errors.Any())
                throw new ValidationException("Validation failed", new Dictionary<string, string[]> 
                { 
                    { "ValidationErrors", errors.ToArray() } 
                });

            return true;
        }

        public async Task<bool> ValidatePersonUpdateDataAsync(int id, UpdatePersonRequest request)
        {
            var errors = new List<string>();

            // Get existing person for validation
            var existingPerson = await _personRepository.GetByIdAsync(id);
            if (existingPerson == null)
                throw new NotFoundException($"Person with ID {id} not found");

            // Validate conditional required fields
            var isMarried = request.IsMarried ?? existingPerson.IsMarried;
            var dateOfMarriage = request.DateOfMarriage ?? existingPerson.DateOfMarriage;
            
            if (isMarried == true && !dateOfMarriage.HasValue)
                errors.Add("Date of marriage is required when person is married");

            var hasAssociate = request.HasAssociate ?? existingPerson.HasAssociate;
            var associateName = request.AssociateName ?? existingPerson.AssociateName;
            var associateRelation = request.AssociateRelation ?? existingPerson.AssociateRelation;
            var associateMobile = request.AssociateMobile ?? existingPerson.AssociateMobile;

            if (hasAssociate)
            {
                if (string.IsNullOrEmpty(associateName))
                    errors.Add("Associate name is required when person has an associate");
                
                if (string.IsNullOrEmpty(associateRelation))
                    errors.Add("Associate relation is required when person has an associate");
                
                if (string.IsNullOrEmpty(associateMobile))
                    errors.Add("Associate mobile is required when person has an associate");
            }

            var usingWebsite = request.UsingWebsite ?? existingPerson.UsingWebsite;
            var websiteLink = request.WebsiteLink ?? existingPerson.WebsiteLink;
            
            if (usingWebsite && string.IsNullOrEmpty(websiteLink))
                errors.Add("Website link is required when person is using website");

            var usingCRMApp = request.UsingCRMApp ?? existingPerson.UsingCRMApp;
            var crmAppLink = request.CRMAppLink ?? existingPerson.CRMAppLink;
            
            if (usingCRMApp && string.IsNullOrEmpty(crmAppLink))
                errors.Add("CRM app link is required when person is using CRM app");

            // Validate date logic
            var dateOfBirth = request.DateOfBirth ?? existingPerson.DateOfBirth;
            
            if (request.DateOfBirth.HasValue && request.DateOfBirth.Value > DateTime.Now)
                errors.Add("Date of birth cannot be in the future");

            if (request.DateOfMarriage.HasValue && request.DateOfMarriage.Value > DateTime.Now)
                errors.Add("Date of marriage cannot be in the future");

            if (dateOfBirth.HasValue && dateOfMarriage.HasValue && 
                dateOfMarriage.Value < dateOfBirth.Value)
                errors.Add("Date of marriage cannot be before date of birth");

            // Validate alternate numbers format
            if (request.AlternateNumbers?.Any() == true)
            {
                foreach (var number in request.AlternateNumbers)
                {
                    if (!System.Text.RegularExpressions.Regex.IsMatch(number, @"^(\+91[\-\s]?)?[0]?(91)?[789]\d{9}$"))
                        errors.Add($"Invalid alternate number format: {number}");
                }
            }

            // Validate alternate emails format
            if (request.AlternateEmailIds?.Any() == true)
            {
                foreach (var email in request.AlternateEmailIds)
                {
                    if (!new System.ComponentModel.DataAnnotations.EmailAddressAttribute().IsValid(email))
                        errors.Add($"Invalid alternate email format: {email}");
                }
            }

            if (errors.Any())
                throw new ValidationException("Validation failed", new Dictionary<string, string[]> 
                { 
                    { "ValidationErrors", errors.ToArray() } 
                });

            return true;
        }

        public async Task<PersonStatisticsResponse> GetPersonStatisticsAsync()
        {
            var allPersons = await _personRepository.GetAllAsync();
            var deletedPersons = await _personRepository.GetAllAsync(includeDeleted: true);
            
            return new PersonStatisticsResponse
            {
                TotalPersons = deletedPersons.Count(),
                ActivePersons = allPersons.Count(),
                DeletedPersons = deletedPersons.Count(p => p.IsDeleted),
                PersonsByNature = allPersons.GroupBy(p => p.Nature.ToString())
                    .ToDictionary(g => g.Key, g => g.Count()),
                PersonsByGender = allPersons.Where(p => p.Gender.HasValue)
                    .GroupBy(p => p.Gender!.Value.ToString())
                    .ToDictionary(g => g.Key, g => g.Count()),
                AverageStarRating = await _personRepository.GetAverageStarRatingAsync(),
                TotalTransactionValue = await _personRepository.GetTotalTransactionValueAsync(),
                PersonsWithAssociates = allPersons.Count(p => p.HasAssociate),
                PersonsUsingWebsite = allPersons.Count(p => p.UsingWebsite),
                PersonsUsingCRMApp = allPersons.Count(p => p.UsingCRMApp)
            };
        }

        public async Task<PersonStatisticsResponse> GetPersonStatisticsByDivisionAsync(int divisionId)
        {
            var persons = await _personRepository.GetByDivisionAsync(divisionId);
            var allPersonsInDivision = await _personRepository.GetByDivisionAsync(divisionId, includeDeleted: true);
            
            return new PersonStatisticsResponse
            {
                TotalPersons = allPersonsInDivision.Count(),
                ActivePersons = persons.Count(),
                DeletedPersons = allPersonsInDivision.Count(p => p.IsDeleted),
                PersonsByNature = persons.GroupBy(p => p.Nature.ToString())
                    .ToDictionary(g => g.Key, g => g.Count()),
                PersonsByGender = persons.Where(p => p.Gender.HasValue)
                    .GroupBy(p => p.Gender!.Value.ToString())
                    .ToDictionary(g => g.Key, g => g.Count()),
                AverageStarRating = persons.Where(p => p.StarRating.HasValue).Any() 
                    ? (decimal)persons.Where(p => p.StarRating.HasValue).Average(p => p.StarRating!.Value) 
                    : 0,
                TotalTransactionValue = persons.Where(p => p.TransactionValue.HasValue).Sum(p => p.TransactionValue!.Value),
                PersonsWithAssociates = persons.Count(p => p.HasAssociate),
                PersonsUsingWebsite = persons.Count(p => p.UsingWebsite),
                PersonsUsingCRMApp = persons.Count(p => p.UsingCRMApp)
            };
        }

        public async Task<PersonStatisticsResponse> GetPersonStatisticsByCategoryAsync(int categoryId)
        {
            var persons = await _personRepository.GetByCategoryAsync(categoryId);
            var allPersonsInCategory = await _personRepository.GetByCategoryAsync(categoryId, includeDeleted: true);
            
            return new PersonStatisticsResponse
            {
                TotalPersons = allPersonsInCategory.Count(),
                ActivePersons = persons.Count(),
                DeletedPersons = allPersonsInCategory.Count(p => p.IsDeleted),
                PersonsByNature = persons.GroupBy(p => p.Nature.ToString())
                    .ToDictionary(g => g.Key, g => g.Count()),
                PersonsByGender = persons.Where(p => p.Gender.HasValue)
                    .GroupBy(p => p.Gender!.Value.ToString())
                    .ToDictionary(g => g.Key, g => g.Count()),
                AverageStarRating = persons.Where(p => p.StarRating.HasValue).Any() 
                    ? (decimal)persons.Where(p => p.StarRating.HasValue).Average(p => p.StarRating!.Value) 
                    : 0,
                TotalTransactionValue = persons.Where(p => p.TransactionValue.HasValue).Sum(p => p.TransactionValue!.Value),
                PersonsWithAssociates = persons.Count(p => p.HasAssociate),
                PersonsUsingWebsite = persons.Count(p => p.UsingWebsite),
                PersonsUsingCRMApp = persons.Count(p => p.UsingCRMApp)
            };
        }

        public async Task<IEnumerable<PersonResponse>> CreateBulkPersonsAsync(IEnumerable<CreatePersonRequest> requests)
        {
            var requestList = requests.ToList();
            var persons = new List<Models.Person.Person>();

            // Validate all requests first
            foreach (var request in requestList)
            {
                await ValidatePersonDataAsync(request);
                
                // Check if division exists
                if (!await _divisionRepository.ExistsAsync(request.DivisionId))
                    throw new NotFoundException($"Division with ID {request.DivisionId} not found");

                // Check if category exists
                if (!await _categoryRepository.ExistsAsync(request.CategoryId))
                    throw new NotFoundException($"Category with ID {request.CategoryId} not found");

                // Check if subcategory exists (if provided)
                if (request.SubCategoryId.HasValue && !await _subCategoryRepository.ExistsAsync(request.SubCategoryId.Value))
                    throw new NotFoundException($"SubCategory with ID {request.SubCategoryId} not found");

                persons.Add(MapToEntity(request));
            }

            // Check for duplicate mobile numbers within the batch
            var duplicateMobiles = persons
                .GroupBy(p => new { p.MobileNumber, p.DivisionId, p.CategoryId })
                .Where(g => g.Count() > 1)
                .Select(g => g.Key.MobileNumber)
                .ToList();

            if (duplicateMobiles.Any())
                throw new BusinessException($"Duplicate mobile numbers found in batch: {string.Join(", ", duplicateMobiles)}");

            // Check for existing mobile numbers in database
            foreach (var person in persons)
            {
                if (await _personRepository.MobileNumberExistsInDivisionCategoryAsync(
                    person.MobileNumber, person.DivisionId, person.CategoryId))
                    throw new BusinessException($"A person with mobile number '{person.MobileNumber}' already exists in division {person.DivisionId} and category {person.CategoryId}");
            }

            var createdPersons = await _personRepository.CreateBulkAsync(persons);
            return createdPersons.Select(MapToResponse);
        }

        public async Task<bool> SoftDeleteBulkPersonsAsync(IEnumerable<int> ids)
        {
            var idList = ids.ToList();
            
            // Check if all persons exist
            foreach (var id in idList)
            {
                if (!await _personRepository.ExistsAsync(id))
                    throw new NotFoundException($"Person with ID {id} not found");
            }

            return await _personRepository.SoftDeleteBulkAsync(idList);
        }

        public async Task<bool> RestoreBulkPersonsAsync(IEnumerable<int> ids)
        {
            var idList = ids.ToList();
            
            // Check if all persons exist (including deleted)
            foreach (var id in idList)
            {
                if (!await _personRepository.ExistsAsync(id, includeDeleted: true))
                    throw new NotFoundException($"Person with ID {id} not found");
            }

            return await _personRepository.RestoreBulkAsync(idList);
        }

        private PersonResponse MapToResponse(Models.Person.Person person)
        {
            return new PersonResponse
            {
                Id = person.Id,
                DivisionId = person.DivisionId,
                CategoryId = person.CategoryId,
                SubCategoryId = person.SubCategoryId,
                Name = person.Name,
                MobileNumber = person.MobileNumber,
                Nature = person.Nature,
                Gender = person.Gender,
                AlternateNumbers = person.AlternateNumbers,
                PrimaryEmailId = person.PrimaryEmailId,
                AlternateEmailIds = person.AlternateEmailIds,
                Website = person.Website,
                DateOfBirth = person.DateOfBirth,
                IsMarried = person.IsMarried,
                DateOfMarriage = person.DateOfMarriage,
                WorkingState = person.WorkingState,
                DomesticState = person.DomesticState,
                District = person.District,
                Address = person.Address,
                WorkingArea = person.WorkingArea,
                HasAssociate = person.HasAssociate,
                AssociateName = person.AssociateName,
                AssociateRelation = person.AssociateRelation,
                AssociateMobile = person.AssociateMobile,
                UsingWebsite = person.UsingWebsite,
                WebsiteLink = person.WebsiteLink,
                UsingCRMApp = person.UsingCRMApp,
                CRMAppLink = person.CRMAppLink,
                TransactionValue = person.TransactionValue,
                RERARegistrationNumber = person.RERARegistrationNumber,
                WorkingProfiles = person.WorkingProfiles,
                StarRating = person.StarRating,
                Source = person.Source,
                Remarks = person.Remarks,
                FirmName = person.FirmName,
                NumberOfOffices = person.NumberOfOffices,
                NumberOfBranches = person.NumberOfBranches,
                TotalEmployeeStrength = person.TotalEmployeeStrength,
                AuthorizedPersonName = person.AuthorizedPersonName,
                AuthorizedPersonEmail = person.AuthorizedPersonEmail,
                Designation = person.Designation,
                MarketingContact = person.MarketingContact,
                MarketingDesignation = person.MarketingDesignation,
                PlaceOfPosting = person.PlaceOfPosting,
                Department = person.Department,
                CreatedAt = person.CreatedAt,
                UpdatedAt = person.UpdatedAt,
                Division = person.Division != null ? new DivisionResponse
                {
                    Id = person.Division.Id,
                    Name = person.Division.Name,
                    CreatedAt = person.Division.CreatedAt,
                    UpdatedAt = person.Division.UpdatedAt
                } : null,
                Category = person.Category != null ? new CategoryResponse
                {
                    Id = person.Category.Id,
                    DivisionId = person.Category.DivisionId,
                    Name = person.Category.Name,
                    CreatedAt = person.Category.CreatedAt,
                    UpdatedAt = person.Category.UpdatedAt
                } : null,
                SubCategory = person.SubCategory != null ? new SubCategoryResponse
                {
                    Id = person.SubCategory.Id,
                    CategoryId = person.SubCategory.CategoryId,
                    Name = person.SubCategory.Name,
                    CreatedAt = person.SubCategory.CreatedAt,
                    UpdatedAt = person.SubCategory.UpdatedAt
                } : null
            };
        }

        private Models.Person.Person MapToEntity(CreatePersonRequest request)
        {
            return new Models.Person.Person
            {
                DivisionId = request.DivisionId,
                CategoryId = request.CategoryId,
                SubCategoryId = request.SubCategoryId,
                Name = request.Name,
                MobileNumber = request.MobileNumber,
                Nature = request.Nature,
                Gender = request.Gender,
                AlternateNumbers = request.AlternateNumbers,
                PrimaryEmailId = request.PrimaryEmailId,
                AlternateEmailIds = request.AlternateEmailIds,
                Website = request.Website,
                DateOfBirth = request.DateOfBirth,
                IsMarried = request.IsMarried,
                DateOfMarriage = request.DateOfMarriage,
                WorkingState = request.WorkingState,
                DomesticState = request.DomesticState,
                District = request.District,
                Address = request.Address,
                WorkingArea = request.WorkingArea,
                HasAssociate = request.HasAssociate,
                AssociateName = request.AssociateName,
                AssociateRelation = request.AssociateRelation,
                AssociateMobile = request.AssociateMobile,
                UsingWebsite = request.UsingWebsite,
                WebsiteLink = request.WebsiteLink,
                UsingCRMApp = request.UsingCRMApp,
                CRMAppLink = request.CRMAppLink,
                TransactionValue = request.TransactionValue,
                RERARegistrationNumber = request.RERARegistrationNumber,
                WorkingProfiles = request.WorkingProfiles,
                StarRating = request.StarRating,
                Source = request.Source,
                Remarks = request.Remarks,
                FirmName = request.FirmName,
                NumberOfOffices = request.NumberOfOffices,
                NumberOfBranches = request.NumberOfBranches,
                TotalEmployeeStrength = request.TotalEmployeeStrength,
                AuthorizedPersonName = request.AuthorizedPersonName,
                AuthorizedPersonEmail = request.AuthorizedPersonEmail,
                Designation = request.Designation,
                MarketingContact = request.MarketingContact,
                MarketingDesignation = request.MarketingDesignation,
                PlaceOfPosting = request.PlaceOfPosting,
                Department = request.Department
            };
        }

        private void UpdatePersonFromRequest(Models.Person.Person person, UpdatePersonRequest request)
        {
            if (request.DivisionId.HasValue) person.DivisionId = request.DivisionId.Value;
            if (request.CategoryId.HasValue) person.CategoryId = request.CategoryId.Value;
            if (request.SubCategoryId.HasValue) person.SubCategoryId = request.SubCategoryId.Value;
            if (!string.IsNullOrEmpty(request.Name)) person.Name = request.Name;
            if (!string.IsNullOrEmpty(request.MobileNumber)) person.MobileNumber = request.MobileNumber;
            if (request.Nature.HasValue) person.Nature = request.Nature.Value;
            if (request.Gender.HasValue) person.Gender = request.Gender.Value;
            if (request.AlternateNumbers != null) person.AlternateNumbers = request.AlternateNumbers;
            if (!string.IsNullOrEmpty(request.PrimaryEmailId)) person.PrimaryEmailId = request.PrimaryEmailId;
            if (request.AlternateEmailIds != null) person.AlternateEmailIds = request.AlternateEmailIds;
            if (!string.IsNullOrEmpty(request.Website)) person.Website = request.Website;
            if (request.DateOfBirth.HasValue) person.DateOfBirth = request.DateOfBirth;
            if (request.IsMarried.HasValue) person.IsMarried = request.IsMarried;
            if (request.DateOfMarriage.HasValue) person.DateOfMarriage = request.DateOfMarriage;
            if (!string.IsNullOrEmpty(request.WorkingState)) person.WorkingState = request.WorkingState;
            if (!string.IsNullOrEmpty(request.DomesticState)) person.DomesticState = request.DomesticState;
            if (!string.IsNullOrEmpty(request.District)) person.District = request.District;
            if (!string.IsNullOrEmpty(request.Address)) person.Address = request.Address;
            if (!string.IsNullOrEmpty(request.WorkingArea)) person.WorkingArea = request.WorkingArea;
            if (request.HasAssociate.HasValue) person.HasAssociate = request.HasAssociate.Value;
            if (!string.IsNullOrEmpty(request.AssociateName)) person.AssociateName = request.AssociateName;
            if (!string.IsNullOrEmpty(request.AssociateRelation)) person.AssociateRelation = request.AssociateRelation;
            if (!string.IsNullOrEmpty(request.AssociateMobile)) person.AssociateMobile = request.AssociateMobile;
            if (request.UsingWebsite.HasValue) person.UsingWebsite = request.UsingWebsite.Value;
            if (!string.IsNullOrEmpty(request.WebsiteLink)) person.WebsiteLink = request.WebsiteLink;
            if (request.UsingCRMApp.HasValue) person.UsingCRMApp = request.UsingCRMApp.Value;
            if (!string.IsNullOrEmpty(request.CRMAppLink)) person.CRMAppLink = request.CRMAppLink;
            if (request.TransactionValue.HasValue) person.TransactionValue = request.TransactionValue;
            if (!string.IsNullOrEmpty(request.RERARegistrationNumber)) person.RERARegistrationNumber = request.RERARegistrationNumber;
            if (request.WorkingProfiles != null) person.WorkingProfiles = request.WorkingProfiles;
            if (request.StarRating.HasValue) person.StarRating = request.StarRating;
            if (!string.IsNullOrEmpty(request.Source)) person.Source = request.Source;
            if (!string.IsNullOrEmpty(request.Remarks)) person.Remarks = request.Remarks;
            if (!string.IsNullOrEmpty(request.FirmName)) person.FirmName = request.FirmName;
            if (request.NumberOfOffices.HasValue) person.NumberOfOffices = request.NumberOfOffices;
            if (request.NumberOfBranches.HasValue) person.NumberOfBranches = request.NumberOfBranches;
            if (request.TotalEmployeeStrength.HasValue) person.TotalEmployeeStrength = request.TotalEmployeeStrength;
            if (!string.IsNullOrEmpty(request.AuthorizedPersonName)) person.AuthorizedPersonName = request.AuthorizedPersonName;
            if (!string.IsNullOrEmpty(request.AuthorizedPersonEmail)) person.AuthorizedPersonEmail = request.AuthorizedPersonEmail;
            if (!string.IsNullOrEmpty(request.Designation)) person.Designation = request.Designation;
            if (!string.IsNullOrEmpty(request.MarketingContact)) person.MarketingContact = request.MarketingContact;
            if (!string.IsNullOrEmpty(request.MarketingDesignation)) person.MarketingDesignation = request.MarketingDesignation;
            if (!string.IsNullOrEmpty(request.PlaceOfPosting)) person.PlaceOfPosting = request.PlaceOfPosting;
            if (!string.IsNullOrEmpty(request.Department)) person.Department = request.Department;
        }
    }
}
