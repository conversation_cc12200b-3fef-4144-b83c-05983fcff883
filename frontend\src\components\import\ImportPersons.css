/* Standalone page styles */
.import-page {
  padding: 2rem;
  min-height: 100vh;
  background-color: #f8f9fa;
}

.import-container {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* Legacy modal styles for backward compatibility */
.import-persons-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  padding: 1rem;
}

.import-modal-content {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 1200px;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.import-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #e1e5e9;
  background-color: #f8f9fa;
}

.import-header h2 {
  margin: 0;
  color: #495057;
}

.import-subtitle {
  margin: 0.5rem 0 0 0;
  color: #6c757d;
  font-size: 0.9rem;
}

.close-button {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #6c757d;
  padding: 0;
  width: 2rem;
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
}

.close-button:hover {
  background-color: #e9ecef;
  color: #495057;
}

.step-indicator {
  display: flex;
  justify-content: center;
  padding: 1.5rem;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e1e5e9;
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1rem;
  margin: 0 1rem;
  border-radius: 8px;
  transition: all 0.3s ease;
  min-width: 120px;
}

.step.active {
  background-color: #e7f3ff;
  border: 2px solid #007bff;
}

.step.current {
  background-color: #007bff;
  color: white;
}

.step-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.step-title {
  font-size: 0.875rem;
  font-weight: 600;
  text-align: center;
}

.import-body {
  flex: 1;
  overflow-y: auto;
  padding: 2rem;
}

.import-error {
  background-color: #f8d7da;
  border: 1px solid #f5c6cb;
  border-radius: 4px;
  padding: 1rem;
  margin: 1rem;
}

.error-content {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.error-icon {
  font-size: 1.5rem;
}

.error-message {
  flex: 1;
  color: #721c24;
  font-weight: 500;
}

.retry-button {
  background-color: #dc3545;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.875rem;
}

.retry-button:hover {
  background-color: #c82333;
}

/* Responsive design */
@media (max-width: 768px) {
  .import-persons-modal {
    padding: 0.5rem;
  }
  
  .import-modal-content {
    max-width: 100%;
    max-height: 95vh;
  }
  
  .import-header {
    padding: 1rem;
  }
  
  .step-indicator {
    padding: 1rem;
    overflow-x: auto;
  }
  
  .step {
    margin: 0 0.5rem;
    min-width: 100px;
  }
  
  .step-icon {
    font-size: 1.5rem;
  }
  
  .step-title {
    font-size: 0.75rem;
  }
  
  .import-body {
    padding: 1rem;
  }
}

@media (max-width: 480px) {
  .step {
    flex-direction: row;
    min-width: auto;
    padding: 0.5rem;
  }
  
  .step-icon {
    font-size: 1.25rem;
    margin-bottom: 0;
    margin-right: 0.5rem;
  }
  
  .step-title {
    font-size: 0.7rem;
  }
}
