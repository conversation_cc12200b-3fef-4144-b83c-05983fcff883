using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using CrmApi.Data;
using CrmApi.Models.Admin;

namespace CrmApi.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class TestController : ControllerBase
    {
        private readonly CrmDbContext _context;
        private readonly ILogger<TestController> _logger;

        public TestController(CrmDbContext context, ILogger<TestController> logger)
        {
            _context = context;
            _logger = logger;
        }

        /// <summary>
        /// Test endpoint to check if admin user exists and create one if needed
        /// </summary>
        [HttpGet("admin-status")]
        public async Task<ActionResult> CheckAdminStatus()
        {
            try
            {
                // Check if admin user exists
                var adminUser = await _context.Admins
                    .Where(a => a.Username == "admin")
                    .FirstOrDefaultAsync();

                if (adminUser == null)
                {
                    // Create admin user with proper BCrypt hash
                    var passwordHash = BCrypt.Net.BCrypt.HashPassword("admin123");
                    
                    var newAdmin = new Admin
                    {
                        Username = "admin",
                        Email = "<EMAIL>",
                        PasswordHash = passwordHash,
                        FirstName = "System",
                        LastName = "Administrator",
                        IsActive = true,
                        CreatedAt = DateTime.UtcNow,
                        UpdatedAt = DateTime.UtcNow
                    };

                    _context.Admins.Add(newAdmin);
                    await _context.SaveChangesAsync();

                    return Ok(new { 
                        message = "Admin user created successfully",
                        username = "admin",
                        password = "admin123",
                        created = true
                    });
                }
                else
                {
                    // Test password verification
                    bool isPasswordValid = BCrypt.Net.BCrypt.Verify("admin123", adminUser.PasswordHash);
                    
                    return Ok(new { 
                        message = "Admin user already exists",
                        username = adminUser.Username,
                        email = adminUser.Email,
                        isActive = adminUser.IsActive,
                        passwordValid = isPasswordValid,
                        created = false,
                        lastLogin = adminUser.LastLoginAt
                    });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking admin status");
                return StatusCode(500, new { error = ex.Message });
            }
        }

        /// <summary>
        /// Reset admin password to admin123
        /// </summary>
        [HttpGet("reset-admin-password")]
        public async Task<ActionResult> ResetAdminPassword()
        {
            try
            {
                var adminUser = await _context.Admins
                    .Where(a => a.Username == "admin")
                    .FirstOrDefaultAsync();

                if (adminUser == null)
                {
                    return NotFound(new { message = "Admin user not found" });
                }

                // Generate new password hash
                var newPasswordHash = BCrypt.Net.BCrypt.HashPassword("admin123");
                adminUser.PasswordHash = newPasswordHash;
                adminUser.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                // Verify the new password
                bool isPasswordValid = BCrypt.Net.BCrypt.Verify("admin123", adminUser.PasswordHash);

                return Ok(new { 
                    message = "Admin password reset successfully",
                    username = "admin",
                    password = "admin123",
                    passwordValid = isPasswordValid
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error resetting admin password");
                return StatusCode(500, new { error = ex.Message });
            }
        }

        /// <summary>
        /// Test login functionality
        /// </summary>
        [HttpPost("test-login")]
        public async Task<ActionResult> TestLogin([FromBody] TestLoginRequest request)
        {
            try
            {
                var adminUser = await _context.Admins
                    .Where(a => a.Username == request.Username)
                    .FirstOrDefaultAsync();

                if (adminUser == null)
                {
                    return Ok(new { 
                        success = false, 
                        message = "User not found",
                        username = request.Username
                    });
                }

                bool isPasswordValid = BCrypt.Net.BCrypt.Verify(request.Password, adminUser.PasswordHash);

                return Ok(new { 
                    success = isPasswordValid,
                    message = isPasswordValid ? "Login successful" : "Invalid password",
                    username = adminUser.Username,
                    email = adminUser.Email,
                    isActive = adminUser.IsActive,
                    passwordHash = adminUser.PasswordHash.Substring(0, 20) + "..." // Show partial hash for debugging
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error testing login");
                return StatusCode(500, new { error = ex.Message });
            }
        }
    }

    public class TestLoginRequest
    {
        public string Username { get; set; } = "";
        public string Password { get; set; } = "";
    }
}
