using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using CrmApi.Models.Admin;
using CrmApi.Repositories.Admin;
using CrmApi.Exceptions;

namespace CrmApi.Services.Admin
{
    public class AdminService : IAdminService
    {
        private readonly IAdminRepository _adminRepository;
        private readonly IConfiguration _configuration;

        public AdminService(IAdminRepository adminRepository, IConfiguration configuration)
        {
            _adminRepository = adminRepository;
            _configuration = configuration;
        }

        public async Task<IEnumerable<AdminResponse>> GetAllAdminsAsync()
        {
            var admins = await _adminRepository.GetAllAsync();
            return admins.Select(MapToResponse);
        }

        public async Task<AdminResponse?> GetAdminByIdAsync(int id)
        {
            var admin = await _adminRepository.GetByIdAsync(id);
            return admin != null ? MapToResponse(admin) : null;
        }

        public async Task<AdminResponse> CreateAdminAsync(CreateAdminRequest request)
        {
            // Check if username already exists
            if (await _adminRepository.UsernameExistsAsync(request.Username))
                throw new BusinessException($"Username '{request.Username}' already exists");

            // Check if email already exists
            if (await _adminRepository.EmailExistsAsync(request.Email))
                throw new BusinessException($"Email '{request.Email}' already exists");

            var admin = new Models.Admin.Admin
            {
                Username = request.Username,
                Email = request.Email,
                PasswordHash = HashPassword(request.Password),
                FirstName = request.FirstName,
                LastName = request.LastName,
                IsActive = request.IsActive
            };

            var createdAdmin = await _adminRepository.CreateAsync(admin);
            return MapToResponse(createdAdmin);
        }

        public async Task<AdminResponse> UpdateAdminAsync(int id, UpdateAdminRequest request)
        {
            var admin = await _adminRepository.GetByIdAsync(id);
            if (admin == null)
                throw new NotFoundException($"Admin with ID {id} not found");

            // Check username uniqueness if provided
            if (!string.IsNullOrEmpty(request.Username) && 
                await _adminRepository.UsernameExistsAsync(request.Username, id))
                throw new BusinessException($"Username '{request.Username}' already exists");

            // Check email uniqueness if provided
            if (!string.IsNullOrEmpty(request.Email) && 
                await _adminRepository.EmailExistsAsync(request.Email, id))
                throw new BusinessException($"Email '{request.Email}' already exists");

            // Update fields if provided
            if (!string.IsNullOrEmpty(request.Username))
                admin.Username = request.Username;
            if (!string.IsNullOrEmpty(request.Email))
                admin.Email = request.Email;
            if (!string.IsNullOrEmpty(request.Password))
                admin.PasswordHash = HashPassword(request.Password);
            if (!string.IsNullOrEmpty(request.FirstName))
                admin.FirstName = request.FirstName;
            if (!string.IsNullOrEmpty(request.LastName))
                admin.LastName = request.LastName;
            if (request.IsActive.HasValue)
                admin.IsActive = request.IsActive.Value;

            var updatedAdmin = await _adminRepository.UpdateAsync(admin);
            return MapToResponse(updatedAdmin);
        }

        public async Task<bool> DeleteAdminAsync(int id)
        {
            if (!await _adminRepository.ExistsAsync(id))
                throw new NotFoundException($"Admin with ID {id} not found");

            return await _adminRepository.DeleteAsync(id);
        }

        public async Task<LoginResponse?> LoginAsync(LoginRequest request)
        {
            var admin = await _adminRepository.GetByUsernameAsync(request.Username);
            
            if (admin == null || !admin.IsActive || !VerifyPassword(request.Password, admin.PasswordHash))
                return null;

            // Update last login
            admin.LastLoginAt = DateTime.UtcNow;
            await _adminRepository.UpdateAsync(admin);

            var token = GenerateJwtToken(admin);
            
            return new LoginResponse
            {
                Token = token,
                Username = admin.Username,
                Email = admin.Email,
                FirstName = admin.FirstName,
                LastName = admin.LastName,
                ExpiresAt = DateTime.UtcNow.AddHours(24)
            };
        }

        public Task<bool> ValidateTokenAsync(string token)
        {
            try
            {
                var tokenHandler = new JwtSecurityTokenHandler();
                var jwtSettings = _configuration.GetSection("JwtSettings");
                var key = Encoding.ASCII.GetBytes(jwtSettings["SecretKey"] ?? "your-super-secret-key-that-is-at-least-32-characters-long");

                tokenHandler.ValidateToken(token, new TokenValidationParameters
                {
                    ValidateIssuerSigningKey = true,
                    IssuerSigningKey = new SymmetricSecurityKey(key),
                    ValidateIssuer = true,
                    ValidIssuer = jwtSettings["Issuer"] ?? "CrmApi",
                    ValidateAudience = true,
                    ValidAudience = jwtSettings["Audience"] ?? "CrmClient",
                    ValidateLifetime = true,
                    ClockSkew = TimeSpan.Zero
                }, out SecurityToken validatedToken);

                return Task.FromResult(true);
            }
            catch
            {
                return Task.FromResult(false);
            }
        }

        private AdminResponse MapToResponse(Models.Admin.Admin admin)
        {
            return new AdminResponse
            {
                Id = admin.Id,
                Username = admin.Username,
                Email = admin.Email,
                FirstName = admin.FirstName,
                LastName = admin.LastName,
                IsActive = admin.IsActive,
                CreatedAt = admin.CreatedAt,
                UpdatedAt = admin.UpdatedAt,
                LastLoginAt = admin.LastLoginAt
            };
        }

        private string HashPassword(string password)
        {
            return BCrypt.Net.BCrypt.HashPassword(password);
        }

        private bool VerifyPassword(string password, string hash)
        {
            return BCrypt.Net.BCrypt.Verify(password, hash);
        }

        private string GenerateJwtToken(Models.Admin.Admin admin)
        {
            var jwtSettings = _configuration.GetSection("JwtSettings");
            var key = Encoding.ASCII.GetBytes(jwtSettings["SecretKey"] ?? "your-super-secret-key-that-is-at-least-32-characters-long");

            var tokenDescriptor = new SecurityTokenDescriptor
            {
                Subject = new ClaimsIdentity(new[]
                {
                    new Claim(ClaimTypes.NameIdentifier, admin.Id.ToString()),
                    new Claim(ClaimTypes.Name, admin.Username),
                    new Claim(ClaimTypes.Email, admin.Email),
                    new Claim("FirstName", admin.FirstName),
                    new Claim("LastName", admin.LastName)
                }),
                Expires = DateTime.UtcNow.AddHours(24),
                SigningCredentials = new SigningCredentials(new SymmetricSecurityKey(key), SecurityAlgorithms.HmacSha256Signature),
                Issuer = jwtSettings["Issuer"] ?? "CrmApi",
                Audience = jwtSettings["Audience"] ?? "CrmClient"
            };

            var tokenHandler = new JwtSecurityTokenHandler();
            var token = tokenHandler.CreateToken(tokenDescriptor);
            return tokenHandler.WriteToken(token);
        }
    }
}
