.form-builder {
  display: flex;
  flex-direction: column;
  min-height: calc(100vh - 120px);
  height: auto;
  background-color: #f8f9fa;
  overflow: visible;
}

.form-builder-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  background-color: white;
  border-bottom: 1px solid #e1e5e9;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.form-builder-header h2 {
  margin: 0;
  color: #495057;
}

.header-actions {
  display: flex;
  gap: 0.5rem;
}

.form-builder-content {
  display: flex;
  flex: 1;
  overflow: visible;
  min-height: 0;
}

.config-panel {
  width: 300px;
  background-color: white;
  border-right: 1px solid #e1e5e9;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  max-height: calc(100vh - 200px);
  flex-shrink: 0;
}

.config-section {
  padding: 1.5rem;
  border-bottom: 1px solid #e1e5e9;
  background-color: white;
}

.config-section h3 {
  margin: 0 0 1rem 0;
  color: #495057;
  font-size: 1.1rem;
}

.form-group {
  margin-bottom: 1rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: #495057;
}

.form-help-text {
  margin: 0.5rem 0;
  font-size: 0.875rem;
  color: #6c757d;
  line-height: 1.4;
}

/* Saved Forms Section */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.btn-link {
  background: none;
  border: none;
  color: #007bff;
  cursor: pointer;
  font-size: 0.875rem;
  text-decoration: underline;
}

.btn-link:hover {
  color: #0056b3;
}

.saved-forms-list {
  max-height: 300px;
  overflow-y: auto;
}

.no-forms-message {
  color: #6c757d;
  font-style: italic;
  text-align: center;
  padding: 1rem;
  margin: 0;
}

.saved-form-item {
  border: 1px solid #e1e5e9;
  border-radius: 6px;
  padding: 1rem;
  margin-bottom: 0.75rem;
  background-color: #f8f9fa;
}

.form-item-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.5rem;
}

.form-item-header h4 {
  margin: 0;
  font-size: 1rem;
  color: #495057;
}

.form-type-badge {
  background-color: #007bff;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  text-transform: uppercase;
  font-weight: 500;
}

.form-description {
  margin: 0.5rem 0;
  font-size: 0.875rem;
  color: #6c757d;
  line-height: 1.4;
}

.form-meta {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.75rem;
  color: #6c757d;
  margin-bottom: 0.75rem;
}

.form-actions {
  display: flex;
  gap: 0.5rem;
}

.btn-small {
  padding: 0.375rem 0.75rem;
  font-size: 0.75rem;
  border-radius: 4px;
  border: 1px solid;
  cursor: pointer;
  font-weight: 500;
}

.btn-small.btn-outline {
  background-color: transparent;
  color: #007bff;
  border-color: #007bff;
}

.btn-small.btn-outline:hover {
  background-color: #007bff;
  color: white;
}

.btn-small.btn-danger {
  background-color: #dc3545;
  color: white;
  border-color: #dc3545;
}

.btn-small.btn-danger:hover {
  background-color: #c82333;
  border-color: #bd2130;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 1rem;
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #80bdff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-group input.error,
.form-group textarea.error {
  border-color: #dc3545;
}

.selected-fields {
  max-height: 400px;
  overflow-y: auto;
}

.empty-state {
  text-align: center;
  color: #6c757d;
  font-style: italic;
  padding: 2rem;
  border: 2px dashed #dee2e6;
  border-radius: 4px;
}

.selected-field {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  margin-bottom: 0.5rem;
  background-color: #f8f9fa;
  border: 1px solid #e1e5e9;
  border-radius: 4px;
}

.field-info {
  flex: 1;
}

.field-label {
  display: block;
  font-weight: 600;
  color: #495057;
}

.field-type {
  font-size: 0.875rem;
  color: #6c757d;
  margin-right: 0.5rem;
}

.required-badge {
  background-color: #dc3545;
  color: white;
  padding: 0.125rem 0.375rem;
  border-radius: 3px;
  font-size: 0.75rem;
  font-weight: 600;
}

.conditional-badge {
  background-color: #ffc107;
  color: #212529;
  padding: 0.125rem 0.375rem;
  border-radius: 3px;
  font-size: 0.75rem;
  font-weight: 600;
}

.field-actions {
  display: flex;
  gap: 0.25rem;
}

.btn-icon {
  background: none;
  border: none;
  padding: 0.25rem;
  cursor: pointer;
  border-radius: 3px;
  font-size: 0.875rem;
}

.btn-icon:hover {
  background-color: #e9ecef;
}

.btn-icon:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-icon.remove:hover {
  background-color: #f8d7da;
  color: #721c24;
}

.fields-panel {
  flex: 1;
  background-color: white;
  display: flex;
  flex-direction: column;
  padding: 1rem;
  overflow-y: auto;
  max-height: calc(100vh - 200px);
  min-width: 0;
}

.fields-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #e1e5e9;
}

.fields-header h3 {
  margin: 0;
  color: #495057;
}

.hierarchy-selection {
  background-color: #f8f9fa;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  padding: 1.5rem;
  margin: 1rem 0;
}

.hierarchy-selection h4 {
  margin: 0 0 1rem 0;
  color: #495057;
  font-size: 1.1rem;
}

.hierarchy-dropdowns {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.hierarchy-dropdowns .form-group {
  margin-bottom: 0;
}

.hierarchy-dropdowns label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #495057;
}

.hierarchy-dropdowns select {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 1rem;
  background-color: white;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.hierarchy-dropdowns select:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.hierarchy-dropdowns select:disabled {
  background-color: #e9ecef;
  opacity: 0.6;
  cursor: not-allowed;
}

.hierarchy-dropdowns select.error {
  border-color: #dc3545;
}

.hierarchy-dropdowns select.error:focus {
  border-color: #dc3545;
  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}



.search-input {
  padding: 0.75rem;
  border: 1px solid #ced4da;
  border-radius: 4px;
  width: 100%;
  font-size: 1rem;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.search-input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.section-tabs {
  display: flex;
  border-bottom: 1px solid #e1e5e9;
  overflow-x: auto;
  flex-shrink: 0;
  scrollbar-width: thin;
}

.section-tab {
  background: none;
  border: none;
  padding: 0.75rem 1rem;
  cursor: pointer;
  border-bottom: 3px solid transparent;
  white-space: nowrap;
  color: #6c757d;
  font-weight: 500;
  flex-shrink: 0;
  min-width: fit-content;
}

.section-tab:hover {
  background-color: #f8f9fa;
  color: #495057;
}

.section-tab.active {
  color: #007bff;
  border-bottom-color: #007bff;
  background-color: #f8f9fa;
}

.fields-list {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
  min-height: 400px;
  max-height: calc(100vh - 400px);
}

.field-item {
  display: flex;
  align-items: center;
  padding: 0.75rem;
  margin-bottom: 0.5rem;
  border: 1px solid #e1e5e9;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.field-item:hover {
  background-color: #f8f9fa;
  border-color: #007bff;
}

.field-item.selected {
  background-color: #e7f3ff;
  border-color: #007bff;
}

.field-checkbox {
  margin-right: 0.75rem;
}

.field-checkbox input[type="checkbox"] {
  width: 1.2rem;
  height: 1.2rem;
}

.field-details {
  flex: 1;
}

.field-name {
  font-weight: 600;
  color: #495057;
  margin-bottom: 0.25rem;
}

.field-meta {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* Buttons */
.btn {
  padding: 0.5rem 1rem;
  border: 1px solid transparent;
  border-radius: 4px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  text-decoration: none;
  display: inline-block;
  transition: all 0.2s ease;
}

.btn-primary {
  background-color: #007bff;
  border-color: #007bff;
  color: white;
}

.btn-primary:hover {
  background-color: #0056b3;
  border-color: #0056b3;
}

.btn-primary:disabled {
  background-color: #6c757d;
  border-color: #6c757d;
  cursor: not-allowed;
}

.btn-secondary {
  background-color: #6c757d;
  border-color: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background-color: #545b62;
  border-color: #545b62;
}

.btn-secondary:disabled {
  opacity: 0.65;
  cursor: not-allowed;
}

.btn-outline {
  background-color: transparent;
  border-color: #6c757d;
  color: #6c757d;
}

.btn-outline:hover {
  background-color: #6c757d;
  color: white;
}

/* Alerts */
.alert {
  padding: 0.75rem 1.25rem;
  margin-bottom: 1rem;
  border: 1px solid transparent;
  border-radius: 4px;
}

.alert-error {
  color: #721c24;
  background-color: #f8d7da;
  border-color: #f5c6cb;
}

.error-message {
  color: #dc3545;
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

/* Responsive design */
@media (max-width: 768px) {
  .form-builder-content {
    flex-direction: column;
  }
  
  .config-panel {
    width: 100%;
    max-height: 50vh;
  }
  
  .fields-panel {
    height: 50vh;
  }
  
  .header-actions {
    flex-direction: column;
    gap: 0.25rem;
  }
  
  .section-tabs {
    flex-wrap: wrap;
  }
}

/* Additional responsive improvements */
@media (max-width: 1200px) {
  .config-panel {
    width: 250px;
  }
}

/* Ensure scrollbars are visible and styled */
.section-tabs::-webkit-scrollbar {
  height: 6px;
}

.section-tabs::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.section-tabs::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.section-tabs::-webkit-scrollbar-thumb:hover {
  background: #a1a1a1;
}

.fields-list::-webkit-scrollbar {
  width: 8px;
}

.fields-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.fields-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.fields-list::-webkit-scrollbar-thumb:hover {
  background: #a1a1a1;
}
