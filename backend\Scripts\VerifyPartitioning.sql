-- Script to verify that partitioning has been successfully created
-- Run this in your MySQL client or workbench

USE data_crm;

-- 1. Check if the table is partitioned
SELECT 
    TABLE_SCHEMA,
    TABLE_NAME,
    PARTITION_NAME,
    PARTITION_ORDINAL_POSITION,
    <PERSON><PERSON><PERSON><PERSON>_METHOD,
    PARTITION_EXPRESSION,
    TABLE_ROWS,
    DATA_LENGTH,
    INDEX_LENGTH
FROM INFORMATION_SCHEMA.PARTITIONS 
WHERE TABLE_SCHEMA = 'data_crm' 
  AND TABLE_NAME = 'persons'
ORDER BY PARTITION_ORDINAL_POSITION;

-- 2. Show table creation statement (includes partition definition)
SHOW CREATE TABLE persons;

-- 3. Check partition distribution (shows how data is distributed across partitions)
SELECT 
    PARTITION_NAME,
    TABLE_ROWS,
    ROUND(DATA_LENGTH/1024/1024, 2) AS 'Data Size (MB)',
    ROUND(INDEX_LENGTH/1024/1024, 2) AS 'Index Size (MB)',
    PARTITION_DESCRIPTION
FROM INFORMATION_SCHEMA.PARTITIONS 
WHERE TABLE_SCHEMA = 'data_crm' 
  AND TABLE_NAME = 'persons'
  AND PARTITION_NAME IS NOT NULL
ORDER BY PARTITION_ORDINAL_POSITION;

-- 4. Verify partition pruning (this will show which partitions are accessed)
-- First, let's insert some test data to see partitioning in action
INSERT INTO persons (
    division_id, category_id, sub_category_id, name, mobile_number, nature,
    alternate_numbers, primary_email_id, alternate_email_ids, website,
    working_state, domestic_state, district, address, working_area,
    has_associate, associate_name, associate_relation, associate_mobile,
    using_website, website_link, using_crm_app, crm_app_link,
    rera_registration_number, working_profiles, source, remarks,
    firm_name, authorized_person_name, authorized_person_email,
    designation, marketing_contact, marketing_designation,
    place_of_posting, department, created_at, updated_at, is_deleted
) VALUES 
-- Test data for Division 1, Category 1 (will go to one partition)
(1, 1, 1, 'John Doe', '9876543210', 1, '', '<EMAIL>', '', '', 
 'Maharashtra', 'Maharashtra', 'Mumbai', '123 Main St', 'Andheri',
 0, '', '', '', 0, '', 0, '', '', '', 'Website', 'Test person 1',
 'ABC Corp', 'John Doe', '<EMAIL>', 'Manager', 'Jane Smith', 'Marketing Manager',
 'Mumbai', 'Sales', NOW(), NOW(), 0),

-- Test data for Division 2, Category 1 (will go to different partition)
(2, 1, 1, 'Jane Smith', '9876543211', 1, '', '<EMAIL>', '', '',
 'Delhi', 'Delhi', 'New Delhi', '456 Park Ave', 'CP',
 0, '', '', '', 0, '', 0, '', '', '', 'Referral', 'Test person 2',
 'XYZ Ltd', 'Jane Smith', '<EMAIL>', 'Director', 'John Doe', 'Sales Manager',
 'Delhi', 'Marketing', NOW(), NOW(), 0),

-- Test data for Division 1, Category 2 (will go to another partition)
(1, 2, 2, 'Bob Wilson', '9876543212', 2, '', '<EMAIL>', '', '',
 'Karnataka', 'Karnataka', 'Bangalore', '789 Tech Park', 'Whitefield',
 0, '', '', '', 0, '', 0, '', '', '', 'Online', 'Test person 3',
 'Tech Solutions', 'Bob Wilson', '<EMAIL>', 'CEO', 'Alice Brown', 'HR Manager',
 'Bangalore', 'Technology', NOW(), NOW(), 0);

-- 5. Now check partition distribution after inserting data
SELECT 
    PARTITION_NAME,
    TABLE_ROWS,
    ROUND(DATA_LENGTH/1024/1024, 2) AS 'Data Size (MB)',
    ROUND(INDEX_LENGTH/1024/1024, 2) AS 'Index Size (MB)'
FROM INFORMATION_SCHEMA.PARTITIONS 
WHERE TABLE_SCHEMA = 'data_crm' 
  AND TABLE_NAME = 'persons'
  AND PARTITION_NAME IS NOT NULL
  AND TABLE_ROWS > 0
ORDER BY PARTITION_ORDINAL_POSITION;

-- 6. Test partition pruning with EXPLAIN
-- This should show which specific partitions are being accessed
EXPLAIN PARTITIONS 
SELECT * FROM persons 
WHERE division_id = 1 AND category_id = 1;

EXPLAIN PARTITIONS 
SELECT * FROM persons 
WHERE division_id = 2 AND category_id = 1;

-- 7. Show all tables in the database to confirm structure
SELECT 
    TABLE_NAME,
    ENGINE,
    TABLE_ROWS,
    ROUND(DATA_LENGTH/1024/1024, 2) AS 'Data Size (MB)',
    CREATE_TIME
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = 'data_crm'
ORDER BY TABLE_NAME;

-- 8. Verify indexes on partitioned table
SELECT 
    INDEX_NAME,
    COLUMN_NAME,
    SEQ_IN_INDEX,
    NON_UNIQUE
FROM INFORMATION_SCHEMA.STATISTICS 
WHERE TABLE_SCHEMA = 'data_crm' 
  AND TABLE_NAME = 'persons'
ORDER BY INDEX_NAME, SEQ_IN_INDEX;
