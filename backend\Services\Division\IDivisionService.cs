using CrmApi.Models.Division;

namespace CrmApi.Services.Division
{
    public interface IDivisionService
    {
        Task<IEnumerable<DivisionResponse>> GetAllDivisionsAsync();
        Task<DivisionResponse?> GetDivisionByIdAsync(int id);
        Task<DivisionResponse?> GetDivisionWithCategoriesAsync(int id);
        Task<DivisionResponse> CreateDivisionAsync(CreateDivisionRequest request);
        Task<DivisionResponse> UpdateDivisionAsync(int id, UpdateDivisionRequest request);
        Task<bool> DeleteDivisionAsync(int id);
    }
}
