using System.Collections.Concurrent;
using System.Globalization;
using System.Text;
using CsvHelper;
using CsvHelper.Configuration;
using OfficeOpenXml;
using CrmApi.Models.ImportExport;
using CrmApi.Models.Person;
using CrmApi.Services.Person;
using CrmApi.Repositories.Division;
using CrmApi.Repositories.Category;
using CrmApi.Repositories.SubCategory;
using CrmApi.Exceptions;

namespace CrmApi.Services.ImportExport
{
    public partial class ImportExportService : IImportExportService
    {
        private readonly IPersonService _personService;
        private readonly IDivisionRepository _divisionRepository;
        private readonly ICategoryRepository _categoryRepository;
        private readonly ISubCategoryRepository _subCategoryRepository;
        private readonly ILogger<ImportExportService> _logger;
        private readonly IServiceProvider _serviceProvider;
        
        // In-memory storage for import jobs (in production, use Redis or database)
        private static readonly ConcurrentDictionary<string, PersonImportResponse> _importJobs = new();
        private static readonly ConcurrentDictionary<string, ImportProgressUpdate> _importProgress = new();

        public ImportExportService(
            IPersonService personService,
            IDivisionRepository divisionRepository,
            ICategoryRepository categoryRepository,
            ISubCategoryRepository subCategoryRepository,
            ILogger<ImportExportService> logger,
            IServiceProvider serviceProvider)
        {
            _personService = personService;
            _divisionRepository = divisionRepository;
            _categoryRepository = categoryRepository;
            _subCategoryRepository = subCategoryRepository;
            _logger = logger;
            _serviceProvider = serviceProvider;
            
            // Set EPPlus license context
            ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
        }

        public async Task<PersonImportResponse> ImportPersonsAsync(PersonImportRequest request)
        {
            var jobId = Guid.NewGuid().ToString();
            var response = new PersonImportResponse
            {
                JobId = jobId,
                Status = ImportStatus.Pending,
                StartedAt = DateTime.UtcNow
            };

            _importJobs[jobId] = response;

            try
            {
                // Copy file content to memory stream before starting background task
                // This prevents "Cannot access a closed Stream" errors when the HTTP context is disposed
                var fileContent = new MemoryStream();
                using (var sourceStream = request.File.OpenReadStream())
                {
                    await sourceStream.CopyToAsync(fileContent);
                }
                fileContent.Position = 0; // Reset position for reading

                // Create a modified request with the copied stream
                var processRequest = new PersonImportProcessRequest
                {
                    FileContent = fileContent,
                    FileName = request.File.FileName,
                    ImportMode = request.ImportMode,
                    ValidateOnly = request.ValidateOnly,
                    BatchSize = request.BatchSize,
                    DefaultDivisionId = request.DefaultDivisionId,
                    DefaultCategoryId = request.DefaultCategoryId,
                    DefaultSubCategoryId = request.DefaultSubCategoryId,
                    DefaultValues = request.DefaultValues,
                    FieldMapping = request.FieldMapping
                };

                // Start background processing with new scope to avoid DbContext disposal issues
                _ = Task.Run(async () =>
                {
                    using var scope = _serviceProvider.CreateScope();
                    var scopedImportService = scope.ServiceProvider.GetRequiredService<IImportExportService>();
                    await ((ImportExportService)scopedImportService).ProcessImportAsync(jobId, processRequest);
                });

                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to start import job {JobId}", jobId);
                response.Status = ImportStatus.Failed;
                response.ErrorMessage = ex.Message;
                response.CompletedAt = DateTime.UtcNow;
                return response;
            }
        }

        public async Task<PersonImportResponse> ValidateImportFileAsync(PersonImportRequest request)
        {
            var jobId = Guid.NewGuid().ToString();
            var response = new PersonImportResponse
            {
                JobId = jobId,
                Status = ImportStatus.Processing,
                StartedAt = DateTime.UtcNow
            };

            try
            {
                // Parse file
                var fileFormat = GetFileFormat(request.File.FileName);
                using var stream = request.File.OpenReadStream();
                var importData = await ParseFileAsync(stream, fileFormat);

                response.TotalRows = importData.Count;

                // Validate data
                var validationErrors = await ValidateImportDataAsync(importData, request);
                response.Errors = validationErrors;
                response.FailedRows = validationErrors.Count(e => e.Severity == ErrorSeverity.Error);
                response.SuccessfulRows = response.TotalRows - response.FailedRows;
                response.Status = ImportStatus.Completed;
                response.CompletedAt = DateTime.UtcNow;

                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to validate import file for job {JobId}", jobId);
                response.Status = ImportStatus.Failed;
                response.ErrorMessage = ex.Message;
                response.CompletedAt = DateTime.UtcNow;
                return response;
            }
        }

        public async Task<PersonImportResponse> GetImportStatusAsync(string jobId)
        {
            if (_importJobs.TryGetValue(jobId, out var response))
            {
                return response;
            }

            throw new NotFoundException($"Import job with ID {jobId} not found");
        }

        public async Task<bool> CancelImportAsync(string jobId)
        {
            if (_importJobs.TryGetValue(jobId, out var response))
            {
                if (response.Status == ImportStatus.Processing)
                {
                    response.Status = ImportStatus.Cancelled;
                    response.CompletedAt = DateTime.UtcNow;
                    return true;
                }
            }
            return false;
        }

        public async Task<PersonExportResponse> ExportPersonsAsync(PersonExportRequest request)
        {
            try
            {
                // Build search request from export request
                var searchRequest = new PersonSearchRequest
                {
                    DivisionId = request.DivisionId,
                    CategoryId = request.CategoryId,
                    SubCategoryId = request.SubCategoryId,
                    Nature = request.Nature,
                    Gender = request.Gender,
                    WorkingState = request.WorkingState,
                    District = request.District,
                    CreatedAfter = request.CreatedAfter,
                    CreatedBefore = request.CreatedBefore,
                    SortBy = request.SortBy,
                    SortDirection = request.SortDirection,
                    Page = 1,
                    PageSize = request.MaxRecords ?? int.MaxValue,
                    IncludeDivision = request.IncludeRelatedData,
                    IncludeCategory = request.IncludeRelatedData,
                    IncludeSubCategory = request.IncludeRelatedData
                };

                // Get persons data
                var searchResult = await _personService.SearchPersonsAsync(searchRequest);
                var persons = searchResult.Persons;

                // Generate file content
                byte[] fileContent;
                string contentType;
                string fileName;

                if (request.Format == FileFormat.Excel)
                {
                    fileContent = await GenerateExcelExportAsync(persons, request);
                    contentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
                    fileName = request.FileName ?? $"persons_export_{DateTime.UtcNow:yyyyMMdd_HHmmss}.xlsx";
                }
                else
                {
                    fileContent = await GenerateCsvExportAsync(persons, request);
                    contentType = "text/csv";
                    fileName = request.FileName ?? $"persons_export_{DateTime.UtcNow:yyyyMMdd_HHmmss}.csv";
                }

                return new PersonExportResponse
                {
                    FileName = fileName,
                    ContentType = contentType,
                    FileContent = fileContent,
                    RecordCount = persons.Count,
                    FileSizeBytes = fileContent.Length
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to export persons");
                throw new BusinessException($"Export failed: {ex.Message}");
            }
        }

        public async Task<PersonExportResponse> GenerateImportTemplateAsync(ExportTemplateRequest request)
        {
            try
            {
                byte[] fileContent;
                string contentType;
                string fileName;

                if (request.Format == FileFormat.Excel)
                {
                    fileContent = await GenerateExcelTemplateAsync(request);
                    contentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
                    fileName = "person_import_template.xlsx";
                }
                else
                {
                    fileContent = await GenerateCsvTemplateAsync(request);
                    contentType = "text/csv";
                    fileName = "person_import_template.csv";
                }

                return new PersonExportResponse
                {
                    FileName = fileName,
                    ContentType = contentType,
                    FileContent = fileContent,
                    RecordCount = request.IncludeSampleData ? 1 : 0,
                    FileSizeBytes = fileContent.Length
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to generate import template");
                throw new BusinessException($"Template generation failed: {ex.Message}");
            }
        }

        public async Task<List<PersonImportData>> ParseFileAsync(Stream fileStream, FileFormat format)
        {
            try
            {
                return format switch
                {
                    FileFormat.Excel => await ParseExcelFileAsync(fileStream),
                    FileFormat.CSV => await ParseCsvFileAsync(fileStream),
                    _ => throw new BusinessException($"Unsupported file format: {format}")
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to parse file with format {Format}", format);
                throw new BusinessException($"File parsing failed: {ex.Message}");
            }
        }

        public async Task<List<PersonImportData>> ParseFileAsync(Stream fileStream, FileFormat format, PersonImportProcessRequest? request)
        {
            try
            {
                return format switch
                {
                    FileFormat.Excel => await ParseExcelFileAsync(fileStream, request),
                    FileFormat.CSV => await ParseCsvFileAsync(fileStream, request),
                    _ => throw new BusinessException($"Unsupported file format: {format}")
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to parse file with format {Format}", format);
                throw new BusinessException($"File parsing failed: {ex.Message}");
            }
        }

        public async Task<List<ImportValidationError>> ValidateImportDataAsync(List<PersonImportData> importData, PersonImportRequest? request = null)
        {
            var errors = new List<ImportValidationError>();
            var divisions = (await _divisionRepository.GetAllAsync()).ToList();
            var categories = (await _categoryRepository.GetAllAsync()).ToList();
            var subCategories = (await _subCategoryRepository.GetAllAsync()).ToList();

            foreach (var data in importData)
            {
                await ValidatePersonImportDataAsync(data, divisions, categories, subCategories, errors, request);
            }

            return errors;
        }

        public async Task<List<ImportValidationError>> ValidateImportDataAsync(List<PersonImportData> importData, PersonImportProcessRequest? request = null)
        {
            var errors = new List<ImportValidationError>();
            var divisions = (await _divisionRepository.GetAllAsync()).ToList();
            var categories = (await _categoryRepository.GetAllAsync()).ToList();
            var subCategories = (await _subCategoryRepository.GetAllAsync()).ToList();

            foreach (var data in importData)
            {
                await ValidatePersonImportDataAsync(data, divisions, categories, subCategories, errors, request);
            }

            return errors;
        }

        public async Task<ImportProgressUpdate> GetImportProgressAsync(string jobId)
        {
            if (_importProgress.TryGetValue(jobId, out var progress))
            {
                return progress;
            }

            throw new NotFoundException($"Import progress for job {jobId} not found");
        }

        public async Task UpdateImportProgressAsync(string jobId, ImportProgressUpdate progress)
        {
            _importProgress[jobId] = progress;
        }

        public async Task CleanupTempFilesAsync(DateTime olderThan)
        {
            // Implementation for cleaning up temporary files
            // This would typically clean up files in a temp directory
            _logger.LogInformation("Cleaning up temporary files older than {Date}", olderThan);
        }

        public async Task CleanupCompletedJobsAsync(DateTime olderThan)
        {
            var jobsToRemove = _importJobs
                .Where(kvp => kvp.Value.CompletedAt.HasValue && kvp.Value.CompletedAt.Value < olderThan)
                .Select(kvp => kvp.Key)
                .ToList();

            foreach (var jobId in jobsToRemove)
            {
                _importJobs.TryRemove(jobId, out _);
                _importProgress.TryRemove(jobId, out _);
            }

            _logger.LogInformation("Cleaned up {Count} completed import jobs", jobsToRemove.Count);
        }

        private FileFormat GetFileFormat(string fileName)
        {
            var extension = Path.GetExtension(fileName).ToLowerInvariant();
            return extension switch
            {
                ".xlsx" or ".xls" => FileFormat.Excel,
                ".csv" => FileFormat.CSV,
                _ => throw new BusinessException($"Unsupported file format: {extension}")
            };
        }

        public async Task ProcessImportAsync(string jobId, PersonImportProcessRequest request)
        {
            var response = _importJobs[jobId];
            var progress = new ImportProgressUpdate
            {
                JobId = jobId,
                Status = ImportStatus.Processing,
                CurrentOperation = "Starting import process"
            };

            try
            {
                using (request.FileContent) // Ensure proper disposal of the MemoryStream
                {
                response.Status = ImportStatus.Processing;
                await UpdateImportProgressAsync(jobId, progress);

                // Parse file
                progress.CurrentOperation = "Parsing file";
                await UpdateImportProgressAsync(jobId, progress);

                var fileFormat = GetFileFormat(request.FileName);
                _logger.LogInformation("Detected file format: {FileFormat} for file: {FileName}", fileFormat, request.FileName);
                request.FileContent.Position = 0; // Reset position for reading
                var importData = await ParseFileAsync(request.FileContent, fileFormat, request);

                response.TotalRows = importData.Count;
                progress.TotalRows = importData.Count;

                // Validate data
                progress.CurrentOperation = "Validating data";
                await UpdateImportProgressAsync(jobId, progress);

                var validationErrors = await ValidateImportDataAsync(importData, request);
                response.Errors = validationErrors;

                // Process valid records
                var validRecords = importData.Where(d => d.IsValid).ToList();
                var batchSize = request.BatchSize ?? 100;

                progress.CurrentOperation = "Processing records";
                await UpdateImportProgressAsync(jobId, progress);

                var summary = new ImportSummary();
                var startTime = DateTime.UtcNow;

                for (int i = 0; i < validRecords.Count; i += batchSize)
                {
                    if (response.Status == ImportStatus.Cancelled)
                        break;

                    var batch = validRecords.Skip(i).Take(batchSize).ToList();
                    await ProcessBatchAsync(batch, request.ImportMode, summary);

                    progress.ProcessedRows = Math.Min(i + batchSize, validRecords.Count);
                    progress.SuccessfulRows = summary.NewPersonsCreated + summary.ExistingPersonsUpdated;
                    await UpdateImportProgressAsync(jobId, progress);
                }

                summary.ProcessingTime = DateTime.UtcNow - startTime;
                response.Summary = summary;
                response.SuccessfulRows = summary.NewPersonsCreated + summary.ExistingPersonsUpdated;
                response.FailedRows = validationErrors.Count(e => e.Severity == ErrorSeverity.Error);
                response.SkippedRows = summary.DuplicatesSkipped;
                response.UpdatedRows = summary.ExistingPersonsUpdated;
                response.ProcessedRows = response.TotalRows;

                response.Status = response.Status == ImportStatus.Cancelled ? ImportStatus.Cancelled : ImportStatus.Completed;
                response.CompletedAt = DateTime.UtcNow;

                progress.Status = response.Status;
                progress.CurrentOperation = "Import completed";
                await UpdateImportProgressAsync(jobId, progress);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Import job {JobId} failed", jobId);
                response.Status = ImportStatus.Failed;
                response.ErrorMessage = ex.Message;
                response.CompletedAt = DateTime.UtcNow;

                progress.Status = ImportStatus.Failed;
                progress.CurrentOperation = $"Import failed: {ex.Message}";
                await UpdateImportProgressAsync(jobId, progress);
            }
        }

        private async Task ProcessBatchAsync(List<PersonImportData> batch, ImportMode importMode, ImportSummary summary)
        {
            foreach (var data in batch)
            {
                try
                {
                    if (data.IsDuplicate)
                    {
                        switch (importMode)
                        {
                            case ImportMode.SkipDuplicates:
                                summary.DuplicatesSkipped++;
                                continue;
                            case ImportMode.FailOnDuplicates:
                                summary.ValidationFailures++;
                                continue;
                            case ImportMode.UpdateExisting:
                                if (data.ExistingPersonId.HasValue)
                                {
                                    var updateRequest = MapToUpdateRequest(data);
                                    await _personService.UpdatePersonAsync(data.ExistingPersonId.Value, updateRequest);
                                    summary.ExistingPersonsUpdated++;
                                }
                                break;
                        }
                    }
                    else
                    {
                        var createRequest = MapToCreateRequest(data);
                        await _personService.CreatePersonAsync(createRequest);
                        summary.NewPersonsCreated++;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to process person at row {Row}", data.RowNumber);
                    summary.ValidationFailures++;
                }
            }
        }

        private async Task<List<PersonImportData>> ParseExcelFileAsync(Stream fileStream)
        {
            var importData = new List<PersonImportData>();

            using var package = new ExcelPackage(fileStream);
            var worksheet = package.Workbook.Worksheets.FirstOrDefault();

            if (worksheet == null)
                throw new BusinessException("Excel file contains no worksheets");

            var headerRow = 1;
            var headers = new Dictionary<string, int>();

            // Read headers
            for (int col = 1; col <= worksheet.Dimension.Columns; col++)
            {
                var header = worksheet.Cells[headerRow, col].Text?.Trim();
                if (!string.IsNullOrEmpty(header))
                {
                    headers[header] = col;
                }
            }

            // Validate required columns
            ValidateRequiredColumns(headers.Keys.ToList());

            // Read data rows
            for (int row = headerRow + 1; row <= worksheet.Dimension.Rows; row++)
            {
                var data = new PersonImportData { RowNumber = row - 1 };

                // Map Excel cells to import data properties
                MapExcelRowToImportData(worksheet, row, headers, data, null);

                importData.Add(data);
            }

            return importData;
        }

        private async Task<List<PersonImportData>> ParseExcelFileAsync(Stream fileStream, PersonImportProcessRequest? request)
        {
            var importData = new List<PersonImportData>();

            using var package = new ExcelPackage(fileStream);
            var worksheet = package.Workbook.Worksheets.FirstOrDefault();

            if (worksheet == null)
                throw new BusinessException("Excel file contains no worksheets");

            var headerRow = 1;
            var headers = new Dictionary<string, int>();

            // Read headers
            for (int col = 1; col <= worksheet.Dimension.Columns; col++)
            {
                var header = worksheet.Cells[headerRow, col].Text?.Trim();
                if (!string.IsNullOrEmpty(header))
                {
                    headers[header] = col;
                }
            }

            // Log headers found in Excel file
            _logger.LogInformation("Excel headers found: [{Headers}]", string.Join(", ", headers.Keys));

            // Validate required columns with request context
            ValidateRequiredColumns(headers.Keys.ToList(), request);

            // Read data rows
            for (int row = headerRow + 1; row <= worksheet.Dimension.Rows; row++)
            {
                var data = new PersonImportData { RowNumber = row - 1 };

                // Map Excel cells to import data properties
                MapExcelRowToImportData(worksheet, row, headers, data, request);

                // Log the mapped data for debugging
                _logger.LogInformation("Row {RowNumber}: Name='{Name}', MobileNumber='{MobileNumber}', Nature='{Nature}'",
                    data.RowNumber, data.Name ?? "NULL", data.MobileNumber ?? "NULL", data.Nature ?? "NULL");

                importData.Add(data);
            }

            return importData;
        }

        private async Task<List<PersonImportData>> ParseCsvFileAsync(Stream fileStream)
        {
            var importData = new List<PersonImportData>();

            using var reader = new StreamReader(fileStream);
            using var csv = new CsvReader(reader, new CsvConfiguration(CultureInfo.InvariantCulture)
            {
                HasHeaderRecord = true,
                MissingFieldFound = null,
                HeaderValidated = null
            });

            var records = csv.GetRecords<dynamic>().ToList();
            var headers = csv.HeaderRecord?.ToList() ?? new List<string>();

            // Validate required columns
            ValidateRequiredColumns(headers);

            for (int i = 0; i < records.Count; i++)
            {
                var record = records[i] as IDictionary<string, object>;
                var data = new PersonImportData { RowNumber = i + 2 }; // +2 because of header row and 1-based indexing

                // Map CSV record to import data properties
                MapCsvRecordToImportData(record, data);

                importData.Add(data);
            }

            return importData;
        }

        private async Task<List<PersonImportData>> ParseCsvFileAsync(Stream fileStream, PersonImportProcessRequest? request)
        {
            var importData = new List<PersonImportData>();

            using var reader = new StreamReader(fileStream);
            using var csv = new CsvReader(reader, new CsvConfiguration(CultureInfo.InvariantCulture)
            {
                HasHeaderRecord = true,
                MissingFieldFound = null,
                HeaderValidated = null
            });

            var records = csv.GetRecords<dynamic>().ToList();
            var headers = csv.HeaderRecord?.ToList() ?? new List<string>();

            // Validate required columns with request context
            ValidateRequiredColumns(headers, request);

            for (int i = 0; i < records.Count; i++)
            {
                var record = records[i] as IDictionary<string, object>;
                var data = new PersonImportData { RowNumber = i + 2 }; // +2 because of header row and 1-based indexing

                // Map CSV record to import data properties
                MapCsvRecordToImportData(record, data);

                importData.Add(data);
            }

            return importData;
        }

        private void ValidateRequiredColumns(List<string> headers)
        {
            var missingColumns = PersonImportColumns.RequiredColumns
                .Where(required => !headers.Any(h => h.Equals(required, StringComparison.OrdinalIgnoreCase)))
                .ToList();

            if (missingColumns.Any())
            {
                throw new CrmApi.Exceptions.ValidationException($"Missing required columns: {string.Join(", ", missingColumns)}");
            }
        }

        private void ValidateRequiredColumns(List<string> headers, PersonImportProcessRequest? request)
        {
            var missingColumns = new List<string>();
            var defaultValues = new Dictionary<string, string>();
            var fieldMapping = new Dictionary<string, string>();

            // Parse default values if provided
            if (request != null && !string.IsNullOrEmpty(request.DefaultValues))
            {
                try
                {
                    defaultValues = System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, string>>(request.DefaultValues) ?? new Dictionary<string, string>();
                    _logger.LogInformation("Parsed default values: {DefaultValues}", string.Join(", ", defaultValues.Select(kv => $"{kv.Key}={kv.Value}")));
                }
                catch (System.Text.Json.JsonException ex)
                {
                    _logger.LogWarning(ex, "Failed to parse default values JSON");
                }
            }

            // Parse field mapping if provided (this is the key missing piece!)
            if (request != null && !string.IsNullOrEmpty(request.FieldMapping))
            {
                try
                {
                    fieldMapping = System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, string>>(request.FieldMapping) ?? new Dictionary<string, string>();
                    _logger.LogInformation("Parsed field mapping: {FieldMapping}", string.Join(", ", fieldMapping.Select(kv => $"{kv.Key}->{kv.Value}")));
                }
                catch (System.Text.Json.JsonException ex)
                {
                    _logger.LogWarning(ex, "Failed to parse field mapping JSON");
                }
            }

            // Check each required column, but allow defaults for any field
            _logger.LogInformation("Checking required columns. Headers: [{Headers}], Required: [{Required}]",
                string.Join(", ", headers), string.Join(", ", PersonImportColumns.RequiredColumns));

            foreach (var required in PersonImportColumns.RequiredColumns)
            {
                // Check if column exists directly by name
                var hasColumn = headers.Any(h => h.Equals(required, StringComparison.OrdinalIgnoreCase));

                // Check if column is mapped from Excel headers to person fields
                var isMapped = false;
                var requiredFieldKey = GetPersonFieldKeyFromColumnName(required);
                if (!string.IsNullOrEmpty(requiredFieldKey))
                {
                    isMapped = fieldMapping.Values.Any(v => v.Equals(requiredFieldKey, StringComparison.OrdinalIgnoreCase));
                }

                if (!hasColumn && !isMapped)
                {
                    _logger.LogInformation("Missing column '{Required}' (field key: {FieldKey}), checking for defaults...", required, requiredFieldKey);

                    // Allow missing Division Name if default division is provided
                    if (required == PersonImportColumns.DivisionName && request?.DefaultDivisionId > 0)
                    {
                        _logger.LogInformation("Division Name missing but default division ID provided: {DivisionId}", request.DefaultDivisionId);
                        continue;
                    }

                    // Allow missing Category Name if default category is provided
                    if (required == PersonImportColumns.CategoryName && request?.DefaultCategoryId > 0)
                    {
                        _logger.LogInformation("Category Name missing but default category ID provided: {CategoryId}", request.DefaultCategoryId);
                        continue;
                    }

                    // Allow missing Name if default value is provided
                    if (required == PersonImportColumns.Name && defaultValues.ContainsKey("name") && !string.IsNullOrEmpty(defaultValues["name"]))
                    {
                        _logger.LogInformation("Name missing but default value provided: {DefaultName}", defaultValues["name"]);
                        continue;
                    }

                    // Allow missing Mobile Number if default value is provided
                    if (required == PersonImportColumns.MobileNumber && defaultValues.ContainsKey("mobileNumber") && !string.IsNullOrEmpty(defaultValues["mobileNumber"]))
                    {
                        _logger.LogInformation("Mobile Number missing but default value provided: {DefaultMobile}", defaultValues["mobileNumber"]);
                        continue;
                    }

                    // Allow missing Nature if default value is provided
                    if (required == PersonImportColumns.Nature && defaultValues.ContainsKey("nature") && !string.IsNullOrEmpty(defaultValues["nature"]))
                    {
                        _logger.LogInformation("Nature missing but default value provided: {DefaultNature}", defaultValues["nature"]);
                        continue;
                    }

                    _logger.LogWarning("Required column '{Required}' is missing and no default value provided", required);
                    missingColumns.Add(required);
                }
                else
                {
                    _logger.LogInformation("Required column '{Required}' found in headers or mapped from Excel", required);
                }
            }

            if (missingColumns.Any())
            {
                _logger.LogError("Validation failed. Missing columns after default checks: [{MissingColumns}]", string.Join(", ", missingColumns));
                throw new CrmApi.Exceptions.ValidationException($"Missing required columns: {string.Join(", ", missingColumns)}");
            }
            else
            {
                _logger.LogInformation("All required columns validated successfully (some may use defaults)");
            }
        }

        private string? GetPersonFieldKeyFromColumnName(string columnName)
        {
            return columnName switch
            {
                PersonImportColumns.DivisionName => "divisionId", // Not used in mapping, handled by defaults
                PersonImportColumns.CategoryName => "categoryId", // Not used in mapping, handled by defaults
                PersonImportColumns.Name => "name",
                PersonImportColumns.MobileNumber => "mobileNumber",
                PersonImportColumns.Nature => "nature",
                _ => null
            };
        }

        private void MapExcelRowToImportData(ExcelWorksheet worksheet, int row, Dictionary<string, int> headers, PersonImportData data, PersonImportProcessRequest? request = null)
        {
            // Parse field mapping if provided
            var fieldMapping = new Dictionary<string, string>();
            if (request != null && !string.IsNullOrEmpty(request.FieldMapping))
            {
                try
                {
                    fieldMapping = System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, string>>(request.FieldMapping) ?? new Dictionary<string, string>();
                    _logger.LogInformation("MapExcelRowToImportData - Parsed field mapping: {FieldMapping}", string.Join(", ", fieldMapping.Select(kv => $"{kv.Key}->{kv.Value}")));
                }
                catch (System.Text.Json.JsonException ex)
                {
                    _logger.LogWarning(ex, "Failed to parse field mapping JSON in MapExcelRowToImportData");
                }
            }
            else
            {
                _logger.LogInformation("MapExcelRowToImportData - No field mapping provided. Request: {HasRequest}, FieldMapping: {FieldMapping}",
                    request != null, request?.FieldMapping ?? "null");
            }

            data.DivisionName = GetCellValueWithMapping(worksheet, row, headers, PersonImportColumns.DivisionName, "divisionId", fieldMapping);
            data.CategoryName = GetCellValueWithMapping(worksheet, row, headers, PersonImportColumns.CategoryName, "categoryId", fieldMapping);
            data.SubCategoryName = GetCellValueWithMapping(worksheet, row, headers, PersonImportColumns.SubCategoryName, "subCategoryId", fieldMapping);
            data.Name = GetCellValueWithMapping(worksheet, row, headers, PersonImportColumns.Name, "name", fieldMapping);
            data.MobileNumber = GetCellValueWithMapping(worksheet, row, headers, PersonImportColumns.MobileNumber, "mobileNumber", fieldMapping);
            data.Nature = GetCellValueWithMapping(worksheet, row, headers, PersonImportColumns.Nature, "nature", fieldMapping);
            data.Gender = GetCellValue(worksheet, row, headers, PersonImportColumns.Gender);
            data.AlternateNumbers = GetCellValue(worksheet, row, headers, PersonImportColumns.AlternateNumbers);
            data.PrimaryEmailId = GetCellValue(worksheet, row, headers, PersonImportColumns.PrimaryEmailId);
            data.AlternateEmailIds = GetCellValue(worksheet, row, headers, PersonImportColumns.AlternateEmailIds);
            data.Website = GetCellValue(worksheet, row, headers, PersonImportColumns.Website);
            data.DateOfBirth = GetCellValue(worksheet, row, headers, PersonImportColumns.DateOfBirth);
            data.IsMarried = GetCellValue(worksheet, row, headers, PersonImportColumns.IsMarried);
            data.DateOfMarriage = GetCellValue(worksheet, row, headers, PersonImportColumns.DateOfMarriage);
            data.WorkingState = GetCellValue(worksheet, row, headers, PersonImportColumns.WorkingState);
            data.DomesticState = GetCellValue(worksheet, row, headers, PersonImportColumns.DomesticState);
            data.District = GetCellValue(worksheet, row, headers, PersonImportColumns.District);
            data.Address = GetCellValue(worksheet, row, headers, PersonImportColumns.Address);
            data.WorkingArea = GetCellValue(worksheet, row, headers, PersonImportColumns.WorkingArea);
            data.HasAssociate = GetCellValue(worksheet, row, headers, PersonImportColumns.HasAssociate);
            data.AssociateName = GetCellValue(worksheet, row, headers, PersonImportColumns.AssociateName);
            data.AssociateRelation = GetCellValue(worksheet, row, headers, PersonImportColumns.AssociateRelation);
            data.AssociateMobile = GetCellValue(worksheet, row, headers, PersonImportColumns.AssociateMobile);
            data.UsingWebsite = GetCellValue(worksheet, row, headers, PersonImportColumns.UsingWebsite);
            data.WebsiteLink = GetCellValue(worksheet, row, headers, PersonImportColumns.WebsiteLink);
            data.UsingCRMApp = GetCellValue(worksheet, row, headers, PersonImportColumns.UsingCRMApp);
            data.CRMAppLink = GetCellValue(worksheet, row, headers, PersonImportColumns.CRMAppLink);
            data.TransactionValue = GetCellValue(worksheet, row, headers, PersonImportColumns.TransactionValue);
            data.RERARegistrationNumber = GetCellValue(worksheet, row, headers, PersonImportColumns.RERARegistrationNumber);
            data.WorkingProfiles = GetCellValue(worksheet, row, headers, PersonImportColumns.WorkingProfiles);
            data.StarRating = GetCellValue(worksheet, row, headers, PersonImportColumns.StarRating);
            data.Source = GetCellValue(worksheet, row, headers, PersonImportColumns.Source);
            data.Remarks = GetCellValue(worksheet, row, headers, PersonImportColumns.Remarks);
            data.FirmName = GetCellValue(worksheet, row, headers, PersonImportColumns.FirmName);
            data.NumberOfOffices = GetCellValue(worksheet, row, headers, PersonImportColumns.NumberOfOffices);
            data.NumberOfBranches = GetCellValue(worksheet, row, headers, PersonImportColumns.NumberOfBranches);
            data.TotalEmployeeStrength = GetCellValue(worksheet, row, headers, PersonImportColumns.TotalEmployeeStrength);
            data.AuthorizedPersonName = GetCellValue(worksheet, row, headers, PersonImportColumns.AuthorizedPersonName);
            data.AuthorizedPersonEmail = GetCellValue(worksheet, row, headers, PersonImportColumns.AuthorizedPersonEmail);
            data.Designation = GetCellValue(worksheet, row, headers, PersonImportColumns.Designation);
            data.MarketingContact = GetCellValue(worksheet, row, headers, PersonImportColumns.MarketingContact);
            data.MarketingDesignation = GetCellValue(worksheet, row, headers, PersonImportColumns.MarketingDesignation);
            data.PlaceOfPosting = GetCellValue(worksheet, row, headers, PersonImportColumns.PlaceOfPosting);
            data.Department = GetCellValue(worksheet, row, headers, PersonImportColumns.Department);
        }

        private void MapCsvRecordToImportData(IDictionary<string, object> record, PersonImportData data)
        {
            data.DivisionName = GetFieldValue(record, PersonImportColumns.DivisionName);
            data.CategoryName = GetFieldValue(record, PersonImportColumns.CategoryName);
            data.SubCategoryName = GetFieldValue(record, PersonImportColumns.SubCategoryName);
            data.Name = GetFieldValue(record, PersonImportColumns.Name);
            data.MobileNumber = GetFieldValue(record, PersonImportColumns.MobileNumber);
            data.Nature = GetFieldValue(record, PersonImportColumns.Nature);
            data.Gender = GetFieldValue(record, PersonImportColumns.Gender);
            data.AlternateNumbers = GetFieldValue(record, PersonImportColumns.AlternateNumbers);
            data.PrimaryEmailId = GetFieldValue(record, PersonImportColumns.PrimaryEmailId);
            data.AlternateEmailIds = GetFieldValue(record, PersonImportColumns.AlternateEmailIds);
            data.Website = GetFieldValue(record, PersonImportColumns.Website);
            data.DateOfBirth = GetFieldValue(record, PersonImportColumns.DateOfBirth);
            data.IsMarried = GetFieldValue(record, PersonImportColumns.IsMarried);
            data.DateOfMarriage = GetFieldValue(record, PersonImportColumns.DateOfMarriage);
            data.WorkingState = GetFieldValue(record, PersonImportColumns.WorkingState);
            data.DomesticState = GetFieldValue(record, PersonImportColumns.DomesticState);
            data.District = GetFieldValue(record, PersonImportColumns.District);
            data.Address = GetFieldValue(record, PersonImportColumns.Address);
            data.WorkingArea = GetFieldValue(record, PersonImportColumns.WorkingArea);
            data.HasAssociate = GetFieldValue(record, PersonImportColumns.HasAssociate);
            data.AssociateName = GetFieldValue(record, PersonImportColumns.AssociateName);
            data.AssociateRelation = GetFieldValue(record, PersonImportColumns.AssociateRelation);
            data.AssociateMobile = GetFieldValue(record, PersonImportColumns.AssociateMobile);
            data.UsingWebsite = GetFieldValue(record, PersonImportColumns.UsingWebsite);
            data.WebsiteLink = GetFieldValue(record, PersonImportColumns.WebsiteLink);
            data.UsingCRMApp = GetFieldValue(record, PersonImportColumns.UsingCRMApp);
            data.CRMAppLink = GetFieldValue(record, PersonImportColumns.CRMAppLink);
            data.TransactionValue = GetFieldValue(record, PersonImportColumns.TransactionValue);
            data.RERARegistrationNumber = GetFieldValue(record, PersonImportColumns.RERARegistrationNumber);
            data.WorkingProfiles = GetFieldValue(record, PersonImportColumns.WorkingProfiles);
            data.StarRating = GetFieldValue(record, PersonImportColumns.StarRating);
            data.Source = GetFieldValue(record, PersonImportColumns.Source);
            data.Remarks = GetFieldValue(record, PersonImportColumns.Remarks);
            data.FirmName = GetFieldValue(record, PersonImportColumns.FirmName);
            data.NumberOfOffices = GetFieldValue(record, PersonImportColumns.NumberOfOffices);
            data.NumberOfBranches = GetFieldValue(record, PersonImportColumns.NumberOfBranches);
            data.TotalEmployeeStrength = GetFieldValue(record, PersonImportColumns.TotalEmployeeStrength);
            data.AuthorizedPersonName = GetFieldValue(record, PersonImportColumns.AuthorizedPersonName);
            data.AuthorizedPersonEmail = GetFieldValue(record, PersonImportColumns.AuthorizedPersonEmail);
            data.Designation = GetFieldValue(record, PersonImportColumns.Designation);
            data.MarketingContact = GetFieldValue(record, PersonImportColumns.MarketingContact);
            data.MarketingDesignation = GetFieldValue(record, PersonImportColumns.MarketingDesignation);
            data.PlaceOfPosting = GetFieldValue(record, PersonImportColumns.PlaceOfPosting);
            data.Department = GetFieldValue(record, PersonImportColumns.Department);
        }

        private string? GetCellValue(ExcelWorksheet worksheet, int row, Dictionary<string, int> headers, string columnName)
        {
            if (headers.TryGetValue(columnName, out var col))
            {
                return worksheet.Cells[row, col].Text?.Trim();
            }
            return null;
        }

        private string? GetCellValueWithMapping(ExcelWorksheet worksheet, int row, Dictionary<string, int> headers, string expectedColumnName, string fieldKey, Dictionary<string, string> fieldMapping)
        {
            // First try direct column name match
            if (headers.TryGetValue(expectedColumnName, out var col))
            {
                var value = worksheet.Cells[row, col].Text?.Trim();
                _logger.LogInformation("Found direct column '{ExpectedColumn}' with value '{Value}' for row {Row}", expectedColumnName, value ?? "NULL", row);
                return value;
            }

            // Then try field mapping - find Excel column that maps to this field
            var mappedColumn = fieldMapping.FirstOrDefault(kv => kv.Value.Equals(fieldKey, StringComparison.OrdinalIgnoreCase));
            if (!string.IsNullOrEmpty(mappedColumn.Key) && headers.TryGetValue(mappedColumn.Key, out col))
            {
                var value = worksheet.Cells[row, col].Text?.Trim();
                _logger.LogInformation("Found mapped column '{MappedColumn}' -> '{FieldKey}' with value '{Value}' for row {Row}", mappedColumn.Key, fieldKey, value ?? "NULL", row);
                return value;
            }

            _logger.LogInformation("No column found for '{ExpectedColumn}' (field: {FieldKey}) in row {Row}", expectedColumnName, fieldKey, row);
            return null;
        }

        private string? GetFieldValue(IDictionary<string, object> record, string fieldName)
        {
            var key = record.Keys.FirstOrDefault(k => k.Equals(fieldName, StringComparison.OrdinalIgnoreCase));
            if (key != null && record.TryGetValue(key, out var value))
            {
                return value?.ToString()?.Trim();
            }
            return null;
        }

        private async Task ValidatePersonImportDataAsync(
            PersonImportData data,
            List<Models.Division.Division> divisions,
            List<Models.Category.Category> categories,
            List<Models.SubCategory.SubCategory> subCategories,
            List<ImportValidationError> errors,
            PersonImportRequest? request = null)
        {
            // Apply default values for missing required fields
            if (request != null && !string.IsNullOrEmpty(request.DefaultValues))
            {
                try
                {
                    var defaultValues = System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, string>>(request.DefaultValues);

                    // Apply default values only if the field is empty or whitespace in the import data
                    if (string.IsNullOrWhiteSpace(data.Name) && defaultValues.ContainsKey("name"))
                    {
                        data.Name = defaultValues["name"];
                        _logger.LogInformation("Applied default name '{DefaultName}' to row {RowNumber}", defaultValues["name"], data.RowNumber);
                    }

                    if (string.IsNullOrWhiteSpace(data.MobileNumber) && defaultValues.ContainsKey("mobileNumber"))
                    {
                        data.MobileNumber = defaultValues["mobileNumber"];
                        _logger.LogInformation("Applied default mobile number '{DefaultMobile}' to row {RowNumber}", defaultValues["mobileNumber"], data.RowNumber);
                    }

                    if (string.IsNullOrWhiteSpace(data.Nature) && defaultValues.ContainsKey("nature"))
                    {
                        data.Nature = defaultValues["nature"];
                        _logger.LogInformation("Applied default nature '{DefaultNature}' to row {RowNumber}", defaultValues["nature"], data.RowNumber);
                    }
                }
                catch (System.Text.Json.JsonException ex)
                {
                    _logger.LogWarning(ex, "Failed to parse default values JSON for row {RowNumber}", data.RowNumber);
                }
            }

            // Validate required fields
            if (string.IsNullOrWhiteSpace(data.Name))
            {
                _logger.LogWarning("Row {RowNumber}: Name is empty after applying defaults. Value: '{Value}'", data.RowNumber, data.Name ?? "NULL");
                errors.Add(new ImportValidationError
                {
                    RowNumber = data.RowNumber,
                    FieldName = PersonImportColumns.Name,
                    ErrorMessage = "Name is required",
                    Severity = ErrorSeverity.Error,
                    ErrorCode = "REQUIRED_FIELD"
                });
                data.IsValid = false;
            }

            if (string.IsNullOrWhiteSpace(data.MobileNumber))
            {
                _logger.LogWarning("Row {RowNumber}: Mobile number is empty after applying defaults. Value: '{Value}'", data.RowNumber, data.MobileNumber ?? "NULL");
                errors.Add(new ImportValidationError
                {
                    RowNumber = data.RowNumber,
                    FieldName = PersonImportColumns.MobileNumber,
                    ErrorMessage = "Mobile number is required",
                    Severity = ErrorSeverity.Error,
                    ErrorCode = "REQUIRED_FIELD"
                });
                data.IsValid = false;
            }
            else if (!System.Text.RegularExpressions.Regex.IsMatch(data.MobileNumber, @"^(\+91[\-\s]?)?[0]?(91)?[789]\d{9}$"))
            {
                errors.Add(new ImportValidationError
                {
                    RowNumber = data.RowNumber,
                    FieldName = PersonImportColumns.MobileNumber,
                    ErrorMessage = "Invalid mobile number format",
                    FieldValue = data.MobileNumber,
                    Severity = ErrorSeverity.Error,
                    ErrorCode = "INVALID_FORMAT"
                });
                data.IsValid = false;
            }

            // Validate division - use default if not provided in Excel
            if (!string.IsNullOrEmpty(data.DivisionName))
            {
                var division = divisions.FirstOrDefault(d => d.Name.Equals(data.DivisionName, StringComparison.OrdinalIgnoreCase));
                if (division != null)
                {
                    data.DivisionId = division.Id;
                }
                else
                {
                    errors.Add(new ImportValidationError
                    {
                        RowNumber = data.RowNumber,
                        FieldName = PersonImportColumns.DivisionName,
                        ErrorMessage = $"Division '{data.DivisionName}' not found",
                        FieldValue = data.DivisionName,
                        Severity = ErrorSeverity.Error,
                        ErrorCode = "NOT_FOUND"
                    });
                    data.IsValid = false;
                }
            }
            else if (request != null)
            {
                // Use default division from request
                var defaultDivision = divisions.FirstOrDefault(d => d.Id == request.DefaultDivisionId);
                if (defaultDivision != null)
                {
                    data.DivisionId = request.DefaultDivisionId;
                    data.DivisionName = defaultDivision.Name;
                }
                else
                {
                    errors.Add(new ImportValidationError
                    {
                        RowNumber = data.RowNumber,
                        FieldName = PersonImportColumns.DivisionName,
                        ErrorMessage = $"Default division with ID {request.DefaultDivisionId} not found",
                        Severity = ErrorSeverity.Error,
                        ErrorCode = "NOT_FOUND"
                    });
                    data.IsValid = false;
                }
            }
            else
            {
                errors.Add(new ImportValidationError
                {
                    RowNumber = data.RowNumber,
                    FieldName = PersonImportColumns.DivisionName,
                    ErrorMessage = "Division name is required",
                    Severity = ErrorSeverity.Error,
                    ErrorCode = "REQUIRED_FIELD"
                });
                data.IsValid = false;
            }

            // Validate category - use default if not provided in Excel
            if (!string.IsNullOrEmpty(data.CategoryName))
            {
                var category = categories.FirstOrDefault(c => c.Name.Equals(data.CategoryName, StringComparison.OrdinalIgnoreCase));
                if (category != null)
                {
                    data.CategoryId = category.Id;
                }
                else
                {
                    errors.Add(new ImportValidationError
                    {
                        RowNumber = data.RowNumber,
                        FieldName = PersonImportColumns.CategoryName,
                        ErrorMessage = $"Category '{data.CategoryName}' not found",
                        FieldValue = data.CategoryName,
                        Severity = ErrorSeverity.Error,
                        ErrorCode = "NOT_FOUND"
                    });
                    data.IsValid = false;
                }
            }
            else if (request != null)
            {
                // Use default category from request
                var defaultCategory = categories.FirstOrDefault(c => c.Id == request.DefaultCategoryId);
                if (defaultCategory != null)
                {
                    data.CategoryId = request.DefaultCategoryId;
                    data.CategoryName = defaultCategory.Name;
                }
                else
                {
                    errors.Add(new ImportValidationError
                    {
                        RowNumber = data.RowNumber,
                        FieldName = PersonImportColumns.CategoryName,
                        ErrorMessage = $"Default category with ID {request.DefaultCategoryId} not found",
                        Severity = ErrorSeverity.Error,
                        ErrorCode = "NOT_FOUND"
                    });
                    data.IsValid = false;
                }
            }
            else
            {
                errors.Add(new ImportValidationError
                {
                    RowNumber = data.RowNumber,
                    FieldName = PersonImportColumns.CategoryName,
                    ErrorMessage = "Category name is required",
                    Severity = ErrorSeverity.Error,
                    ErrorCode = "REQUIRED_FIELD"
                });
                data.IsValid = false;
            }

            // Validate nature
            if (!string.IsNullOrEmpty(data.Nature))
            {
                if (!Enum.TryParse<PersonNature>(data.Nature, true, out _))
                {
                    errors.Add(new ImportValidationError
                    {
                        RowNumber = data.RowNumber,
                        FieldName = PersonImportColumns.Nature,
                        ErrorMessage = $"Invalid nature value. Valid values are: {string.Join(", ", Enum.GetNames<PersonNature>())}",
                        FieldValue = data.Nature,
                        Severity = ErrorSeverity.Error,
                        ErrorCode = "INVALID_ENUM"
                    });
                    data.IsValid = false;
                }
            }
            else
            {
                errors.Add(new ImportValidationError
                {
                    RowNumber = data.RowNumber,
                    FieldName = PersonImportColumns.Nature,
                    ErrorMessage = "Nature is required",
                    Severity = ErrorSeverity.Error,
                    ErrorCode = "REQUIRED_FIELD"
                });
                data.IsValid = false;
            }

            // Validate email format
            if (!string.IsNullOrEmpty(data.PrimaryEmailId))
            {
                if (!new System.ComponentModel.DataAnnotations.EmailAddressAttribute().IsValid(data.PrimaryEmailId))
                {
                    errors.Add(new ImportValidationError
                    {
                        RowNumber = data.RowNumber,
                        FieldName = PersonImportColumns.PrimaryEmailId,
                        ErrorMessage = "Invalid email format",
                        FieldValue = data.PrimaryEmailId,
                        Severity = ErrorSeverity.Error,
                        ErrorCode = "INVALID_FORMAT"
                    });
                    data.IsValid = false;
                }
            }

            // Check for duplicates if valid
            if (data.IsValid && data.DivisionId.HasValue && data.CategoryId.HasValue)
            {
                data.IsDuplicate = false; // Simplified for now
            }
        }

        private async Task ValidatePersonImportDataAsync(
            PersonImportData data,
            List<Models.Division.Division> divisions,
            List<Models.Category.Category> categories,
            List<Models.SubCategory.SubCategory> subCategories,
            List<ImportValidationError> errors,
            PersonImportProcessRequest? request = null)
        {
            // Convert PersonImportProcessRequest to PersonImportRequest for validation
            PersonImportRequest? originalRequest = null;
            if (request != null)
            {
                originalRequest = new PersonImportRequest
                {
                    DefaultDivisionId = request.DefaultDivisionId,
                    DefaultCategoryId = request.DefaultCategoryId,
                    DefaultSubCategoryId = request.DefaultSubCategoryId,
                    DefaultValues = request.DefaultValues,
                    FieldMapping = request.FieldMapping,
                    ImportMode = request.ImportMode,
                    ValidateOnly = request.ValidateOnly,
                    BatchSize = request.BatchSize,
                    File = null! // Not needed for validation
                };
            }

            // Call the original validation method
            await ValidatePersonImportDataAsync(data, divisions, categories, subCategories, errors, originalRequest);
        }
    }
}
