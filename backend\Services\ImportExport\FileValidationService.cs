using CrmApi.Models.ImportExport;
using CrmApi.Exceptions;

namespace CrmApi.Services.ImportExport
{
    public interface IFileValidationService
    {
        Task<FileValidationResult> ValidateFileAsync(IFormFile file);
        Task<string> SaveTempFileAsync(IFormFile file);
        Task CleanupTempFileAsync(string filePath);
        Task CleanupOldTempFilesAsync(DateTime olderThan);
    }

    public class FileValidationService : IFileValidationService
    {
        private readonly ILogger<FileValidationService> _logger;
        private readonly string _tempDirectory;
        
        // Configuration
        private readonly long _maxFileSizeBytes = 10 * 1024 * 1024; // 10MB
        private readonly string[] _allowedExtensions = { ".csv", ".xlsx", ".xls" };
        private readonly string[] _allowedMimeTypes = { 
            "text/csv", 
            "application/csv",
            "application/vnd.ms-excel",
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        };

        public FileValidationService(ILogger<FileValidationService> logger, IWebHostEnvironment environment)
        {
            _logger = logger;
            _tempDirectory = Path.Combine(environment.ContentRootPath, "temp", "imports");
            
            // Ensure temp directory exists
            Directory.CreateDirectory(_tempDirectory);
        }

        public async Task<FileValidationResult> ValidateFileAsync(IFormFile file)
        {
            var result = new FileValidationResult();

            try
            {
                // Check if file exists
                if (file == null || file.Length == 0)
                {
                    result.IsValid = false;
                    result.Errors.Add("No file uploaded or file is empty");
                    return result;
                }

                // Check file size
                if (file.Length > _maxFileSizeBytes)
                {
                    result.IsValid = false;
                    result.Errors.Add($"File size ({file.Length:N0} bytes) exceeds maximum allowed size ({_maxFileSizeBytes:N0} bytes)");
                }

                // Check file extension
                var fileExtension = Path.GetExtension(file.FileName).ToLowerInvariant();
                if (!_allowedExtensions.Contains(fileExtension))
                {
                    result.IsValid = false;
                    result.Errors.Add($"File extension '{fileExtension}' is not allowed. Allowed extensions: {string.Join(", ", _allowedExtensions)}");
                }

                // Check MIME type
                if (!string.IsNullOrEmpty(file.ContentType) && !_allowedMimeTypes.Contains(file.ContentType.ToLowerInvariant()))
                {
                    result.IsValid = false;
                    result.Errors.Add($"File content type '{file.ContentType}' is not allowed. Allowed types: {string.Join(", ", _allowedMimeTypes)}");
                }

                // Check filename for security
                var fileName = Path.GetFileName(file.FileName);
                if (string.IsNullOrEmpty(fileName) || fileName.Contains("..") || fileName.IndexOfAny(Path.GetInvalidFileNameChars()) >= 0)
                {
                    result.IsValid = false;
                    result.Errors.Add("Invalid filename");
                }

                // Additional file content validation
                if (result.IsValid)
                {
                    await ValidateFileContentAsync(file, result);
                }

                result.FileSize = file.Length;
                result.FileName = fileName;
                result.FileExtension = fileExtension;
                result.ContentType = file.ContentType;

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating file {FileName}", file?.FileName);
                result.IsValid = false;
                result.Errors.Add($"File validation failed: {ex.Message}");
                return result;
            }
        }

        public async Task<string> SaveTempFileAsync(IFormFile file)
        {
            try
            {
                var fileName = $"{Guid.NewGuid()}_{Path.GetFileName(file.FileName)}";
                var filePath = Path.Combine(_tempDirectory, fileName);

                using var stream = new FileStream(filePath, FileMode.Create);
                await file.CopyToAsync(stream);

                _logger.LogInformation("Temporary file saved: {FilePath}", filePath);
                return filePath;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to save temporary file {FileName}", file.FileName);
                throw new BusinessException($"Failed to save temporary file: {ex.Message}");
            }
        }

        public async Task CleanupTempFileAsync(string filePath)
        {
            try
            {
                if (File.Exists(filePath))
                {
                    File.Delete(filePath);
                    _logger.LogInformation("Temporary file deleted: {FilePath}", filePath);
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to delete temporary file {FilePath}", filePath);
            }
        }

        public async Task CleanupOldTempFilesAsync(DateTime olderThan)
        {
            try
            {
                if (!Directory.Exists(_tempDirectory))
                    return;

                var files = Directory.GetFiles(_tempDirectory);
                var deletedCount = 0;

                foreach (var file in files)
                {
                    var fileInfo = new FileInfo(file);
                    if (fileInfo.CreationTime < olderThan)
                    {
                        try
                        {
                            File.Delete(file);
                            deletedCount++;
                        }
                        catch (Exception ex)
                        {
                            _logger.LogWarning(ex, "Failed to delete old temporary file {FilePath}", file);
                        }
                    }
                }

                _logger.LogInformation("Cleaned up {Count} old temporary files", deletedCount);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to cleanup old temporary files");
            }
        }

        private async Task ValidateFileContentAsync(IFormFile file, FileValidationResult result)
        {
            try
            {
                using var stream = file.OpenReadStream();
                var buffer = new byte[1024];
                var bytesRead = await stream.ReadAsync(buffer, 0, buffer.Length);

                if (bytesRead == 0)
                {
                    result.IsValid = false;
                    result.Errors.Add("File appears to be empty");
                    return;
                }

                // Basic content validation based on file type
                var extension = Path.GetExtension(file.FileName).ToLowerInvariant();
                
                if (extension == ".csv")
                {
                    // Check if it looks like CSV content
                    var content = System.Text.Encoding.UTF8.GetString(buffer, 0, bytesRead);
                    if (!content.Contains(',') && !content.Contains('\n'))
                    {
                        result.Warnings.Add("File does not appear to contain CSV data");
                    }
                }
                else if (extension == ".xlsx" || extension == ".xls")
                {
                    // Check for Excel file signatures
                    var isValidExcel = false;
                    
                    // XLSX signature (ZIP file starting with PK)
                    if (buffer.Length >= 2 && buffer[0] == 0x50 && buffer[1] == 0x4B)
                    {
                        isValidExcel = true;
                    }
                    // XLS signature
                    else if (buffer.Length >= 8 && 
                             buffer[0] == 0xD0 && buffer[1] == 0xCF && 
                             buffer[2] == 0x11 && buffer[3] == 0xE0)
                    {
                        isValidExcel = true;
                    }

                    if (!isValidExcel)
                    {
                        result.IsValid = false;
                        result.Errors.Add("File does not appear to be a valid Excel file");
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to validate file content for {FileName}", file.FileName);
                result.Warnings.Add("Could not validate file content");
            }
        }
    }

    public class FileValidationResult
    {
        public bool IsValid { get; set; } = true;
        public List<string> Errors { get; set; } = new List<string>();
        public List<string> Warnings { get; set; } = new List<string>();
        public long FileSize { get; set; }
        public string FileName { get; set; } = string.Empty;
        public string FileExtension { get; set; } = string.Empty;
        public string? ContentType { get; set; }
    }
}
