{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Advanced\\\\Advanced CRM filed all use\\\\frontend\\\\src\\\\components\\\\forms\\\\FormBuilder.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { PersonFieldDefinitions, getAllPersonFields } from '../../constants/personConstants';\nimport formConfigService from '../../services/formConfigService';\nimport apiService from '../../services/apiService';\nimport FieldConfigModal from './FieldConfigModal';\nimport FormPreview from './FormPreview';\nimport './FormBuilder.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst FormBuilder = ({\n  onSave,\n  onCancel,\n  initialConfig = null\n}) => {\n  _s();\n  const [selectedHierarchy, setSelectedHierarchy] = useState({});\n  const [formName, setFormName] = useState('');\n  const [formDescription, setFormDescription] = useState('');\n  const [availableFields, setAvailableFields] = useState([]);\n  const [selectedFields, setSelectedFields] = useState([]);\n  const [currentSection, setCurrentSection] = useState('all');\n  const [searchTerm, setSearchTerm] = useState('');\n  const [showPreview, setShowPreview] = useState(false);\n  const [showFieldConfig, setShowFieldConfig] = useState(false);\n  const [configField, setConfigField] = useState(null);\n  const [errors, setErrors] = useState({});\n  const [saving, setSaving] = useState(false);\n  const [savedForms, setSavedForms] = useState([]);\n  const [showSavedForms, setShowSavedForms] = useState(false);\n\n  // State for dropdown data\n  const [divisions, setDivisions] = useState([]);\n  const [categories, setCategories] = useState([]);\n  const [subCategories, setSubCategories] = useState([]);\n  const [loading, setLoading] = useState({\n    divisions: false,\n    categories: false,\n    subCategories: false\n  });\n  useEffect(() => {\n    initializeFields();\n    loadSavedForms();\n    if (initialConfig) {\n      loadInitialConfig(initialConfig);\n    }\n  }, [initialConfig]);\n  const initializeFields = () => {\n    const allFields = getAllPersonFields();\n    setAvailableFields(allFields);\n  };\n  const loadSavedForms = () => {\n    const forms = formConfigService.getAllFormConfigs();\n    setSavedForms(forms);\n  };\n  const loadInitialConfig = config => {\n    setFormName(config.name || '');\n    setFormDescription(config.description || '');\n    setSelectedFields(config.fields || []);\n    if (config.type === 'division' && config.associatedId) {\n      // Load division info for hierarchy selector\n      setSelectedHierarchy({\n        divisionId: config.associatedId\n      });\n    } else if (config.type === 'category' && config.associatedId) {\n      // Load category info for hierarchy selector\n      setSelectedHierarchy({\n        categoryId: config.associatedId\n      });\n    } else if (config.type === 'subcategory' && config.associatedId) {\n      // Load subcategory info for hierarchy selector\n      setSelectedHierarchy({\n        subCategoryId: config.associatedId\n      });\n    }\n\n    // If hierarchy is stored in config, use that\n    if (config.hierarchy) {\n      setSelectedHierarchy(config.hierarchy);\n    }\n  };\n\n  // Load divisions on component mount\n  useEffect(() => {\n    loadDivisions();\n  }, []);\n  const loadDivisions = async () => {\n    setLoading(prev => ({\n      ...prev,\n      divisions: true\n    }));\n    try {\n      const response = await apiService.getDivisions();\n      setDivisions(response.data || []);\n    } catch (error) {\n      console.error('Error loading divisions:', error);\n    } finally {\n      setLoading(prev => ({\n        ...prev,\n        divisions: false\n      }));\n    }\n  };\n  const loadCategories = async divisionId => {\n    if (!divisionId) {\n      setCategories([]);\n      setSubCategories([]);\n      return;\n    }\n    setLoading(prev => ({\n      ...prev,\n      categories: true\n    }));\n    try {\n      const response = await apiService.getCategoriesByDivision(divisionId);\n      setCategories(response.data || []);\n    } catch (error) {\n      console.error('Error loading categories:', error);\n      setCategories([]);\n    } finally {\n      setLoading(prev => ({\n        ...prev,\n        categories: false\n      }));\n    }\n  };\n  const loadSubCategories = async categoryId => {\n    if (!categoryId) {\n      setSubCategories([]);\n      return;\n    }\n    setLoading(prev => ({\n      ...prev,\n      subCategories: true\n    }));\n    try {\n      const response = await apiService.getSubCategoriesByCategory(categoryId);\n      setSubCategories(response.data || []);\n    } catch (error) {\n      console.error('Error loading subcategories:', error);\n      setSubCategories([]);\n    } finally {\n      setLoading(prev => ({\n        ...prev,\n        subCategories: false\n      }));\n    }\n  };\n  const handleDivisionChange = e => {\n    const divisionId = e.target.value;\n    const division = divisions.find(d => d.id === parseInt(divisionId));\n    setSelectedHierarchy({\n      divisionId: divisionId || null,\n      categoryId: null,\n      subCategoryId: null,\n      division: division || null,\n      category: null,\n      subCategory: null\n    });\n    setCategories([]);\n    setSubCategories([]);\n    if (divisionId) {\n      loadCategories(divisionId);\n    }\n  };\n  const handleCategoryChange = e => {\n    const categoryId = e.target.value;\n    const category = categories.find(c => c.id === parseInt(categoryId));\n    setSelectedHierarchy(prev => ({\n      ...prev,\n      categoryId: categoryId || null,\n      subCategoryId: null,\n      category: category || null,\n      subCategory: null\n    }));\n    setSubCategories([]);\n    if (categoryId) {\n      loadSubCategories(categoryId);\n    }\n  };\n  const handleSubCategoryChange = e => {\n    const subCategoryId = e.target.value;\n    const subCategory = subCategories.find(sc => sc.id === parseInt(subCategoryId));\n    setSelectedHierarchy(prev => ({\n      ...prev,\n      subCategoryId: subCategoryId || null,\n      subCategory: subCategory || null\n    }));\n  };\n  const handleFieldToggle = field => {\n    const isSelected = selectedFields.some(f => f.key === field.key);\n    if (isSelected) {\n      setSelectedFields(prev => prev.filter(f => f.key !== field.key));\n    } else {\n      setSelectedFields(prev => [...prev, {\n        ...field\n      }]);\n    }\n  };\n  const handleFieldConfig = field => {\n    setConfigField(field);\n    setShowFieldConfig(true);\n  };\n  const handleFieldConfigSave = updatedField => {\n    setSelectedFields(prev => prev.map(f => f.key === updatedField.key ? updatedField : f));\n    setShowFieldConfig(false);\n    setConfigField(null);\n  };\n  const handleFieldMove = (fromIndex, toIndex) => {\n    const newFields = [...selectedFields];\n    const [movedField] = newFields.splice(fromIndex, 1);\n    newFields.splice(toIndex, 0, movedField);\n    setSelectedFields(newFields);\n  };\n  const handleSave = async () => {\n    const validation = validateForm();\n    if (!validation.isValid) {\n      setErrors(validation.errors);\n      return;\n    }\n    setSaving(true);\n    try {\n      // Ensure complete hierarchy information is included\n      const completeHierarchy = {\n        ...selectedHierarchy,\n        // Include division info\n        divisionId: selectedHierarchy.divisionId,\n        division: selectedHierarchy.division,\n        // Include category info if selected\n        categoryId: selectedHierarchy.categoryId || null,\n        category: selectedHierarchy.category || null,\n        // Include subcategory info if selected\n        subCategoryId: selectedHierarchy.subCategoryId || null,\n        subCategory: selectedHierarchy.subCategory || null\n      };\n      const config = {\n        name: formName,\n        description: formDescription,\n        fields: selectedFields,\n        hierarchy: completeHierarchy,\n        settings: {\n          showSections: true,\n          allowConditionalFields: true,\n          validateOnChange: true\n        }\n      };\n      let savedConfig;\n      // Save form with the most specific level selected, but include complete hierarchy\n      if (selectedHierarchy.subCategoryId) {\n        savedConfig = formConfigService.saveFormConfig('subcategory', selectedHierarchy.subCategoryId, config);\n      } else if (selectedHierarchy.categoryId) {\n        savedConfig = formConfigService.saveFormConfig('category', selectedHierarchy.categoryId, config);\n      } else if (selectedHierarchy.divisionId) {\n        savedConfig = formConfigService.saveFormConfig('division', selectedHierarchy.divisionId, config);\n      } else {\n        throw new Error('Please select at least a division');\n      }\n\n      // Reload saved forms list\n      loadSavedForms();\n      if (onSave) {\n        onSave(savedConfig);\n      } else {\n        // Show success message for standalone usage\n        alert('Form configuration saved successfully!');\n        // Reset form\n        setFormName('');\n        setFormDescription('');\n        setSelectedFields([]);\n        setSelectedHierarchy({});\n      }\n    } catch (error) {\n      console.error('Error saving form:', error);\n      setErrors({\n        general: error.message\n      });\n    } finally {\n      setSaving(false);\n    }\n  };\n  const validateForm = () => {\n    const errors = {};\n    if (!formName.trim()) {\n      errors.formName = 'Form name is required';\n    }\n\n    // Division is required, category and subcategory are optional but must follow hierarchy\n    if (!selectedHierarchy.divisionId) {\n      errors.hierarchy = 'Please select a division';\n    } else if (selectedHierarchy.subCategoryId && !selectedHierarchy.categoryId) {\n      errors.hierarchy = 'Cannot select subcategory without selecting category';\n    }\n    if (selectedFields.length === 0) {\n      errors.fields = 'Please select at least one field';\n    }\n\n    // Check required fields\n    const requiredFields = ['name', 'mobileNumber', 'nature'];\n    const selectedFieldKeys = selectedFields.map(f => f.key);\n    const missingRequired = requiredFields.filter(rf => !selectedFieldKeys.includes(rf));\n    if (missingRequired.length > 0) {\n      errors.requiredFields = `Required fields missing: ${missingRequired.join(', ')}`;\n    }\n    return {\n      isValid: Object.keys(errors).length === 0,\n      errors\n    };\n  };\n  const getFilteredFields = () => {\n    let filtered = availableFields;\n\n    // Filter by section\n    if (currentSection !== 'all') {\n      filtered = filtered.filter(field => field.section === currentSection);\n    }\n\n    // Filter by search term\n    if (searchTerm) {\n      filtered = filtered.filter(field => field.label.toLowerCase().includes(searchTerm.toLowerCase()) || field.key.toLowerCase().includes(searchTerm.toLowerCase()));\n    }\n    return filtered;\n  };\n  const getSectionCounts = () => {\n    const counts = {\n      all: availableFields.length\n    };\n    Object.keys(PersonFieldDefinitions).forEach(sectionKey => {\n      counts[sectionKey] = availableFields.filter(f => f.section === sectionKey).length;\n    });\n    return counts;\n  };\n  const sectionCounts = getSectionCounts();\n  const filteredFields = getFilteredFields();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"form-builder\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"form-builder-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Form Builder\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 352,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-actions\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          onClick: () => setShowPreview(true),\n          className: \"btn btn-secondary\",\n          disabled: selectedFields.length === 0,\n          children: \"Preview Form\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 354,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          onClick: handleSave,\n          className: \"btn btn-primary\",\n          disabled: saving,\n          children: saving ? 'Saving...' : 'Save Form'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 362,\n          columnNumber: 11\n        }, this), onCancel && /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          onClick: onCancel,\n          className: \"btn btn-outline\",\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 371,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 353,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 351,\n      columnNumber: 7\n    }, this), errors.general && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"alert alert-error\",\n      children: errors.general\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 383,\n      columnNumber: 9\n    }, this), Object.keys(errors).length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"validation-errors\",\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        children: \"Please fix the following issues:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 389,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n        children: Object.entries(errors).map(([key, message]) => /*#__PURE__*/_jsxDEV(\"li\", {\n          children: message\n        }, key, false, {\n          fileName: _jsxFileName,\n          lineNumber: 392,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 390,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 388,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"form-builder-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"config-panel\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"config-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"section-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: [\"Saved Forms (\", savedForms.length, \")\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 404,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              className: \"btn-link\",\n              onClick: () => setShowSavedForms(!showSavedForms),\n              children: showSavedForms ? 'Hide' : 'Show'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 405,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 403,\n            columnNumber: 13\n          }, this), showSavedForms && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"saved-forms-list\",\n            children: savedForms.length === 0 ? /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"no-forms-message\",\n              children: \"No saved forms yet. Create your first form below!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 417,\n              columnNumber: 19\n            }, this) : savedForms.map(form => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"saved-form-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-item-header\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  children: form.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 422,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"form-type-badge\",\n                  children: form.type\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 423,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 421,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"form-description\",\n                children: form.description || 'No description'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 425,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-meta\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [form.summary.fieldCount, \" fields\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 427,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"\\u2022\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 428,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [\"Updated \", new Date(form.updatedAt).toLocaleDateString()]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 429,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 426,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-actions\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"button\",\n                  className: \"btn-small btn-outline\",\n                  onClick: () => loadInitialConfig(form),\n                  children: \"Load\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 432,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"button\",\n                  className: \"btn-small btn-danger\",\n                  onClick: () => {\n                    if (window.confirm('Are you sure you want to delete this form?')) {\n                      formConfigService.deleteFormConfig(form.type, form.associatedId);\n                      loadSavedForms();\n                    }\n                  },\n                  children: \"Delete\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 439,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 431,\n                columnNumber: 23\n              }, this)]\n            }, form.key, true, {\n              fileName: _jsxFileName,\n              lineNumber: 420,\n              columnNumber: 21\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 415,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 402,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"config-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Form Configuration\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 461,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"Form Name *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 464,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: formName,\n              onChange: e => setFormName(e.target.value),\n              placeholder: \"Enter form name\",\n              className: errors.formName ? 'error' : ''\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 465,\n              columnNumber: 15\n            }, this), errors.formName && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"error-message\",\n              children: errors.formName\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 472,\n              columnNumber: 35\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 463,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"Description\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 476,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n              value: formDescription,\n              onChange: e => setFormDescription(e.target.value),\n              placeholder: \"Enter form description\",\n              rows: \"3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 477,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 475,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 460,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"config-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: [\"Selected Fields (\", selectedFields.length, \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 490,\n            columnNumber: 13\n          }, this), errors.fields && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"error-message\",\n            children: errors.fields\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 491,\n            columnNumber: 31\n          }, this), errors.requiredFields && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"error-message\",\n            children: errors.requiredFields\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 492,\n            columnNumber: 39\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"selected-fields\",\n            children: selectedFields.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"empty-state\",\n              children: \"No fields selected. Choose fields from the available fields panel.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 496,\n              columnNumber: 17\n            }, this) : selectedFields.map((field, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"selected-field\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"field-info\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"field-label\",\n                  children: field.label\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 503,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"field-type\",\n                  children: field.type\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 504,\n                  columnNumber: 23\n                }, this), field.required && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"required-badge\",\n                  children: \"Required\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 505,\n                  columnNumber: 42\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 502,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"field-actions\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"button\",\n                  onClick: () => handleFieldConfig(field),\n                  className: \"btn-icon\",\n                  title: \"Configure field\",\n                  children: \"\\u2699\\uFE0F\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 508,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"button\",\n                  onClick: () => handleFieldMove(index, Math.max(0, index - 1)),\n                  className: \"btn-icon\",\n                  disabled: index === 0,\n                  title: \"Move up\",\n                  children: \"\\u2191\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 516,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"button\",\n                  onClick: () => handleFieldMove(index, Math.min(selectedFields.length - 1, index + 1)),\n                  className: \"btn-icon\",\n                  disabled: index === selectedFields.length - 1,\n                  title: \"Move down\",\n                  children: \"\\u2193\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 525,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"button\",\n                  onClick: () => handleFieldToggle(field),\n                  className: \"btn-icon remove\",\n                  title: \"Remove field\",\n                  children: \"\\u2715\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 534,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 507,\n                columnNumber: 21\n              }, this)]\n            }, field.key, true, {\n              fileName: _jsxFileName,\n              lineNumber: 501,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 494,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 489,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 400,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"fields-panel\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"fields-header\",\n          children: /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Available Fields\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 553,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 552,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hierarchy-selection\",\n          style: {\n            backgroundColor: '#f8f9fa',\n            border: '1px solid #e1e5e9',\n            borderRadius: '8px',\n            padding: '1.5rem',\n            margin: '1rem 0'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            style: {\n              margin: '0 0 1rem 0',\n              color: '#495057'\n            },\n            children: \"Associate with Division/Category/SubCategory *\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 564,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"hierarchy-dropdowns\",\n            style: {\n              display: 'grid',\n              gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n              gap: '1rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                style: {\n                  display: 'block',\n                  marginBottom: '0.5rem',\n                  fontWeight: '500',\n                  color: '#495057'\n                },\n                children: \"Division *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 575,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: selectedHierarchy.divisionId || '',\n                onChange: handleDivisionChange,\n                disabled: loading.divisions,\n                className: errors.divisionId ? 'error' : '',\n                style: {\n                  width: '100%',\n                  padding: '0.75rem',\n                  border: '1px solid #ced4da',\n                  borderRadius: '4px',\n                  fontSize: '1rem',\n                  backgroundColor: 'white'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Select Division\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 597,\n                  columnNumber: 19\n                }, this), divisions.map(division => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: division.id,\n                  children: division.name\n                }, division.id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 599,\n                  columnNumber: 21\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 583,\n                columnNumber: 17\n              }, this), errors.divisionId && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  color: '#dc3545',\n                  fontSize: '0.875rem',\n                  marginTop: '0.25rem'\n                },\n                children: errors.divisionId\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 605,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 574,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                style: {\n                  display: 'block',\n                  marginBottom: '0.5rem',\n                  fontWeight: '500',\n                  color: '#495057'\n                },\n                children: \"Category (Optional)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 613,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: selectedHierarchy.categoryId || '',\n                onChange: handleCategoryChange,\n                disabled: !selectedHierarchy.divisionId || loading.categories,\n                className: errors.categoryId ? 'error' : '',\n                style: {\n                  width: '100%',\n                  padding: '0.75rem',\n                  border: '1px solid #ced4da',\n                  borderRadius: '4px',\n                  fontSize: '1rem',\n                  backgroundColor: 'white'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: !selectedHierarchy.divisionId ? 'Select Division first' : 'Select Category (Optional)'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 635,\n                  columnNumber: 19\n                }, this), categories.map(category => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: category.id,\n                  children: category.name\n                }, category.id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 639,\n                  columnNumber: 21\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 621,\n                columnNumber: 17\n              }, this), errors.categoryId && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  color: '#dc3545',\n                  fontSize: '0.875rem',\n                  marginTop: '0.25rem'\n                },\n                children: errors.categoryId\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 645,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 612,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                style: {\n                  display: 'block',\n                  marginBottom: '0.5rem',\n                  fontWeight: '500',\n                  color: '#495057'\n                },\n                children: \"SubCategory (Optional)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 653,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: selectedHierarchy.subCategoryId || '',\n                onChange: handleSubCategoryChange,\n                disabled: !selectedHierarchy.categoryId || loading.subCategories,\n                style: {\n                  width: '100%',\n                  padding: '0.75rem',\n                  border: '1px solid #ced4da',\n                  borderRadius: '4px',\n                  fontSize: '1rem',\n                  backgroundColor: 'white'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: !selectedHierarchy.categoryId ? 'Select Category first' : 'Select SubCategory (Optional)'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 674,\n                  columnNumber: 19\n                }, this), subCategories.map(subCategory => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: subCategory.id,\n                  children: subCategory.name\n                }, subCategory.id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 678,\n                  columnNumber: 21\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 661,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 652,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 568,\n            columnNumber: 13\n          }, this), errors.hierarchy && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              color: '#dc3545',\n              fontSize: '0.875rem',\n              marginTop: '0.5rem'\n            },\n            children: errors.hierarchy\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 687,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 557,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"fields-controls\",\n          children: /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            placeholder: \"Search fields...\",\n            value: searchTerm,\n            onChange: e => setSearchTerm(e.target.value),\n            className: \"search-input\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 695,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 694,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"section-tabs\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            onClick: () => setCurrentSection('all'),\n            className: `section-tab ${currentSection === 'all' ? 'active' : ''}`,\n            children: [\"All (\", sectionCounts.all, \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 706,\n            columnNumber: 13\n          }, this), Object.entries(PersonFieldDefinitions).map(([sectionKey, section]) => /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            onClick: () => setCurrentSection(sectionKey),\n            className: `section-tab ${currentSection === sectionKey ? 'active' : ''}`,\n            children: [section.title, \" (\", sectionCounts[sectionKey], \")\"]\n          }, sectionKey, true, {\n            fileName: _jsxFileName,\n            lineNumber: 714,\n            columnNumber: 15\n          }, this))]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 705,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"fields-list\",\n          children: filteredFields.map(field => {\n            const isSelected = selectedFields.some(f => f.key === field.key);\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `field-item ${isSelected ? 'selected' : ''}`,\n              onClick: () => handleFieldToggle(field),\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"field-checkbox\",\n                children: /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"checkbox\",\n                  checked: isSelected,\n                  onChange: () => handleFieldToggle(field)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 736,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 735,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"field-details\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"field-name\",\n                  children: field.label\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 743,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"field-meta\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"field-type\",\n                    children: field.type\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 745,\n                    columnNumber: 23\n                  }, this), field.required && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"required-badge\",\n                    children: \"Required\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 746,\n                    columnNumber: 42\n                  }, this), field.conditional && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"conditional-badge\",\n                    children: \"Conditional\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 747,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 744,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 742,\n                columnNumber: 19\n              }, this)]\n            }, field.key, true, {\n              fileName: _jsxFileName,\n              lineNumber: 730,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 726,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 551,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 398,\n      columnNumber: 7\n    }, this), showFieldConfig && configField && /*#__PURE__*/_jsxDEV(FieldConfigModal, {\n      field: configField,\n      onSave: handleFieldConfigSave,\n      onCancel: () => {\n        setShowFieldConfig(false);\n        setConfigField(null);\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 759,\n      columnNumber: 9\n    }, this), showPreview && /*#__PURE__*/_jsxDEV(FormPreview, {\n      fields: selectedFields,\n      formName: formName,\n      onClose: () => setShowPreview(false)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 770,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 350,\n    columnNumber: 5\n  }, this);\n};\n_s(FormBuilder, \"UM4Nfe1CLkbbCJBa+5c520CzEdo=\");\n_c = FormBuilder;\nexport default FormBuilder;\nvar _c;\n$RefreshReg$(_c, \"FormBuilder\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "PersonFieldDefinitions", "<PERSON><PERSON><PERSON><PERSON><PERSON>F<PERSON>s", "formConfigService", "apiService", "FieldConfigModal", "FormPreview", "jsxDEV", "_jsxDEV", "FormBuilder", "onSave", "onCancel", "initialConfig", "_s", "selectedHierarchy", "setSelectedHierarchy", "formName", "setFormName", "formDescription", "setFormDescription", "availableFields", "setAvailableFields", "<PERSON><PERSON><PERSON>s", "setSelectedFields", "currentSection", "setCurrentSection", "searchTerm", "setSearchTerm", "showPreview", "setShowPreview", "showFieldConfig", "setShowFieldConfig", "config<PERSON><PERSON>", "setConfigField", "errors", "setErrors", "saving", "setSaving", "savedForms", "setSavedForms", "showSavedForms", "setShowSavedForms", "divisions", "setDivisions", "categories", "setCategories", "subCategories", "setSubCategories", "loading", "setLoading", "initializeFields", "loadSavedForms", "loadInitialConfig", "allFields", "forms", "getAllFormConfigs", "config", "name", "description", "fields", "type", "associatedId", "divisionId", "categoryId", "subCategoryId", "hierarchy", "loadDivisions", "prev", "response", "getDivisions", "data", "error", "console", "loadCategories", "getCategoriesByDivision", "loadSubCategories", "getSubCategoriesByCategory", "handleDivisionChange", "e", "target", "value", "division", "find", "d", "id", "parseInt", "category", "subCategory", "handleCategoryChange", "c", "handleSubCategoryChange", "sc", "handleFieldToggle", "field", "isSelected", "some", "f", "key", "filter", "handleFieldConfig", "handleFieldConfigSave", "updatedField", "map", "handleFieldMove", "fromIndex", "toIndex", "new<PERSON>ields", "movedField", "splice", "handleSave", "validation", "validateForm", "<PERSON><PERSON><PERSON><PERSON>", "completeHierarchy", "settings", "showSections", "allowConditionalFields", "validateOnChange", "savedConfig", "saveFormConfig", "Error", "alert", "general", "message", "trim", "length", "requiredFields", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "missingRequired", "rf", "includes", "join", "Object", "keys", "getFilteredFields", "filtered", "section", "label", "toLowerCase", "getSectionCounts", "counts", "all", "for<PERSON>ach", "sectionKey", "sectionCounts", "filteredFields", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "disabled", "entries", "form", "summary", "fieldCount", "Date", "updatedAt", "toLocaleDateString", "window", "confirm", "deleteFormConfig", "onChange", "placeholder", "rows", "index", "required", "title", "Math", "max", "min", "style", "backgroundColor", "border", "borderRadius", "padding", "margin", "color", "display", "gridTemplateColumns", "gap", "marginBottom", "fontWeight", "width", "fontSize", "marginTop", "checked", "conditional", "onClose", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/Advanced/Advanced CRM filed all use/frontend/src/components/forms/FormBuilder.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { PersonFieldDefinitions, getAllPersonFields } from '../../constants/personConstants';\nimport formConfigService from '../../services/formConfigService';\nimport apiService from '../../services/apiService';\nimport FieldConfigModal from './FieldConfigModal';\nimport FormPreview from './FormPreview';\nimport './FormBuilder.css';\n\nconst FormBuilder = ({ onSave, onCancel, initialConfig = null }) => {\n  const [selectedHierarchy, setSelectedHierarchy] = useState({});\n  const [formName, setFormName] = useState('');\n  const [formDescription, setFormDescription] = useState('');\n  const [availableFields, setAvailableFields] = useState([]);\n  const [selectedFields, setSelectedFields] = useState([]);\n  const [currentSection, setCurrentSection] = useState('all');\n  const [searchTerm, setSearchTerm] = useState('');\n  const [showPreview, setShowPreview] = useState(false);\n  const [showFieldConfig, setShowFieldConfig] = useState(false);\n  const [configField, setConfigField] = useState(null);\n  const [errors, setErrors] = useState({});\n  const [saving, setSaving] = useState(false);\n  const [savedForms, setSavedForms] = useState([]);\n  const [showSavedForms, setShowSavedForms] = useState(false);\n\n  // State for dropdown data\n  const [divisions, setDivisions] = useState([]);\n  const [categories, setCategories] = useState([]);\n  const [subCategories, setSubCategories] = useState([]);\n  const [loading, setLoading] = useState({\n    divisions: false,\n    categories: false,\n    subCategories: false\n  });\n\n  useEffect(() => {\n    initializeFields();\n    loadSavedForms();\n    if (initialConfig) {\n      loadInitialConfig(initialConfig);\n    }\n  }, [initialConfig]);\n\n  const initializeFields = () => {\n    const allFields = getAllPersonFields();\n    setAvailableFields(allFields);\n  };\n\n  const loadSavedForms = () => {\n    const forms = formConfigService.getAllFormConfigs();\n    setSavedForms(forms);\n  };\n\n  const loadInitialConfig = (config) => {\n    setFormName(config.name || '');\n    setFormDescription(config.description || '');\n    setSelectedFields(config.fields || []);\n    \n    if (config.type === 'division' && config.associatedId) {\n      // Load division info for hierarchy selector\n      setSelectedHierarchy({\n        divisionId: config.associatedId\n      });\n    } else if (config.type === 'category' && config.associatedId) {\n      // Load category info for hierarchy selector\n      setSelectedHierarchy({\n        categoryId: config.associatedId\n      });\n    } else if (config.type === 'subcategory' && config.associatedId) {\n      // Load subcategory info for hierarchy selector\n      setSelectedHierarchy({\n        subCategoryId: config.associatedId\n      });\n    }\n\n    // If hierarchy is stored in config, use that\n    if (config.hierarchy) {\n      setSelectedHierarchy(config.hierarchy);\n    }\n  };\n\n  // Load divisions on component mount\n  useEffect(() => {\n    loadDivisions();\n  }, []);\n\n  const loadDivisions = async () => {\n    setLoading(prev => ({ ...prev, divisions: true }));\n    try {\n      const response = await apiService.getDivisions();\n      setDivisions(response.data || []);\n    } catch (error) {\n      console.error('Error loading divisions:', error);\n    } finally {\n      setLoading(prev => ({ ...prev, divisions: false }));\n    }\n  };\n\n  const loadCategories = async (divisionId) => {\n    if (!divisionId) {\n      setCategories([]);\n      setSubCategories([]);\n      return;\n    }\n\n    setLoading(prev => ({ ...prev, categories: true }));\n    try {\n      const response = await apiService.getCategoriesByDivision(divisionId);\n      setCategories(response.data || []);\n    } catch (error) {\n      console.error('Error loading categories:', error);\n      setCategories([]);\n    } finally {\n      setLoading(prev => ({ ...prev, categories: false }));\n    }\n  };\n\n  const loadSubCategories = async (categoryId) => {\n    if (!categoryId) {\n      setSubCategories([]);\n      return;\n    }\n\n    setLoading(prev => ({ ...prev, subCategories: true }));\n    try {\n      const response = await apiService.getSubCategoriesByCategory(categoryId);\n      setSubCategories(response.data || []);\n    } catch (error) {\n      console.error('Error loading subcategories:', error);\n      setSubCategories([]);\n    } finally {\n      setLoading(prev => ({ ...prev, subCategories: false }));\n    }\n  };\n\n  const handleDivisionChange = (e) => {\n    const divisionId = e.target.value;\n    const division = divisions.find(d => d.id === parseInt(divisionId));\n\n    setSelectedHierarchy({\n      divisionId: divisionId || null,\n      categoryId: null,\n      subCategoryId: null,\n      division: division || null,\n      category: null,\n      subCategory: null\n    });\n\n    setCategories([]);\n    setSubCategories([]);\n\n    if (divisionId) {\n      loadCategories(divisionId);\n    }\n  };\n\n  const handleCategoryChange = (e) => {\n    const categoryId = e.target.value;\n    const category = categories.find(c => c.id === parseInt(categoryId));\n\n    setSelectedHierarchy(prev => ({\n      ...prev,\n      categoryId: categoryId || null,\n      subCategoryId: null,\n      category: category || null,\n      subCategory: null\n    }));\n\n    setSubCategories([]);\n\n    if (categoryId) {\n      loadSubCategories(categoryId);\n    }\n  };\n\n  const handleSubCategoryChange = (e) => {\n    const subCategoryId = e.target.value;\n    const subCategory = subCategories.find(sc => sc.id === parseInt(subCategoryId));\n\n    setSelectedHierarchy(prev => ({\n      ...prev,\n      subCategoryId: subCategoryId || null,\n      subCategory: subCategory || null\n    }));\n  };\n\n  const handleFieldToggle = (field) => {\n    const isSelected = selectedFields.some(f => f.key === field.key);\n    \n    if (isSelected) {\n      setSelectedFields(prev => prev.filter(f => f.key !== field.key));\n    } else {\n      setSelectedFields(prev => [...prev, { ...field }]);\n    }\n  };\n\n  const handleFieldConfig = (field) => {\n    setConfigField(field);\n    setShowFieldConfig(true);\n  };\n\n  const handleFieldConfigSave = (updatedField) => {\n    setSelectedFields(prev => \n      prev.map(f => f.key === updatedField.key ? updatedField : f)\n    );\n    setShowFieldConfig(false);\n    setConfigField(null);\n  };\n\n  const handleFieldMove = (fromIndex, toIndex) => {\n    const newFields = [...selectedFields];\n    const [movedField] = newFields.splice(fromIndex, 1);\n    newFields.splice(toIndex, 0, movedField);\n    setSelectedFields(newFields);\n  };\n\n  const handleSave = async () => {\n    const validation = validateForm();\n\n    if (!validation.isValid) {\n      setErrors(validation.errors);\n      return;\n    }\n\n    setSaving(true);\n    try {\n      // Ensure complete hierarchy information is included\n      const completeHierarchy = {\n        ...selectedHierarchy,\n        // Include division info\n        divisionId: selectedHierarchy.divisionId,\n        division: selectedHierarchy.division,\n        // Include category info if selected\n        categoryId: selectedHierarchy.categoryId || null,\n        category: selectedHierarchy.category || null,\n        // Include subcategory info if selected\n        subCategoryId: selectedHierarchy.subCategoryId || null,\n        subCategory: selectedHierarchy.subCategory || null\n      };\n\n      const config = {\n        name: formName,\n        description: formDescription,\n        fields: selectedFields,\n        hierarchy: completeHierarchy,\n        settings: {\n          showSections: true,\n          allowConditionalFields: true,\n          validateOnChange: true\n        }\n      };\n\n      let savedConfig;\n      // Save form with the most specific level selected, but include complete hierarchy\n      if (selectedHierarchy.subCategoryId) {\n        savedConfig = formConfigService.saveFormConfig('subcategory', selectedHierarchy.subCategoryId, config);\n      } else if (selectedHierarchy.categoryId) {\n        savedConfig = formConfigService.saveFormConfig('category', selectedHierarchy.categoryId, config);\n      } else if (selectedHierarchy.divisionId) {\n        savedConfig = formConfigService.saveFormConfig('division', selectedHierarchy.divisionId, config);\n      } else {\n        throw new Error('Please select at least a division');\n      }\n\n      // Reload saved forms list\n      loadSavedForms();\n\n      if (onSave) {\n        onSave(savedConfig);\n      } else {\n        // Show success message for standalone usage\n        alert('Form configuration saved successfully!');\n        // Reset form\n        setFormName('');\n        setFormDescription('');\n        setSelectedFields([]);\n        setSelectedHierarchy({});\n      }\n    } catch (error) {\n      console.error('Error saving form:', error);\n      setErrors({ general: error.message });\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  const validateForm = () => {\n    const errors = {};\n\n    if (!formName.trim()) {\n      errors.formName = 'Form name is required';\n    }\n\n    // Division is required, category and subcategory are optional but must follow hierarchy\n    if (!selectedHierarchy.divisionId) {\n      errors.hierarchy = 'Please select a division';\n    } else if (selectedHierarchy.subCategoryId && !selectedHierarchy.categoryId) {\n      errors.hierarchy = 'Cannot select subcategory without selecting category';\n    }\n\n    if (selectedFields.length === 0) {\n      errors.fields = 'Please select at least one field';\n    }\n\n    // Check required fields\n    const requiredFields = ['name', 'mobileNumber', 'nature'];\n    const selectedFieldKeys = selectedFields.map(f => f.key);\n    const missingRequired = requiredFields.filter(rf => !selectedFieldKeys.includes(rf));\n\n    if (missingRequired.length > 0) {\n      errors.requiredFields = `Required fields missing: ${missingRequired.join(', ')}`;\n    }\n\n    return {\n      isValid: Object.keys(errors).length === 0,\n      errors\n    };\n  };\n\n  const getFilteredFields = () => {\n    let filtered = availableFields;\n\n    // Filter by section\n    if (currentSection !== 'all') {\n      filtered = filtered.filter(field => field.section === currentSection);\n    }\n\n    // Filter by search term\n    if (searchTerm) {\n      filtered = filtered.filter(field => \n        field.label.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        field.key.toLowerCase().includes(searchTerm.toLowerCase())\n      );\n    }\n\n    return filtered;\n  };\n\n  const getSectionCounts = () => {\n    const counts = { all: availableFields.length };\n    Object.keys(PersonFieldDefinitions).forEach(sectionKey => {\n      counts[sectionKey] = availableFields.filter(f => f.section === sectionKey).length;\n    });\n    return counts;\n  };\n\n  const sectionCounts = getSectionCounts();\n  const filteredFields = getFilteredFields();\n\n  return (\n    <div className=\"form-builder\">\n      <div className=\"form-builder-header\">\n        <h2>Form Builder</h2>\n        <div className=\"header-actions\">\n          <button \n            type=\"button\" \n            onClick={() => setShowPreview(true)}\n            className=\"btn btn-secondary\"\n            disabled={selectedFields.length === 0}\n          >\n            Preview Form\n          </button>\n          <button\n            type=\"button\"\n            onClick={handleSave}\n            className=\"btn btn-primary\"\n            disabled={saving}\n          >\n            {saving ? 'Saving...' : 'Save Form'}\n          </button>\n          {onCancel && (\n            <button\n              type=\"button\"\n              onClick={onCancel}\n              className=\"btn btn-outline\"\n            >\n              Cancel\n            </button>\n          )}\n        </div>\n      </div>\n\n      {errors.general && (\n        <div className=\"alert alert-error\">{errors.general}</div>\n      )}\n\n      {/* Validation Status */}\n      {Object.keys(errors).length > 0 && (\n        <div className=\"validation-errors\">\n          <h4>Please fix the following issues:</h4>\n          <ul>\n            {Object.entries(errors).map(([key, message]) => (\n              <li key={key}>{message}</li>\n            ))}\n          </ul>\n        </div>\n      )}\n\n      <div className=\"form-builder-content\">\n        {/* Form Configuration Panel */}\n        <div className=\"config-panel\">\n          {/* Saved Forms Section */}\n          <div className=\"config-section\">\n            <div className=\"section-header\">\n              <h3>Saved Forms ({savedForms.length})</h3>\n              <button\n                type=\"button\"\n                className=\"btn-link\"\n                onClick={() => setShowSavedForms(!showSavedForms)}\n              >\n                {showSavedForms ? 'Hide' : 'Show'}\n              </button>\n            </div>\n\n            {showSavedForms && (\n              <div className=\"saved-forms-list\">\n                {savedForms.length === 0 ? (\n                  <p className=\"no-forms-message\">No saved forms yet. Create your first form below!</p>\n                ) : (\n                  savedForms.map((form) => (\n                    <div key={form.key} className=\"saved-form-item\">\n                      <div className=\"form-item-header\">\n                        <h4>{form.name}</h4>\n                        <span className=\"form-type-badge\">{form.type}</span>\n                      </div>\n                      <p className=\"form-description\">{form.description || 'No description'}</p>\n                      <div className=\"form-meta\">\n                        <span>{form.summary.fieldCount} fields</span>\n                        <span>•</span>\n                        <span>Updated {new Date(form.updatedAt).toLocaleDateString()}</span>\n                      </div>\n                      <div className=\"form-actions\">\n                        <button\n                          type=\"button\"\n                          className=\"btn-small btn-outline\"\n                          onClick={() => loadInitialConfig(form)}\n                        >\n                          Load\n                        </button>\n                        <button\n                          type=\"button\"\n                          className=\"btn-small btn-danger\"\n                          onClick={() => {\n                            if (window.confirm('Are you sure you want to delete this form?')) {\n                              formConfigService.deleteFormConfig(form.type, form.associatedId);\n                              loadSavedForms();\n                            }\n                          }}\n                        >\n                          Delete\n                        </button>\n                      </div>\n                    </div>\n                  ))\n                )}\n              </div>\n            )}\n          </div>\n\n          {/* Form Configuration Section */}\n          <div className=\"config-section\">\n            <h3>Form Configuration</h3>\n\n            <div className=\"form-group\">\n              <label>Form Name *</label>\n              <input\n                type=\"text\"\n                value={formName}\n                onChange={(e) => setFormName(e.target.value)}\n                placeholder=\"Enter form name\"\n                className={errors.formName ? 'error' : ''}\n              />\n              {errors.formName && <div className=\"error-message\">{errors.formName}</div>}\n            </div>\n\n            <div className=\"form-group\">\n              <label>Description</label>\n              <textarea\n                value={formDescription}\n                onChange={(e) => setFormDescription(e.target.value)}\n                placeholder=\"Enter form description\"\n                rows=\"3\"\n              />\n            </div>\n\n\n          </div>\n\n          {/* Selected Fields Panel */}\n          <div className=\"config-section\">\n            <h3>Selected Fields ({selectedFields.length})</h3>\n            {errors.fields && <div className=\"error-message\">{errors.fields}</div>}\n            {errors.requiredFields && <div className=\"error-message\">{errors.requiredFields}</div>}\n            \n            <div className=\"selected-fields\">\n              {selectedFields.length === 0 ? (\n                <div className=\"empty-state\">\n                  No fields selected. Choose fields from the available fields panel.\n                </div>\n              ) : (\n                selectedFields.map((field, index) => (\n                  <div key={field.key} className=\"selected-field\">\n                    <div className=\"field-info\">\n                      <span className=\"field-label\">{field.label}</span>\n                      <span className=\"field-type\">{field.type}</span>\n                      {field.required && <span className=\"required-badge\">Required</span>}\n                    </div>\n                    <div className=\"field-actions\">\n                      <button\n                        type=\"button\"\n                        onClick={() => handleFieldConfig(field)}\n                        className=\"btn-icon\"\n                        title=\"Configure field\"\n                      >\n                        ⚙️\n                      </button>\n                      <button\n                        type=\"button\"\n                        onClick={() => handleFieldMove(index, Math.max(0, index - 1))}\n                        className=\"btn-icon\"\n                        disabled={index === 0}\n                        title=\"Move up\"\n                      >\n                        ↑\n                      </button>\n                      <button\n                        type=\"button\"\n                        onClick={() => handleFieldMove(index, Math.min(selectedFields.length - 1, index + 1))}\n                        className=\"btn-icon\"\n                        disabled={index === selectedFields.length - 1}\n                        title=\"Move down\"\n                      >\n                        ↓\n                      </button>\n                      <button\n                        type=\"button\"\n                        onClick={() => handleFieldToggle(field)}\n                        className=\"btn-icon remove\"\n                        title=\"Remove field\"\n                      >\n                        ✕\n                      </button>\n                    </div>\n                  </div>\n                ))\n              )}\n            </div>\n          </div>\n        </div>\n\n        {/* Available Fields Panel */}\n        <div className=\"fields-panel\">\n          <div className=\"fields-header\">\n            <h3>Available Fields</h3>\n          </div>\n\n          {/* Division/Category/SubCategory Selection */}\n          <div className=\"hierarchy-selection\" style={{\n            backgroundColor: '#f8f9fa',\n            border: '1px solid #e1e5e9',\n            borderRadius: '8px',\n            padding: '1.5rem',\n            margin: '1rem 0'\n          }}>\n            <h4 style={{ margin: '0 0 1rem 0', color: '#495057' }}>\n              Associate with Division/Category/SubCategory *\n            </h4>\n\n            <div className=\"hierarchy-dropdowns\" style={{\n              display: 'grid',\n              gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n              gap: '1rem'\n            }}>\n              {/* Division Dropdown */}\n              <div className=\"form-group\">\n                <label style={{\n                  display: 'block',\n                  marginBottom: '0.5rem',\n                  fontWeight: '500',\n                  color: '#495057'\n                }}>\n                  Division *\n                </label>\n                <select\n                  value={selectedHierarchy.divisionId || ''}\n                  onChange={handleDivisionChange}\n                  disabled={loading.divisions}\n                  className={errors.divisionId ? 'error' : ''}\n                  style={{\n                    width: '100%',\n                    padding: '0.75rem',\n                    border: '1px solid #ced4da',\n                    borderRadius: '4px',\n                    fontSize: '1rem',\n                    backgroundColor: 'white'\n                  }}\n                >\n                  <option value=\"\">Select Division</option>\n                  {divisions.map(division => (\n                    <option key={division.id} value={division.id}>\n                      {division.name}\n                    </option>\n                  ))}\n                </select>\n                {errors.divisionId && (\n                  <div style={{ color: '#dc3545', fontSize: '0.875rem', marginTop: '0.25rem' }}>\n                    {errors.divisionId}\n                  </div>\n                )}\n              </div>\n\n              {/* Category Dropdown */}\n              <div className=\"form-group\">\n                <label style={{\n                  display: 'block',\n                  marginBottom: '0.5rem',\n                  fontWeight: '500',\n                  color: '#495057'\n                }}>\n                  Category (Optional)\n                </label>\n                <select\n                  value={selectedHierarchy.categoryId || ''}\n                  onChange={handleCategoryChange}\n                  disabled={!selectedHierarchy.divisionId || loading.categories}\n                  className={errors.categoryId ? 'error' : ''}\n                  style={{\n                    width: '100%',\n                    padding: '0.75rem',\n                    border: '1px solid #ced4da',\n                    borderRadius: '4px',\n                    fontSize: '1rem',\n                    backgroundColor: 'white'\n                  }}\n                >\n                  <option value=\"\">\n                    {!selectedHierarchy.divisionId ? 'Select Division first' : 'Select Category (Optional)'}\n                  </option>\n                  {categories.map(category => (\n                    <option key={category.id} value={category.id}>\n                      {category.name}\n                    </option>\n                  ))}\n                </select>\n                {errors.categoryId && (\n                  <div style={{ color: '#dc3545', fontSize: '0.875rem', marginTop: '0.25rem' }}>\n                    {errors.categoryId}\n                  </div>\n                )}\n              </div>\n\n              {/* SubCategory Dropdown */}\n              <div className=\"form-group\">\n                <label style={{\n                  display: 'block',\n                  marginBottom: '0.5rem',\n                  fontWeight: '500',\n                  color: '#495057'\n                }}>\n                  SubCategory (Optional)\n                </label>\n                <select\n                  value={selectedHierarchy.subCategoryId || ''}\n                  onChange={handleSubCategoryChange}\n                  disabled={!selectedHierarchy.categoryId || loading.subCategories}\n                  style={{\n                    width: '100%',\n                    padding: '0.75rem',\n                    border: '1px solid #ced4da',\n                    borderRadius: '4px',\n                    fontSize: '1rem',\n                    backgroundColor: 'white'\n                  }}\n                >\n                  <option value=\"\">\n                    {!selectedHierarchy.categoryId ? 'Select Category first' : 'Select SubCategory (Optional)'}\n                  </option>\n                  {subCategories.map(subCategory => (\n                    <option key={subCategory.id} value={subCategory.id}>\n                      {subCategory.name}\n                    </option>\n                  ))}\n                </select>\n              </div>\n            </div>\n\n            {errors.hierarchy && (\n              <div style={{ color: '#dc3545', fontSize: '0.875rem', marginTop: '0.5rem' }}>\n                {errors.hierarchy}\n              </div>\n            )}\n          </div>\n\n          {/* Search and Filter Controls */}\n          <div className=\"fields-controls\">\n            <input\n              type=\"text\"\n              placeholder=\"Search fields...\"\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              className=\"search-input\"\n            />\n          </div>\n\n          {/* Section Tabs */}\n          <div className=\"section-tabs\">\n            <button\n              type=\"button\"\n              onClick={() => setCurrentSection('all')}\n              className={`section-tab ${currentSection === 'all' ? 'active' : ''}`}\n            >\n              All ({sectionCounts.all})\n            </button>\n            {Object.entries(PersonFieldDefinitions).map(([sectionKey, section]) => (\n              <button\n                key={sectionKey}\n                type=\"button\"\n                onClick={() => setCurrentSection(sectionKey)}\n                className={`section-tab ${currentSection === sectionKey ? 'active' : ''}`}\n              >\n                {section.title} ({sectionCounts[sectionKey]})\n              </button>\n            ))}\n          </div>\n\n          {/* Fields List */}\n          <div className=\"fields-list\">\n            {filteredFields.map(field => {\n              const isSelected = selectedFields.some(f => f.key === field.key);\n              return (\n                <div\n                  key={field.key}\n                  className={`field-item ${isSelected ? 'selected' : ''}`}\n                  onClick={() => handleFieldToggle(field)}\n                >\n                  <div className=\"field-checkbox\">\n                    <input\n                      type=\"checkbox\"\n                      checked={isSelected}\n                      onChange={() => handleFieldToggle(field)}\n                    />\n                  </div>\n                  <div className=\"field-details\">\n                    <div className=\"field-name\">{field.label}</div>\n                    <div className=\"field-meta\">\n                      <span className=\"field-type\">{field.type}</span>\n                      {field.required && <span className=\"required-badge\">Required</span>}\n                      {field.conditional && <span className=\"conditional-badge\">Conditional</span>}\n                    </div>\n                  </div>\n                </div>\n              );\n            })}\n          </div>\n        </div>\n      </div>\n\n      {/* Modals */}\n      {showFieldConfig && configField && (\n        <FieldConfigModal\n          field={configField}\n          onSave={handleFieldConfigSave}\n          onCancel={() => {\n            setShowFieldConfig(false);\n            setConfigField(null);\n          }}\n        />\n      )}\n\n      {showPreview && (\n        <FormPreview\n          fields={selectedFields}\n          formName={formName}\n          onClose={() => setShowPreview(false)}\n        />\n      )}\n    </div>\n  );\n};\n\nexport default FormBuilder;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,sBAAsB,EAAEC,kBAAkB,QAAQ,iCAAiC;AAC5F,OAAOC,iBAAiB,MAAM,kCAAkC;AAChE,OAAOC,UAAU,MAAM,2BAA2B;AAClD,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAO,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3B,MAAMC,WAAW,GAAGA,CAAC;EAAEC,MAAM;EAAEC,QAAQ;EAAEC,aAAa,GAAG;AAAK,CAAC,KAAK;EAAAC,EAAA;EAClE,MAAM,CAACC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGhB,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC9D,MAAM,CAACiB,QAAQ,EAAEC,WAAW,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACmB,eAAe,EAAEC,kBAAkB,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACqB,eAAe,EAAEC,kBAAkB,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACuB,cAAc,EAAEC,iBAAiB,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACyB,cAAc,EAAEC,iBAAiB,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAAC2B,UAAU,EAAEC,aAAa,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC6B,WAAW,EAAEC,cAAc,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC+B,eAAe,EAAEC,kBAAkB,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACiC,WAAW,EAAEC,cAAc,CAAC,GAAGlC,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACmC,MAAM,EAAEC,SAAS,CAAC,GAAGpC,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxC,MAAM,CAACqC,MAAM,EAAEC,SAAS,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAACuC,UAAU,EAAEC,aAAa,CAAC,GAAGxC,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACyC,cAAc,EAAEC,iBAAiB,CAAC,GAAG1C,QAAQ,CAAC,KAAK,CAAC;;EAE3D;EACA,MAAM,CAAC2C,SAAS,EAAEC,YAAY,CAAC,GAAG5C,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC6C,UAAU,EAAEC,aAAa,CAAC,GAAG9C,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC+C,aAAa,EAAEC,gBAAgB,CAAC,GAAGhD,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACiD,OAAO,EAAEC,UAAU,CAAC,GAAGlD,QAAQ,CAAC;IACrC2C,SAAS,EAAE,KAAK;IAChBE,UAAU,EAAE,KAAK;IACjBE,aAAa,EAAE;EACjB,CAAC,CAAC;EAEF9C,SAAS,CAAC,MAAM;IACdkD,gBAAgB,CAAC,CAAC;IAClBC,cAAc,CAAC,CAAC;IAChB,IAAIvC,aAAa,EAAE;MACjBwC,iBAAiB,CAACxC,aAAa,CAAC;IAClC;EACF,CAAC,EAAE,CAACA,aAAa,CAAC,CAAC;EAEnB,MAAMsC,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,MAAMG,SAAS,GAAGnD,kBAAkB,CAAC,CAAC;IACtCmB,kBAAkB,CAACgC,SAAS,CAAC;EAC/B,CAAC;EAED,MAAMF,cAAc,GAAGA,CAAA,KAAM;IAC3B,MAAMG,KAAK,GAAGnD,iBAAiB,CAACoD,iBAAiB,CAAC,CAAC;IACnDhB,aAAa,CAACe,KAAK,CAAC;EACtB,CAAC;EAED,MAAMF,iBAAiB,GAAII,MAAM,IAAK;IACpCvC,WAAW,CAACuC,MAAM,CAACC,IAAI,IAAI,EAAE,CAAC;IAC9BtC,kBAAkB,CAACqC,MAAM,CAACE,WAAW,IAAI,EAAE,CAAC;IAC5CnC,iBAAiB,CAACiC,MAAM,CAACG,MAAM,IAAI,EAAE,CAAC;IAEtC,IAAIH,MAAM,CAACI,IAAI,KAAK,UAAU,IAAIJ,MAAM,CAACK,YAAY,EAAE;MACrD;MACA9C,oBAAoB,CAAC;QACnB+C,UAAU,EAAEN,MAAM,CAACK;MACrB,CAAC,CAAC;IACJ,CAAC,MAAM,IAAIL,MAAM,CAACI,IAAI,KAAK,UAAU,IAAIJ,MAAM,CAACK,YAAY,EAAE;MAC5D;MACA9C,oBAAoB,CAAC;QACnBgD,UAAU,EAAEP,MAAM,CAACK;MACrB,CAAC,CAAC;IACJ,CAAC,MAAM,IAAIL,MAAM,CAACI,IAAI,KAAK,aAAa,IAAIJ,MAAM,CAACK,YAAY,EAAE;MAC/D;MACA9C,oBAAoB,CAAC;QACnBiD,aAAa,EAAER,MAAM,CAACK;MACxB,CAAC,CAAC;IACJ;;IAEA;IACA,IAAIL,MAAM,CAACS,SAAS,EAAE;MACpBlD,oBAAoB,CAACyC,MAAM,CAACS,SAAS,CAAC;IACxC;EACF,CAAC;;EAED;EACAjE,SAAS,CAAC,MAAM;IACdkE,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChCjB,UAAU,CAACkB,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAEzB,SAAS,EAAE;IAAK,CAAC,CAAC,CAAC;IAClD,IAAI;MACF,MAAM0B,QAAQ,GAAG,MAAMhE,UAAU,CAACiE,YAAY,CAAC,CAAC;MAChD1B,YAAY,CAACyB,QAAQ,CAACE,IAAI,IAAI,EAAE,CAAC;IACnC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;IAClD,CAAC,SAAS;MACRtB,UAAU,CAACkB,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEzB,SAAS,EAAE;MAAM,CAAC,CAAC,CAAC;IACrD;EACF,CAAC;EAED,MAAM+B,cAAc,GAAG,MAAOX,UAAU,IAAK;IAC3C,IAAI,CAACA,UAAU,EAAE;MACfjB,aAAa,CAAC,EAAE,CAAC;MACjBE,gBAAgB,CAAC,EAAE,CAAC;MACpB;IACF;IAEAE,UAAU,CAACkB,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAEvB,UAAU,EAAE;IAAK,CAAC,CAAC,CAAC;IACnD,IAAI;MACF,MAAMwB,QAAQ,GAAG,MAAMhE,UAAU,CAACsE,uBAAuB,CAACZ,UAAU,CAAC;MACrEjB,aAAa,CAACuB,QAAQ,CAACE,IAAI,IAAI,EAAE,CAAC;IACpC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD1B,aAAa,CAAC,EAAE,CAAC;IACnB,CAAC,SAAS;MACRI,UAAU,CAACkB,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEvB,UAAU,EAAE;MAAM,CAAC,CAAC,CAAC;IACtD;EACF,CAAC;EAED,MAAM+B,iBAAiB,GAAG,MAAOZ,UAAU,IAAK;IAC9C,IAAI,CAACA,UAAU,EAAE;MACfhB,gBAAgB,CAAC,EAAE,CAAC;MACpB;IACF;IAEAE,UAAU,CAACkB,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAErB,aAAa,EAAE;IAAK,CAAC,CAAC,CAAC;IACtD,IAAI;MACF,MAAMsB,QAAQ,GAAG,MAAMhE,UAAU,CAACwE,0BAA0B,CAACb,UAAU,CAAC;MACxEhB,gBAAgB,CAACqB,QAAQ,CAACE,IAAI,IAAI,EAAE,CAAC;IACvC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpDxB,gBAAgB,CAAC,EAAE,CAAC;IACtB,CAAC,SAAS;MACRE,UAAU,CAACkB,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAErB,aAAa,EAAE;MAAM,CAAC,CAAC,CAAC;IACzD;EACF,CAAC;EAED,MAAM+B,oBAAoB,GAAIC,CAAC,IAAK;IAClC,MAAMhB,UAAU,GAAGgB,CAAC,CAACC,MAAM,CAACC,KAAK;IACjC,MAAMC,QAAQ,GAAGvC,SAAS,CAACwC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAKC,QAAQ,CAACvB,UAAU,CAAC,CAAC;IAEnE/C,oBAAoB,CAAC;MACnB+C,UAAU,EAAEA,UAAU,IAAI,IAAI;MAC9BC,UAAU,EAAE,IAAI;MAChBC,aAAa,EAAE,IAAI;MACnBiB,QAAQ,EAAEA,QAAQ,IAAI,IAAI;MAC1BK,QAAQ,EAAE,IAAI;MACdC,WAAW,EAAE;IACf,CAAC,CAAC;IAEF1C,aAAa,CAAC,EAAE,CAAC;IACjBE,gBAAgB,CAAC,EAAE,CAAC;IAEpB,IAAIe,UAAU,EAAE;MACdW,cAAc,CAACX,UAAU,CAAC;IAC5B;EACF,CAAC;EAED,MAAM0B,oBAAoB,GAAIV,CAAC,IAAK;IAClC,MAAMf,UAAU,GAAGe,CAAC,CAACC,MAAM,CAACC,KAAK;IACjC,MAAMM,QAAQ,GAAG1C,UAAU,CAACsC,IAAI,CAACO,CAAC,IAAIA,CAAC,CAACL,EAAE,KAAKC,QAAQ,CAACtB,UAAU,CAAC,CAAC;IAEpEhD,oBAAoB,CAACoD,IAAI,KAAK;MAC5B,GAAGA,IAAI;MACPJ,UAAU,EAAEA,UAAU,IAAI,IAAI;MAC9BC,aAAa,EAAE,IAAI;MACnBsB,QAAQ,EAAEA,QAAQ,IAAI,IAAI;MAC1BC,WAAW,EAAE;IACf,CAAC,CAAC,CAAC;IAEHxC,gBAAgB,CAAC,EAAE,CAAC;IAEpB,IAAIgB,UAAU,EAAE;MACdY,iBAAiB,CAACZ,UAAU,CAAC;IAC/B;EACF,CAAC;EAED,MAAM2B,uBAAuB,GAAIZ,CAAC,IAAK;IACrC,MAAMd,aAAa,GAAGc,CAAC,CAACC,MAAM,CAACC,KAAK;IACpC,MAAMO,WAAW,GAAGzC,aAAa,CAACoC,IAAI,CAACS,EAAE,IAAIA,EAAE,CAACP,EAAE,KAAKC,QAAQ,CAACrB,aAAa,CAAC,CAAC;IAE/EjD,oBAAoB,CAACoD,IAAI,KAAK;MAC5B,GAAGA,IAAI;MACPH,aAAa,EAAEA,aAAa,IAAI,IAAI;MACpCuB,WAAW,EAAEA,WAAW,IAAI;IAC9B,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMK,iBAAiB,GAAIC,KAAK,IAAK;IACnC,MAAMC,UAAU,GAAGxE,cAAc,CAACyE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,GAAG,KAAKJ,KAAK,CAACI,GAAG,CAAC;IAEhE,IAAIH,UAAU,EAAE;MACdvE,iBAAiB,CAAC4C,IAAI,IAAIA,IAAI,CAAC+B,MAAM,CAACF,CAAC,IAAIA,CAAC,CAACC,GAAG,KAAKJ,KAAK,CAACI,GAAG,CAAC,CAAC;IAClE,CAAC,MAAM;MACL1E,iBAAiB,CAAC4C,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;QAAE,GAAG0B;MAAM,CAAC,CAAC,CAAC;IACpD;EACF,CAAC;EAED,MAAMM,iBAAiB,GAAIN,KAAK,IAAK;IACnC5D,cAAc,CAAC4D,KAAK,CAAC;IACrB9D,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,MAAMqE,qBAAqB,GAAIC,YAAY,IAAK;IAC9C9E,iBAAiB,CAAC4C,IAAI,IACpBA,IAAI,CAACmC,GAAG,CAACN,CAAC,IAAIA,CAAC,CAACC,GAAG,KAAKI,YAAY,CAACJ,GAAG,GAAGI,YAAY,GAAGL,CAAC,CAC7D,CAAC;IACDjE,kBAAkB,CAAC,KAAK,CAAC;IACzBE,cAAc,CAAC,IAAI,CAAC;EACtB,CAAC;EAED,MAAMsE,eAAe,GAAGA,CAACC,SAAS,EAAEC,OAAO,KAAK;IAC9C,MAAMC,SAAS,GAAG,CAAC,GAAGpF,cAAc,CAAC;IACrC,MAAM,CAACqF,UAAU,CAAC,GAAGD,SAAS,CAACE,MAAM,CAACJ,SAAS,EAAE,CAAC,CAAC;IACnDE,SAAS,CAACE,MAAM,CAACH,OAAO,EAAE,CAAC,EAAEE,UAAU,CAAC;IACxCpF,iBAAiB,CAACmF,SAAS,CAAC;EAC9B,CAAC;EAED,MAAMG,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,MAAMC,UAAU,GAAGC,YAAY,CAAC,CAAC;IAEjC,IAAI,CAACD,UAAU,CAACE,OAAO,EAAE;MACvB7E,SAAS,CAAC2E,UAAU,CAAC5E,MAAM,CAAC;MAC5B;IACF;IAEAG,SAAS,CAAC,IAAI,CAAC;IACf,IAAI;MACF;MACA,MAAM4E,iBAAiB,GAAG;QACxB,GAAGnG,iBAAiB;QACpB;QACAgD,UAAU,EAAEhD,iBAAiB,CAACgD,UAAU;QACxCmB,QAAQ,EAAEnE,iBAAiB,CAACmE,QAAQ;QACpC;QACAlB,UAAU,EAAEjD,iBAAiB,CAACiD,UAAU,IAAI,IAAI;QAChDuB,QAAQ,EAAExE,iBAAiB,CAACwE,QAAQ,IAAI,IAAI;QAC5C;QACAtB,aAAa,EAAElD,iBAAiB,CAACkD,aAAa,IAAI,IAAI;QACtDuB,WAAW,EAAEzE,iBAAiB,CAACyE,WAAW,IAAI;MAChD,CAAC;MAED,MAAM/B,MAAM,GAAG;QACbC,IAAI,EAAEzC,QAAQ;QACd0C,WAAW,EAAExC,eAAe;QAC5ByC,MAAM,EAAErC,cAAc;QACtB2C,SAAS,EAAEgD,iBAAiB;QAC5BC,QAAQ,EAAE;UACRC,YAAY,EAAE,IAAI;UAClBC,sBAAsB,EAAE,IAAI;UAC5BC,gBAAgB,EAAE;QACpB;MACF,CAAC;MAED,IAAIC,WAAW;MACf;MACA,IAAIxG,iBAAiB,CAACkD,aAAa,EAAE;QACnCsD,WAAW,GAAGnH,iBAAiB,CAACoH,cAAc,CAAC,aAAa,EAAEzG,iBAAiB,CAACkD,aAAa,EAAER,MAAM,CAAC;MACxG,CAAC,MAAM,IAAI1C,iBAAiB,CAACiD,UAAU,EAAE;QACvCuD,WAAW,GAAGnH,iBAAiB,CAACoH,cAAc,CAAC,UAAU,EAAEzG,iBAAiB,CAACiD,UAAU,EAAEP,MAAM,CAAC;MAClG,CAAC,MAAM,IAAI1C,iBAAiB,CAACgD,UAAU,EAAE;QACvCwD,WAAW,GAAGnH,iBAAiB,CAACoH,cAAc,CAAC,UAAU,EAAEzG,iBAAiB,CAACgD,UAAU,EAAEN,MAAM,CAAC;MAClG,CAAC,MAAM;QACL,MAAM,IAAIgE,KAAK,CAAC,mCAAmC,CAAC;MACtD;;MAEA;MACArE,cAAc,CAAC,CAAC;MAEhB,IAAIzC,MAAM,EAAE;QACVA,MAAM,CAAC4G,WAAW,CAAC;MACrB,CAAC,MAAM;QACL;QACAG,KAAK,CAAC,wCAAwC,CAAC;QAC/C;QACAxG,WAAW,CAAC,EAAE,CAAC;QACfE,kBAAkB,CAAC,EAAE,CAAC;QACtBI,iBAAiB,CAAC,EAAE,CAAC;QACrBR,oBAAoB,CAAC,CAAC,CAAC,CAAC;MAC1B;IACF,CAAC,CAAC,OAAOwD,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1CpC,SAAS,CAAC;QAAEuF,OAAO,EAAEnD,KAAK,CAACoD;MAAQ,CAAC,CAAC;IACvC,CAAC,SAAS;MACRtF,SAAS,CAAC,KAAK,CAAC;IAClB;EACF,CAAC;EAED,MAAM0E,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAM7E,MAAM,GAAG,CAAC,CAAC;IAEjB,IAAI,CAAClB,QAAQ,CAAC4G,IAAI,CAAC,CAAC,EAAE;MACpB1F,MAAM,CAAClB,QAAQ,GAAG,uBAAuB;IAC3C;;IAEA;IACA,IAAI,CAACF,iBAAiB,CAACgD,UAAU,EAAE;MACjC5B,MAAM,CAAC+B,SAAS,GAAG,0BAA0B;IAC/C,CAAC,MAAM,IAAInD,iBAAiB,CAACkD,aAAa,IAAI,CAAClD,iBAAiB,CAACiD,UAAU,EAAE;MAC3E7B,MAAM,CAAC+B,SAAS,GAAG,sDAAsD;IAC3E;IAEA,IAAI3C,cAAc,CAACuG,MAAM,KAAK,CAAC,EAAE;MAC/B3F,MAAM,CAACyB,MAAM,GAAG,kCAAkC;IACpD;;IAEA;IACA,MAAMmE,cAAc,GAAG,CAAC,MAAM,EAAE,cAAc,EAAE,QAAQ,CAAC;IACzD,MAAMC,iBAAiB,GAAGzG,cAAc,CAACgF,GAAG,CAACN,CAAC,IAAIA,CAAC,CAACC,GAAG,CAAC;IACxD,MAAM+B,eAAe,GAAGF,cAAc,CAAC5B,MAAM,CAAC+B,EAAE,IAAI,CAACF,iBAAiB,CAACG,QAAQ,CAACD,EAAE,CAAC,CAAC;IAEpF,IAAID,eAAe,CAACH,MAAM,GAAG,CAAC,EAAE;MAC9B3F,MAAM,CAAC4F,cAAc,GAAG,4BAA4BE,eAAe,CAACG,IAAI,CAAC,IAAI,CAAC,EAAE;IAClF;IAEA,OAAO;MACLnB,OAAO,EAAEoB,MAAM,CAACC,IAAI,CAACnG,MAAM,CAAC,CAAC2F,MAAM,KAAK,CAAC;MACzC3F;IACF,CAAC;EACH,CAAC;EAED,MAAMoG,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,IAAIC,QAAQ,GAAGnH,eAAe;;IAE9B;IACA,IAAII,cAAc,KAAK,KAAK,EAAE;MAC5B+G,QAAQ,GAAGA,QAAQ,CAACrC,MAAM,CAACL,KAAK,IAAIA,KAAK,CAAC2C,OAAO,KAAKhH,cAAc,CAAC;IACvE;;IAEA;IACA,IAAIE,UAAU,EAAE;MACd6G,QAAQ,GAAGA,QAAQ,CAACrC,MAAM,CAACL,KAAK,IAC9BA,KAAK,CAAC4C,KAAK,CAACC,WAAW,CAAC,CAAC,CAACR,QAAQ,CAACxG,UAAU,CAACgH,WAAW,CAAC,CAAC,CAAC,IAC5D7C,KAAK,CAACI,GAAG,CAACyC,WAAW,CAAC,CAAC,CAACR,QAAQ,CAACxG,UAAU,CAACgH,WAAW,CAAC,CAAC,CAC3D,CAAC;IACH;IAEA,OAAOH,QAAQ;EACjB,CAAC;EAED,MAAMI,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,MAAMC,MAAM,GAAG;MAAEC,GAAG,EAAEzH,eAAe,CAACyG;IAAO,CAAC;IAC9CO,MAAM,CAACC,IAAI,CAACpI,sBAAsB,CAAC,CAAC6I,OAAO,CAACC,UAAU,IAAI;MACxDH,MAAM,CAACG,UAAU,CAAC,GAAG3H,eAAe,CAAC8E,MAAM,CAACF,CAAC,IAAIA,CAAC,CAACwC,OAAO,KAAKO,UAAU,CAAC,CAAClB,MAAM;IACnF,CAAC,CAAC;IACF,OAAOe,MAAM;EACf,CAAC;EAED,MAAMI,aAAa,GAAGL,gBAAgB,CAAC,CAAC;EACxC,MAAMM,cAAc,GAAGX,iBAAiB,CAAC,CAAC;EAE1C,oBACE9H,OAAA;IAAK0I,SAAS,EAAC,cAAc;IAAAC,QAAA,gBAC3B3I,OAAA;MAAK0I,SAAS,EAAC,qBAAqB;MAAAC,QAAA,gBAClC3I,OAAA;QAAA2I,QAAA,EAAI;MAAY;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACrB/I,OAAA;QAAK0I,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7B3I,OAAA;UACEoD,IAAI,EAAC,QAAQ;UACb4F,OAAO,EAAEA,CAAA,KAAM3H,cAAc,CAAC,IAAI,CAAE;UACpCqH,SAAS,EAAC,mBAAmB;UAC7BO,QAAQ,EAAEnI,cAAc,CAACuG,MAAM,KAAK,CAAE;UAAAsB,QAAA,EACvC;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT/I,OAAA;UACEoD,IAAI,EAAC,QAAQ;UACb4F,OAAO,EAAE3C,UAAW;UACpBqC,SAAS,EAAC,iBAAiB;UAC3BO,QAAQ,EAAErH,MAAO;UAAA+G,QAAA,EAEhB/G,MAAM,GAAG,WAAW,GAAG;QAAW;UAAAgH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC,EACR5I,QAAQ,iBACPH,OAAA;UACEoD,IAAI,EAAC,QAAQ;UACb4F,OAAO,EAAE7I,QAAS;UAClBuI,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAC5B;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAELrH,MAAM,CAACwF,OAAO,iBACblH,OAAA;MAAK0I,SAAS,EAAC,mBAAmB;MAAAC,QAAA,EAAEjH,MAAM,CAACwF;IAAO;MAAA0B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CACzD,EAGAnB,MAAM,CAACC,IAAI,CAACnG,MAAM,CAAC,CAAC2F,MAAM,GAAG,CAAC,iBAC7BrH,OAAA;MAAK0I,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChC3I,OAAA;QAAA2I,QAAA,EAAI;MAAgC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACzC/I,OAAA;QAAA2I,QAAA,EACGf,MAAM,CAACsB,OAAO,CAACxH,MAAM,CAAC,CAACoE,GAAG,CAAC,CAAC,CAACL,GAAG,EAAE0B,OAAO,CAAC,kBACzCnH,OAAA;UAAA2I,QAAA,EAAexB;QAAO,GAAb1B,GAAG;UAAAmD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAe,CAC5B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CACN,eAED/I,OAAA;MAAK0I,SAAS,EAAC,sBAAsB;MAAAC,QAAA,gBAEnC3I,OAAA;QAAK0I,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAE3B3I,OAAA;UAAK0I,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7B3I,OAAA;YAAK0I,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7B3I,OAAA;cAAA2I,QAAA,GAAI,eAAa,EAAC7G,UAAU,CAACuF,MAAM,EAAC,GAAC;YAAA;cAAAuB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC1C/I,OAAA;cACEoD,IAAI,EAAC,QAAQ;cACbsF,SAAS,EAAC,UAAU;cACpBM,OAAO,EAAEA,CAAA,KAAM/G,iBAAiB,CAAC,CAACD,cAAc,CAAE;cAAA2G,QAAA,EAEjD3G,cAAc,GAAG,MAAM,GAAG;YAAM;cAAA4G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,EAEL/G,cAAc,iBACbhC,OAAA;YAAK0I,SAAS,EAAC,kBAAkB;YAAAC,QAAA,EAC9B7G,UAAU,CAACuF,MAAM,KAAK,CAAC,gBACtBrH,OAAA;cAAG0I,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAC;YAAiD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,GAErFjH,UAAU,CAACgE,GAAG,CAAEqD,IAAI,iBAClBnJ,OAAA;cAAoB0I,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAC7C3I,OAAA;gBAAK0I,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,gBAC/B3I,OAAA;kBAAA2I,QAAA,EAAKQ,IAAI,CAAClG;gBAAI;kBAAA2F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACpB/I,OAAA;kBAAM0I,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,EAAEQ,IAAI,CAAC/F;gBAAI;kBAAAwF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD,CAAC,eACN/I,OAAA;gBAAG0I,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,EAAEQ,IAAI,CAACjG,WAAW,IAAI;cAAgB;gBAAA0F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC1E/I,OAAA;gBAAK0I,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxB3I,OAAA;kBAAA2I,QAAA,GAAOQ,IAAI,CAACC,OAAO,CAACC,UAAU,EAAC,SAAO;gBAAA;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC7C/I,OAAA;kBAAA2I,QAAA,EAAM;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACd/I,OAAA;kBAAA2I,QAAA,GAAM,UAAQ,EAAC,IAAIW,IAAI,CAACH,IAAI,CAACI,SAAS,CAAC,CAACC,kBAAkB,CAAC,CAAC;gBAAA;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjE,CAAC,eACN/I,OAAA;gBAAK0I,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBAC3B3I,OAAA;kBACEoD,IAAI,EAAC,QAAQ;kBACbsF,SAAS,EAAC,uBAAuB;kBACjCM,OAAO,EAAEA,CAAA,KAAMpG,iBAAiB,CAACuG,IAAI,CAAE;kBAAAR,QAAA,EACxC;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACT/I,OAAA;kBACEoD,IAAI,EAAC,QAAQ;kBACbsF,SAAS,EAAC,sBAAsB;kBAChCM,OAAO,EAAEA,CAAA,KAAM;oBACb,IAAIS,MAAM,CAACC,OAAO,CAAC,4CAA4C,CAAC,EAAE;sBAChE/J,iBAAiB,CAACgK,gBAAgB,CAACR,IAAI,CAAC/F,IAAI,EAAE+F,IAAI,CAAC9F,YAAY,CAAC;sBAChEV,cAAc,CAAC,CAAC;oBAClB;kBACF,CAAE;kBAAAgG,QAAA,EACH;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA,GA/BEI,IAAI,CAAC1D,GAAG;cAAAmD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAgCb,CACN;UACF;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGN/I,OAAA;UAAK0I,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7B3I,OAAA;YAAA2I,QAAA,EAAI;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAE3B/I,OAAA;YAAK0I,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB3I,OAAA;cAAA2I,QAAA,EAAO;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC1B/I,OAAA;cACEoD,IAAI,EAAC,MAAM;cACXoB,KAAK,EAAEhE,QAAS;cAChBoJ,QAAQ,EAAGtF,CAAC,IAAK7D,WAAW,CAAC6D,CAAC,CAACC,MAAM,CAACC,KAAK,CAAE;cAC7CqF,WAAW,EAAC,iBAAiB;cAC7BnB,SAAS,EAAEhH,MAAM,CAAClB,QAAQ,GAAG,OAAO,GAAG;YAAG;cAAAoI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC,EACDrH,MAAM,CAAClB,QAAQ,iBAAIR,OAAA;cAAK0I,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAEjH,MAAM,CAAClB;YAAQ;cAAAoI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvE,CAAC,eAEN/I,OAAA;YAAK0I,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB3I,OAAA;cAAA2I,QAAA,EAAO;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC1B/I,OAAA;cACEwE,KAAK,EAAE9D,eAAgB;cACvBkJ,QAAQ,EAAGtF,CAAC,IAAK3D,kBAAkB,CAAC2D,CAAC,CAACC,MAAM,CAACC,KAAK,CAAE;cACpDqF,WAAW,EAAC,wBAAwB;cACpCC,IAAI,EAAC;YAAG;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAGH,CAAC,eAGN/I,OAAA;UAAK0I,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7B3I,OAAA;YAAA2I,QAAA,GAAI,mBAAiB,EAAC7H,cAAc,CAACuG,MAAM,EAAC,GAAC;UAAA;YAAAuB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EACjDrH,MAAM,CAACyB,MAAM,iBAAInD,OAAA;YAAK0I,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAEjH,MAAM,CAACyB;UAAM;YAAAyF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,EACrErH,MAAM,CAAC4F,cAAc,iBAAItH,OAAA;YAAK0I,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAEjH,MAAM,CAAC4F;UAAc;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAEtF/I,OAAA;YAAK0I,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAC7B7H,cAAc,CAACuG,MAAM,KAAK,CAAC,gBAC1BrH,OAAA;cAAK0I,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAE7B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,GAENjI,cAAc,CAACgF,GAAG,CAAC,CAACT,KAAK,EAAE0E,KAAK,kBAC9B/J,OAAA;cAAqB0I,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7C3I,OAAA;gBAAK0I,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB3I,OAAA;kBAAM0I,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAEtD,KAAK,CAAC4C;gBAAK;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAClD/I,OAAA;kBAAM0I,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAEtD,KAAK,CAACjC;gBAAI;kBAAAwF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,EAC/C1D,KAAK,CAAC2E,QAAQ,iBAAIhK,OAAA;kBAAM0I,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChE,CAAC,eACN/I,OAAA;gBAAK0I,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC5B3I,OAAA;kBACEoD,IAAI,EAAC,QAAQ;kBACb4F,OAAO,EAAEA,CAAA,KAAMrD,iBAAiB,CAACN,KAAK,CAAE;kBACxCqD,SAAS,EAAC,UAAU;kBACpBuB,KAAK,EAAC,iBAAiB;kBAAAtB,QAAA,EACxB;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACT/I,OAAA;kBACEoD,IAAI,EAAC,QAAQ;kBACb4F,OAAO,EAAEA,CAAA,KAAMjD,eAAe,CAACgE,KAAK,EAAEG,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEJ,KAAK,GAAG,CAAC,CAAC,CAAE;kBAC9DrB,SAAS,EAAC,UAAU;kBACpBO,QAAQ,EAAEc,KAAK,KAAK,CAAE;kBACtBE,KAAK,EAAC,SAAS;kBAAAtB,QAAA,EAChB;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACT/I,OAAA;kBACEoD,IAAI,EAAC,QAAQ;kBACb4F,OAAO,EAAEA,CAAA,KAAMjD,eAAe,CAACgE,KAAK,EAAEG,IAAI,CAACE,GAAG,CAACtJ,cAAc,CAACuG,MAAM,GAAG,CAAC,EAAE0C,KAAK,GAAG,CAAC,CAAC,CAAE;kBACtFrB,SAAS,EAAC,UAAU;kBACpBO,QAAQ,EAAEc,KAAK,KAAKjJ,cAAc,CAACuG,MAAM,GAAG,CAAE;kBAC9C4C,KAAK,EAAC,WAAW;kBAAAtB,QAAA,EAClB;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACT/I,OAAA;kBACEoD,IAAI,EAAC,QAAQ;kBACb4F,OAAO,EAAEA,CAAA,KAAM5D,iBAAiB,CAACC,KAAK,CAAE;kBACxCqD,SAAS,EAAC,iBAAiB;kBAC3BuB,KAAK,EAAC,cAAc;kBAAAtB,QAAA,EACrB;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA,GAzCE1D,KAAK,CAACI,GAAG;cAAAmD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA0Cd,CACN;UACF;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN/I,OAAA;QAAK0I,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3B3I,OAAA;UAAK0I,SAAS,EAAC,eAAe;UAAAC,QAAA,eAC5B3I,OAAA;YAAA2I,QAAA,EAAI;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC,eAGN/I,OAAA;UAAK0I,SAAS,EAAC,qBAAqB;UAAC2B,KAAK,EAAE;YAC1CC,eAAe,EAAE,SAAS;YAC1BC,MAAM,EAAE,mBAAmB;YAC3BC,YAAY,EAAE,KAAK;YACnBC,OAAO,EAAE,QAAQ;YACjBC,MAAM,EAAE;UACV,CAAE;UAAA/B,QAAA,gBACA3I,OAAA;YAAIqK,KAAK,EAAE;cAAEK,MAAM,EAAE,YAAY;cAAEC,KAAK,EAAE;YAAU,CAAE;YAAAhC,QAAA,EAAC;UAEvD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAEL/I,OAAA;YAAK0I,SAAS,EAAC,qBAAqB;YAAC2B,KAAK,EAAE;cAC1CO,OAAO,EAAE,MAAM;cACfC,mBAAmB,EAAE,sCAAsC;cAC3DC,GAAG,EAAE;YACP,CAAE;YAAAnC,QAAA,gBAEA3I,OAAA;cAAK0I,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzB3I,OAAA;gBAAOqK,KAAK,EAAE;kBACZO,OAAO,EAAE,OAAO;kBAChBG,YAAY,EAAE,QAAQ;kBACtBC,UAAU,EAAE,KAAK;kBACjBL,KAAK,EAAE;gBACT,CAAE;gBAAAhC,QAAA,EAAC;cAEH;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR/I,OAAA;gBACEwE,KAAK,EAAElE,iBAAiB,CAACgD,UAAU,IAAI,EAAG;gBAC1CsG,QAAQ,EAAEvF,oBAAqB;gBAC/B4E,QAAQ,EAAEzG,OAAO,CAACN,SAAU;gBAC5BwG,SAAS,EAAEhH,MAAM,CAAC4B,UAAU,GAAG,OAAO,GAAG,EAAG;gBAC5C+G,KAAK,EAAE;kBACLY,KAAK,EAAE,MAAM;kBACbR,OAAO,EAAE,SAAS;kBAClBF,MAAM,EAAE,mBAAmB;kBAC3BC,YAAY,EAAE,KAAK;kBACnBU,QAAQ,EAAE,MAAM;kBAChBZ,eAAe,EAAE;gBACnB,CAAE;gBAAA3B,QAAA,gBAEF3I,OAAA;kBAAQwE,KAAK,EAAC,EAAE;kBAAAmE,QAAA,EAAC;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EACxC7G,SAAS,CAAC4D,GAAG,CAACrB,QAAQ,iBACrBzE,OAAA;kBAA0BwE,KAAK,EAAEC,QAAQ,CAACG,EAAG;kBAAA+D,QAAA,EAC1ClE,QAAQ,CAACxB;gBAAI,GADHwB,QAAQ,CAACG,EAAE;kBAAAgE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEhB,CACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC,EACRrH,MAAM,CAAC4B,UAAU,iBAChBtD,OAAA;gBAAKqK,KAAK,EAAE;kBAAEM,KAAK,EAAE,SAAS;kBAAEO,QAAQ,EAAE,UAAU;kBAAEC,SAAS,EAAE;gBAAU,CAAE;gBAAAxC,QAAA,EAC1EjH,MAAM,CAAC4B;cAAU;gBAAAsF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAGN/I,OAAA;cAAK0I,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzB3I,OAAA;gBAAOqK,KAAK,EAAE;kBACZO,OAAO,EAAE,OAAO;kBAChBG,YAAY,EAAE,QAAQ;kBACtBC,UAAU,EAAE,KAAK;kBACjBL,KAAK,EAAE;gBACT,CAAE;gBAAAhC,QAAA,EAAC;cAEH;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR/I,OAAA;gBACEwE,KAAK,EAAElE,iBAAiB,CAACiD,UAAU,IAAI,EAAG;gBAC1CqG,QAAQ,EAAE5E,oBAAqB;gBAC/BiE,QAAQ,EAAE,CAAC3I,iBAAiB,CAACgD,UAAU,IAAId,OAAO,CAACJ,UAAW;gBAC9DsG,SAAS,EAAEhH,MAAM,CAAC6B,UAAU,GAAG,OAAO,GAAG,EAAG;gBAC5C8G,KAAK,EAAE;kBACLY,KAAK,EAAE,MAAM;kBACbR,OAAO,EAAE,SAAS;kBAClBF,MAAM,EAAE,mBAAmB;kBAC3BC,YAAY,EAAE,KAAK;kBACnBU,QAAQ,EAAE,MAAM;kBAChBZ,eAAe,EAAE;gBACnB,CAAE;gBAAA3B,QAAA,gBAEF3I,OAAA;kBAAQwE,KAAK,EAAC,EAAE;kBAAAmE,QAAA,EACb,CAACrI,iBAAiB,CAACgD,UAAU,GAAG,uBAAuB,GAAG;gBAA4B;kBAAAsF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjF,CAAC,EACR3G,UAAU,CAAC0D,GAAG,CAAChB,QAAQ,iBACtB9E,OAAA;kBAA0BwE,KAAK,EAAEM,QAAQ,CAACF,EAAG;kBAAA+D,QAAA,EAC1C7D,QAAQ,CAAC7B;gBAAI,GADH6B,QAAQ,CAACF,EAAE;kBAAAgE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEhB,CACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC,EACRrH,MAAM,CAAC6B,UAAU,iBAChBvD,OAAA;gBAAKqK,KAAK,EAAE;kBAAEM,KAAK,EAAE,SAAS;kBAAEO,QAAQ,EAAE,UAAU;kBAAEC,SAAS,EAAE;gBAAU,CAAE;gBAAAxC,QAAA,EAC1EjH,MAAM,CAAC6B;cAAU;gBAAAqF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAGN/I,OAAA;cAAK0I,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzB3I,OAAA;gBAAOqK,KAAK,EAAE;kBACZO,OAAO,EAAE,OAAO;kBAChBG,YAAY,EAAE,QAAQ;kBACtBC,UAAU,EAAE,KAAK;kBACjBL,KAAK,EAAE;gBACT,CAAE;gBAAAhC,QAAA,EAAC;cAEH;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR/I,OAAA;gBACEwE,KAAK,EAAElE,iBAAiB,CAACkD,aAAa,IAAI,EAAG;gBAC7CoG,QAAQ,EAAE1E,uBAAwB;gBAClC+D,QAAQ,EAAE,CAAC3I,iBAAiB,CAACiD,UAAU,IAAIf,OAAO,CAACF,aAAc;gBACjE+H,KAAK,EAAE;kBACLY,KAAK,EAAE,MAAM;kBACbR,OAAO,EAAE,SAAS;kBAClBF,MAAM,EAAE,mBAAmB;kBAC3BC,YAAY,EAAE,KAAK;kBACnBU,QAAQ,EAAE,MAAM;kBAChBZ,eAAe,EAAE;gBACnB,CAAE;gBAAA3B,QAAA,gBAEF3I,OAAA;kBAAQwE,KAAK,EAAC,EAAE;kBAAAmE,QAAA,EACb,CAACrI,iBAAiB,CAACiD,UAAU,GAAG,uBAAuB,GAAG;gBAA+B;kBAAAqF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpF,CAAC,EACRzG,aAAa,CAACwD,GAAG,CAACf,WAAW,iBAC5B/E,OAAA;kBAA6BwE,KAAK,EAAEO,WAAW,CAACH,EAAG;kBAAA+D,QAAA,EAChD5D,WAAW,CAAC9B;gBAAI,GADN8B,WAAW,CAACH,EAAE;kBAAAgE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEnB,CACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAELrH,MAAM,CAAC+B,SAAS,iBACfzD,OAAA;YAAKqK,KAAK,EAAE;cAAEM,KAAK,EAAE,SAAS;cAAEO,QAAQ,EAAE,UAAU;cAAEC,SAAS,EAAE;YAAS,CAAE;YAAAxC,QAAA,EACzEjH,MAAM,CAAC+B;UAAS;YAAAmF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGN/I,OAAA;UAAK0I,SAAS,EAAC,iBAAiB;UAAAC,QAAA,eAC9B3I,OAAA;YACEoD,IAAI,EAAC,MAAM;YACXyG,WAAW,EAAC,kBAAkB;YAC9BrF,KAAK,EAAEtD,UAAW;YAClB0I,QAAQ,EAAGtF,CAAC,IAAKnD,aAAa,CAACmD,CAAC,CAACC,MAAM,CAACC,KAAK,CAAE;YAC/CkE,SAAS,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGN/I,OAAA;UAAK0I,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3B3I,OAAA;YACEoD,IAAI,EAAC,QAAQ;YACb4F,OAAO,EAAEA,CAAA,KAAM/H,iBAAiB,CAAC,KAAK,CAAE;YACxCyH,SAAS,EAAE,eAAe1H,cAAc,KAAK,KAAK,GAAG,QAAQ,GAAG,EAAE,EAAG;YAAA2H,QAAA,GACtE,OACM,EAACH,aAAa,CAACH,GAAG,EAAC,GAC1B;UAAA;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EACRnB,MAAM,CAACsB,OAAO,CAACzJ,sBAAsB,CAAC,CAACqG,GAAG,CAAC,CAAC,CAACyC,UAAU,EAAEP,OAAO,CAAC,kBAChEhI,OAAA;YAEEoD,IAAI,EAAC,QAAQ;YACb4F,OAAO,EAAEA,CAAA,KAAM/H,iBAAiB,CAACsH,UAAU,CAAE;YAC7CG,SAAS,EAAE,eAAe1H,cAAc,KAAKuH,UAAU,GAAG,QAAQ,GAAG,EAAE,EAAG;YAAAI,QAAA,GAEzEX,OAAO,CAACiC,KAAK,EAAC,IAAE,EAACzB,aAAa,CAACD,UAAU,CAAC,EAAC,GAC9C;UAAA,GANOA,UAAU;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAMT,CACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGN/I,OAAA;UAAK0I,SAAS,EAAC,aAAa;UAAAC,QAAA,EACzBF,cAAc,CAAC3C,GAAG,CAACT,KAAK,IAAI;YAC3B,MAAMC,UAAU,GAAGxE,cAAc,CAACyE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,GAAG,KAAKJ,KAAK,CAACI,GAAG,CAAC;YAChE,oBACEzF,OAAA;cAEE0I,SAAS,EAAE,cAAcpD,UAAU,GAAG,UAAU,GAAG,EAAE,EAAG;cACxD0D,OAAO,EAAEA,CAAA,KAAM5D,iBAAiB,CAACC,KAAK,CAAE;cAAAsD,QAAA,gBAExC3I,OAAA;gBAAK0I,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,eAC7B3I,OAAA;kBACEoD,IAAI,EAAC,UAAU;kBACfgI,OAAO,EAAE9F,UAAW;kBACpBsE,QAAQ,EAAEA,CAAA,KAAMxE,iBAAiB,CAACC,KAAK;gBAAE;kBAAAuD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1C;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN/I,OAAA;gBAAK0I,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC5B3I,OAAA;kBAAK0I,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAEtD,KAAK,CAAC4C;gBAAK;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC/C/I,OAAA;kBAAK0I,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzB3I,OAAA;oBAAM0I,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAEtD,KAAK,CAACjC;kBAAI;oBAAAwF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,EAC/C1D,KAAK,CAAC2E,QAAQ,iBAAIhK,OAAA;oBAAM0I,SAAS,EAAC,gBAAgB;oBAAAC,QAAA,EAAC;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,EAClE1D,KAAK,CAACgG,WAAW,iBAAIrL,OAAA;oBAAM0I,SAAS,EAAC,mBAAmB;oBAAAC,QAAA,EAAC;kBAAW;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA,GAlBD1D,KAAK,CAACI,GAAG;cAAAmD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAmBX,CAAC;UAEV,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLzH,eAAe,IAAIE,WAAW,iBAC7BxB,OAAA,CAACH,gBAAgB;MACfwF,KAAK,EAAE7D,WAAY;MACnBtB,MAAM,EAAE0F,qBAAsB;MAC9BzF,QAAQ,EAAEA,CAAA,KAAM;QACdoB,kBAAkB,CAAC,KAAK,CAAC;QACzBE,cAAc,CAAC,IAAI,CAAC;MACtB;IAAE;MAAAmH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACF,EAEA3H,WAAW,iBACVpB,OAAA,CAACF,WAAW;MACVqD,MAAM,EAAErC,cAAe;MACvBN,QAAQ,EAAEA,QAAS;MACnB8K,OAAO,EAAEA,CAAA,KAAMjK,cAAc,CAAC,KAAK;IAAE;MAAAuH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtC,CACF;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC1I,EAAA,CAjwBIJ,WAAW;AAAAsL,EAAA,GAAXtL,WAAW;AAmwBjB,eAAeA,WAAW;AAAC,IAAAsL,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}