using CrmApi.Models.SubCategory;
using CrmApi.Models.Category;
using CrmApi.Models.Division;
using CrmApi.Repositories.SubCategory;
using CrmApi.Repositories.Category;
using CrmApi.Exceptions;

namespace CrmApi.Services.SubCategory
{
    public class SubCategoryService : ISubCategoryService
    {
        private readonly ISubCategoryRepository _subCategoryRepository;
        private readonly ICategoryRepository _categoryRepository;

        public SubCategoryService(ISubCategoryRepository subCategoryRepository, ICategoryRepository categoryRepository)
        {
            _subCategoryRepository = subCategoryRepository;
            _categoryRepository = categoryRepository;
        }

        public async Task<IEnumerable<SubCategoryResponse>> GetAllSubCategoriesAsync()
        {
            var subCategories = await _subCategoryRepository.GetAllWithRelationsAsync();
            return subCategories.Select(MapToResponse);
        }

        public async Task<SubCategoryResponse?> GetSubCategoryByIdAsync(int id)
        {
            var subCategory = await _subCategoryRepository.GetByIdWithRelationsAsync(id);
            return subCategory != null ? MapToResponse(subCategory) : null;
        }

        public async Task<IEnumerable<SubCategoryResponse>> GetSubCategoriesByCategoryAsync(int categoryId)
        {
            var subCategories = await _subCategoryRepository.GetByCategoryIdAsync(categoryId);
            return subCategories.Select(sc => new SubCategoryResponse
            {
                Id = sc.Id,
                CategoryId = sc.CategoryId,
                Name = sc.Name,
                CreatedAt = sc.CreatedAt,
                UpdatedAt = sc.UpdatedAt
            });
        }

        public async Task<SubCategoryResponse> CreateSubCategoryAsync(CreateSubCategoryRequest request)
        {
            // Check if category exists
            if (!await _categoryRepository.ExistsAsync(request.CategoryId))
                throw new NotFoundException($"Category with ID {request.CategoryId} not found");

            // Check if name already exists in category
            if (await _subCategoryRepository.NameExistsInCategoryAsync(request.Name, request.CategoryId))
                throw new BusinessException($"SubCategory with name '{request.Name}' already exists in this category");

            var subCategory = new Models.SubCategory.SubCategory
            {
                Name = request.Name,
                CategoryId = request.CategoryId
            };

            var createdSubCategory = await _subCategoryRepository.CreateAsync(subCategory);
            
            // Get the subcategory with relations for response
            var subCategoryWithRelations = await _subCategoryRepository.GetByIdWithRelationsAsync(createdSubCategory.Id);
            return MapToResponse(subCategoryWithRelations!);
        }

        public async Task<SubCategoryResponse> UpdateSubCategoryAsync(int id, UpdateSubCategoryRequest request)
        {
            var subCategory = await _subCategoryRepository.GetByIdAsync(id);
            if (subCategory == null)
                throw new NotFoundException($"SubCategory with ID {id} not found");

            // Check if category exists
            if (!await _categoryRepository.ExistsAsync(request.CategoryId))
                throw new NotFoundException($"Category with ID {request.CategoryId} not found");

            // Check name uniqueness in category
            if (await _subCategoryRepository.NameExistsInCategoryAsync(request.Name, request.CategoryId, id))
                throw new BusinessException($"SubCategory with name '{request.Name}' already exists in this category");

            subCategory.Name = request.Name;
            subCategory.CategoryId = request.CategoryId;
            
            var updatedSubCategory = await _subCategoryRepository.UpdateAsync(subCategory);
            
            // Get the subcategory with relations for response
            var subCategoryWithRelations = await _subCategoryRepository.GetByIdWithRelationsAsync(updatedSubCategory.Id);
            return MapToResponse(subCategoryWithRelations!);
        }

        public async Task<bool> DeleteSubCategoryAsync(int id)
        {
            if (!await _subCategoryRepository.ExistsAsync(id))
                throw new NotFoundException($"SubCategory with ID {id} not found");

            return await _subCategoryRepository.DeleteAsync(id);
        }

        private SubCategoryResponse MapToResponse(Models.SubCategory.SubCategory subCategory)
        {
            return new SubCategoryResponse
            {
                Id = subCategory.Id,
                CategoryId = subCategory.CategoryId,
                Name = subCategory.Name,
                CreatedAt = subCategory.CreatedAt,
                UpdatedAt = subCategory.UpdatedAt,
                Category = subCategory.Category != null ? new CategoryResponse
                {
                    Id = subCategory.Category.Id,
                    DivisionId = subCategory.Category.DivisionId,
                    Name = subCategory.Category.Name,
                    CreatedAt = subCategory.Category.CreatedAt,
                    UpdatedAt = subCategory.Category.UpdatedAt,
                    Division = subCategory.Category.Division != null ? new DivisionResponse
                    {
                        Id = subCategory.Category.Division.Id,
                        Name = subCategory.Category.Division.Name,
                        CreatedAt = subCategory.Category.Division.CreatedAt,
                        UpdatedAt = subCategory.Category.Division.UpdatedAt
                    } : null
                } : null
            };
        }
    }
}
